#! /bin/bash

# Function to find project root and setup PYTHONPATH
setup_pythonpath() {
    # Find project root by looking for project-root.md
    local current_dir="$PWD"
    local project_root=""
    
    while [[ "$current_dir" != "/" ]]; do
        if [[ -f "$current_dir/project-root.md" ]]; then
            project_root="$current_dir"
            break
        fi
        current_dir=$(dirname "$current_dir")
    done
    
    if [[ -n "$project_root" ]]; then
        # Handle case where PYTHONPATH is not set
        if [[ -z "$PYTHONPATH" ]]; then
            export PYTHONPATH="$project_root/interstage_project"
            echo "🔧 PYTHONPATH: export PYTHONPATH=\"$PYTHONPATH\" (newly set)"
        else
            export PYTHONPATH="$PYTHONPATH:$project_root/interstage_project"
            echo "🔧 PYTHONPATH: export PYTHONPATH=\"$PYTHONPATH\" (added: $project_root/interstage_project)"
        fi
    else
        echo "⚠️  Warning: project-root.md not found, PY<PERSON><PERSON><PERSON>TH not set"
    fi
}

# Function to load environment variables from local-variables.env
# and adapt Docker-specific values for local development
load_env_from_docker_file() {
    local script_dir="$(dirname "${BASH_SOURCE[0]}")"
    local env_file="$script_dir/docker_file_env/local-variables.env"
    
    if [[ ! -f "$env_file" ]]; then
        echo "Warning: $env_file not found"
        return 1
    fi
    
    echo "Loading environment variables from $env_file..."
    echo "========================================"
    
    # Setup PYTHONPATH first
    setup_pythonpath
    
    # Read the .env file line by line
    while IFS= read -r line || [[ -n "$line" ]]; do
        # Skip empty lines and comments
        if [[ -z "$line" || "$line" =~ ^[[:space:]]*# ]]; then
            continue
        fi
        
        # Parse key=value pairs
        if [[ "$line" =~ ^[[:space:]]*([^=]+)=(.*)$ ]]; then
            key="${BASH_REMATCH[1]}"
            value="${BASH_REMATCH[2]}"
            original_value="$value"
            
            # Remove leading/trailing whitespace from key
            key=$(echo "$key" | sed 's/^[[:space:]]*//;s/[[:space:]]*$//')
            
            # Remove quotes from value if present
            value=$(echo "$value" | sed 's/^"//;s/"$//')
            
            # Replace Docker-specific hostnames with localhost for certain variables
            case "$key" in
                "DB_HOST"|"CELERY_BROKER_URL"|"CELERY_RESULT_BACKEND"|"ELASTIC_CACHE_SERVER"|"ELASTIC_CACHE_RO_SERVER"|"EVENT_REDIS")
                    value=$(echo "$value" | sed 's/host\.docker\.internal/localhost/g')
                    value=$(echo "$value" | sed 's/ever-redis/localhost/g')
                    if [[ "$value" != "$original_value" ]]; then
                        echo "🔄 ADAPTED: export $key=\"$value\" (was: $original_value)"
                    else
                        echo "export $key=\"$value\""
                    fi
                    ;;
                "KAFKA_BROKER_URL")
                    value=$(echo "$value" | sed 's/^kafka:/localhost:/')
                    value=$(echo "$value" | sed 's/:9092/:29092/g')  # Also adjust port for local
                    if [[ "$value" != "$original_value" ]]; then
                        echo "🔄 ADAPTED: export $key=\"$value\" (was: $original_value)"
                    else
                        echo "export $key=\"$value\""
                    fi
                    ;;
                "DJANGO_ALLOWED_HOSTS")
                    # Add localhost-specific allowed hosts
                    value="$value .amazonaws.com localhost 0.0.0.0"
                    echo "🔄 ADAPTED: export $key=\"$value\" (was: $original_value)"
                    ;;
                "MANNAI_ENDPOINT"|"LIVY_HOST")
                    value=$(echo "$value" | sed 's/host\.docker\.internal/localhost/g')
                    if [[ "$value" != "$original_value" ]]; then
                        echo "🔄 ADAPTED: export $key=\"$value\" (was: $original_value)"
                    else
                        echo "export $key=\"$value\""
                    fi
                    ;;
                *)
                    echo "export $key=\"$value\""
                    ;;
            esac
            
            # Export the variable
            export "$key"="$value"
        fi
    done < "$env_file"
    
    echo "========================================"
    echo "✅ Environment variables loaded and adapted for local development"
}

# Load variables from local-variables.env with localhost adaptations
load_env_from_docker_file

# To make things work inside the notebook
export DJANGO_ALLOW_ASYNC_UNSAFE=true
export DJANGO_SETTINGS_MODULE=interstage_project.settings
export S3_FLOWER=everstage-qa-flower-dump
export ECS_CONTAINER_METADATA_URI_V4="http://localhost:51678/mock-metadata"

# Source this file to set the npm registry token
export NPM_REGISTRY_TOKEN_PART1=ghp_
export NPM_REGISTRY_TOKEN_PART2=192h1ztH90bo4WNpflRZQmHm4cHgWx0RHh2X
# Split the token into two parts to avoid github from flagging it as a secret
export NPM_REGISTRY_TOKEN=$NPM_REGISTRY_TOKEN_PART1$NPM_REGISTRY_TOKEN_PART2
