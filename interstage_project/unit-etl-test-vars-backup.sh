# environment variables that are required to be set before you run unit or etl tests
# Note - this is the complete list of variables required - make sure you source only this and NOT vars.sh
# Make sure to source this file in the corresponding github workflow file as well

export ENV=LOCALDEV
export EXECUTION_ENV=PYTEST
export USE_LOCAL_CACHE='true'
export EVERSTAGE_LOG_LEVEL=ERROR

export DEBUG=1
export SECRET_KEY='@r*&z!0xi#*-t80h0n+w3%4rqr7i(*f6(5k#1vjz70dfp7h^vt'
export DJANGO_ALLOWED_HOSTS=.amazonaws.com localhost

export AWS_ACCESS_KEY_ID=********************
export AWS_SECRET_ACCESS_KEY=+3R344w5ri3/ZVcViDGAcr6qiycjo76mhhN+dyGz

export SLACK_CLIENT_ID=*************.*************
export SLACK_CLIENT_SECRET=85256ec6ce2773e707e65c8d98663dfb
export SLACK_SIGNING_SECRET=0da4a917ceeddc4a9e01a3cde1e4a044
export DOCUSIGN_CLIENT_ID=************************************
export DOCUSIGN_CLIENT_SECRET=********-86fc-405b-8011-31834f6c912c

export SNOWFLAKE_USER=EVERSTAGE_TEST_ETL
export SNOWFLAKE_PASSWORD=Tes#Pwd3
export SNOWFLAKE_ACCOUNT=oea92247
export SNOWFLAKE_REGION=us-west-2
export SNOWFLAKE_WAREHOUSE=EVERSTAGE_TEST_ETL_LOCAL
export SNOWFLAKE_SCHEMA=PUBLIC
export SNOWFLAKE_ROLE=ACCOUNTADMIN

export STORMBREAKER_SNOWFLAKE_USER=$SNOWFLAKE_USER
export STORMBREAKER_SNOWFLAKE_PASSWORD=$SNOWFLAKE_PASSWORD
export STORMBREAKER_SNOWFLAKE_ACCOUNT=$SNOWFLAKE_ACCOUNT
export STORMBREAKER_SNOWFLAKE_REGION=$SNOWFLAKE_REGION
export STORMBREAKER_SNOWFLAKE_WAREHOUSE=$SNOWFLAKE_WAREHOUSE
export STORMBREAKER_SNOWFLAKE_SCHEMA=$SNOWFLAKE_SCHEMA
export STORMBREAKER_SNOWFLAKE_ROLE=$SNOWFLAKE_ROLE

export UDF_S3_LOCATION='s3://udf-bundle'

export TYPESENSE_API_KEY='xyz'
export TYPESENSE_HOST='xxx.a1.typesense.net'
export TYPESENSE_PORT='443'
export TYPESENSE_PROTOCOL='https'
export TYPESENSE_CONNECTION_TIMEOUT=5

export EFS_BASE_PATH=snapshot
export S3_REGION_NAME=us-west-1
