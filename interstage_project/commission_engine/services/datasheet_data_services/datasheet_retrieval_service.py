import copy
import decimal
import gc
import itertools
import logging
from typing import Union
from uuid import UUID

import numpy as np
import pandas as pd
import swifter
from django.core.cache import caches

from commission_engine.accessors.client_accessor import (
    get_client,
    get_ever_comparison,
    is_show_dangling_adjustments_enabled,
    should_use_multi_engine_stormbreaker,
)
from commission_engine.accessors.custom_object_accessor import (
    latest_knowledge_date_for_custom_objects,
)
from commission_engine.accessors.databook_accessor import (
    DatasheetAccessor,
    DatasheetVariableAccessor,
)
from commission_engine.accessors.dataheet_adjustments_accessor import (
    DatasheetAdjustmentsAccessor,
)
from commission_engine.accessors.report_object_accessor import (
    latest_knowledge_date_for_report_objects,
)
from commission_engine.accessors.skd_pkd_map_accessor import DbkdPkdMapAccessor
from commission_engine.data_readers.databook_reader import DatasheetDataReader
from commission_engine.models.databook_models import Datasheet
from commission_engine.services.commission_calculation_service.data_generator import (
    remove_nan_nat,
)
from commission_engine.services.commission_calculation_service.evaluate import (
    evaluate as evaluate_ast,
)
from commission_engine.services.commission_calculation_service.reference_data_type import (
    get_data_types,
    get_datatype_id_name_map,
)
from commission_engine.services.datasheet_data_services.constants import PIVOT_AGG_MAP
from commission_engine.utils.cache_utils import (
    get_ever_comparison_cache,
    set_ever_comparison_cache,
)
from commission_engine.utils.criteria_calculator_utils import create_ast
from commission_engine.utils.general_data import DATASHEET_TRANSFORMATIONS, SYNC_OBJECT
from commission_engine.utils.general_utils import (
    log_time_taken,
    replace_nan_nat,
    replace_nan_nat_for_df,
    set_accurate_column_types,
)
from commission_engine.utils.json_query_utils import agg_fn_map
from commission_engine.utils.report_utils import DataOrigin
from interstage_project.utils import LogWithContext
from spm.accessors.config_accessors.employee_accessor import EmployeeAccessor
from spm.accessors.variable_accessor import VariableDataTypeAccessor
from spm.services.datasheet_permission_services import (
    get_all_hidden_columns_in_a_datasheet_for_user,
)
from spm.services.datasheet_services import get_ordered_columns_for_given_datasheet_id
from spm.services.rbac_services import does_user_have_databook_manage_permission
from spm.types import ObjectMetaDataType
from spm.utils import get_window_based_calculated_fields

logger = logging.getLogger(__name__)

cache = caches["default"]


def get_datasheet(
    client_id,
    databook_id,
    datasheet_id,
):
    """
    Returns the active datasheet record for the given client_id,databook_id,datasheet_id
    """
    ds_acc = DatasheetAccessor(client_id)
    return ds_acc.get_data_by_datasheet_id(databook_id, datasheet_id)[0]


def get_datasheet_as_df(
    client_id,
    databook_id,
    datasheet_id,
    apply_adjustments=True,
    filters=[],
    logged_in_user_email=None,
    apply_datasheet_permissions=False,
):
    """
    Given a databook id and datasheet id, read the source data of the sheet and return
    only the data present in the datasheet as a dataframe with display name of the datasheet
    """
    data_source = get_datasheet(client_id, databook_id, datasheet_id)

    if should_use_multi_engine_stormbreaker(client_id):
        ds_reader = DatasheetDataReader(
            client_id,
            databook_id,
            datasheet_id,
            logged_in_user_email,
            apply_datasheet_permissions,
            compute_strategy="snowflake_flattened",
        )
    else:
        ds_reader = DatasheetDataReader(
            client_id,
            databook_id,
            datasheet_id,
            logged_in_user_email,
            apply_datasheet_permissions,
        )

    for filter in filters:
        column_name = filter["col_name"]
        value = filter["value"] if "value" in filter else None
        operator = filter["operator"]
        ds_reader.apply_data_filter(column_name, operator, value)

    try:
        data_df = ds_reader.fetch_data(
            as_data_frame=True, apply_adjustments=apply_adjustments
        )
    except PermissionError:
        data_df = pd.DataFrame()

    dsv_acc = DatasheetVariableAccessor(client_id)
    ds_vars = dsv_acc.get_variables_for_db_ds(
        data_source.databook_id, data_source.datasheet_id, as_dicts=False
    )
    var_type_map = {var.system_name: var.display_name for var in ds_vars}  # type: ignore

    # Rename the columns to display names
    result_data_df = data_df.rename(columns=var_type_map)  # type: ignore

    return result_data_df


def get_datasheet_export_data(
    client_id,
    knowledge_date,
    databook_id,
    datasheet_id,
    apply_adjustments,
    filters,
    logged_in_user_email,
    pivots=None,
    logger=None,
):
    """
    Given a databook id and datasheet id, read the source data of the sheet and return
    only the data present in the datasheet for export as csv along with the file name
    to be exported as (<datasheet_name>.csv)
    """
    logger = (
        logger
        if logger
        else LogWithContext(
            {
                "client_id": client_id,
                "databook_id": databook_id,
                "datasheet_id": datasheet_id,
            }
        )
    )
    ds_acc = DatasheetAccessor(client_id)
    data_source = ds_acc.get_data_by_datasheet_id(databook_id, datasheet_id)[0]
    if should_use_multi_engine_stormbreaker(client_id):
        ds_reader = DatasheetDataReader(
            client_id,
            databook_id,
            datasheet_id,
            logged_in_user_email,
            compute_strategy="snowflake_flattened",
        )
    else:
        ds_reader = DatasheetDataReader(
            client_id,
            databook_id,
            datasheet_id,
            logged_in_user_email,
        )
    for filter in filters:
        column_name = filter["col_name"]
        value = filter["value"] if "value" in filter else None
        operator = filter["operator"]
        ds_reader.apply_data_filter(column_name, operator, value)

    try:
        data = ds_reader.fetch_data(
            as_data_frame=True, apply_adjustments=apply_adjustments
        )
        # Desired datasheet column order
        ordered_datasheet_columns: list = get_ordered_columns_for_given_datasheet_id(
            client_id=client_id, databook_id=databook_id, datasheet_id=datasheet_id
        )
        # The strombreaker services will return the data in Ascending order of columns names(system_name)
        # Here we again Rearranging the columns in the order given by ordered_datasheet_columns
        ordered_columns = [
            column for column in ordered_datasheet_columns if column in data.columns
        ]

        data = data[ordered_columns]
    except PermissionError as ex:
        logger.error(f"Permission error while fetching data for export: {ex}")
        return pd.DataFrame().to_csv(index=False), f"{data_source.name}.csv"
    dsv_acc = DatasheetVariableAccessor(client_id)
    ds_vars = dsv_acc.get_variables_for_db_ds(
        data_source.databook_id, data_source.datasheet_id, as_dicts=False
    )
    manage_permissions_enabled = does_user_have_databook_manage_permission(
        client_id, logged_in_user_email
    )

    hidden_columns_for_given_source_id = set()
    if manage_permissions_enabled is False:
        hidden_columns_for_given_source_id = set(
            get_all_hidden_columns_in_a_datasheet_for_user(
                client_id, datasheet_id, logged_in_user_email
            )
        )

    var_datatype_map = {}
    var_type_map = {}

    for var in ds_vars:
        if var.system_name not in hidden_columns_for_given_source_id:
            var_type_map[var.system_name] = var.display_name
            var_datatype_map[var.system_name] = var.data_type_id

    # Apply pivots
    if pivots:
        datasheet_manage_permission = does_user_have_databook_manage_permission(
            client_id, logged_in_user_email
        )
        if not datasheet_manage_permission:
            hidden_columns = get_all_hidden_columns_in_a_datasheet_for_user(
                client_id, datasheet_id, logged_in_user_email
            )
            if hidden_columns:
                pivot_columns = set(
                    pivots["index"] + pivots["columns"] + pivots["values"]
                )
                if pivot_columns.intersection(hidden_columns):
                    logger.error("Permission error while applying pivot for export")
                    return pd.DataFrame().to_csv(index=False), f"{data_source.name}.csv"
        MAX_COLUMN_LIMIT = 500
        total_column_count = data[pivots["columns"][0]].nunique() * len(
            pivots["aggfunc"]
        )  # column will always be an array of one element always
        if total_column_count > MAX_COLUMN_LIMIT:
            raise Exception("DATA_PIVOT_MEMORY_ERROR")
        for key, val in pivots["aggfunc"].items():
            if (
                var_datatype_map[key] == 2
            ):  # checking if data_type is Date then formatting date column
                data[key] = pd.to_datetime(data[key]).dt.strftime("%d %b %Y")  # type: ignore
        data = pivot_and_sort_data(data, pivots, var_datatype_map)
        data.reset_index(inplace=True)
    else:
        for key, val in var_datatype_map.items():
            if val == 2:
                # remove milliseconds from datetime
                data[key] = pd.to_datetime(data[key]).dt.strftime("%Y-%m-%d %H:%M:%S")

    # Rename the columns to display names
    if pivots:
        rename_pivot_columns(data, pivots["aggfunc"], var_type_map)
    else:
        data.rename(columns=var_type_map, inplace=True)  # type: ignore

    filename = "{}.csv".format(data_source.name)
    # Fix date format to DD MMM YYYY
    return data.to_csv(index=False, date_format="%d %b %Y"), filename


def fetch_datasheet_data_from_table(
    client_id,
    databook_id,
    datasheet_id,
    logged_in_user_email,
    apply_datasheet_permissions=True,
    filters=None,
):
    """
    Fetch datasheet data from DB table.
    @param: client_id, databook_id, datasheet_id
    @return: dataFrame created from only data column of datasheet_data table
            Also, dataFrame datatype is set to its correct type
    """
    if should_use_multi_engine_stormbreaker(client_id):
        ds_reader = DatasheetDataReader(
            client_id,
            databook_id,
            datasheet_id,
            logged_in_user_email,
            apply_datasheet_permissions,
            compute_strategy="snowflake_flattened",
        )
    else:
        ds_reader = DatasheetDataReader(
            client_id,
            databook_id,
            datasheet_id,
            logged_in_user_email,
            apply_datasheet_permissions,
        )
    if filters:
        for filter in filters:
            column_name = filter["col_name"]
            value = filter["value"] if "value" in filter else None
            operator = filter["operator"]
            ds_reader.apply_data_filter(column_name, operator, value)

    return ds_reader.fetch_data(as_data_frame=True)


def handle_adjustments(
    client_id, databook_id, datasheet_id, data, adjustments_type_filter=None
):
    """
    Fetch adjustments for the datasheet_id
    @param: client_id, databook_id, datasheet_id, data
    @return: dataFrame with adjustments applied and list of dict which contains adjustments

    """
    df_adjustment_data_for_datasheet = DatasheetAdjustmentsAccessor(
        client_id
    ).get_adjustments_for_datasheet_as_frame(
        databook_id, datasheet_id, adjustments_type_filter
    )

    """
    Assuming the fact that length of adjustment data will be very minimal compared
    to whole data we have in table, we'll iterate the adjustment and apply individually
    all of them on top of the data we have.
    """

    # Add is_Adjustment column and set it to default as False in the dataFrame
    data["is_adjustment"] = False

    # If there are adjustments for the datasheet_id then apply them
    # else return the same dataFrame and empty list for adjustment data
    if len(df_adjustment_data_for_datasheet.index) != 0 and len(data.index) != 0:
        # making a copy of the dataFrame for future use of original dataFrame
        original_data = data.copy()
        adjustment_data: dict = {}
        cols_to_be_added = [
            "comments",
            "adjustment_number",
            "adjustment_id",
            "sub_adjustment_number",
            "is_global",
            "row_key",
        ]

        # ------------ UPDATE records Operation ------------

        # Filtering out only the UPDATE operation from all the adjustments
        update_operations_df = df_adjustment_data_for_datasheet[
            df_adjustment_data_for_datasheet.operation == "UPDATE"
        ]

        # converting UPDATE adjustments dataFrame into list of dicts
        #  and Iterating over each adjustment
        update_operations = update_operations_df.to_dict("records")
        for update_operation in update_operations:
            row_key = update_operation["original_row_key"]
            # constructing the adjustment details dictionary for each entry.
            adjustment_details = {
                "type": "UPDATE",
                "adjustment_date": update_operation["knowledge_begin_date"],
            }
            for column in cols_to_be_added:
                adjustment_details[column] = update_operation[column]
            original_values = {}
            for key in update_operation["data"].keys():
                record = data.loc[data["row_key"] == row_key, key]
                if not record.empty:
                    original_values[key] = data.loc[
                        data["row_key"] == row_key, key
                    ].iloc[0]
            # updating all the cells which matches the row key condition
            adjustment_id = str(update_operation["adjustment_id"])
            keys_list = ["is_adjustment", "adjustment_details", "original_values"]
            values_list = [True, adjustment_details, original_values]
            for key, value in update_operation["data"].items():
                keys_list.append(key)
                values_list.append(value)
            data.loc[data.row_key == row_key, keys_list] = values_list
            data_to_append = data[data.row_key == row_key].to_dict("records")
            # if data_to_append:  # will be there, just being extra cautious
            #     data_to_append[0]["is_adjustment"] = True
            #     data_to_append[0]["adjustment_details"] = adjustment_details
            #     data_to_append[0]["original_values"] = original_values

            if adjustment_id not in adjustment_data:
                adjustment_data[adjustment_id] = []
            adjustment_data[adjustment_id].append(data_to_append[0])

        # INSERT records Operation
        # Filtering out only the INSERT operation from all the adjustments
        insert_operations_df = df_adjustment_data_for_datasheet[
            df_adjustment_data_for_datasheet.operation == "INSERT"
        ]
        # converting INSERT adjustments dataFrame into list of dicts
        #  and Iterating over each adjustment
        insert_operations = insert_operations_df.to_dict("records")
        # row_key_to_keys_and_cols = {}
        for insert_operation in insert_operations:
            row_key = insert_operation["original_row_key"]
            if row_key not in original_data.row_key.unique():
                continue
            all_split_values_for_row_key = {}
            records_with_row_key = insert_operations_df[
                insert_operations_df["original_row_key"] == row_key
            ]
            records_with_row_key = records_with_row_key.to_dict("records")

            for idx, record in enumerate(records_with_row_key):
                if record["data"]:
                    all_split_values_for_row_key[idx] = record["data"]

            adjustment_details = {
                "type": "SPLIT",
                "split_values": all_split_values_for_row_key,
                "adjustment_date": insert_operation["knowledge_begin_date"],
            }
            for column in cols_to_be_added:
                adjustment_details[column] = insert_operation[column]
            original_record = original_data[original_data["row_key"] == row_key]
            original_values = {}
            if not original_record.empty:
                for key in insert_operation["data"].keys():
                    original_values[key] = original_record[key].iloc[0]
            original_record["is_adjustment"] = True
            original_record["adjustment_details"] = [adjustment_details]
            original_record["original_values"] = [original_values]
            original_record["row_key"] = insert_operation["row_key"]
            adjustment_id = str(insert_operation["adjustment_id"])
            # original_record["adjustment_id"] = str(insert_operation["adjustment_id"])

            for key, value in insert_operation["data"].items():
                original_record[key] = value
            data = pd.concat([data, pd.DataFrame(original_record)], ignore_index=True)

            if adjustment_id not in adjustment_data:
                adjustment_data[adjustment_id] = []
            adjustment_data[adjustment_id].append(original_record.to_dict("records")[0])
        # HANDLING IGNORE ADJUSTMENTS
        # Filtering out only the IGNORE operation from all the adjustments
        ignore_operations_df = df_adjustment_data_for_datasheet[
            df_adjustment_data_for_datasheet.operation == "IGNORE"
        ]
        # converting Ignore adjustments dataFrame into list of dicts
        #  and Iterating over each adjustment
        ignore_operations = ignore_operations_df.to_dict("records")
        for ignore_operation in ignore_operations:
            original_row_key = ignore_operation["original_row_key"]
            if original_row_key not in original_data.row_key.unique():
                continue
            adjustment_details = {
                "type": "IGNORE",
                "adjustment_date": ignore_operation["knowledge_begin_date"],
            }
            for column in cols_to_be_added:
                adjustment_details[column] = ignore_operation[column]
            data_to_append = data[data.row_key == original_row_key].to_dict("records")
            if data_to_append:
                data_to_append[0]["is_adjustment"] = True
                data_to_append[0]["adjustment_details"] = adjustment_details
                # adjustment_data.extend(data_to_append)
                adjustment_id = str(ignore_operation["adjustment_id"])
                if adjustment_id not in adjustment_data:
                    adjustment_data[adjustment_id] = []
                adjustment_data[adjustment_id].append(data_to_append[0])
        # removing all the ignored rows from the original data
        data = data[
            ~(
                (data.row_key.isin(ignore_operations_df.original_row_key))
                & (data.is_adjustment == False)
                # & (data.adjustment_details == None)
            )
        ]

        # set column type of dataFrame to their original datatype and return the
        # adjustment applied data
        ds_vars = DatasheetVariableAccessor(client_id).get_objects_by_datasheet_id(
            datasheet_id
        )
        data = set_accurate_column_types(ds_vars, data)
        return (data, adjustment_data)
    else:
        return data, {}


def add_row_key_column_to_datasheet_data(client_id, databook_id, datasheet_id, data):
    """
    @param: Dataframe
    @return: Dataframe with new column "row_key" added.
    Fetch primary keys columns of the the datasheet_id
    and construct row_key for each row.

    let's say primary keys are ["id","email"]
    and data is:
    | id | email | name | place |
    | 1  | dg@a  | dg    | ajj  |
    after the below function, data will be changed to:
    | id | email | name | place | row_key     |
    | 1  | dg@a  | dg    | ajj  | 1##::##dg@a |

    """
    primary_keys = DatasheetAccessor(client_id).get_primary_columns_for_datasheet(
        databook_id, datasheet_id
    )
    if len(data.index) != 0:
        data = remove_nan_nat(data)
        data["row_key"] = (
            data[primary_keys].astype(str).apply("##::##".join, axis=1)
        )  # make it lower case
        data["row_key"] = data["row_key"].str.lower()
    return data


def add_row_key_column_to_datasheet_data_after_adjustment(
    client_id, databook_id, datasheet_id, data
):
    """
    @param: Dataframe
    @return: Dataframe with new column "row_key" added.
    Fetch primary keys columns of the the datasheet_id
    and construct row_key for each row.

    let's say primary keys are ["id","email"]
    and data is:
    | id | email | name | place |
    | 1  | dg@a  | dg    | ajj  |
    after the below function, data will be changed to:
    | id | email | name | place | row_key_after_adjustment     |
    | 1  | dg@a  | dg    | ajj  | 1##::##dg@a                  |

    """
    primary_keys = DatasheetAccessor(client_id).get_primary_columns_for_datasheet(
        databook_id, datasheet_id
    )
    if len(data.index) != 0:
        data["row_key_after_adjustment"] = (
            data[primary_keys].astype(str).apply("##::##".join, axis=1)
        )  # make it lower case
        data["row_key_after_adjustment"] = data["row_key_after_adjustment"].str.lower()
    return data


def change_adjustment_structure(adjustment_data):
    """
    @param: List of dict containing adjustment
    @return: Dict with values as list of adjustment values,
            grouped by row_key.
    Example Input:
    [
        {"row_key": "<EMAIL>", "co_1_place": "Chennai"},
        {"row_key": "<EMAIL>", "co_1_country": "Norway"},
        {"row_key": "<EMAIL>", "co_1_country": "India"},
    ]
    Example Output:
    {
        "<EMAIL>" : [
                        {"row_key": "<EMAIL>", "co_1_place": "Chennai"},
                        {"row_key": "<EMAIL>", "co_1_country": "India"}
                    ],
        "<EMAIL>": [
                        {"row_key": "<EMAIL>", "co_1_country": "Norway"},
                    ]
        ,
    }
    """

    adjustment_data = pd.DataFrame(adjustment_data)
    adjustment_data = remove_nan_nat(adjustment_data)
    adjustment_data = adjustment_data.to_dict("records")
    adjustment_data_map = {}
    for adjustment in adjustment_data:
        if adjustment["row_key"] in adjustment_data_map:
            adjustment_data_map[adjustment["row_key"]].append(adjustment)
        else:
            adjustment_data_map[adjustment["row_key"]] = [adjustment]
    for adjustment in adjustment_data_map:
        if len(adjustment_data_map[adjustment]) > 1:
            adjustment_data_map[adjustment][0]["adjustment_details"] = (
                adjustment_data_map[adjustment][1]["adjustment_details"]
            )
            adjustment_data_map[adjustment][0]["adjustment_details"]["type"] = "Insert"
            adjustment_data_map[adjustment][0]["original_values"] = adjustment_data_map[
                adjustment
            ][1]["original_values"]
    return adjustment_data_map


def get_ast_or_str(variable):
    if variable["meta_data"] is None or "ast" not in variable["meta_data"]:
        if (
            not "rank" in variable["meta_data"]
            and not "rolling" in variable["meta_data"]
            and not "hierarchy" in variable["meta_data"]
        ):
            return "No expression!"
        return
    ast = copy.deepcopy(variable["meta_data"]["ast"])
    return ast


def add_row_calculated_fields_snowpark(
    client_id, datasheet_id, datasheet_data_df, knowledge_date
):
    """
    Evaluates the calculated fields in datasheet
    Parameters:
        client_id: string
        datasheet_id: string
        datasheet_data_df: pandas.DataFrame
    """
    logger = logging.getLogger(__name__)

    logger.info("BEGIN - evaluate calculated fields")
    ever_comparison = get_ever_comparison_cache(client_id)
    if ever_comparison is None:
        logger.info("ever_comparison flag not found in cache. Fetching from DB")
        ever_comparison = get_ever_comparison(client_id)
        set_ever_comparison_cache(client_id, ever_comparison)
    logger.info("BEGIN: Remove NaN and NaT")
    datasheet_data_df = remove_nan_nat(datasheet_data_df)
    logger.info("END: Remove NaN and NaT")
    data_type_map = get_data_types()
    datasheet_variables = get_datasheet_variables_for_the_given_kd(
        client_id, datasheet_id, knowledge_date
    )
    grouped_datasheet_variables = itertools.groupby(
        datasheet_variables, key=lambda variable: variable.field_order
    )
    client = get_client(client_id)
    for field_order, order_variables in grouped_datasheet_variables:
        if field_order == 0:
            continue
        for variable in order_variables:
            if variable.meta_data is None or "infix" not in variable.meta_data:
                if (
                    not "rank" in variable.meta_data
                    and not "rolling" in variable.meta_data
                    and not "hierarchy" in variable.meta_data
                ):
                    datasheet_data_df[variable.system_name] = "No expression!"
                continue
            infix_expression = variable.meta_data["infix"]
            ast_res = create_ast(infix_expression)
            ast = ast_res["ast"]

            def evaluate_ast_wrapper(row):
                res = evaluate_ast(
                    ast,
                    data={
                        "line_item": row.to_dict(),
                        "start_month": client.fiscal_start_month,
                        "client_id": client_id,
                    },
                    ever_comparison=ever_comparison,
                )
                if res == float("-inf"):
                    # If "NULL"/"Do Nothing" is enabled, then result could be -Infinity. Resetting it back to 0.
                    res = 0
                if (
                    variable.data_type_id != data_type_map["INTEGER"]
                    and variable.data_type_id != data_type_map["PERCENTAGE"]
                    and not isinstance(res, bool)
                    and res == 0
                ):
                    res = None
                if isinstance(res, decimal.Decimal):
                    res = float(res)
                return res

            logger.info(f"BEGIN - evaluate calculated field - '{variable.system_name}'")
            if not datasheet_data_df.empty:
                datasheet_data_df[variable.system_name] = (
                    datasheet_data_df.swifter.apply(evaluate_ast_wrapper, axis=1)
                )
                logger.info(
                    f"Removing nan nat values on computed calculated field - {variable.system_name}"
                )
                # Remove nan nat from the processed calculated field column
                datasheet_data_df[variable.system_name] = remove_nan_nat(
                    datasheet_data_df[variable.system_name]
                )
            else:
                datasheet_data_df[variable.system_name] = None
            logger.info(f"END - evaluate calculated field - '{variable.system_name}'")

    logger.info("END - evaluate calculated fields")
    return datasheet_data_df


def get_index_to_fetch(adjusted_data_len, page_size, page_num):
    full_adjustment_pages, rem_adjustment_len = divmod(adjusted_data_len, page_size)
    db_start_offset = page_size - rem_adjustment_len

    if page_num < full_adjustment_pages:
        adj_start_idx = page_size * page_num
        adj_end_idx = page_size * page_num + page_size
        qs_start_idx = 0
        qs_end_idx = 0

    elif page_num == full_adjustment_pages:
        adj_start_idx = page_size * page_num
        adj_end_idx = page_size * page_num + rem_adjustment_len
        qs_start_idx = 0
        qs_end_idx = page_size - rem_adjustment_len

    else:
        adj_start_idx = 0
        adj_end_idx = 0
        qs_start_idx = (
            page_size * (page_num - full_adjustment_pages - 1) + db_start_offset
        )
        qs_end_idx = qs_start_idx + page_size

    return ((adj_start_idx, adj_end_idx), (qs_start_idx, qs_end_idx))


@log_time_taken()
def fetch_json_filtered_datasheet_data(
    params_dict, logger=None, apply_datasheet_permissions=True
):
    """
    @param: params_dict which contains client_id, databook_id, datasheet_id
           filters, page_number and page_size
    @return: List of dictionaries containing data for the given datasheet
            with adjustments, filters and pagination applied on top of it.
    """
    client_id = params_dict["client_id"]
    databook_id = params_dict["databook_id"]
    datasheet_id = params_dict["datasheet_id"]
    filters = params_dict["filters"] if "filters" in params_dict else []
    page_size = params_dict["page_size"] if "page_size" in params_dict else 20
    page_num = params_dict["page_num"] if "page_num" in params_dict else 0
    loggedin_user_email = params_dict["logged_in_user_email"]

    logger = (
        logger
        if logger
        else LogWithContext(
            {
                "client_id": client_id,
                "databook_id": databook_id,
                "datasheet_id": datasheet_id,
            }
        )
    )
    logger.info(
        "BEGIN -> Fetch JSON filtered Datasheet Data for datasheet: {}".format(
            datasheet_id
        )
    )

    if params_dict.get("use_duckdb"):
        compute_strategy = "duckdb_fallback_variant_snowflake"
    elif should_use_multi_engine_stormbreaker(client_id):
        compute_strategy = "snowflake_flattened"
    else:
        compute_strategy = "snowflake_variant"
    ds_reader = DatasheetDataReader(
        client_id,
        databook_id,
        datasheet_id,
        loggedin_user_email,
        apply_datasheet_permissions,
        compute_strategy=compute_strategy,
    )
    for filter in filters:
        column_name = filter["col_name"]
        value = filter["value"] if "value" in filter else None
        operator = filter["operator"]
        ds_reader.apply_data_filter(column_name, operator, value)

    data_len = ds_reader.get_count()
    try:
        datasheet_data = ds_reader.fetch_data(
            limit=page_size, offset=page_size * page_num
        )
    except PermissionError as ex:
        logger.exception("Permission error while fetching datasheet data")
        return {"data": [], "data_count": 0, "error": str(ex)}
    return {
        "data": datasheet_data,
        "data_count": data_len,
    }


def fetch_adjustment_data(
    client_id,
    databook_id,
    datasheet_id,
    loggedin_user_email,
    apply_datasheet_permissions=True,
):
    try:
        logger.info("BEGIN -> Fetch Datasheet Adjustment data {}".format(datasheet_id))
        adjustment_data = get_adjustment_data(
            client_id=client_id,
            databook_id=databook_id,
            datasheet_id=datasheet_id,
            filters=None,
            loggedin_user_email=loggedin_user_email,
            apply_datasheet_permissions=apply_datasheet_permissions,
            logger=logger,
        )
        if is_show_dangling_adjustments_enabled(client_id):
            dangling_rows = get_dangling_adjustments(
                client_id, databook_id, datasheet_id
            )
        else:
            dangling_rows = {}
        logger.info("END: Fetch adjustment data")
    except PermissionError as ex:
        logger.error(f"Permission error while fetching datasheet adjustment data: {ex}")
        return {"adjustment_data": {}, "adjustment_data_count": 0, "error": str(ex)}

    logger.info("BEGIN: Replace NaN and NaT for adjustment data")
    replace_nan_nat(adjustment_data)
    logger.info("END: Replace NaN and NaT for adjustment data")
    return {
        "adjustment_data": adjustment_data,
        "dangling_rows": dangling_rows,
    }


def fetch_datasheet_data(
    params_dict,
    logger=None,
    apply_datasheet_permissions=True,
):
    """
    @param: params_dict which contains client_id, databook_id, datasheet_id
           filters, page_number and page_size
    @return: List of dictionaries containing data for the given datasheet
            with adjustments, filters and pagination applied on top of it.
    """
    client_id = params_dict["client_id"]
    databook_id = params_dict["databook_id"]
    datasheet_id = params_dict["datasheet_id"]
    loggedin_user_email = params_dict["logged_in_user_email"]
    filters = params_dict["filters"] if "filters" in params_dict else None
    pivots = params_dict["pivots"] if "pivots" in params_dict else None
    page_size = params_dict["page_size"] if "page_size" in params_dict else None
    page_num = params_dict["page_num"] if "page_num" in params_dict else None
    # Get all valid datasheet_data entries for the datasheet as DataFrame
    # data = fetch_datasheet_data_from_table(
    #     client_id,
    #     databook_id,
    #     datasheet_id,
    # )

    # construct row_key column from the data we have fetched from the table.
    # let's say primary keys are ["id","email"]
    # and data is:
    # | id | email | name | place |
    # | 1  | dg@a  | dg    | ajj  |
    # after the below function, data will be changed to:
    # | id | email | name | place | row_key     |
    # | 1  | dg@a  | dg    | ajj  | 1##::##dg@a |

    # data = add_row_key_column_to_datasheet_data(client_id, databook_id, datasheet_id, data)

    # now apply adjustments of that datasheet on top of the data we have
    # and get the adjustments as list of dict seperately
    # after the below line,
    # data: adjustments applied data
    # adjustment_data: list of dict containing each adjustments as dict.

    # data, adjustment_data = handle_adjustments(client_id, databook_id, datasheet_id, data)

    # If there are multiple adjustments for a row_key,
    # combine it together to make it easy for frontend fetch data, without traversing.
    # For eg:
    # adjustment data:
    # [
    #     {"row_key": "<EMAIL>", "co_1_place": "Chennai"},
    #     {"row_key": "<EMAIL>", "co_1_country": "Norway"},
    #     {"row_key": "<EMAIL>", "co_1_country": "India"},
    # ]
    # after change_adjustment_structure call,
    # {
    #     "<EMAIL>" : [
    #                     {"row_key": "<EMAIL>", "co_1_place": "Chennai"},
    #                     {"row_key": "<EMAIL>", "co_1_country": "India"}
    #                 ],
    #     "<EMAIL>": [
    #                     {"row_key": "<EMAIL>", "co_1_country": "Norway"},
    #                 ]
    #     ,
    # }

    # adjustment_data_map = change_adjustment_structure(adjustment_data)

    # If there are filters, apply them on top of the data we have now.
    # These are the outside filters on the datasheet
    # if filters:
    #     for filter in filters:
    #         data = data_etl.apply_filter_transform(data, filter, client_id)

    # apply pivot on top of the data
    logger = (
        logger
        if logger
        else LogWithContext(
            {
                "client_id": client_id,
                "databook_id": databook_id,
                "datasheet_id": datasheet_id,
            }
        )
    )
    if should_use_multi_engine_stormbreaker(client_id):
        ds_reader = DatasheetDataReader(
            client_id,
            databook_id,
            datasheet_id,
            loggedin_user_email,
            apply_datasheet_permissions,
            compute_strategy="snowflake_flattened",
        )
    else:
        ds_reader = DatasheetDataReader(
            client_id,
            databook_id,
            datasheet_id,
            loggedin_user_email,
            apply_datasheet_permissions,
        )
    if filters is not None:
        for filter in filters:
            column_name = filter["col_name"]
            value = filter["value"] if "value" in filter else None
            operator = filter["operator"]
            ds_reader.apply_data_filter(column_name, operator, value)

    whole_data_len = ds_reader.get_count()
    try:
        data = ds_reader.fetch_data(as_data_frame=True)
    except PermissionError as ex:
        logger.error(f"Permission error while fetching datasheet data: {ex}")
        return {
            "data": [],
            "data_count": 0,
            "pivoted_columns": {},
        }

    # Re-evaluating calculated fields for the adjusted data

    pivoted_columns = None
    if pivots:
        datasheet_manage_permission = does_user_have_databook_manage_permission(
            client_id, loggedin_user_email
        )
        print("datasheet_manage_permission", datasheet_manage_permission)
        if not datasheet_manage_permission:
            hidden_columns = get_all_hidden_columns_in_a_datasheet_for_user(
                client_id, datasheet_id, loggedin_user_email
            )
            print("hidden_columns", hidden_columns)
            if hidden_columns:
                pivot_columns = set(
                    pivots["index"] + pivots["columns"] + pivots["values"]
                )
                print("pivot_columns", pivot_columns)
                if pivot_columns.intersection(hidden_columns):
                    logger.error(
                        f"Permission error while applying pivot on datasheet data"
                    )
                    return {
                        "data": [],
                        "adjustment_data": {},
                        "data_count": 0,
                        "pivoted_columns": {},
                    }
        MAX_COLUMN_LIMIT = 500
        total_column_count = data[pivots["columns"][0]].nunique() * len(
            pivots["aggfunc"]
        )  # column will always be an array of one element always
        if total_column_count > MAX_COLUMN_LIMIT:
            raise Exception("DATA_PIVOT_MEMORY_ERROR")
        dsv_acc = DatasheetVariableAccessor(client_id)
        ds_vars = dsv_acc.get_variables_for_db_ds(
            databook_id, datasheet_id, as_dicts=False
        )
        var_datatype_map = {var.system_name: var.data_type_id for var in ds_vars}  # type: ignore
        logger.info(
            "Applying pivots for datasheet - {} with config - {}".format(
                datasheet_id, pivots
            )
        )
        data = pivot_and_sort_data(data, pivots, var_datatype_map)
        data.columns = [
            "__".join([str(c) for c in c_list]) for c_list in data.columns.values
        ]
        pivot_columns = data.columns.tolist()  # type: ignore
        index_columns = data.index.names
        pivoted_columns = {
            "index_columns": index_columns,
            "pivot_columns": pivot_columns,
        }
        data.reset_index(inplace=True)
        whole_data_len = len(data.index)  # To get the data length of pivoted data

    # apply pagination on top of the data.
    # This will slice the part from whole data, using the page size and page number.
    # whole_data_len = len(data)
    if page_size is not None and page_num is not None:
        data = data[(page_size * page_num) : (page_size * page_num) + page_size]

    # Now converting this sliced dataFrame to list of dicts
    # and removing nan and nat from the dataFrame to make it readable for frontend.

    data_to_return = None
    if isinstance(data, pd.DataFrame):  # will be dataframe only, always
        data_to_return = remove_nan_nat(data)
        data_to_return = data_to_return.to_dict("records")
    replace_nan_nat(data_to_return)
    del data
    gc.collect()
    return {
        "data": data_to_return,
        "data_count": whole_data_len,
        "pivot_columns": pivoted_columns,
    }


@log_time_taken()
def get_dangling_adjustments(client_id, databook_id, datasheet_id):
    dangling_rows = {}
    cols_to_be_added = [
        "comments",
        "adjustment_number",
        "adjustment_id",
        "sub_adjustment_number",
        "is_global",
        "row_key",
    ]
    dangling_adjustments_df = DatasheetAdjustmentsAccessor(
        client_id
    ).get_dangling_adjustments_as_frame(databook_id, datasheet_id)
    if len(dangling_adjustments_df.index) != 0:
        employee_email_ids = set(
            dangling_adjustments_df.additional_details.apply(
                lambda additional_details: additional_details.get("updated_by")
            ).to_list()
        )
        records = list(
            EmployeeAccessor(
                client_id=client_id
            ).get_employees_name_as_dict_with_full_name(
                employee_email_ids=employee_email_ids
            )
        )
        email_id_map = {
            record["employee_email_id"]: record["full_name"] for record in records
        }
        adjustments_other_then_ignore = dangling_adjustments_df[
            dangling_adjustments_df.operation != "IGNORE"
        ]
        dangling_adjustments_other_then_ignore = adjustments_other_then_ignore.to_dict(
            "records"
        )
        # HANDELING DANGLING ADJUSTMENTS
        datasheet_variables_accessor = DatasheetVariableAccessor(client_id)
        for dangling_adjustment in dangling_adjustments_other_then_ignore:
            adjustment_type = dangling_adjustment["operation"]
            original_row_key = dangling_adjustment["original_row_key"]
            updated_by = dangling_adjustment["additional_details"].get("updated_by", "")
            # Convert data to key, display_name, value structure
            adjusted_data = []
            if adjustment_type == "INSERT":
                split_records = dangling_adjustments_df[
                    dangling_adjustments_df["original_row_key"] == original_row_key
                ].to_dict("records")
                for split_record in split_records:
                    if split_record.get("data"):
                        for key, value in split_record["data"].items():
                            display_name_and_data_type_id = datasheet_variables_accessor.get_display_name_from_system_name_latest(
                                key, databook_id, datasheet_id
                            )
                            adjusted_data.append(
                                {
                                    "row_key": split_record["row_key"],
                                    "key": key,
                                    "display_name": (
                                        display_name_and_data_type_id.get(
                                            "display_name", key
                                        )
                                        if display_name_and_data_type_id
                                        else key
                                    ),
                                    "data_type_id": (
                                        display_name_and_data_type_id.get(
                                            "data_type_id", None
                                        )
                                        if display_name_and_data_type_id
                                        else None
                                    ),
                                    "value": value,
                                }
                            )
            else:
                if dangling_adjustment.get("data"):
                    for key, value in dangling_adjustment["data"].items():
                        display_name_and_data_type_id = datasheet_variables_accessor.get_display_name_from_system_name_latest(
                            key, databook_id, datasheet_id
                        )
                        adjusted_data.append(
                            {
                                "key": key,
                                "display_name": (
                                    display_name_and_data_type_id.get(
                                        "display_name", key
                                    )
                                    if display_name_and_data_type_id
                                    else key
                                ),
                                "data_type_id": (
                                    display_name_and_data_type_id.get(
                                        "data_type_id", None
                                    )
                                    if display_name_and_data_type_id
                                    else None
                                ),
                                "value": value,
                            }
                        )

            # Create adjustment details following the same pattern as regular adjustments
            adjustment_details = {
                "type": ("SPLIT" if adjustment_type == "INSERT" else adjustment_type),
                "adjustment_date": dangling_adjustment["knowledge_begin_date"],
                "applied_by": (
                    f"{email_id_map[updated_by]} ({updated_by})"
                    if email_id_map.get(updated_by)
                    else None
                ),
                "adjusted_data": adjusted_data,  # Add the structured data here
                "invalidated_at": dangling_adjustment.get("invalidated_at"),
            }
            # Add other fields from cols_to_be_added like regular adjustments
            for column in cols_to_be_added:
                adjustment_details[column] = dangling_adjustment[column]

            # Create empty record structure similar to regular adjustments
            data_to_append = {
                "row_key": dangling_adjustment["row_key"],
                "original_row_key": original_row_key,
                "dangling_reason": dangling_adjustment.get("dangling_reason"),
            }

            # Set adjustment flags and details like regular adjustments
            data_to_append["is_adjustment"] = True
            data_to_append["adjustment_details"] = adjustment_details

            adjustment_id = str(dangling_adjustment["adjustment_id"])
            if adjustment_id not in dangling_rows:
                dangling_rows[adjustment_id] = []
            dangling_rows[adjustment_id].append(data_to_append)

        dangling_adjustments_ignore = dangling_adjustments_df[
            dangling_adjustments_df.operation == "IGNORE"
        ]
        dangling_adjustments_ignore = dangling_adjustments_ignore.to_dict("records")
        for dangling_adjustment in dangling_adjustments_ignore:
            adjustment_type = dangling_adjustment["operation"]
            original_row_key = dangling_adjustment["original_row_key"]
            updated_by = dangling_adjustment["additional_details"].get("updated_by", "")
            # Convert data to key, display_name, value structure
            adjusted_data = []
            # Create adjustment details following the same pattern as regular adjustments
            adjustment_details = {
                "type": adjustment_type,
                "adjustment_date": dangling_adjustment["knowledge_begin_date"],
                "applied_by": (
                    f"{email_id_map[updated_by]} ({updated_by})"
                    if email_id_map.get(updated_by)
                    else None
                ),
                "adjusted_data": adjusted_data,  # Add the structured data here
                "invalidated_at": dangling_adjustment.get("invalidated_at"),
            }
            # Add other fields from cols_to_be_added like regular adjustments
            for column in cols_to_be_added:
                adjustment_details[column] = dangling_adjustment[column]

            # Create empty record structure similar to regular adjustments
            data_to_append = {
                "row_key": dangling_adjustment["row_key"],
                "original_row_key": original_row_key,
                "dangling_reason": dangling_adjustment.get("dangling_reason"),
            }

            # Set adjustment flags and details like regular adjustments
            data_to_append["is_adjustment"] = True
            data_to_append["adjustment_details"] = adjustment_details

            adjustment_id = str(dangling_adjustment["adjustment_id"])
            if adjustment_id not in dangling_rows:
                dangling_rows[adjustment_id] = []
            dangling_rows[adjustment_id].append(data_to_append)
        return dangling_rows
    return {}


@log_time_taken()
def get_adjustment_data(
    client_id,
    databook_id,
    datasheet_id,
    filters,
    loggedin_user_email,
    apply_datasheet_permissions,
    logger,
):
    logger = (
        logger
        if logger
        else LogWithContext(
            {
                "client_id": client_id,
                "databook_id": databook_id,
                "datasheet_id": datasheet_id,
            }
        )
    )
    logger.info("Fetching adjustment row keys")
    adjustment_row_keys = DatasheetAdjustmentsAccessor(
        client_id
    ).get_row_keys_for_adjustments(databook_id, datasheet_id)
    if None in adjustment_row_keys:
        adjustment_row_keys.remove(None)
    if adjustment_row_keys:
        logger.info(
            "Adjustments present for this datasheet: {}".format(
                len(adjustment_row_keys)
            )
        )

        if should_use_multi_engine_stormbreaker(client_id):
            ds_reader = DatasheetDataReader(
                client_id,
                databook_id,
                datasheet_id,
                loggedin_user_email,
                apply_datasheet_permissions,
                compute_strategy="snowflake_flattened",
            )
        else:
            ds_reader = DatasheetDataReader(
                client_id,
                databook_id,
                datasheet_id,
                loggedin_user_email,
                apply_datasheet_permissions,
            )
        # for filter in filters:
        #     column_name = filter["col_name"]
        #     value = filter["value"] if "value" in filter else None
        #     operator = filter["operator"]
        #     ds_reader.apply_data_filter(column_name, operator, value)
        try:
            logger.info("BEGIN: Fetch original data")
            data = ds_reader.fetch_data(as_data_frame=True, only_adjustments=True)
            logger.info("END: Fetch original data: {} rows".format(len(data)))
        except PermissionError as ex:
            logger.error(f"Permission error while fetching datasheet data: {ex}")
            return {}
        logger.info("BEGIN: Fetch Adjustment data as frame")
        df_adjustment_data_for_datasheet = DatasheetAdjustmentsAccessor(
            client_id
        ).get_adjustments_for_datasheet_as_frame(
            databook_id, datasheet_id, adjustments_type_filter=None
        )

        logger.info("END: Fetch Adjustment data as frame")
        if len(df_adjustment_data_for_datasheet.index) != 0 and len(data.index) != 0:
            # making a copy of the dataFrame for future use of original dataFrame
            original_data = data.copy()
            adjustment_data = {}
            cols_to_be_added = [
                "comments",
                "adjustment_number",
                "adjustment_id",
                "sub_adjustment_number",
                "is_global",
                "row_key",
            ]
            logger.info("BEGIN: Update records operation")
            employee_email_ids = set(
                df_adjustment_data_for_datasheet.additional_details.apply(
                    lambda additional_details: additional_details.get("updated_by")
                ).to_list()
            )
            records = list(
                EmployeeAccessor(
                    client_id=client_id
                ).get_employees_name_as_dict_with_full_name(
                    employee_email_ids=employee_email_ids
                )
            )
            email_id_map = {
                record["employee_email_id"]: record["full_name"] for record in records
            }

            # ------------ UPDATE records Operation ------------

            # Filtering out only the UPDATE operation from all the adjustments
            update_operations_df = df_adjustment_data_for_datasheet[
                df_adjustment_data_for_datasheet.operation == "UPDATE"
            ]

            # converting UPDATE adjustments dataFrame into list of dicts
            #  and Iterating over each adjustment
            update_operations = update_operations_df.to_dict("records")
            for update_operation in update_operations:
                row_key = update_operation["original_row_key"]
                # constructing the adjustment details dictionary for each entry.
                updated_by = update_operation["additional_details"].get(
                    "updated_by", ""
                )
                adjustment_details = {
                    "type": "UPDATE",
                    "adjustment_date": update_operation["knowledge_begin_date"],
                    "applied_by": (
                        f"{email_id_map[updated_by]} ({updated_by})"
                        if email_id_map.get(updated_by)
                        else None
                    ),
                }
                for column in cols_to_be_added:
                    adjustment_details[column] = update_operation[column]
                original_values = {}
                for key in update_operation["data"].keys():
                    record = data.loc[data["row_key"] == row_key, key]
                    if not record.empty:
                        original_values[key] = data.loc[
                            data["row_key"] == row_key, key
                        ].iloc[0]
                # updating all the cells which matches the row key condition
                adjustment_id = str(update_operation["adjustment_id"])
                keys_list = ["is_adjustment", "adjustment_details", "original_values"]
                values_list = [True, adjustment_details, original_values]
                for key, value in update_operation["data"].items():
                    keys_list.append(key)
                    values_list.append(value)
                data.loc[data.row_key == row_key, keys_list] = values_list
                data_to_append = data[data.row_key == row_key].to_dict("records")
                # if data_to_append:  # will be there, just being extra cautious
                #     data_to_append[0]["is_adjustment"] = True
                #     data_to_append[0]["adjustment_details"] = adjustment_details
                #     data_to_append[0]["original_values"] = original_values

                if adjustment_id not in adjustment_data:
                    adjustment_data[adjustment_id] = []
                if data_to_append:
                    adjustment_data[adjustment_id].append(data_to_append[0])
            logger.info("END: Update records operation")

            logger.info("BEGIN: Insert records operation")
            # INSERT records Operation
            # Filtering out only the INSERT operation from all the adjustments
            insert_operations_df = df_adjustment_data_for_datasheet[
                df_adjustment_data_for_datasheet.operation == "INSERT"
            ]
            # converting INSERT adjustments dataFrame into list of dicts
            #  and Iterating over each adjustment
            insert_operations = insert_operations_df.to_dict("records")
            # row_key_to_keys_and_cols = {}
            for insert_operation in insert_operations:
                row_key = insert_operation["original_row_key"]
                if row_key not in original_data.row_key.unique():
                    continue
                all_split_values_for_row_key = {}
                records_with_row_key = insert_operations_df[
                    insert_operations_df["original_row_key"] == row_key
                ]
                records_with_row_key = records_with_row_key.to_dict("records")

                for idx, record in enumerate(records_with_row_key):
                    if record["data"]:
                        all_split_values_for_row_key[idx] = record["data"]
                    else:
                        all_split_values_for_row_key[idx] = {}

                updated_by = insert_operation["additional_details"].get(
                    "updated_by", ""
                )

                adjustment_details = {
                    "type": "SPLIT",
                    "split_values": all_split_values_for_row_key,
                    "adjustment_date": insert_operation["knowledge_begin_date"],
                    "applied_by": (
                        f"{email_id_map[updated_by]} ({updated_by})"
                        if email_id_map.get(updated_by)
                        else None
                    ),
                }
                for column in cols_to_be_added:
                    adjustment_details[column] = insert_operation[column]
                original_record = original_data[original_data["row_key"] == row_key]
                original_values = {}
                if not original_record.empty:
                    for key in insert_operation["data"].keys():
                        original_values[key] = original_record[key].iloc[0]
                original_record["is_adjustment"] = True
                original_record["adjustment_details"] = [adjustment_details]
                original_record["original_values"] = [original_values]
                original_record["row_key"] = insert_operation["row_key"]
                adjustment_id = str(insert_operation["adjustment_id"])
                # original_record["adjustment_id"] = str(insert_operation["adjustment_id"])

                for key, value in insert_operation["data"].items():
                    original_record[key] = value
                data = pd.concat(
                    [data, pd.DataFrame(original_record)], ignore_index=True
                )
                if adjustment_id not in adjustment_data:
                    adjustment_data[adjustment_id] = []
                adjustment_data[adjustment_id].append(
                    original_record.to_dict("records")[0]
                )
            logger.info("END: Insert records operation")
            logger.info("BEGIN: Ignore records operation")
            # HANDLING IGNORE ADJUSTMENTS
            # Filtering out only the IGNORE operation from all the adjustments
            ignore_operations_df = df_adjustment_data_for_datasheet[
                df_adjustment_data_for_datasheet.operation == "IGNORE"
            ]
            # converting Ignore adjustments dataFrame into list of dicts
            #  and Iterating over each adjustment
            ignore_operations = ignore_operations_df.to_dict("records")
            for ignore_operation in ignore_operations:
                original_row_key = ignore_operation["original_row_key"]
                if original_row_key not in original_data.row_key.unique():
                    continue

                updated_by = ignore_operation["additional_details"].get(
                    "updated_by", ""
                )
                adjustment_details = {
                    "type": "IGNORE",
                    "adjustment_date": ignore_operation["knowledge_begin_date"],
                    "applied_by": (
                        f"{email_id_map[updated_by]} ({updated_by})"
                        if email_id_map.get(updated_by)
                        else None
                    ),
                }
                for column in cols_to_be_added:
                    adjustment_details[column] = ignore_operation[column]
                data_to_append = data[data.row_key == original_row_key].to_dict(
                    "records"
                )
                if data_to_append:
                    data_to_append[0]["is_adjustment"] = True
                    data_to_append[0]["adjustment_details"] = adjustment_details
                    # adjustment_data.extend(data_to_append)
                    adjustment_id = str(ignore_operation["adjustment_id"])
                    if adjustment_id not in adjustment_data:
                        adjustment_data[adjustment_id] = []
                    adjustment_data[adjustment_id].append(data_to_append[0])
            logger.info("END: Ignore records operation")

            return adjustment_data
        else:
            return {}
    else:
        logger.info("No adjustments for this datasheet")
        return {}


def pivot_and_sort_data(data: pd.DataFrame, pivots, var_datatype_map):
    agg_func_map = {k: agg_fn_map[v] for k, v in pivots["aggfunc"].items()}

    # Converting None values to empty strings
    pivoting_columns: list = pivots["index"] + pivots["columns"]
    data[pivoting_columns] = replace_nan_nat_for_df(
        data[pivoting_columns], replace_with=""
    )

    data = data.pivot_table(
        index=pivots["index"],
        values=pivots["values"],
        aggfunc=agg_func_map,
        columns=pivots["columns"],
        fill_value=pivots["fill_value"],
    )

    # sort rows & columns
    # data.sort_values(by=pivots["columns"], axis=1, inplace=True, ascending=False)
    def change_empty_value(val):
        for idx, i in enumerate(val):
            if i == "":
                val[idx] = None
        return val

    data.sort_index(level=[0, 1], axis=1, inplace=True, ascending=[True, True])
    data.sort_values(
        by=pivots["index"], key=change_empty_value, inplace=True, ascending=True
    )

    def rename_date_col(val):
        if val:
            return pd.to_datetime(val).strftime("%d %b %Y")
        return ""

    if var_datatype_map[pivots["columns"][0]] == 2:
        data.rename(rename_date_col, level=1, axis=1, inplace=True)
    return data


def rename_pivot_columns(
    data: pd.DataFrame, aggfunc: dict, var_display_map: dict
) -> pd.DataFrame:
    """
    Rename DataFrame columns based on aggregation functions and display names.

    Args:
        data: DataFrame whose columns need to be renamed
        aggfunc: Mapping of column names to their aggregation functions
        var_display_map: Mapping of column names to their display names

    Returns:
        DataFrame with renamed columns

    Example:
        >>> data = pd.DataFrame({
        ...     'col1': [1, 2, 3],
        ...     'col2': [4, 5, 6],
        ...     'col3': [7, 8, 9],
        ... })
        >>> aggfunc = {'col1': 'sum', 'col2': 'max', 'col3': 'max'}
        >>> var_display_map = {'col1': 'display1', 'col2': 'display2', 'col3': 'display3'}
        >>> rename_pivot_columns(data, aggfunc, var_display_map)
        pd.DataFrame({
            'Sum(display1)': [6],
            'Max(display2)': [5],
            'Max(display3)': [9],
        })
    """
    # Create formatted column names with aggregation function for pivoted columns
    aggregated_column_names = {
        col: f"{PIVOT_AGG_MAP[agg_func]} ({var_display_map[col]})"
        for col, agg_func in aggfunc.items()
    }

    # Build the final column mapping dictionary
    column_mapping = {}
    for col_name, display_name in var_display_map.items():
        # Use the aggregated name if available, otherwise use the display name
        column_mapping[col_name] = aggregated_column_names.get(col_name, display_name)

    # Apply the column renaming and return the DataFrame
    data.rename(columns=column_mapping, inplace=True)
    return data


def get_datasheets_by_id(
    client_id, datasheet_ids=None, as_dicts=True, columns=None
) -> list:
    """
    This function is used to get record based on datasheet_ids for the specified columns
    from datasheet table.

    If datasheet_id is None, Fetch for all datasheet_ids (not deleted datasheets)
    If columns is None, Fetch values for all columns
    If both datasheet_ids and columns are None, fetch all records for all datasheet_ids and all columns

    If as_dicts is True, Returns in datasheet records in dict format
    If as_dicts is False, Returns in datasheet records in Datasheet object format

    If columns ["datasheet_id", "is_datasheet_generated"] is passed,
    Returns:-
        [
            {
                "datasheet_id":
                "is_datasheet_generated":
            },
            {
                "datasheet_id":
                "is_datasheet_generated":
            }
        ]

    """
    return DatasheetAccessor(client_id=client_id).get_datasheets_by_id(
        datasheet_ids=datasheet_ids, as_dicts=as_dicts, columns=columns
    )


def get_datasheet_ids_by_databook_id(client_id, databook_id):
    """
    This function is used to get datasheet_ids for the specified databook_id
    """
    return DatasheetAccessor(client_id=client_id).get_datasheet_ids_by_databook_id(
        databook_id=databook_id
    )


def is_datasheet_exists(client_id, databook_id, datasheet_id):
    return DatasheetAccessor(client_id).is_datasheet_exist(
        databook_id=databook_id, datasheet_id=datasheet_id
    )


def get_qualifying_objects(
    client_id,
    knowledge_date,
    databook_id,
    datasheet_id,
    filters,
    login_user,
    payee_field=None,
    payee_email_tuple=None,
    as_dicts=False,
    apply_datasheet_permissions=True,
):
    if should_use_multi_engine_stormbreaker(client_id):
        ds_reader = DatasheetDataReader(
            client_id,
            databook_id,
            datasheet_id,
            login_user,
            apply_datasheet_permissions,
            compute_strategy="duckdb_fallback_variant_snowflake",
            knowledge_date=knowledge_date,
        )
    else:
        ds_reader = DatasheetDataReader(
            client_id,
            databook_id,
            datasheet_id,
            login_user,
            apply_datasheet_permissions,
        )
    ds_reader.set_knowledge_date(knowledge_date)
    for filter in filters:
        column_name = filter["col_name"]
        value = filter["value"] if "value" in filter else None
        operator = filter["operator"]
        ds_reader.apply_data_filter(column_name, operator, value)
    if payee_field and payee_email_tuple:
        ds_reader.apply_payee_filters(payee_field, payee_email_tuple)
    try:
        as_data_frame = not as_dicts
        return ds_reader.fetch_data(as_data_frame=as_data_frame)
    except PermissionError:
        return [] if as_dicts else pd.DataFrame()


def get_objects_by_datasheet_id(client_id, datasheet_id, order_by=None, as_dicts=False):
    return DatasheetVariableAccessor(client_id).get_objects_by_datasheet_id(
        datasheet_id=datasheet_id, order_by=order_by, as_dicts=as_dicts
    )


def get_variables_for_ds_ids(
    client_id, datasheet_ids, as_dicts=False, knowledge_date=None
):
    if knowledge_date is None:
        return DatasheetVariableAccessor(client_id).get_variables_for_ds_ids(
            datasheet_ids=datasheet_ids, as_dicts=as_dicts
        )
    return DatasheetVariableAccessor(client_id).get_variables_for_ds_ids_kd_aware(
        datasheet_ids, knowledge_date
    )


def get_variables_without_calc_fields(
    client_id,
    databook_id,
    datasheet_id,
    knowledge_date,
    exclude_row_fields=True,
    exclude_window_fields=True,
    as_dicts=False,
):
    """
    gets variables excluding calculated fields
    (either row_fields or window fields(currently rank only) or both)
    """
    datasheet = DatasheetAccessor(client_id).get_datasheet_by_kd(
        databook_id, datasheet_id, knowledge_date
    )[0]

    all_variables = DatasheetVariableAccessor(
        client_id
    ).get_variables_for_ds_ids_kd_aware(
        datasheet_ids=datasheet_id, knowledge_date=knowledge_date, as_dicts=as_dicts
    )
    data_types = get_all_data_types()
    data_types_dict = dict((d.id, d.data_type) for d in data_types)

    datasheet_variables = []
    for object_variable in all_variables:
        # ignore calculated fields
        if object_variable.field_order > 0 and any(
            field in object_variable.meta_data
            for field in get_window_based_calculated_fields()
        ):
            var_type = "window"
        elif object_variable.field_order > 0:
            var_type = "row"
        else:
            var_type = "normal"

        variable = {
            "system_name": object_variable.system_name,
            "display_name": object_variable.display_name,
            "data_type_id": object_variable.data_type_id,
        }
        variable.update({"data_type": data_types_dict[variable["data_type_id"]]})

        if var_type == "normal":
            datasheet_variables.append(variable)
        elif var_type == "row" and not exclude_row_fields:
            datasheet_variables.append(variable)
        elif var_type == "window" and not exclude_window_fields:
            datasheet_variables.append(variable)

    current_datasheet_variables = {
        "id": datasheet.datasheet_id,
        "name": datasheet.name,
        "variables": datasheet_variables,
    }

    return current_datasheet_variables


def set_datasheet_generated(client_id, databook_id, datasheet_id, knowledge_date):
    """
    Set `is_generated_datasheet` column of datasheet table to True.
    """
    DatasheetAccessor(client_id).set_datasheet_generated(
        databook_id=databook_id,
        datasheet_id=datasheet_id,
        knowledge_date=knowledge_date,
    )


def set_datasheet_config_update(
    client_id: int, databook_id: str, datasheet_id: str, config_names_and_values: dict
):
    """
    Given a dictionary of config variable names and their values
    update it in datasheet
    """
    DatasheetAccessor(client_id=client_id).set_datasheet_config_flag(
        databook_id=databook_id,
        datasheet_id=datasheet_id,
        flag_and_values=config_names_and_values,
    )


def get_all_data_types(type_id=None):
    return VariableDataTypeAccessor().get_data_type_by_id(type_id=type_id)


def get_datasheet_meta_data(
    client_id, databook_id, datasheet_id, knowledge_date=None
) -> ObjectMetaDataType:
    """
    Returns all or specified datasheet_ids meta data.

    Eg:
    [{'id': UUID('4667bfe1-e6ff-438d-80bd-922f55d535ec'),
     'name': 'Opportunity Stage History',
     'variables': [{'system_name': 'co_8_serial_no',
        'display_name': 'Serial No',
        'data_type_id': 4,
        'data_type': 'String'},
       {'system_name': 'co_8_opportunity_id',
        'display_name': 'Opportunity Id',
        'data_type_id': 4,
        'data_type': 'String'}]
    }]
    """
    # fetches the first datasheet id by kd
    datasheet = DatasheetAccessor(client_id).get_datasheet_by_kd(
        databook_id, datasheet_id, knowledge_date
    )[0]

    all_variables = DatasheetVariableAccessor(
        client_id
    ).get_ds_var_system_name_and_dtype(databook_id, datasheet_id, knowledge_date)
    data_types = get_all_data_types()

    data_types_dict = dict((d.id, d.data_type) for d in data_types)
    datasheet_variables = []
    for object_variable in all_variables:
        variable = {
            "system_name": object_variable.get("system_name"),
            "data_type_id": object_variable.get("data_type_id"),
        }
        variable.update({"data_type": data_types_dict[variable["data_type_id"]]})
        datasheet_variables.append(variable)

    current_datasheet_variables = {
        # "source_type": "custom_object",
        "id": datasheet.datasheet_id,
        "name": datasheet.name,
        "variables": datasheet_variables,
    }
    return current_datasheet_variables


def sync_type_to_data_origins(sync_type: Union[str, None]) -> list:
    """
    Returns the list of data origin for the given sync_type.
    """
    data_origin_map = {
        SYNC_OBJECT.DATABOOK_CUSTOM_OBJECT_SYNC.value: [DataOrigin.CUSTOM_OBJECT.value],
        SYNC_OBJECT.DATABOOK_REPORT_OBJECT_SYNC.value: [
            DataOrigin.INTER_OBJECT.value,
            DataOrigin.SYSTEM_OBJECT.value,
            DataOrigin.COMMISSION_OBJECT.value,
            DataOrigin.FORECAST_OBJECT.value,
            DataOrigin.INTER_FORECAST_OBJECT.value,
        ],
        SYNC_OBJECT.DATABOOK_SYSTEM_CUSTOM_SYNC.value: [
            DataOrigin.CUSTOM_OBJECT.value,
            DataOrigin.SYSTEM_OBJECT.value,
        ],
        SYNC_OBJECT.DATABOOK_COMMISSION_OBJECT.value: [
            DataOrigin.COMMISSION_OBJECT.value
        ],
        SYNC_OBJECT.DATABOOK_FORECAST_OBJECT.value: [DataOrigin.FORECAST_OBJECT.value],
        SYNC_OBJECT.DATABOOK_INTER_OBJECT_SYNC.value: [DataOrigin.INTER_OBJECT.value],
        SYNC_OBJECT.DATABOOK_INTER_FORECAST_OBJECT_SYNC.value: [
            DataOrigin.INTER_FORECAST_OBJECT.value
        ],
        SYNC_OBJECT.DATABOOK_SETTLEMENT_OBJECT.value: [
            DataOrigin.COMMISSION_OBJECT.value
        ],
        None: [data_origin.value for data_origin in DataOrigin],
    }
    return data_origin_map.get(sync_type, [])


def get_datasheets_for_databooks_by_sync_type(
    client_id: int, databook_ids=None, sync_type=None, knowledge_date=None
) -> list[Datasheet]:
    """
    This function used to get datasheets objects for the given databook_ids
    and sync_type

    If databook_ids is None, then will take all databook_ids for the given client_id.
    If sync_type is None, then will take all data_origins.
    Returns:
        list of datasheet objects
        [
            datasheet_object1,
            datasheet_object2,
            ...
        ]
    """

    # If single databook_id is passed, then convert it to list
    # Example:- databook_ids = "datasheet_id1"
    # Then convert this to list databook_ids = ["datasheet_id1"]
    if not (isinstance(databook_ids, list)) and databook_ids is not None:
        databook_ids = [databook_ids]

    data_origins = sync_type_to_data_origins(sync_type=sync_type)
    return DatasheetAccessor(
        client_id=client_id
    ).get_datasheets_for_databooks_by_data_origins(
        databook_ids=databook_ids,
        data_origins=data_origins,
        knowledge_date=knowledge_date,
    )


def get_datasheet_variables_for_the_given_kd(client_id, datasheet_id, knowledge_date):
    return DatasheetVariableAccessor(client_id).get_objects_by_datasheet_id_kd_aware(
        datasheet_id, knowledge_date, order_by="field_order"
    )


def get_datasheet_variables_for_the_given_kd_as_dicts(
    client_id, datasheet_id, knowledge_date
):
    return DatasheetVariableAccessor(client_id).get_objects_by_datasheet_id_kd_aware(
        datasheet_id, knowledge_date, order_by="field_order", as_dicts=True
    )


def get_system_name_display_name_data_type_map(client_id, datasheet_id, system_names):
    datasheet_variable_object = DatasheetVariableAccessor(
        client_id
    ).get_display_names_from_system_names(
        datasheet_id=datasheet_id, system_names=system_names
    )

    datatype_id_name_map = get_datatype_id_name_map()
    display_name_mapping = {}

    for var in datasheet_variable_object:
        display_name_mapping[var["system_name"]] = var

    result = []

    for system_col_name in system_names:
        result.append(
            {
                "system_name": system_col_name,
                "display_name": display_name_mapping[system_col_name]["display_name"],
                "data_type": datatype_id_name_map[
                    display_name_mapping[system_col_name]["data_type"]
                ],
            }
        )

    return result


def bulk_datasheet_config_update(
    client_id: int,
    datasheet_ids: list[UUID],
    config_names_and_values: dict,
    knowledge_end_date,
) -> None:
    """
    bulk update datasheet config
    """
    DatasheetAccessor(client_id=client_id).bulk_datasheet_config_update(
        datasheet_ids=datasheet_ids,
        config_names_and_values=config_names_and_values,
        knowledge_date=knowledge_end_date,
    )


def update_dbkbd_map_bulk(
    client_id: int, datasheet_ids: list[UUID], knowledge_date
) -> None:
    """
    This function used to Update and insert datasheet record in dbkd_pkd_map table in bulk
    """
    db_primary_acc = DbkdPkdMapAccessor(client_id=client_id)
    db_primary_acc.bulk_invalidate_and_insert_datasheet_records(
        client_id=client_id,
        datasheet_ids=datasheet_ids,
        knowledge_date=knowledge_date,
    )


def bulk_set_datasheet_generated(
    client_id: int, datasheet_ids: list[UUID], knowledge_date
) -> None:
    """
    Set `is_generated_datasheet` column of datasheet table to True.
    """
    DatasheetAccessor(client_id=client_id).bulk_set_datasheet_generated(
        datasheet_ids=datasheet_ids,
        knowledge_date=knowledge_date,
    )


def get_adjusted_row_keys_from_datasheet(
    client_id,
    databook_id,
    datasheet_id,
    adjustments_type_filter=None,
    knowledge_date=None,
):
    """
    fetches the list of row keys that haven been adjusted in datasheet_data
    """

    return list(
        DatasheetAdjustmentsAccessor(client_id=client_id).get_row_keys_for_adjustments(
            databook_id=databook_id,
            datasheet_id=datasheet_id,
            adjustments_type_filter=adjustments_type_filter,
            knowledge_date=knowledge_date,
        )
    )


def fetch_records_from_dbpkdmap_table_for_specified_columns(
    client_id, datasheet_ids=None, columns=None
) -> list:
    """
    This function is used to get record based on datasheet_ids for the specified columns.
    from dbpkd_map table.

    If datasheet_id is None, Fetch for all datasheet_ids (not deleted datasheets)
    If columns is None, Fetch values for all columns
    If both datasheet_ids and columns are None, fetch all records for all datasheet_ids and all columns

    If columns ["datasheet_id", "primary_kd"] is passed,
    Returns
        [
            {
                "datasheet_id":
                "primary_kd":
            },
            {
                "datasheet_id":
                "primary_kd":
            }
        ]

    """

    return DbkdPkdMapAccessor(client_id=client_id).fetch_records_for_specified_columns(
        datasheet_ids=datasheet_ids, columns=columns
    )


def knowledge_date_map_for_custom_and_report_objects(
    client_id, datasheet_list, snowpark_session=None
) -> dict:
    """
    Getting maximum knowledge date of custom_object_id whose order is 1 from custom object data
    """
    custom_object_ids, report_object_ids = _separate_custom_report_objects(
        datasheet_list=datasheet_list
    )

    custom_object_id_to_max_kd = {}
    if len(custom_object_ids) > 0:
        custom_object_id_to_max_kd = latest_knowledge_date_for_custom_objects(
            client_id=client_id,
            custom_object_ids=custom_object_ids,
        )

    report_object_id_to_max_kd = {}
    if len(report_object_ids) > 0:
        report_object_id_to_max_kd = latest_knowledge_date_for_report_objects(
            client_id=client_id,
            report_object_ids=report_object_ids,
        )

    knowledge_date_for_custom_and_report_objects = {
        **custom_object_id_to_max_kd,
        **report_object_id_to_max_kd,
    }
    return knowledge_date_for_custom_and_report_objects


def _separate_custom_report_objects(datasheet_list) -> tuple[list, list]:
    """
    Separating custom and report objects from datasheet list
    """

    custom_objects = set()
    report_objects = set()

    for datasheet_obj in datasheet_list:
        if datasheet_obj.source_type == "object":
            custom_objects.add(datasheet_obj.source_id)
        elif datasheet_obj.source_type == "report":
            report_objects.add(datasheet_obj.source_id)

        for transformation_dict in datasheet_obj.transformation_spec:
            # In temporal splice, object/report can be chosen as source inside transformation, hence
            # we need to check for source_type and source_id inside transformation_spec
            if (
                transformation_dict["type"]
                == DATASHEET_TRANSFORMATIONS.TEMPORAL_SPLICE.name
            ):
                for source in transformation_dict["meta"]:
                    if source["source_type"] == "object":
                        custom_objects.add(source["source_id"])
                    elif source["source_type"] == "report":
                        report_objects.add(source["source_id"])

            # handle get user property transformation
            elif (
                transformation_dict["type"]
                == DATASHEET_TRANSFORMATIONS.GET_USER_PROPERTIES.name
            ):
                # add user object as dependency
                report_objects.add("user")

    return list(custom_objects), list(report_objects)
