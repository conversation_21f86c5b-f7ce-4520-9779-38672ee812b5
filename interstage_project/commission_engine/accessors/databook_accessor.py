# pylint: disable=pointless-string-statement
from datetime import datetime
from itertools import chain
from typing import Any, Dict, List
from uuid import UUID

from django.db.models import Case, <PERSON>, <PERSON>, Q, QuerySet, Value, When
from django.db.models.functions import Lower

import commission_engine.utils.general_data as utils
from commission_engine.models.databook_models import (
    Databook,
    Datasheet,
    DatasheetFilter,
    DatasheetPermissions,
    DatasheetVariable,
    DSFilterOperators,
    DSPermissionsTarget,
)
from commission_engine.utils.report_utils import DataOrigin
from commission_engine.utils.transformation_models import TransformationType
from interstage_project.db.models import EsIntegerField


class DatabookAccessor:
    def __init__(self, client_id):
        self.client_id = client_id

    def client_aware(self):
        return Databook.objects.filter(client_id=self.client_id)

    def client_hidden_aware(self):
        return self.client_aware().filter(
            Q(
                additional_details__analytics_default_dashboard_databook=False
            )  # key set to False
            | Q(
                additional_details__analytics_default_dashboard_databook__isnull=True
            )  # key is absent in the JSON
            | Q(
                additional_details__analytics_default_dashboard_databook=None
            )  # key present in the JSON but set to null
        )

    def client_hidden(self):
        return self.client_aware().filter(additional_details__hidden=True)

    def invalidate_all_client_records(self, time):
        self.client_aware().filter(knowledge_end_date__isnull=True).update(
            knowledge_end_date=time, is_deleted=True
        )

    def delete_all_client_records(self):
        self.client_aware().delete()

    def client_kd_deleted_aware(self, knowledge_date):
        return (
            self.client_hidden_aware()
            .filter(is_deleted=False, knowledge_begin_date__lte=knowledge_date)
            .filter(
                Q(knowledge_end_date__gt=knowledge_date)
                | Q(knowledge_end_date__isnull=True)
            )
        )

    def client_latest_kd_aware(self):
        return self.client_hidden_aware().filter(
            is_deleted=False, knowledge_end_date__isnull=True
        )

    def client_latest_kd_aware_with_hidden(self):
        return self.client_aware().filter(
            is_deleted=False, knowledge_end_date__isnull=True
        )

    def get_all_databook_ids(self, skip_archived_books=True) -> list[UUID]:
        """
        Returns a list of databook ids for the client.

        If skip_archived_books is True, then only non-archived databooks are returned.
        If skip_archived_books is False, then all databooks are returned.
        """
        if skip_archived_books:
            return list(
                self.client_latest_kd_aware_with_hidden()
                .filter(is_archived=False)
                .values_list("databook_id", flat=True)
            )
        else:
            return list(
                self.client_latest_kd_aware_with_hidden().values_list(
                    "databook_id", flat=True
                )
            )

    def get_databook_by_id(self, databook_id):
        return (
            self.client_latest_kd_aware_with_hidden()
            .filter(databook_id=databook_id)
            .first()
        )

    def get_non_archived_databooks_by_db_ids(self, databook_ids):
        qs = self.client_latest_kd_aware()
        if isinstance(databook_ids, list):
            qs = qs.filter(databook_id__in=databook_ids)
        else:
            qs = qs.filter(databook_id=databook_ids)
        qs = qs.filter(is_archived=False)
        return list(qs.values_list("databook_id", flat=True))

    def get_databook_by_db_ids(self, databook_ids, as_dicts=False):
        qs = self.client_latest_kd_aware_with_hidden()
        if isinstance(databook_ids, list):
            qs = qs.filter(databook_id__in=databook_ids)
        else:
            qs = qs.filter(databook_id=databook_ids)
        qs = list(qs.values()) if as_dicts else list(qs)
        return qs

    def persist_databook(self, data):
        data.save()

    def invalidate_databook(self, databook_id, knowledge_date):
        self.client_kd_deleted_aware(knowledge_date).filter(
            databook_id=databook_id
        ).update(knowledge_end_date=knowledge_date)

    def get_databook_id_name_map(self) -> list:
        return list(self.client_latest_kd_aware().values("databook_id", "name"))

    def get_databook_names(self):
        return list(self.client_latest_kd_aware().values("name"))

    def get_creation_date(self, databook_id):
        query_set = self.client_hidden_aware().filter(
            is_deleted=False, databook_id=databook_id
        )
        if query_set.exists():
            return query_set.order_by("knowledge_begin_date").first()

    def save_datasheet_order(self, databook_id, ds_order, modified_date):
        latest_record = self.get_databook_by_id(databook_id)
        if latest_record:
            self.invalidate_databook(databook_id, modified_date)
            latest_record.pk = None
            latest_record.knowledge_begin_date = modified_date
            latest_record.datasheet_order = ds_order
            self.persist_databook(latest_record)

    def get_datasheet_order_by_databook_id(self, databook_id):
        return (
            self.client_latest_kd_aware_with_hidden()
            .filter(databook_id=databook_id)
            .only("datasheet_order")
            .first()
        )

    def get_databooks_count_with_search(self, search_term):
        qs = self.client_latest_kd_aware()
        if search_term:
            qs = qs.filter(name__icontains=search_term)
        return qs.count()

    def get_databooks_list_with_search_and_limit(self, search_term, offset, limit):
        qs = self.client_latest_kd_aware()
        if search_term:
            qs = qs.filter(name__icontains=search_term)
        qs = qs.order_by(Lower("name"))[(offset * limit) : (offset * limit) + limit]
        return list(qs)

    def get_databook_id_by_name(self, name):
        return (
            self.client_latest_kd_aware_with_hidden()
            .filter(name=name)
            .values("databook_id")
            .first()
        )

    def remove_archived_databooks(self, databook_ids: list[UUID]) -> list[UUID]:
        """
        Returns a list of databook ids after removing the archived books from the list
        """
        return list(
            self.client_latest_kd_aware_with_hidden()
            .filter(is_archived=False, databook_id__in=databook_ids)
            .values_list("databook_id", flat=True)
        )

    def get_archived_books_from_list(self, databook_ids: list[UUID]) -> list[UUID]:
        """
        Returns a list of databook ids after removing the active books from the list
        """
        ans = list(
            self.client_latest_kd_aware()
            .filter(is_archived=True, databook_id__in=databook_ids)
            .values_list("databook_id", flat=True)
        )
        return ans

    def is_databook_exist(self, databook_id) -> bool:
        """
        Checks if the databook exists for the client or not
        If databook exists, it returns True else False
        """
        return (
            self.client_latest_kd_aware_with_hidden()
            .filter(databook_id=databook_id)
            .exists()
        )

    def databook_names_by_ids(
        self, databook_ids: list | None = None, as_string: bool = False
    ) -> dict:
        """
        Returns a dict of databook names for the given databook_ids.
        """
        qs = self.client_latest_kd_aware_with_hidden()
        if databook_ids:
            qs = qs.filter(databook_id__in=databook_ids)
        records = qs.values("databook_id", "name")
        if as_string:
            return {str(record["databook_id"]): record["name"] for record in records}
        return {record["databook_id"]: record["name"] for record in records}

    def get_dashboard_databook(self) -> Databook | None:
        return (
            self.client_latest_kd_aware_with_hidden()
            .filter(additional_details__analytics_default_dashboard_databook=True)
            .first()
        )

    def remove_datasheets_which_are_from_archived_databooks(
        self, datasheet_ids: list[str]
    ) -> list[str]:
        """
        Removes the datasheets which are from archived databooks from the given datasheet_ids.
        """
        # Get all archived databook IDs
        archived_databook_ids = (
            self.client_latest_kd_aware_with_hidden()
            .filter(is_archived=True)
            .values_list("databook_id", flat=True)
        )

        # Filter out datasheets that belong to archived databooks
        non_archived_datasheets = (
            Datasheet.objects.filter(
                datasheet_id__in=datasheet_ids,
                knowledge_end_date__isnull=True,
                is_deleted=False,
            )
            .exclude(databook_id__in=archived_databook_ids)
            .values_list("datasheet_id", flat=True)
        )

        # Convert to set to remove duplicates
        non_archived_datasheets = set(non_archived_datasheets)

        # Converting uuid to string
        non_archived_datasheets = [
            str(datasheet_id) for datasheet_id in non_archived_datasheets
        ]
        return non_archived_datasheets


class DatasheetAccessor:
    def __init__(
        self,
        client_id,
    ):
        self.client_id = client_id

    def client_aware(self):
        query_set = Datasheet.objects.filter(client_id=self.client_id).filter()
        return query_set

    def client_hidden_aware(self):
        return self.client_aware().filter(
            Q(
                additional_details__analytics_default_dashboard_datasheet=False
            )  # key set to False
            | Q(
                additional_details__analytics_default_dashboard_datasheet__isnull=True
            )  # key is absent in the JSON
            | Q(
                additional_details__analytics_default_dashboard_datasheet=None
            )  # key present in the JSON but set to null
        )

    def client_hidden(self):
        return self.client_aware().filter(additional_details__hidden=True)

    def invalidate_all_client_records(self, time):
        self.client_aware().filter(knowledge_end_date__isnull=True).update(
            knowledge_end_date=time, is_deleted=True
        )

    def delete_all_client_records(self):
        self.client_aware().delete()

    def client_kd_deleted_aware(self, knowledge_date):
        return (
            self.client_hidden_aware()
            .filter(is_deleted=False, knowledge_begin_date__lte=knowledge_date)
            .filter(
                Q(knowledge_end_date__gt=knowledge_date)
                | Q(knowledge_end_date__isnull=True)
            )
        )

    def client_kd_deleted_aware_with_hidden(self, knowledge_date):
        return (
            self.client_aware()
            .filter(is_deleted=False, knowledge_begin_date__lte=knowledge_date)
            .filter(
                Q(knowledge_end_date__gt=knowledge_date)
                | Q(knowledge_end_date__isnull=True)
            )
        )

    def client_latest_kd_aware(self):
        return self.client_hidden_aware().filter(
            is_deleted=False, knowledge_end_date__isnull=True
        )

    def client_latest_kd_aware_with_hidden(self):
        return self.client_aware().filter(
            is_deleted=False, knowledge_end_date__isnull=True
        )

    def get_datasheets_for_databook_with_hidden(
        self, databook_id: UUID
    ) -> list[Datasheet]:
        return list(
            self.client_latest_kd_aware_with_hidden().filter(databook_id=databook_id)
        )

    def get_all_transformations_using_user_report(self):
        return self.client_latest_kd_aware().filter(
            Q(
                transformation_spec__contains=[
                    {"type": TransformationType.GET_USER_PROPERTIES.value}
                ]
            )
            | Q(
                transformation_spec__contains=[
                    {"type": TransformationType.TEMPORAL_SPLICE.value}
                ]
            )
        )

    def get_datasheets_by_source_type_and_source_id(self, source_type, source_id):
        return self.client_latest_kd_aware().filter(
            source_type=source_type, source_id=source_id
        )

    def get_all_datasheets(self):
        return list(self.client_latest_kd_aware())

    def retrieve_all_datasheets_for_client(self, projections=None):
        qs = self.client_latest_kd_aware()
        if projections:
            qs = qs.values(*projections)
        return list(qs.values())

    def get_datasheet_by_id(self, datasheet_id):
        return (
            self.client_latest_kd_aware_with_hidden()
            .filter(datasheet_id=datasheet_id)
            .first()
        )

    def get_all_datasheets_queryset(self) -> QuerySet[Datasheet]:
        return self.client_latest_kd_aware()

    def get_datasheets_for_databooks(self, databook_ids):
        qs = self.client_latest_kd_aware_with_hidden()
        if isinstance(databook_ids, list):
            qs = qs.filter(databook_id__in=databook_ids)
        else:
            qs = qs.filter(databook_id=databook_ids)
        return list(qs)

    def get_datasheet_objects_for_databook(self, databook_id, knowledge_date=None):
        if knowledge_date:
            return tuple(
                self.client_kd_deleted_aware(knowledge_date).filter(
                    databook_id=databook_id
                )
            )
        return tuple(self.client_latest_kd_aware().filter(databook_id=databook_id))

    def get_datasheets_ids_for_databooks(self, databook_ids=None):
        """
        Returns a list of datasheet_ids for the given databook_ids.
        """
        qs = self.client_latest_kd_aware_with_hidden()

        if databook_ids is not None:
            databook_ids = (
                [databook_ids] if not isinstance(databook_ids, list) else databook_ids
            )
            qs = qs.filter(databook_id__in=databook_ids)

        return list(qs.values_list("datasheet_id", flat=True))

    def databook_aware(self, databook_id):
        return self.client_latest_kd_aware_with_hidden().filter(databook_id=databook_id)

    def get_datasheet_by_kd(self, databook_id, datasheet_id, kd):
        qs = self.client_kd_deleted_aware_with_hidden(kd).filter(
            databook_id=databook_id, datasheet_id=datasheet_id
        )
        return list(qs)

    def get_data_by_datasheet_id(self, databook_id, datasheet_id):
        qs = self.databook_aware(databook_id)
        if isinstance(datasheet_id, list):
            qs = qs.filter(datasheet_id__in=datasheet_id)
        else:
            qs = qs.filter(datasheet_id=datasheet_id)
        return list(qs)

    def get_data_by_source_id(self, databook_id, source_id):
        qs = self.databook_aware(databook_id)
        if isinstance(source_id, list):
            qs = qs.filter(source_id__in=source_id)
        else:
            qs = qs.filter(source_id=source_id)
        return list(qs)

    def get_data_by_datasheet_id_without_db(self, datasheet_id):
        qs = self.client_latest_kd_aware_with_hidden()
        if isinstance(datasheet_id, list):
            qs = qs.filter(datasheet_id__in=datasheet_id)
        else:
            qs = qs.filter(datasheet_id=datasheet_id)
        return list(qs)

    def persist_datasheet(self, data):
        data.save()

    def invalidate_datasheet(self, db_id, ds_id, time):
        qs = self.databook_aware(db_id).filter(datasheet_id=ds_id)
        if qs.exists():
            qs.update(knowledge_end_date=time)

    def datasheet_bulk_create(self, datasheets):
        Datasheet.objects.bulk_create(datasheets, batch_size=100)

    def get_datasheet_names(self):
        return list(self.client_latest_kd_aware().values("name"))

    def invalidate_all_datasheets(self, databook_id, knowledge_date):
        query_set = self.databook_aware(databook_id)
        if query_set.exists():
            query_set.update(knowledge_end_date=knowledge_date)

    def is_datasheet_exist(self, databook_id, datasheet_id):
        return (
            self.databook_aware(databook_id).filter(datasheet_id=datasheet_id).exists()
        )

    def get_primary_columns_for_datasheet(
        self, databook_id: UUID, datasheet_id: UUID
    ) -> List[str]:
        return (
            self.client_latest_kd_aware()
            .filter(databook_id=databook_id)
            .filter(datasheet_id=datasheet_id)
            .first()
            .primary_key
        )  # type: ignore # if it is valid, there will be one entry for it in the table

    def get_knowledge_begin_date_for_latest_primary_key(
        self, databook_id, datasheet_id
    ):
        return (
            self.client_latest_kd_aware()
            .filter(databook_id=databook_id)
            .filter(datasheet_id=datasheet_id)
            .first()
            .knowledge_begin_date
        )

    def get_primary_columns_for_datasheet_kd_aware(
        self, databook_id, datasheet_id, knowledge_date
    ):
        return (
            self.client_kd_deleted_aware(knowledge_date)
            .filter(databook_id=databook_id)
            .filter(datasheet_id=datasheet_id)
            .first()
            .primary_key
        )

    def set_datasheet_config_flag(self, databook_id, datasheet_id, flag_and_values):
        """
        Used to set values of is_pk_modified, is_config_changed and is_calc_field_changed
        flag_and_values - dictionary of flags and their values to be set
        """
        qs = self.databook_aware(databook_id=databook_id).filter(
            datasheet_id=datasheet_id
        )
        if qs.exists():
            qs.update(**flag_and_values)

    def set_datasheet_generated(self, databook_id, datasheet_id, knowledge_date):
        # During the process of generating datasheets or databooks, if a user has
        # deleted a datasheet, it means we cannot set the is_datasheet_generated flag to True
        # for that particular datasheet. Therefore, we must perform a check here and raise an exception
        from spm.custom_exceptions.datasheet_exceptions import (
            DatasheetNotFoundException,
        )

        datasheet_list = self.get_data_by_datasheet_id(
            databook_id=databook_id, datasheet_id=datasheet_id
        )
        if datasheet_list is None or len(datasheet_list) == 0:
            raise DatasheetNotFoundException(datasheet_id=datasheet_id)

        datasheet = datasheet_list[0]
        if datasheet and not datasheet.is_datasheet_generated:
            self.invalidate_datasheet(databook_id, datasheet_id, knowledge_date)
            datasheet.pk = None
            datasheet.knowledge_begin_date = knowledge_date
            datasheet.is_datasheet_generated = True
            datasheet.data_last_updated_at = knowledge_date
            datasheet.save()

    def bulk_set_datasheet_generated(self, datasheet_ids: list, knowledge_date) -> None:
        """
        Bulk set the is_datasheet_generated flag to True for the given datasheet_ids.
        """

        qs = self.client_kd_deleted_aware_with_hidden(knowledge_date=knowledge_date)
        qs = qs.filter(datasheet_id__in=datasheet_ids, is_datasheet_generated=False)
        datasheet_objects = list(qs.values())
        qs.update(knowledge_end_date=knowledge_date)
        # Create a list of Datasheet objects with modified attributes
        excluded_attributes = [
            "knowledge_begin_date",
            "is_datasheet_generated",
            "data_last_updated_at",
            "temporal_id",
        ]
        datasheet_records = [
            Datasheet(
                knowledge_begin_date=knowledge_date,
                is_datasheet_generated=True,
                data_last_updated_at=knowledge_date,
                **{
                    key: value
                    for key, value in datasheet_object.items()
                    if key not in excluded_attributes
                },
            )
            for datasheet_object in datasheet_objects
        ]
        # Bulk create the modified Datasheet objects
        self.datasheet_bulk_create(datasheets=datasheet_records)

    def set_datasheet_pk_modified(self, databook_id, datasheet_id, pk_modified):
        qs = self.databook_aware(databook_id=databook_id).filter(
            datasheet_id=datasheet_id
        )
        if qs.exists():
            qs.update(is_pk_modified=pk_modified)

    def bulk_datasheet_config_update(
        self, datasheet_ids: list[UUID], config_names_and_values: dict, knowledge_date
    ) -> None:
        """
        Bulk update the config values for the given datasheet_ids.
        """
        qs = self.client_kd_deleted_aware_with_hidden(knowledge_date=knowledge_date)
        qs.filter(datasheet_id__in=datasheet_ids).update(**config_names_and_values)

    def get_datasheet_name_by_id(self, datasheet_id):
        return list(
            self.client_latest_kd_aware()
            .filter(datasheet_id__in=datasheet_id)
            .values_list("name", flat=True)
        )

    def get_datasheet_id_to_databook_id_dict(self, datasheet_ids: list[UUID]):
        """
        Returns a dictionary of datasheet_id to databook_id mapping for the given datasheet_ids.
        """
        records = (
            self.client_latest_kd_aware_with_hidden()
            .filter(datasheet_id__in=datasheet_ids)
            .values("datasheet_id", "databook_id")
        )
        return {record["datasheet_id"]: record["databook_id"] for record in records}

    def get_all_revisions_for_datasheet(self, databook_id, datasheet_id):
        query_set = (
            self.client_hidden_aware()
            .filter(databook_id=databook_id)
            .filter(datasheet_id=datasheet_id)
        )
        if query_set.exists():
            return list(query_set.order_by("-knowledge_begin_date"))

    def get_source_id_and_datasheet_id_for_databook(self, databook_id):
        return list(
            self.client_latest_kd_aware()
            .filter(databook_id=databook_id)
            .values("source_id", "datasheet_id", "name")
        )

    def get_datasheets_for_databooks_by_data_origins(
        self, databook_ids=None, data_origins=None, knowledge_date=None
    ) -> list:
        """
        This function returns the datasheet_ids for the given databook_ids and data_origin.

        # If databook_ids is None
        # we take all databook_ids for the client

        # If data_origins is None
        # we take all data_origins

        """
        if knowledge_date is None:
            query_set = self.client_latest_kd_aware_with_hidden()
        else:
            query_set = self.client_kd_deleted_aware_with_hidden(knowledge_date)

        if databook_ids is not None:
            query_set = query_set.filter(databook_id__in=databook_ids)
        if data_origins is not None:
            query_set = query_set.filter(data_origin__in=data_origins)
        return list(query_set)

    def get_datasheet_ids_for_comm_object_sync_type(self):
        qs = (
            self.client_latest_kd_aware()
            .filter(data_origin=DataOrigin.COMMISSION_OBJECT.value)
            .values_list("datasheet_id", flat=True)
        )

        return list(qs)

    def get_datasheet_id_name_map_for_data_origins(self, data_origins):
        return list(
            self.client_latest_kd_aware()
            .filter(data_origin__in=data_origins)
            .values(
                "data_origin",
                "databook_id",
                "datasheet_id",
                "name",
                "transformation_spec",
            )
        )

    def get_datasheet_ids_for_inter_object_sync_type(self):
        qs = (
            self.client_latest_kd_aware()
            .filter(data_origin=DataOrigin.INTER_OBJECT.value)
            .values_list("datasheet_id", flat=True)
        )

        return list(qs)

    def get_datasheet_names_for_inter_object_sync_type(self):
        qs = (
            self.client_latest_kd_aware()
            .filter(data_origin=DataOrigin.INTER_OBJECT.value)
            .values_list("name", flat=True)
        )

        return list(qs)

    def get_datasheet_select_for_update(self, databook_id, datasheet_id):
        """
        Function to get latest datasheet and lock it till the transactions
        ends to make sure no other update happens on the same.
        Please make sure to wrap this function call inside a transaction
        """
        return (
            self.databook_aware(databook_id)
            .filter(datasheet_id=datasheet_id)
            .select_for_update(nowait=True)
            .first()
        )

    def get_datasheets_of_databook_select_for_update(self, databook_id):
        """
        Function to get latest datasheets of the databook and lock it till the transactions
        ends to make sure no other update happens on the same.
        Please make sure to wrap this function call inside a transaction
        """
        return list(self.databook_aware(databook_id).select_for_update(nowait=True))

    def get_distinct_databook_ids_for_source_type_and_id(self, source_type, source_id):
        return list(
            self.client_latest_kd_aware()
            .filter(source_type=source_type, source_id=source_id)
            .values_list("databook_id", flat=True)
            .distinct()
        )

    def get_objects_by_tag(self, tags):
        # tags = [{"value": "referral","category": "gamification"}]
        if not isinstance(tags, list):
            tags = [tags]
        qs = (
            self.client_latest_kd_aware()
            .filter(tags__contains=tags)
            .values("datasheet_id", "databook_id")
        )
        return qs.first()

    def get_source_data_of_datasheet(self, databook_id, datasheet_id):
        return (
            self.client_latest_kd_aware()
            .filter(databook_id=databook_id, datasheet_id=datasheet_id)
            .values("source_id", "source_type", "databook_id", "datasheet_id")
        )

    def datasheet_names_for_report_enrichment(
        self, databook_ids: list, data_origins
    ) -> list:
        """
        Returns a list of databook names for the given databook_ids excluding the data_origins.
        """
        qs = self.client_latest_kd_aware().filter(
            ~Q(data_origin__in=data_origins), databook_id__in=databook_ids
        )
        records = qs.values("databook_id", "datasheet_id", "name")
        return list(records)

    def datasheet_names_by_ids(self, datasheet_ids: list | None = None) -> dict:
        """
        Returns a dict of databook names for the given databook_ids.
        """
        qs = self.client_latest_kd_aware()
        if datasheet_ids:
            qs = qs.filter(datasheet_id__in=datasheet_ids)
        records = qs.values("datasheet_id", "name")

        return {record["datasheet_id"]: record["name"] for record in records}

    def get_datasheets_by_id(
        self, datasheet_ids=None, as_dicts=True, columns=None
    ) -> list:
        """
        Returns a list of active datasheets for this client. If datasheet_ids is None, it returns all active datasheets.
        """
        query = self.client_latest_kd_aware()

        if datasheet_ids is not None:
            datasheet_ids = (
                [datasheet_ids]
                if not isinstance(datasheet_ids, (list, tuple))
                else datasheet_ids
            )
            query = query.filter(datasheet_id__in=datasheet_ids)

        # If as_dicts is True, then we return the query as a list of dictionaries
        if as_dicts and not columns:
            return list(query.values())

        # If columns is not None, then we return the query as a list of dictionaries with only the specified columns
        if columns:
            query = query.values(*columns)

        return list(query)

    def get_datasheet_ids_by_databook_id(self, databook_id):
        qs = self.client_latest_kd_aware()
        qs = qs.filter(databook_id=databook_id).only("datasheet_id")
        return list(qs)

    def get_records_in_databook(self, databook_id: UUID) -> QuerySet[Datasheet]:
        return self.client_latest_kd_aware().filter(databook_id=databook_id)

    def get_knowledge_begin_date(self, databook_id, datasheet_ids=None):
        qs = self.databook_aware(databook_id).values(
            "datasheet_id", "knowledge_begin_date"
        )
        if datasheet_ids:
            qs = qs.filter(datasheet_id__in=datasheet_ids)
        return qs

    def get_datasheet_source_and_transformation_spec(
        self, knowledge_date=None, datasheet_id=None
    ):
        if knowledge_date:
            query_set = self.client_kd_deleted_aware(knowledge_date)
        else:
            query_set = self.client_latest_kd_aware_with_hidden()

        if datasheet_id:
            query_set = query_set.filter(datasheet_id=datasheet_id)

        return tuple(
            query_set.order_by("order").values(
                "datasheet_id", "source_id", "source_type", "transformation_spec"
            )
        )

    def get_datasheets_count_with_search(self, databook_id, search_term):
        qs = self.client_latest_kd_aware().filter(databook_id=databook_id)
        if search_term:
            qs = qs.filter(name__icontains=search_term)
        return qs.count()

    def get_datasheets_list_with_search_and_limit(
        self, databook_id, search_term, offset, limit
    ):
        qs = self.client_latest_kd_aware().filter(databook_id=databook_id)
        if search_term:
            qs = qs.filter(name__icontains=search_term)
        qs = qs.order_by(Lower("name"))[(offset * limit) : (offset * limit) + limit]
        return list(qs)

    def does_datasheet_exist(self, datasheet_id):
        return self.client_latest_kd_aware().filter(datasheet_id=datasheet_id).exists()

    def get_ordered_datasheets_of_databook(self, databook_id):
        return list(
            self.databook_aware(databook_id)
            .order_by("order")
            .values(
                "datasheet_id",
                "name",
                "source_type",
                "source_id",
                "transformation_spec",
            )
        )

    def get_datasheet_id_by_name(self, databook_id, name):
        return (
            self.client_latest_kd_aware_with_hidden()
            .filter(databook_id=databook_id, name=name)
            .values("client_id", "datasheet_id")
            .first()
        )

    def get_databook_id_using_datasheet_id(self, datasheet_id) -> UUID:
        """
        Returns the databook_id for given datasheet_id
        """
        # Here datasheet_id can be a UUID or string both are valid
        # and will work
        result = (
            self.client_latest_kd_aware()
            .filter(datasheet_id=datasheet_id)
            .values("databook_id")[0]
        )
        return result["databook_id"]

    def bulk_datasheet_order_update(self, records: dict[str, int]) -> None:
        """
        This function updates the order of the datasheets in bulk for the given records.
        records is a mapping of datasheet_id to order
        """
        self.client_latest_kd_aware().filter(datasheet_id__in=records.keys()).update(
            order=Case(
                *[
                    When(datasheet_id=datasheet_id, then=Value(order))
                    for datasheet_id, order in records.items()
                ],
                output_field=EsIntegerField(),
            )
        )

    def get_ordered_columns_for_given_datasheet_id(self, databook_id, datasheet_id):
        """
        Get ordered variables for the given datasheet_id
        """
        return list(
            self.databook_aware(databook_id)
            .filter(datasheet_id=datasheet_id)
            .values("ordered_columns")
        )

    def get_ordered_columns(self, datasheet_id):
        """
        Get ordered variables for the given datasheet_id
        """
        return list(
            self.client_latest_kd_aware()
            .filter(datasheet_id=datasheet_id)
            .values("ordered_columns")
        )

    def is_datasheet_exist_for_given_client(
        self, datasheet_id
    ) -> tuple[bool, list[Datasheet] | None]:
        """
        Check if a datasheet exists for the given client.

        Returns a tuple (bool, list) where the boolean is True if the datasheet exists, and the list contains Datasheet object or is None.
        """
        result = list(self.client_latest_kd_aware().filter(datasheet_id=datasheet_id))
        if len(result) > 0:
            return True, result
        return False, None

    def does_datasheet_with_settlement_source_exists(self) -> bool:
        """
        check if a datasheet with settlement report source exists
        """
        return self.client_latest_kd_aware().filter(source_id="settlement").exists()

    def fill_description_for_datasheet(
        self, datasheet_id: UUID, description: str
    ) -> None:
        """
        Fill the description for the given datasheet_id
        """
        self.client_latest_kd_aware().filter(datasheet_id=datasheet_id).update(
            description=description
        )

    def get_prompt_details(self, datasheet_ids: list):
        """
        Returns name, transformation_spec of variables for the given datasheet_ids
        """
        return list(
            self.client_latest_kd_aware()
            .filter(datasheet_id__in=datasheet_ids)
            .values(
                "datasheet_id",
                "name",
                "transformation_spec",
            )
        )

    def get_all_datasheet_ids_in_current_fiscal_year(self, min_kbd: datetime):
        result = (
            self.client_aware()
            .values("datasheet_id")
            .annotate(min_kb_date=Min("knowledge_begin_date"))
            .filter(min_kb_date__gt=min_kbd)
            .values_list("datasheet_id")
        )
        return [str(x[0]) for x in result]

    def get_force_skip_enabled_datasheet_ids(self) -> list[str]:
        """
        Get the list of datasheet ids that are force datasheet skip enabled
        """
        records = (
            self.client_latest_kd_aware()
            .filter(is_force_skip=True)
            .values_list("datasheet_id", flat=True)
        )
        return [str(record) for record in records]

    def is_datasheet_force_skipped(self, datasheet_id: str) -> bool:
        """
        Check if a datasheet is force skipped
        """
        return (
            self.client_latest_kd_aware()
            .filter(datasheet_id=datasheet_id, is_force_skip=True)
            .exists()
        )

    def get_force_skip_statuses(self, databook_ids: list[str]) -> dict[str, bool]:
        """
        Get the force skip statuses for the given databook_id
        """
        queryset = (
            self.client_latest_kd_aware()
            .filter(databook_id__in=databook_ids)
            .values_list("datasheet_id", "is_force_skip")
        )
        return {str(ds_id): is_force_skip for ds_id, is_force_skip in queryset}


class DatasheetVariableAccessor:
    def __init__(self, client_id):
        self.client_id = client_id

    def client_aware(self):
        # Adding is_selected to the filter to get only the selected variables
        # is_selected introduced in datasheet v2 to handle the selected variables
        # so for v1 all the variables will be selected
        return DatasheetVariable.objects.filter(
            client_id=self.client_id, is_selected=True
        )

    def invalidate_all_client_records(self, time):
        self.client_aware().filter(knowledge_end_date__isnull=True).update(
            knowledge_end_date=time, is_deleted=True
        )

    def delete_all_client_records(self):
        self.client_aware().delete()

    def client_kd_deleted_aware(self, knowledge_date):
        return (
            self.client_aware()
            .filter(is_deleted=False, knowledge_begin_date__lte=knowledge_date)
            .filter(
                Q(knowledge_end_date__gt=knowledge_date)
                | Q(knowledge_end_date__isnull=True)
            )
        )

    def client_latest_kd_aware(self):
        return self.client_aware().filter(
            is_deleted=False, knowledge_end_date__isnull=True
        )

    def databook_aware(self, databook_id):
        return self.client_latest_kd_aware().filter(databook_id=databook_id)

    def get_objects_by_display_name(self, display_name):
        return list(self.client_latest_kd_aware().filter(display_name=display_name))

    def get_objects_by_system_name(self, system_name):
        return self.client_latest_kd_aware().filter(system_name=system_name)

    def get_max_knowledge_date(self, datasheet_id, databook_id):
        max_knowledge_date = (
            self.client_latest_kd_aware()
            .filter(datasheet_id=datasheet_id)
            .filter(databook_id=databook_id)
            .aggregate(Max("knowledge_begin_date"))
        )
        max_knowledge_date_value = max_knowledge_date.get("knowledge_begin_date__max")
        return max_knowledge_date_value

    def get_objects_by_datasheet_id(
        self,
        datasheet_id,
        system_name=None,
        order_by=None,
        as_dicts=False,
        knowledge_date=None,
        projection=None,
    ):
        if knowledge_date:
            qs = self.client_kd_deleted_aware(knowledge_date)
        else:
            qs = self.client_latest_kd_aware()
        qs = qs.filter(datasheet_id=datasheet_id)
        if system_name:
            if isinstance(system_name, list):
                qs = qs.filter(system_name__in=system_name)
            else:
                qs = qs.filter(system_name=system_name)
        if order_by is not None:
            qs = qs.order_by(order_by)
        if projection:
            return list(qs.values(*projection))
        return list(qs.values()) if as_dicts else list(qs)

    def get_objects_by_datasheet_id_kd_aware(
        self, datasheet_id, knowledge_date, order_by=None, as_dicts=False
    ):
        qs = self.client_kd_deleted_aware(knowledge_date).filter(
            datasheet_id=datasheet_id
        )
        if order_by is not None:
            qs = qs.order_by(order_by)
        return list(qs.values()) if as_dicts else list(qs)

    def get_calc_fields_by_datasheet_id_kd_aware(
        self, datasheet_id, knowledge_date=None, order_by=None, as_dicts=False
    ) -> tuple:
        # Any datasheet variable with a field order greater than or equal to 1 is treated as a calculated field
        if knowledge_date:
            qs = self.client_kd_deleted_aware(knowledge_date).filter(
                datasheet_id=datasheet_id, field_order__gte=1
            )
        else:
            qs = self.client_latest_kd_aware().filter(
                datasheet_id=datasheet_id, field_order__gte=1
            )
        if order_by is not None:
            qs = qs.order_by(order_by)
        return tuple(qs.values()) if as_dicts else tuple(qs)

    def get_objects_by_tag(self, datasheet_id, tags):
        # tags = [{"value": "deal_id","category": "field_label"}]
        if not isinstance(tags, list):
            tags = [tags]
        qs = (
            self.client_latest_kd_aware()
            .filter(datasheet_id=datasheet_id)
            .filter(tags__contains=tags)
        )
        return list(qs)

    def get_objects_by_datatype(self, datatype_id):
        return list(self.client_latest_kd_aware().filter(data_type_id=datatype_id))

    def get_datasheet_objects_by_datatype(self, datasheet_id, datatype_id):
        return list(
            self.client_latest_kd_aware().filter(
                datasheet_id=datasheet_id, data_type_id=datatype_id
            )
        )

    def get_system_names_by_datatype(self, datasheet_id, datatype_id):
        return list(
            self.client_latest_kd_aware()
            .filter(datasheet_id=datasheet_id, data_type_id=datatype_id)
            .values_list("system_name", flat=True)
        )

    def get_datasheet_variables_by_datatype_kd(
        self, databook_id, datasheet_id, datatype_id, variables=None, kd=None
    ):
        if kd:
            qs = self.client_kd_deleted_aware(kd)
        else:
            qs = self.client_latest_kd_aware()
        qs = qs.filter(
            databook_id=databook_id, datasheet_id=datasheet_id, data_type_id=datatype_id
        )

        if variables:
            qs = qs.filter(system_name__in=variables)
        return qs.values_list("system_name", flat=True)

    def get_variables_for_db_ds(
        self, databook_id, datasheet_ids, as_dicts=True, fetch_only_selected=False
    ):
        datasheet_ids = (
            datasheet_ids if isinstance(datasheet_ids, list) else [datasheet_ids]
        )
        qs = self.client_latest_kd_aware().filter(
            databook_id=databook_id, datasheet_id__in=datasheet_ids
        )
        if fetch_only_selected:
            qs = qs.filter(is_selected=True)
        return list(qs.values()) if as_dicts else list(qs)

    def check_variable_exist(self, datasheet_ids, variables):
        return list(
            self.client_latest_kd_aware()
            .filter(datasheet_id__in=datasheet_ids, system_name__in=variables)
            .values("system_name", "datasheet_id")
        )

    def get_variables_for_ds_ids(self, datasheet_ids, as_dicts=False, columns=None):
        if columns is None:
            columns = []
        if not isinstance(datasheet_ids, list):
            datasheet_ids = [datasheet_ids]
        query_set = (
            self.client_latest_kd_aware()
            .filter(datasheet_id__in=datasheet_ids)
            .order_by("temporal_id")
        )
        variables = list(query_set.values(*columns)) if as_dicts else list(query_set)
        return variables

    def get_all_variables(self, as_dicts=False):
        query_set = self.client_latest_kd_aware()
        variables = list(query_set.values()) if as_dicts else list(query_set)
        return variables

    def get_variables_for_ds_ids_kd_aware(
        self, datasheet_ids, knowledge_date, as_dicts=False
    ):
        if not isinstance(datasheet_ids, list):
            datasheet_ids = [datasheet_ids]
        query_set = self.client_kd_deleted_aware(knowledge_date).filter(
            datasheet_id__in=datasheet_ids
        )
        variables = list(query_set.values()) if as_dicts else list(query_set)
        return variables

    def get_variables_endswith_var_for_ds_ids(
        self, datasheet_ids, variable_endswith, as_dicts=False
    ):
        if not isinstance(datasheet_ids, list):
            datasheet_ids = [datasheet_ids]
        query_set = self.client_latest_kd_aware().filter(
            datasheet_id__in=datasheet_ids, system_name__endswith=variable_endswith
        )
        variables = list(query_set.values()) if as_dicts else list(query_set)
        return variables

    def get_variables_for_ds_id_invalidated_in_range(
        self,
        databook_id: str,
        datasheet_id: str,
        last_generated_time: datetime,
        knowledge_date: datetime,
    ):
        """
        gets the list of variables that were deleted in the time range as
        that of incremental ETL
        """
        query_set = self.client_aware().filter(
            Q(databook_id=databook_id),
            Q(datasheet_id=datasheet_id),
            Q(knowledge_begin_date__lte=last_generated_time),
            Q(knowledge_end_date__isnull=False),
            Q(knowledge_end_date__gt=last_generated_time),
            Q(knowledge_end_date__lte=knowledge_date),
        )
        variables = list(query_set)
        return variables

    def get_ds_variables_for_datasheet(self, datasheet_id):
        return (
            self.client_latest_kd_aware()
            .filter(datasheet_id=datasheet_id)
            .values(
                "display_name",
                "system_name",
                "data_type__id",
                "data_type__data_type",
            )
        )

    def get_ds_variables_for_datasheet_with_variable_id(self, datasheet_id):
        return (
            self.client_latest_kd_aware()
            .filter(datasheet_id=datasheet_id)
            .values(
                "display_name",
                "system_name",
                "data_type__id",
                "data_type__data_type",
                "variable_id",
            )
        )

    def get_ds_variables_for_databook_builder(self, datasheet_id):
        """
        Retrieves variables associated with a given datasheet to use in the databook builder.
        """
        return (
            self.client_latest_kd_aware()
            .filter(datasheet_id=datasheet_id)
            .values(
                "display_name",
                "system_name",
                "data_type__id",
                "data_type__data_type",
                "variable_id",
                "source_variable_id",
                "is_primary",
                "source_type",
            )
        )

    def get_datatype_for_given_datasheet_variables(self, datasheet_id, variables):
        return (
            self.client_latest_kd_aware()
            .filter(datasheet_id=datasheet_id, system_name__in=variables)
            .values(
                "display_name", "system_name", "data_type__id", "data_type__data_type"
            )
        )

    def persist_variables(self, data):
        data.save()

    def invalidate_datasheet_variables(self, db_id, ds_id, time):
        qs = self.client_latest_kd_aware().filter(databook_id=db_id, datasheet_id=ds_id)
        if qs.exists():
            qs.update(knowledge_end_date=time)

    def create_objects(self, records):  # each record is a dict
        datasheet_var_records = []
        if isinstance(records, list):
            for record in records:
                if isinstance(record, dict):
                    record = DatasheetVariable(**record)
                record.pk = None
                datasheet_var_records.append(record)
        try:
            DatasheetVariable.objects.bulk_create(
                datasheet_var_records, batch_size=1000
            )
        except Exception as e:
            print("DATASHEET VAR BULK INSERT EXP - {}".format(e))
            raise e

    def invalidate_all_datasheets_variables(self, databook_id, knowledge_date):
        query_set = self.databook_aware(databook_id)
        if query_set.exists():
            query_set.update(knowledge_end_date=knowledge_date)

    def get_datasheet_vars_system_name(self, datasheet_id, knowledge_date=None):
        qs = (
            self.client_kd_deleted_aware(knowledge_date)
            if knowledge_date
            else self.client_latest_kd_aware()
        )
        return list(
            qs.filter(datasheet_id=datasheet_id).values_list("system_name", flat=True)
        )

    def get_datasheet_var_for_db_ds_sys_name(self, db, ds, sys_name):
        return self.client_latest_kd_aware().filter(
            databook_id=db, datasheet_id=ds, system_name=sys_name
        )

    def get_display_names_from_system_names(
        self, system_names: list, datasheet_id=None
    ) -> tuple:
        """
        Returns a tuple of display_name, system_name and data_type for the
        given system_names.

        If datasheet_id is passed, Then the datasheet_id is also used as a filter.
        """
        queryset = self.client_latest_kd_aware().filter(system_name__in=system_names)

        if datasheet_id is not None:
            queryset = queryset.filter(datasheet_id=datasheet_id)

        return tuple(queryset.values("system_name", "display_name", "data_type"))

    def get_display_names_from_system_names_with_meta_data(
        self, system_names: list, datasheet_id=None
    ) -> tuple:
        """
        Returns a tuple of display_name, system_name, data_type and meta_data for the
        given system_names.

        If datasheet_id is passed, Then the datasheet_id is also used as a filter.
        """
        queryset = self.client_latest_kd_aware().filter(system_name__in=system_names)

        if datasheet_id is not None:
            queryset = queryset.filter(datasheet_id=datasheet_id)

        return tuple(
            queryset.values("system_name", "display_name", "data_type", "meta_data")
        )

    def get_datasheet_display_name_for_system_names(self, datasheet_id, system_names):
        return list(
            self.client_latest_kd_aware()
            .filter(datasheet_id=datasheet_id)
            .filter(system_name__in=system_names)
            .values_list("display_name", flat=True)
        )

    def get_system_name_and_tags(self, datasheet_id):
        return list(
            self.client_latest_kd_aware()
            .filter(datasheet_id=datasheet_id)
            .values("system_name", "tags")
        )

    def get_ds_var_system_name_and_dtype(
        self, databook_id, datasheet_id, knowledge_date, fields=None
    ):
        if knowledge_date:
            qs = self.client_kd_deleted_aware(knowledge_date)
        else:
            qs = self.client_latest_kd_aware()
        qs = qs.filter(databook_id=databook_id, datasheet_id=datasheet_id)
        if fields is None:
            fields = ["system_name", "data_type_id"]
        return tuple(qs.values(*fields))

    def get_latest_system_name_and_dtype_for_datasheet(
        self, databook_id, datasheet_id, variables
    ):
        qs = self.client_latest_kd_aware().filter(
            databook_id=databook_id,
            datasheet_id=datasheet_id,
            system_name__in=variables,
        )
        return tuple(qs.values("system_name", "data_type_id"))

    def get_ds_var_system_name_and_dtype_sorted_by_field_order(
        self, databook_id, datasheet_id, knowledge_date, fields=None
    ):
        if knowledge_date:
            qs = self.client_kd_deleted_aware(knowledge_date)
        else:
            qs = self.client_latest_kd_aware()
        qs = qs.filter(databook_id=databook_id, datasheet_id=datasheet_id).order_by(
            "field_order"
        )
        if fields is None:
            fields = ["system_name", "data_type_id"]
        return tuple(qs.values(*fields))

    def get_ds_var_system_name_and_dtype_superset(
        self, databook_id, datasheet_id, knowledge_date
    ):
        if knowledge_date:
            qs = self.client_kd_deleted_aware(knowledge_date)
        else:
            qs = self.client_latest_kd_aware()
        qs = qs.filter(databook_id=databook_id, datasheet_id=datasheet_id)
        return tuple(qs.values("system_name", "data_type_id", "display_name"))

    def get_knowledge_begin_date(self, databook_id, datasheet_ids=None):
        qs = self.databook_aware(databook_id).values(
            "datasheet_id", "knowledge_begin_date"
        )
        if datasheet_ids:
            qs = qs.filter(datasheet_id__in=datasheet_ids)
        return qs

    def get_hierarchy_meta_data(self, databook_id=None) -> list:
        """
        Deprecated
        Returns a list of hierarchy meta data for the given databook_id.
        """
        queryset = self.client_latest_kd_aware().filter(
            meta_data__hierarchy__isnull=False
        )

        if databook_id is not None:
            queryset = queryset.filter(databook_id=databook_id)

        return list(queryset.values("datasheet_id", "meta_data"))

    def get_hierarchy_variables(
        self, databook_id=None, datasheet_id=None, as_dict=False
    ) -> list[DatasheetVariable] | list[dict]:
        """
        Returns datasheet variables identified as a hierarchy calculated field type
        """
        queryset = self.client_latest_kd_aware().filter(
            meta_data__hierarchy__isnull=False
        )

        if databook_id is not None:
            queryset = queryset.filter(databook_id=databook_id)

        if datasheet_id is not None:
            queryset = queryset.filter(datasheet_id=datasheet_id)

        if as_dict:
            return list(queryset.values())
        return list(queryset)

    def get_datasheet_variables(
        self, databook_id, datasheet_id, columns
    ) -> list[Dict[str, Any]]:
        """
        Returns datasheet variables for the given column names
        """
        return list(
            self.client_latest_kd_aware()
            .filter(
                databook_id=databook_id,
                datasheet_id=datasheet_id,
            )
            .values(*columns)
        )

    def get_prompt_details(self, datasheet_ids: list):
        """
        Returns system name, display name of variables for the given datasheet_ids
        """
        return list(
            self.client_latest_kd_aware()
            .filter(datasheet_id__in=datasheet_ids)
            .values(
                "datasheet_id",
                "system_name",
                "display_name",
            )
        )

    def get_display_name_from_system_name_latest(
        self, system_name, databook_id, datasheet_id
    ):
        return (
            DatasheetVariable.objects.filter(client_id=self.client_id)
            .filter(
                databook_id=databook_id,
                datasheet_id=datasheet_id,
                system_name=system_name,
            )
            .order_by("knowledge_begin_date")
            .values("display_name", "data_type_id")
            .first()
        )


class DatasheetFilterAccessor:
    def __init__(self, client_id):
        self.client_id = client_id

    def client_aware(self):
        return DatasheetFilter.objects.filter(client_id=self.client_id)

    def client_filter_aware(self):
        return self.client_aware().filter(
            config_type=utils.DATASHEET_FILTER_TYPE.FILTER.value
        )

    def client_pivot_aware(self):
        return self.client_aware().filter(
            config_type=utils.DATASHEET_FILTER_TYPE.PIVOT.value
        )

    def invalidate_all_client_records(self, time):
        self.client_aware().filter(knowledge_end_date__isnull=True).update(
            knowledge_end_date=time, is_deleted=True
        )

    def delete_all_client_records(self):
        self.client_aware().delete()

    def client_kd_deleted_aware(self, knowledge_date):
        return (
            self.client_filter_aware()
            .filter(is_deleted=False, knowledge_begin_date__lte=knowledge_date)
            .filter(
                Q(knowledge_end_date__gt=knowledge_date)
                | Q(knowledge_end_date__isnull=True)
            )
        )

    def client_latest_kd_aware(self):
        return self.client_aware().filter(
            is_deleted=False, knowledge_end_date__isnull=True
        )

    def client_latest_kd_filter_aware(self):
        return self.client_filter_aware().filter(
            is_deleted=False, knowledge_end_date__isnull=True
        )

    def databook_aware(self, databook_id):
        return self.client_latest_kd_filter_aware().filter(databook_id=databook_id)

    def datasheet_aware(self, datasheet_id):
        return self.client_latest_kd_filter_aware().filter(datasheet_id=datasheet_id)

    def get_all_ds_filters(self, databook_id, datasheet_id):
        return list(
            self.client_latest_kd_aware()
            .filter(databook_id=databook_id, datasheet_id=datasheet_id)
            .order_by("knowledge_begin_date")
        )

    def get_filter_by_email(self, email):
        return list(
            self.client_latest_kd_filter_aware().filter(last_updated_by=email).values()
        )

    def get_object_by_filter_name(self, filter_name):
        return (
            self.client_latest_kd_filter_aware().filter(filter_name=filter_name).first()
        )

    def get_filter_by_id(self, filter_id):
        return self.client_latest_kd_aware().filter(filter_id=filter_id).first()

    def is_filter_name_exist(self, databook_id, datasheet_id, filter_name, config_type):
        return (
            self.client_latest_kd_aware()
            .filter(
                databook_id=databook_id,
                datasheet_id=datasheet_id,
                config_type=config_type,
                filter_name__iexact=filter_name,
            )
            .exists()
        )

    def is_filter_id_exist(self, filter_id):
        return (
            self.client_latest_kd_aware().filter(filter_id__iexact=filter_id).exists()
        )

    def invalidate_filter(self, filter_id, knowledge_date):
        self.client_latest_kd_aware().filter(filter_id=filter_id).update(
            knowledge_end_date=knowledge_date
        )

    def save_filter(self, filter_ser):
        filter_ser.save()


class DSFilterOperatorsAccessor:
    def kd_aware(self):
        return DSFilterOperators.objects.filter(
            is_deleted=False, knowledge_end_date__isnull=True
        )

    def get_all_operators(self, projection=None):
        qs = self.kd_aware()
        if projection:
            qs = qs.values(*projection)
        return list(qs)


class DatasheetPermissionsAccessor:
    def __init__(self, client_id):
        self.client_id = client_id

    def client_aware(self):
        return DatasheetPermissions.objects.filter(client_id=self.client_id)

    def client_latest_kd_aware(self):
        return self.client_aware().filter(
            is_deleted=False, knowledge_end_date__isnull=True
        )

    def databook_aware(self, databook_id):
        return self.client_latest_kd_aware().filter(databook_id=databook_id)

    def databook_datasheet_aware(self, databook_id, datasheet_id):
        return self.client_latest_kd_aware().filter(
            databook_id=databook_id, datasheet_id=datasheet_id
        )

    def invalidate_permissions(self, databook_id, datasheet_id, time=None):
        if time is None:
            time = datetime.now()
        self.databook_datasheet_aware(databook_id, datasheet_id).update(
            knowledge_end_date=time
        )

    def get_all_permissions_of_datasheet(self, databook_id, datasheet_id):
        return (
            self.client_latest_kd_aware()
            .filter(databook_id=databook_id, datasheet_id=datasheet_id)
            .values()
        )

    def get_only_permissions_of_datasheet(self, databook_id, datasheet_id):
        return list(
            self.client_latest_kd_aware()
            .filter(databook_id=databook_id, datasheet_id=datasheet_id)
            .values_list("permission_set_id", flat=True)
        )

    def persist_permissions(self, data):
        return data.save()

    def get_filter_list_of_permissions(
        self, databook_id, datasheet_id, permission_set_ids_list
    ):
        return list(
            self.client_latest_kd_aware()
            .filter(databook_id=databook_id, datasheet_id=datasheet_id)
            .filter(permission_set_id__in=permission_set_ids_list)
            .values("filter_list", "columns_to_be_hidden")
        )

    def get_all_hidden_columns_of_datasheet(self, datasheet_id):
        columns_to_be_hidden = list(
            self.client_latest_kd_aware()
            .filter(datasheet_id=datasheet_id)
            .values_list("columns_to_be_hidden", flat=True)
        )
        final_columns_to_be_hidden = list(chain(*columns_to_be_hidden))
        return final_columns_to_be_hidden

    def get_hidden_columns_of_selected_permissions_of_datasheet(
        self, datasheet_id, permission_set_ids_list
    ):
        columns_to_be_hidden = list(
            self.client_latest_kd_aware()
            .filter(datasheet_id=datasheet_id)
            .filter(permission_set_id__in=permission_set_ids_list)
            .values_list("columns_to_be_hidden", flat=True)
        )

        final_columns_to_be_hidden = list(chain(*columns_to_be_hidden))
        return final_columns_to_be_hidden

    def get_all_permissions_of_datasheet_given_ds_id(self, datasheet_id):
        return list(
            self.client_latest_kd_aware()
            .filter(datasheet_id=datasheet_id)
            .values_list("permission_set_id", flat=True)
        )

    def get_filter_list(self, databook_id, datasheet_id):
        filter_list = list(
            self.client_latest_kd_aware()
            .filter(databook_id=databook_id, datasheet_id=datasheet_id)
            .values_list("filter_list", flat=True)
        )

        all_filter_lists = list(chain(*filter_list))
        return all_filter_lists
        # return filter_list


class DatasheetPermissionsTargetAccessor:
    def __init__(self, client_id):
        self.client_id = client_id

    def client_aware(self):
        return DSPermissionsTarget.objects.filter(client_id=self.client_id)

    def client_latest_kd_aware(self):
        return self.client_aware().filter(
            is_deleted=False, knowledge_end_date__isnull=True
        )

    def client_kd_deleted_aware(self, knowledge_date):
        return (
            self.client_aware()
            .filter(is_deleted=False, knowledge_begin_date__lte=knowledge_date)
            .filter(
                Q(knowledge_end_date__gt=knowledge_date)
                | Q(knowledge_end_date__isnull=True)
            )
        )

    def permission_aware(self, permission_set_id):
        return self.client_latest_kd_aware().filter(permission_set_id=permission_set_id)

    def get_users(self, permission_set_id):
        return (
            self.permission_aware(permission_set_id)
            .filter(target_type="user")
            .values_list("target", flat=True)
        )

    def get_user_groups(self, permission_set_id):
        return list(
            self.permission_aware(permission_set_id)
            .filter(target_type="user_group")
            .values_list("target", flat=True)
        )

    def get_all_permissions_for_users_and_user_groups(
        self, permission_set_ids_list, user_and_user_group_names_list
    ):
        return (
            self.client_latest_kd_aware()
            .filter(permission_set_id__in=permission_set_ids_list)
            .filter(target__in=user_and_user_group_names_list)
            .values_list("permission_set_id", flat=True)
        )

    def invalidate_permissions(self, permission_set_ids_list, time=None):
        if time is None:
            time = datetime.now()
        self.client_latest_kd_aware().filter(
            permission_set_id__in=permission_set_ids_list
        ).update(knowledge_end_date=time)

    def persist_permissions(self, data):
        return data.save()

    def does_permission_exist_for_user(self, permission_set_ids, user_and_user_group):
        return (
            self.client_latest_kd_aware()
            .filter(
                permission_set_id__in=permission_set_ids, target__in=user_and_user_group
            )
            .exists()
        )

    def does_permissions_with_given_user_group_id_exist(self, user_group_id):
        return (
            self.client_latest_kd_aware()
            .filter(target_type="user_group", target=user_group_id)
            .exists()
        )
