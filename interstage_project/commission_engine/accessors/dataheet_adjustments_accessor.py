# fmt: off

from collections import Counter
from datetime import datetime
from typing import Union

import pandas as pd
from django.db.models import Max, Q

from commission_engine.models import DatasheetAdjustments
from commission_engine.utils.general_data import (
    DATASHEET_ADJUSTMENT_OPERATION,
    DATASHEET_ADJUSTMENT_TYPE,
)


class DatasheetAdjustmentsAccessor:
    def __init__(self, client_id):
        self.client_id = client_id

    def client_aware(self):
        return DatasheetAdjustments.objects.filter(client=self.client_id)

    def invalidate_all_client_records(self, time):
        self.client_aware().filter(knowledge_end_date__isnull=True).update(knowledge_end_date=time, is_deleted=True)

    def delete_all_client_records(self):
        self.client_aware().delete()

    def client_kd_aware(self):
        return self.client_aware().filter(is_deleted=False, knowledge_end_date__isnull=True)

    def client_asof_kd_aware(self, kd):
        return (
            self.client_aware()
            .filter(is_deleted=False, knowledge_begin_date__lte=kd)
            .filter(Q(knowledge_end_date__gt=kd) | Q(knowledge_end_date__isnull=True))
        )

    def persist_datasheet_adjustments(self, data):
        data.save()

    def get_datasheet_adjustments(self, adjustment_id):
        return list(self.client_kd_aware().filter(adjustment_id=adjustment_id))

    def get_adjustment_by_id_and_number(self, adjustment_id, adjustment_number, sub_number):
        return (
            self.client_kd_aware()
            .filter(
                adjustment_id=adjustment_id,
                adjustment_number=adjustment_number,
                sub_adjustment_number=sub_number,
            )
            .first()
        )

    def invalidate(self, adjustment_id, adjustment_number, sub_number, knowledge_date):
        self.client_kd_aware().filter(
            adjustment_id=adjustment_id,
            adjustment_number=adjustment_number,
            sub_adjustment_number=sub_number,
        ).update(knowledge_end_date=knowledge_date)

    def invalidate_adj_by_id_number(self, adjustment_id, adjustment_number, knowledge_date):
        self.client_kd_aware().filter(adjustment_id=adjustment_id, adjustment_number=adjustment_number).update(
            knowledge_end_date=knowledge_date
        )

    def invalidate_by_adjustment_id(self, adjustment_id, adjustment_number, sub_number, knowledge_date):
        self.client_kd_aware().filter(
            adjustment_id=adjustment_id,
            adjustment_number=adjustment_number,
            sub_adjustment_number=sub_number,
        ).update(knowledge_end_date=knowledge_date)

    def delete_adjustment(self, adjustment_id, adjustment_number, sub_number, knowledge_date):
        prev_record = self.get_adjustment_by_id_and_number(adjustment_id, adjustment_number, sub_number)
        if prev_record:
            self.invalidate(adjustment_id, adjustment_number, sub_number, knowledge_date)
            prev_record.knowledge_begin_date = knowledge_date
            prev_record.is_deleted = True
            self.create_adjustment(prev_record)

    def create_adjustment(self, record):
        record.pk = None
        record.save()

    def update_datasheet_adjustments(
        self,
        adjustment_id,
        adjustment_number,
        sub_number,
        audit_details,
        data,
        knowledge_date,
        comments,
        is_global,
    ):
        prev_record = self.get_adjustment_by_id_and_number(adjustment_id, adjustment_number, sub_number)
        if prev_record:
            self.invalidate(adjustment_id, adjustment_number, sub_number, knowledge_date)
            prev_record.knowledge_begin_date = knowledge_date
            prev_record.data = data
            prev_record.additional_details = audit_details
            prev_record.comments = comments
            prev_record.is_global = is_global
            self.create_adjustment(prev_record)

    def get_max_adjustment_number(self):
        return self.client_aware().aggregate(Max("adjustment_number"))

    def get_datasheet_adjustments_for_ds(self, databook_id, datasheet_id):
        return list(self.client_kd_aware().filter(databook_id=databook_id, datasheet_id=datasheet_id))

    def get_datasheet_adjustments_for_ds_row_key(self, databook_id, datasheet_id, row_key, kd=None):
        if kd:
            qs = self.client_asof_kd_aware(kd)
        else:
            qs = self.client_kd_aware()
        qs = qs.filter(databook_id=databook_id, datasheet_id=datasheet_id)

        if isinstance(row_key, list):
            qs = qs.filter(original_row_key__in=row_key)
        else:
            qs = qs.filter(original_row_key=row_key)
        return list(qs)

    def get_datasheet_adjustments_for_ds_adjustment_row_key_as_frame(
            self, databook_id, datasheet_id, row_key, kd=None
    ):
        if kd:
            qs = self.client_asof_kd_aware(kd)
        else:
            qs = self.client_kd_aware()
        qs = qs.filter(databook_id=databook_id, datasheet_id=datasheet_id)

        if isinstance(row_key, list):
            qs = qs.filter(row_key__in=row_key)
        else:
            qs = qs.filter(row_key=row_key)

        return pd.DataFrame(
            qs.values()
        )

    def get_global_datasheet_adjustments_for_ds_row_key(self, databook_id, datasheet_id, row_key, kd=None):
        if kd:
            qs = self.client_asof_kd_aware(kd)
        else:
            qs = self.client_kd_aware()
        qs = qs.filter(databook_id=databook_id, datasheet_id=datasheet_id, is_global=True)

        if isinstance(row_key, list):
            qs = qs.filter(original_row_key__in=row_key)
        else:
            qs = qs.filter(original_row_key=row_key)
        return list(qs)

    def get_local_datasheet_adjustments_for_ds_row_key(self, databook_id, datasheet_id, row_key, kd=None):
        if kd:
            qs = self.client_asof_kd_aware(kd)
        else:
            qs = self.client_kd_aware()
        qs = qs.filter(databook_id=databook_id, datasheet_id=datasheet_id, is_global=False)

        if isinstance(row_key, list):
            qs = qs.filter(original_row_key__in=row_key)
        else:
            qs = qs.filter(original_row_key=row_key)
        return list(qs)

    def get_adjustments_for_ds_based_on_field(self, kd, databook_id, datasheet_id, field):
        qs = self.client_asof_kd_aware(kd).filter(
            databook_id=databook_id,
            datasheet_id=datasheet_id,
            operation="UPDATE",
            data__has_key=field,
        )
        return list(qs)

    def get_adjustments_for_datasheet_as_frame(
        self, databook_id, datasheet_id, adjustments_type_filter=None, knowledge_date=None
    ):
        if knowledge_date:
            qs = self.client_asof_kd_aware(knowledge_date)
        else:
            qs = self.client_kd_aware()
        if adjustments_type_filter == "GLOBAL":
            return pd.DataFrame(
                qs.filter(databook_id=databook_id, datasheet_id=datasheet_id, is_global=True)
                .values()
            )
        if adjustments_type_filter == "LOCAL":
            return pd.DataFrame(
                qs.filter(databook_id=databook_id, datasheet_id=datasheet_id, is_global=False)
                .values()
            )
        if not adjustments_type_filter:
            return pd.DataFrame(
                qs.filter(databook_id=databook_id, datasheet_id=datasheet_id)
                .values()
            )

    def get_row_keys_for_adjustments(self, databook_id, datasheet_id, adjustments_type_filter=None, knowledge_date=None):
        """Gets row_keys on which adjustments have to be applied

        Parameters
        ----------
        databook_id : str
            Databook ID of the datasheet whose row_keys for adjustment has to be fetched
        datasheet_id : str
            Datasheet ID of the datasheet whose row_keys for adjustment has to be fetched
        adjustments_type_filter : str, optional
            Type of adjustment to be filtered (GLOBAL or LOCAL)

        Returns
        -------
        set
            a set of strings representing the row_keys on which adjustments have to be applied
        """

        if knowledge_date:
            qs = self.client_asof_kd_aware(kd = knowledge_date)
        else:
            qs = self.client_kd_aware()

        if adjustments_type_filter == "GLOBAL":
            return set(
                qs.filter(databook_id=databook_id, datasheet_id=datasheet_id)
                .filter(is_global=True)
                .values_list("row_key", flat=True)
            )
        if adjustments_type_filter == "LOCAL":
            return set(
                qs.filter(databook_id=databook_id, datasheet_id=datasheet_id)
                .filter(is_global=False)
                .values_list("row_key", flat=True)
            )
        if not adjustments_type_filter:
            return set(
                qs.filter(databook_id=databook_id, datasheet_id=datasheet_id)
                .values_list("row_key", flat=True)
            )

        
    def is_adjustment_exist(self, databook_id, datasheet_id, original_row_key):
        return (
            self.client_kd_aware()
            .filter(
                databook_id=databook_id,
                datasheet_id=datasheet_id,
                original_row_key=original_row_key,
            )
            .exists()
        )

    def get_all_original_row_key_changed_after_kd(self, databook_id, datasheet_id, kd):
        return list(
            self.client_aware()
            .filter(
                knowledge_begin_date__gte=kd,
                knowledge_end_date__isnull=True,
                is_deleted=False,
                databook_id=databook_id,
                datasheet_id=datasheet_id,
            )
            .values_list("original_row_key", flat=True)
        )

    def get_original_row_keys_for_row_keys(self, databook_id, datasheet_id, kd, row_keys):
        return list(
            self.client_asof_kd_aware(kd)
            .filter(
                databook_id=databook_id,
                datasheet_id=datasheet_id,
                row_key__in=row_keys,
            )
            .values_list("original_row_key", flat=True)
        )

    def get_deleted_records_after_kd(self, databook_id, datasheet_id, kd):
        query_set = (
            self.client_aware()
            .filter(
                knowledge_end_date__gte=kd,
                databook_id=databook_id,
                datasheet_id=datasheet_id,
                operation__in=["INSERT"],
            )
            .values("row_key")
        )
        return list(query_set)

    def get_adjustments_inserted_in_range(
            self,
            databook_id: str,
            datasheet_id: str, 
            last_gen_time: datetime, 
            knowledge_date: datetime,
            fetch_entire_row: bool = True,
        ) -> list[Union[dict, str]]:
        """
        get adjustments for incremental etl that were inserted in this
        time range [last_gen_time, knowledge_date] and WERE not invalidated
        in the same time range.
        """
        query_set = self.client_aware().filter(
            Q(databook_id=databook_id),
            Q(datasheet_id=datasheet_id),
            Q(knowledge_begin_date__gt=last_gen_time),
            Q(knowledge_begin_date__lte=knowledge_date),
            Q(knowledge_end_date__isnull=True) | Q(knowledge_end_date__gt=knowledge_date)
        )

        if not fetch_entire_row:
            query_set = query_set.values("original_row_key", "row_key")
        else:
            query_set = query_set.values()

        return list(query_set)

    def get_adjustments_invalidated_in_range(
            self, 
            databook_id: str, 
            datasheet_id: str, 
            last_gen_time: datetime, 
            knowledge_date: datetime,
            fetch_entire_row: bool = True,
        ) -> list[Union[dict, str]]:
        """
        get adjustments for incremental etl that were were present before last_gen_time and
        were invalidated in this time range [last_gen_time, knowledge_date].
        """
        query_set = self.client_aware().filter(
            Q(databook_id=databook_id),
            Q(datasheet_id=datasheet_id),
            Q(knowledge_begin_date__lte=last_gen_time),
            Q(knowledge_end_date__isnull=False),
            Q(knowledge_end_date__gt=last_gen_time),
            Q(knowledge_end_date__lte=knowledge_date)
        )

        if not fetch_entire_row:
            query_set = query_set.values("original_row_key", "row_key")
        else:
            query_set = query_set.values()

        return list(query_set)
    
    def get_adjustments_existing_in_range(
            self,
            databook_id: str,
            datasheet_id: str, 
            last_gen_time: datetime, 
            knowledge_date: datetime,
            fetch_entire_row: bool = True,
        ) -> list[dict]:
        """
        get adjustments for incremental etl that were present before last_gen_time and
        did not change in the time range [last_gen_time, knowledge_date]
        """
        query_set = self.client_aware().filter(
            Q(databook_id=databook_id),
            Q(datasheet_id=datasheet_id),
            Q(knowledge_begin_date__lte=last_gen_time),
            Q(knowledge_end_date__isnull=True) | Q(knowledge_end_date__gt=knowledge_date)
        )

        if not fetch_entire_row:
            query_set = query_set.values("original_row_key", "row_key")
        else:
            query_set = query_set.values()

        return list(query_set)


    def total_number_of_adjustments(self, datasheet_id, operations=None, adjustment_type=None) -> dict:
        """
        Count the number of adjustments for a given client, datasheet, operation, and adjustment type.
        
        If the operation or adjustment type is not specified, then all operations and adjustment_types are counted.

        """
        query = self.client_kd_aware().filter(datasheet_id=datasheet_id)

        if isinstance(operations, str):
            operations = [operations]  # Convert single operation to a list
        if operations is None:
            operations = list(DATASHEET_ADJUSTMENT_OPERATION.__members__)

        query = query.filter(operation__in=operations)

        if adjustment_type is not None:
            if adjustment_type == DATASHEET_ADJUSTMENT_TYPE.GLOBAL.value:
                query = query.filter(is_global=True)
            elif adjustment_type == DATASHEET_ADJUSTMENT_TYPE.LOCAL.value:
                query = query.filter(is_global=False)


        operation_counts = Counter(query.filter(operation__in=operations).values_list('operation', flat=True))
        
        count = {operation: operation_counts.get(operation, 0) for operation in operations}
        
        return count
    
    def get_dangling_adjustments_as_frame(self, databook_id, datasheet_id):
        return pd.DataFrame(
            self.client_kd_aware()
            .filter(
                databook_id=databook_id,
                datasheet_id=datasheet_id,
                dangling_reason__isnull=False,
            )
            .values()
        )
