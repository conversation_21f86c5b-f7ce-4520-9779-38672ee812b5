#!/bin/bash
set -ex

echo "WARNING: This script will DELETE ALL migration files for the listed Django apps."
echo "Do NOT run this on a production environment or if you need to preserve migration history."
read -p "Are you sure you want to continue? (y/N): " confirm
if [[ "$confirm" != "y" && "$confirm" != "Y" ]]; then
    echo "Aborted."
    exit 1
fi

# All django apps that we have
declare -a django_apps=(
        "async_tasks"
        "commission_engine"
        "common"
        "crystal"
        "everstage_admin_backend"
        "everstage_ddd"
        "everstage_etl"
        "everstage_infra"
        "kpi"
        "ms_teams_everstage"
        "slack_everstage"
        "spm"
        "superset"
)

# Iterate over django apps in a for loop
for app in "${django_apps[@]}"
do
    if [ -d "$app" ]; then
        rm -rf "$app/migrations"
        mkdir -p "$app/migrations"
        touch "$app/migrations/__init__.py"
    else
        echo "Directory $app does not exist. Skipping."
    fi
done

python manage.py makemigrations

echo "Your migrations files are ready"
echo "Now run 'python manage.py migrate' on a freshly created database"
echo "Then run 'python manage.py runscript commission_engine.tests.create_bootstrap_data' to create some test data"
