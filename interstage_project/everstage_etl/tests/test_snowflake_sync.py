import datetime
import uuid
from unittest.mock import ANY, patch

import pandas as pd
import pytest
from django.test import TestCase

from commission_engine.database.snowflake_connection import (
    create_snowpark_session_wrapper,
)
from everstage_ddd.upstream import UpstreamSnowflakeSync, UpstreamSyncParams
from everstage_etl.tests.custom_exceptions import DataFrameNotMatchError
from everstage_etl.tests.utils import sort_df
from interstage_project.global_utils.general_utils import uuid_in_snakecase


@pytest.mark.usefixtures("snowflake_setup")
class TestSnowflakeSync(TestCase):
    integration_id = uuid.UUID("60577801-7f47-4559-aa37-4dab5b20cb1e")
    changes_start_time = datetime.datetime(2024, 1, 1, tzinfo=datetime.timezone.utc)
    client_id = 10037
    destination_object_id = 1
    e2e_sync_run_id = uuid.uuid4()

    def test_transform_data_count_flat_source(self):
        """Wrapper to test transform data count for flat source"""
        upstream_sync_meta = UpstreamSyncParams(
            source_data_table="deal_table_flat_source_202411",
            custom_object_id=int(self.destination_object_id),
            integration_id=self.integration_id,
            sync_mode="changes",
            client_id=self.client_id,
            e2e_sync_run_id=self.e2e_sync_run_id,
            sync_run_id=uuid.uuid4(),
            changes_sync_time=self.changes_start_time,
            is_source_data_as_variant=False,
        )
        self._transform_data_count(upstream_sync_meta)

    def test_transform_data_count_variant_source(self):
        """Wrapper to test transform data count for variant source"""
        upstream_sync_meta = UpstreamSyncParams(
            source_data_table="deal_table_variant_source_202411",
            custom_object_id=int(self.destination_object_id),
            integration_id=self.integration_id,
            sync_mode="changes",
            client_id=self.client_id,
            e2e_sync_run_id=self.e2e_sync_run_id,
            sync_run_id=uuid.uuid4(),
            changes_sync_time=self.changes_start_time,
            is_source_data_as_variant=True,
        )
        self._transform_data_count(upstream_sync_meta)

    def _transform_data_count(self, upstream_sync_meta: UpstreamSyncParams):
        """Test the number of records in the transformed data and deleted data table"""
        upstream_snowflake_sync = UpstreamSnowflakeSync(upstream_sync_meta)
        upstream_snowflake_sync.transform()

        expected_deleted_table_name = (
            f"deleted_data_{self.client_id}_04db1205_324a_4371_b5cb_3fe262388084"
        )
        expected_transformed_table_name = (
            f"transformed_data_{self.client_id}_04db1205_324a_4371_b5cb_3fe262388084"
        )

        sql_deleted = f"select * from {expected_deleted_table_name}"  # noqa: S608
        sql_transformed = (
            f"select * from {expected_transformed_table_name}"  # noqa: S608
        )

        output_deleted_data_table = f"deleted_data_{self.client_id}_{uuid_in_snakecase(upstream_sync_meta.sync_run_id)}"
        output_transformed_data_table = f"transformed_data_{self.client_id}_{uuid_in_snakecase(upstream_sync_meta.sync_run_id)}"

        sql_output_deleted = f"select * from {output_deleted_data_table}"  # noqa: S608
        sql_output_transformed = (
            f"select * from {output_transformed_data_table}"  # noqa: S608
        )

        with create_snowpark_session_wrapper(client_id=self.client_id) as session:
            expected_deleted_data = session.sql(sql_deleted).collect()
            expected_transformed_data = session.sql(sql_transformed).collect()
            output_deleted_data = session.sql(sql_output_deleted).collect()
            output_transformed_data = session.sql(sql_output_transformed).collect()

        expected_deleted_df = pd.DataFrame(expected_deleted_data)
        expected_transformed_df = pd.DataFrame(expected_transformed_data)
        output_deleted_df = pd.DataFrame(output_deleted_data)
        output_transformed_df = pd.DataFrame(output_transformed_data)

        deleted_df = sort_df(expected_deleted_df, ["ROW_KEY"])
        transformed_df = sort_df(expected_transformed_df, ["ROW_KEY"])
        output_deleted_df = sort_df(output_deleted_df, ["ROW_KEY"])
        output_transformed_df = sort_df(output_transformed_df, ["ROW_KEY"])

        assert len(deleted_df) == len(
            output_deleted_df
        ), "Number of deleted records does not match"
        assert len(transformed_df) == len(
            output_transformed_df
        ), "Number of transformed records does not match"

    def test_transform_column_values_flat_source(self):
        """Wrapper to test column values for records for flat source"""
        upstream_sync_meta = UpstreamSyncParams(
            source_data_table="deal_table_flat_source_202411",
            custom_object_id=int(self.destination_object_id),
            integration_id=self.integration_id,
            sync_mode="changes",
            client_id=self.client_id,
            e2e_sync_run_id=self.e2e_sync_run_id,
            sync_run_id=uuid.uuid4(),
            changes_sync_time=self.changes_start_time,
            is_source_data_as_variant=False,
        )
        self._transform_column_values_for_records(upstream_sync_meta)

    def test_transform_column_values_variant_source(self):
        """Wrapper to test column values for records for variant source"""
        upstream_sync_meta = UpstreamSyncParams(
            source_data_table="deal_table_variant_source_202411",
            custom_object_id=int(self.destination_object_id),
            integration_id=self.integration_id,
            sync_mode="changes",
            client_id=self.client_id,
            e2e_sync_run_id=self.e2e_sync_run_id,
            sync_run_id=uuid.uuid4(),
            changes_sync_time=self.changes_start_time,
            is_source_data_as_variant=True,
        )
        self._transform_column_values_for_records(upstream_sync_meta)

    def test_transform_column_values_with_transformation_logic_flat_source(self):
        """Wrapper to test column values for records for flat source with transformation logic"""
        upstream_sync_meta = UpstreamSyncParams(
            source_data_table="deal_table_flat_source_202411",
            custom_object_id=int(self.destination_object_id),
            integration_id=self.integration_id,
            sync_mode="changes",
            client_id=self.client_id,
            e2e_sync_run_id=self.e2e_sync_run_id,
            sync_run_id=uuid.uuid4(),
            changes_sync_time=self.changes_start_time,
            is_source_data_as_variant=False,
            transformation_logic="client_10037_integration_f201171f_7573_4b82_80bd_654832cdfd46_2",
        )
        self._transform_column_values_for_records(upstream_sync_meta)

    def test_transform_column_values_with_transformation_logic_variant_source(self):
        """Wrapper to test column values for records for variant source with transformation logic"""
        upstream_sync_meta = UpstreamSyncParams(
            source_data_table="deal_table_variant_source_202411",
            custom_object_id=int(self.destination_object_id),
            integration_id=self.integration_id,
            sync_mode="changes",
            client_id=self.client_id,
            e2e_sync_run_id=self.e2e_sync_run_id,
            sync_run_id=uuid.uuid4(),
            changes_sync_time=self.changes_start_time,
            is_source_data_as_variant=True,
            transformation_logic="client_10037_integration_f201171f_7573_4b82_80bd_654832cdfd46_2",
        )
        self._transform_column_values_for_records(upstream_sync_meta)

    def _transform_column_values_for_records(
        self, upstream_sync_meta: UpstreamSyncParams
    ):
        """Test the column values for records in the transformed data table"""
        upstream_snowflake_sync = UpstreamSnowflakeSync(upstream_sync_meta)
        upstream_snowflake_sync.transform()

        if upstream_sync_meta.transformation_logic is not None:
            expected_transformed_table_name = f"pre_transform_with_logic_{self.client_id}_04db1205_324a_4371_b5cb_3fe262388084_3"
        else:
            expected_transformed_table_name = f"transformed_data_{self.client_id}_04db1205_324a_4371_b5cb_3fe262388084_2"

        output_transformed_data_table = f"transformed_data_{self.client_id}_{uuid_in_snakecase(upstream_sync_meta.sync_run_id)}"

        sql_expected_transformed = (
            f"select * from {expected_transformed_table_name}"  # noqa: S608
        )
        sql_output_transformed = (
            f"select * from {output_transformed_data_table}"  # noqa: S608
        )

        with create_snowpark_session_wrapper(client_id=self.client_id) as session:
            expected_transformed_data = session.sql(sql_expected_transformed).collect()
            output_transformed_data = session.sql(sql_output_transformed).collect()

        expected_transformed_df = pd.DataFrame(expected_transformed_data)
        output_transformed_df = pd.DataFrame(output_transformed_data)
        expected_transformed_df = expected_transformed_df.set_index(
            "ROW_KEY"
        ).sort_index()
        output_transformed_df = output_transformed_df.set_index("ROW_KEY").sort_index()

        expected_transformed_df, output_transformed_df = expected_transformed_df.align(
            output_transformed_df, fill_value=None
        )

        comparison_result = expected_transformed_df.compare(output_transformed_df)

        if not comparison_result.empty:
            raise DataFrameNotMatchError()

    def test_deleted_data_row_key_and_snapshot(self):
        """
        Wrapper to test row_key and snapshot_value in the
        deleted data table for flat and variant source
        """
        self._deleted_data_row_key_and_snapshot("deal_table_flat_source_202411", False)
        self._deleted_data_row_key_and_snapshot(
            "deal_table_variant_source_202411", True
        )

    def _deleted_data_row_key_and_snapshot(
        self, source_data_table, is_source_data_as_variant
    ):
        """Test the row_key and snapshot_value in the deleted data table"""
        sync_run_id = uuid.uuid4()
        upstream_sync_meta = UpstreamSyncParams(
            source_data_table=source_data_table,
            custom_object_id=int(self.destination_object_id),
            integration_id=self.integration_id,
            sync_mode="changes",
            client_id=self.client_id,
            e2e_sync_run_id=self.e2e_sync_run_id,
            sync_run_id=sync_run_id,
            changes_sync_time=self.changes_start_time,
            is_source_data_as_variant=is_source_data_as_variant,
        )
        upstream_snowflake_sync = UpstreamSnowflakeSync(upstream_sync_meta)
        upstream_snowflake_sync.transform()

        expected_deleted_table_name = (
            f"deleted_data_{self.client_id}_04db1205_324a_4371_b5cb_3fe262388084"
        )
        output_deleted_data_table = (
            f"deleted_data_{self.client_id}_{uuid_in_snakecase(sync_run_id)}"
        )

        sql_expected_deleted = (
            f"select * from {expected_deleted_table_name}"  # noqa: S608
        )
        sql_output_deleted = f"select * from {output_deleted_data_table}"  # noqa: S608

        with create_snowpark_session_wrapper(client_id=self.client_id) as session:
            expected_deleted_data = session.sql(sql_expected_deleted).collect()
            output_deleted_data = session.sql(sql_output_deleted).collect()

        expected_deleted_df = pd.DataFrame(expected_deleted_data).sort_values(
            by=["ROW_KEY"]
        )
        output_deleted_df = pd.DataFrame(output_deleted_data).sort_values(
            by=["ROW_KEY"]
        )

        for idx in range(len(expected_deleted_df)):
            expected_record = expected_deleted_df.iloc[idx]
            output_record = output_deleted_df.iloc[idx]

            for column in ["ROW_KEY", "SNAPSHOT_VALUE"]:
                expected_value = expected_record[column]
                output_value = output_record[column]
                if pd.isna(expected_value) and pd.isna(output_value):
                    continue
                if output_value == expected_value:
                    print(f"Correct value in {column} at row {idx}: {output_value}")
                assert (
                    output_value == expected_value
                ), f"Value mismatch in {column} at row {idx}"

    @patch.object(UpstreamSnowflakeSync, "insert_data")
    @patch.object(UpstreamSnowflakeSync, "invalidate_and_mark_delete")
    @patch.object(
        UpstreamSnowflakeSync,
        "invalidate_all_valid_custom_object_manual_upload_data_rows",
    )
    @patch(
        "everstage_etl.tasks.loading._invalidate_all_valid_custom_object_upstream_data_rows"
    )
    def test_load_all_mode(  # noqa: PLR0913
        self,
        mock_invalidate_all,
        mock_invalidate_manual_upload,
        mock_invalidate_and_mark_delete,
        mock_insert_data,
    ):
        """
        Test the load method for sync_mode 'all' when persist_manual_upload_data is False.
        invalidate_all_valid_custom_object_manual_upload_data_rows should be called.
        """

        sync_run_id = uuid.uuid4()
        upstream_sync_meta_all = UpstreamSyncParams(
            source_data_table="deal_table_flat_source_202506",
            custom_object_id=int(self.destination_object_id),
            integration_id=self.integration_id,
            sync_mode="all",
            client_id=self.client_id,
            e2e_sync_run_id=self.e2e_sync_run_id,
            sync_run_id=sync_run_id,
            changes_sync_time=self.changes_start_time,
            is_source_data_as_variant=False,
        )

        upstream_snowflake_sync_all = UpstreamSnowflakeSync(upstream_sync_meta_all)
        upstream_snowflake_sync_all.load()

        mock_invalidate_all.assert_called_once()
        mock_invalidate_manual_upload.assert_called_once()
        mock_invalidate_and_mark_delete.assert_not_called()
        mock_insert_data.assert_called_once()

    @patch.object(UpstreamSnowflakeSync, "insert_data")
    @patch.object(UpstreamSnowflakeSync, "invalidate_and_mark_delete")
    @patch(
        "everstage_etl.tasks.loading._invalidate_all_valid_custom_object_upstream_data_rows"
    )
    def test_load_changes_mode(
        self, mock_invalidate_all, mock_invalidate_and_mark_delete, mock_insert_data
    ):
        """Test the load method calls for sync_mode 'changes'"""
        sync_run_id = uuid.uuid4()
        upstream_sync_meta_changes = UpstreamSyncParams(
            source_data_table="deal_table_flat_source_202411",
            custom_object_id=int(self.destination_object_id),
            integration_id=self.integration_id,
            sync_mode="changes",
            client_id=self.client_id,
            e2e_sync_run_id=self.e2e_sync_run_id,
            sync_run_id=sync_run_id,
            changes_sync_time=self.changes_start_time,
            is_source_data_as_variant=False,
        )
        upstream_snowflake_sync_changes = UpstreamSnowflakeSync(
            upstream_sync_meta_changes
        )
        upstream_snowflake_sync_changes.load()
        mock_invalidate_and_mark_delete.assert_called_once_with(
            ked=ANY, key_col="row_key", snowpark_session=ANY
        )
        mock_invalidate_all.assert_not_called()
        mock_insert_data.assert_called_once()

    @patch.object(UpstreamSnowflakeSync, "insert_data")
    @patch.object(UpstreamSnowflakeSync, "invalidate_and_mark_delete")
    @patch(
        "everstage_etl.tasks.loading._invalidate_all_valid_custom_object_upstream_data_rows"
    )
    def test_load_snapshot_mode(
        self, mock_invalidate_all, mock_invalidate_and_mark_delete, mock_insert_data
    ):
        """Test the load method calls for sync_mode 'snapshot'"""
        sync_run_id = uuid.uuid4()
        upstream_sync_meta_snapshot = UpstreamSyncParams(
            source_data_table="deal_table_flat_source_202411",
            custom_object_id=int(self.destination_object_id),
            integration_id=self.integration_id,
            sync_mode="snapshot",
            client_id=self.client_id,
            e2e_sync_run_id=self.e2e_sync_run_id,
            sync_run_id=sync_run_id,
            changes_sync_time=self.changes_start_time,
            is_source_data_as_variant=False,
        )
        upstream_snowflake_sync_snapshot = UpstreamSnowflakeSync(
            upstream_sync_meta_snapshot
        )
        upstream_snowflake_sync_snapshot.load()
        mock_invalidate_and_mark_delete.assert_called_once_with(
            ked=ANY, key_col="snapshot_value", snowpark_session=ANY
        )
        mock_invalidate_all.assert_not_called()
        mock_insert_data.assert_called_once()

    def test_load_and_verify_changes_mode(self):
        """Test the load method and verify the number of deletes and updates"""
        sync_run_id = uuid.uuid4()
        upstream_sync_meta = UpstreamSyncParams(
            source_data_table="deal_table_flat_source_202411",
            custom_object_id=self.destination_object_id,
            integration_id=self.integration_id,
            sync_mode="changes",
            client_id=self.client_id,
            e2e_sync_run_id=self.e2e_sync_run_id,
            sync_run_id=sync_run_id,
            changes_sync_time=self.changes_start_time,
            is_source_data_as_variant=False,
        )

        self._load_and_verify_changes(upstream_sync_meta)

    @pytest.mark.skip(reason="Skipping test_load_and_verify_all_mode")
    def test_load_and_verify_all_mode(self):
        """Test the load method and verify the number of deletes and updates"""
        sync_run_id = uuid.uuid4()
        upstream_sync_meta = UpstreamSyncParams(
            source_data_table="deal_table_flat_source_202506",
            custom_object_id=self.destination_object_id,
            integration_id=self.integration_id,
            sync_mode="all",
            client_id=self.client_id,
            e2e_sync_run_id=self.e2e_sync_run_id,
            sync_run_id=sync_run_id,
            changes_sync_time=self.changes_start_time,
            is_source_data_as_variant=False,
        )

        self._load_and_verify_changes(upstream_sync_meta)

    def _load_and_verify_changes(self, upstream_sync_meta: UpstreamSyncParams):
        """Test the load method and verify the number of deletes and updates"""
        upstream_snowflake_sync = UpstreamSnowflakeSync(upstream_sync_meta)
        upstream_snowflake_sync.transform()
        upstream_snowflake_sync.load()

        with create_snowpark_session_wrapper(client_id=self.client_id) as session:
            sql_expected = f"""SELECT * FROM expected_custom_object_data_{self.client_id}_{self.destination_object_id}"""  # noqa: S608
            sql_actual = f"""SELECT * FROM custom_object_data_{self.client_id}_{self.destination_object_id} 
            WHERE knowledge_end_date IS NULL or is_deleted=true"""  # noqa: S608

            expected_data = session.sql(sql_expected).collect()
            actual_data = session.sql(sql_actual).collect()

        expected_df = pd.DataFrame(expected_data)
        actual_df = pd.DataFrame(actual_data)

        expected_deleted_df = expected_df[
            expected_df["IS_DELETED"] == True  # noqa: E712
        ]
        actual_deleted_df = actual_df[actual_df["IS_DELETED"] == True]  # noqa: E712
        expected_active_df = expected_df[
            expected_df["IS_DELETED"] == False  # noqa: E712
        ]
        actual_active_df = actual_df[actual_df["IS_DELETED"] == False]  # noqa: E712

        expected_deleted_df = expected_deleted_df.set_index("ROW_KEY").sort_index()
        actual_deleted_df = actual_deleted_df.set_index("ROW_KEY").sort_index()
        expected_active_df = expected_active_df.set_index("ROW_KEY").sort_index()
        actual_active_df = actual_active_df.set_index("ROW_KEY").sort_index()

        assert len(expected_deleted_df) == len(
            actual_deleted_df
        ), "Number of deleted records does not match"
        assert len(expected_active_df) == len(
            actual_active_df
        ), "Number of active records does not match"

        assert expected_deleted_df.index.equals(
            actual_deleted_df.index
        ), "Row keys for deleted records do not match"

        assert expected_active_df["SNAPSHOT_VALUE"].equals(
            actual_active_df["SNAPSHOT_VALUE"]
        ), "Snapshot values for active records do not match"

        expected_active_df, actual_active_df = expected_active_df.align(
            actual_active_df, fill_value=None
        )

        comparison_result = expected_active_df["DATA"].compare(actual_active_df["DATA"])

        if not comparison_result.empty:
            raise DataFrameNotMatchError()
