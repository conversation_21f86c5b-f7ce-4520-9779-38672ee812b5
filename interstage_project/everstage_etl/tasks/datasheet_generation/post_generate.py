import datetime
import logging
import time
from uuid import UUID

from django.utils.timezone import make_aware

from commission_engine.accessors.client_accessor import (
    is_show_dangling_adjustments_enabled,
    should_use_multi_engine_stormbreaker,
)
from commission_engine.accessors.databook_accessor import DatasheetAccessor
from commission_engine.accessors.etl_housekeeping_accessor import (
    DatabookETLStatusAccessor,
)
from commission_engine.data_readers.databook_reader import DatasheetDataReader
from commission_engine.services.databook_etl_sync_status_service import (
    get_completed_failed_datasheet_ids,
)
from commission_engine.services.datasheet_data_services.datasheet_retrieval_service import (
    bulk_datasheet_config_update,
    bulk_set_datasheet_generated,
    update_dbkbd_map_bulk,
)
from commission_engine.services.etl_sync_status_service import change_status_to_failed
from commission_engine.utils.general_data import ETL_STATUS, SYNC_OBJECT
from everstage_ddd.datasheet.data_models import get_databook_id_from_datasheet_id
from everstage_ddd.datasheet.enums import DanglingReason
from everstage_ddd.datasheet.selectors.datasheet_adjustment_selector import (
    DatasheetAdjustmentSelector,
)
from everstage_etl.services.datasheet_execution_context_service import (
    is_sync_status_polling_threshold_breached,
)

from .utils import DatabookEtlStatusWrapper, get_primary_kd

logger = logging.getLogger(__name__)


def update_dangling_adjustment_status(
    client_id: int, datasheet_ids: list[UUID]
) -> None:
    """
    This function updates the adjustment status of the dangling adjustments.
    """
    current_time = datetime.datetime.now()
    for datasheet_id in datasheet_ids:
        logger.info("Fetching adjustment row keys")
        databook_id = get_databook_id_from_datasheet_id(client_id, datasheet_id)
        datasheet_adjustment_accessor = DatasheetAdjustmentSelector(client_id)
        adjustment_row_keys = (
            datasheet_adjustment_accessor.get_row_keys_for_adjustments(
                databook_id, datasheet_id
            )
        )
        if None in adjustment_row_keys:
            adjustment_row_keys.remove(None)
        if adjustment_row_keys:
            logger.info(
                "Adjustments present for this datasheet: {}".format(
                    len(adjustment_row_keys)
                )
            )
            start_time = time.monotonic()

            if should_use_multi_engine_stormbreaker(client_id):
                ds_reader = DatasheetDataReader(
                    client_id,
                    databook_id,
                    datasheet_id,
                    compute_strategy="duckdb_fallback_variant_snowflake",
                    apply_datasheet_permissions=False,
                )
            else:
                ds_reader = DatasheetDataReader(
                    client_id,
                    databook_id,
                    datasheet_id,
                    apply_datasheet_permissions=False,
                )

            try:
                logger.info("BEGIN: Fetch original data")
                data = ds_reader.fetch_data(as_data_frame=True, only_adjustments=True)
                logger.info("END: Fetch original data: {} rows".format(len(data)))
                logger.info(
                    "Time taken to fetch original data: %s seconds",
                    time.monotonic() - start_time,
                )
            except PermissionError:
                logger.exception("Permission error while fetching datasheet data")
                return

            df_adjustment_data_for_datasheet = DatasheetAdjustmentSelector(
                client_id
            ).get_adjustments_for_datasheet_as_frame(
                databook_id, datasheet_id, adjustments_type_filter=None
            )
            df_dangling_adjustments = df_adjustment_data_for_datasheet[
                df_adjustment_data_for_datasheet.dangling_reason.notnull()
            ]
            # dangling row keys are row keys present in adjustment_row_keys but are not present in data
            set_of_row_keys_in_adjustment = set(
                df_adjustment_data_for_datasheet.original_row_key
            )
            set_of_row_keys_in_dangling_adjustments = set(
                df_dangling_adjustments.original_row_key
            )
            set_of_row_keys_in_data = set(data.row_key)
            dangling_row_keys = (
                set_of_row_keys_in_adjustment
                - set_of_row_keys_in_dangling_adjustments
                - set_of_row_keys_in_data
            )

            dangling_records = df_adjustment_data_for_datasheet[
                df_adjustment_data_for_datasheet.original_row_key.isin(
                    dangling_row_keys
                )
            ].to_dict(orient="records")

            valid_records = df_dangling_adjustments[
                df_dangling_adjustments.original_row_key.isin(set_of_row_keys_in_data)
            ].to_dict(orient="records")

            for record in valid_records:
                datasheet_adjustment_accessor.update_dangling_adjustment_status(
                    adjustment_id=record.get("adjustment_id"),
                    adjustment_number=record.get("adjustment_number"),
                    sub_number=record.get("sub_adjustment_number"),
                    invalidated_at=None,
                    dangling_reason=None,
                )

            # for each record in dangling records, I want to fetch primary keys field form datasheet table with knowledge begin date <= record.knowledge_begin_date and compare that with datasheet tables latest record primary keys field
            # if they are different then need to mark this record as danglingwith primary key mismatch
            # if they are same then need to mark it as dangling with record update/ deleted
            datasheet_accessor = DatasheetAccessor(client_id)
            latest_primary_key_knowledge_begin_date = (
                datasheet_accessor.get_knowledge_begin_date_for_latest_primary_key(
                    databook_id, datasheet_id
                )
            )
            for record in dangling_records:
                invalidated_at = current_time
                primary_keys_before_update = (
                    datasheet_accessor.get_primary_columns_for_datasheet_kd_aware(
                        databook_id=databook_id,
                        datasheet_id=datasheet_id,
                        knowledge_date=record.get("knowledge_begin_date"),
                    )
                )
                latest_primary_key = (
                    datasheet_accessor.get_primary_columns_for_datasheet(
                        databook_id, datasheet_id
                    )
                )
                if primary_keys_before_update != latest_primary_key:
                    invalidated_at = latest_primary_key_knowledge_begin_date
                    dangling_reason = DanglingReason.PRIMARY_KEY_TRANSFORMED.value
                else:
                    dangling_reason = DanglingReason.ROW_INVALIDATED.value

                logger.info(
                    f"Dangling with reason: {dangling_reason}, row_key: {record.get('row_key')}"
                )
                datasheet_adjustment_accessor.update_dangling_adjustment_status(
                    adjustment_id=record.get("adjustment_id"),
                    adjustment_number=record.get("adjustment_number"),
                    sub_number=record.get("sub_adjustment_number"),
                    invalidated_at=invalidated_at,
                    dangling_reason=dangling_reason,
                )


def datasheet_generation_post_process(
    client_id: int,
    e2e_sync_run_id: UUID,
    wrapper_sync_run_id: UUID,
    sync_type: str | None = None,
    notification_email_id=None,
) -> None:
    """
    This task is responsible for releasing the locks acquired by the wrapper task.
    """
    (
        completed_datasheet_ids,
        failed_datasheet_ids,
    ) = get_completed_failed_datasheet_ids(
        client_id=client_id, e2e_sync_run_id=e2e_sync_run_id
    )

    logger.info(
        "Failed datasheets for client_id -> %s e2e_sync_run_id -> %s Count : %s. are: %s",
        client_id,
        e2e_sync_run_id,
        len(failed_datasheet_ids),
        failed_datasheet_ids,
    )

    is_update_datasheet_metadata_failed = _update_datasheet_metadata_bulk(
        e2e_sync_run_id=e2e_sync_run_id,
        client_id=client_id,
        sync_type=sync_type,
        datasheet_ids=completed_datasheet_ids,
    )

    databook_etl_lock = DatabookEtlStatusWrapper(
        client_id=client_id,
        e2e_sync_run_id=e2e_sync_run_id,
        sync_run_id=wrapper_sync_run_id,
    )

    # Here adding this check is_update_datasheet_metadata_failed because
    # While generating datasheet/databook if someone deletes the datasheet/databook or invalidated
    # at the time we cant update the datasheet metadata.

    is_polling_threshold_breached = is_sync_status_polling_threshold_breached(
        client_id=client_id, e2e_sync_run_id=str(e2e_sync_run_id), sync_type=sync_type
    )
    logger.info(
        "Is update_datasheet_metadata_failed: %s and is_polling_threshold_breached: %s",
        is_update_datasheet_metadata_failed,
        is_polling_threshold_breached,
    )

    if is_polling_threshold_breached is True:
        # If the polling threshold is breached, we will mark all the pending datasheets as failed.
        databook_etl_lock.change_all_pending_datasheets_to_failed(sync_type=sync_type)

    if (
        failed_datasheet_ids
        or is_update_datasheet_metadata_failed
        or is_polling_threshold_breached is True
    ):
        # 1) If there any datasheet is failed
        # 2) failed to update_datasheet_metadata_bulk means,
        # 3) polling threshold is breached
        # we will mark the wrapper sync as failed.

        databook_etl_lock.change_status_to_failed_state()
        change_status_to_failed(  # etl_sync_status_table
            client_id=client_id,
            e2e_sync_run_id=e2e_sync_run_id,
            timestamp=make_aware(datetime.datetime.now()),
            should_update_completion_time=False,
            email_id=notification_email_id,
        )

    else:
        if is_show_dangling_adjustments_enabled(client_id):
            update_dangling_adjustment_status(
                client_id=client_id, datasheet_ids=completed_datasheet_ids
            )
        databook_etl_accessor = DatabookETLStatusAccessor(
            client_id=client_id,
            e2e_sync_run_id=e2e_sync_run_id,
            sync_run_id=wrapper_sync_run_id,
        )
        start_time = databook_etl_accessor.get_task_start_time()
        sync_run_log = {
            "start_time": str(start_time),
            "end_time": str(make_aware(datetime.datetime.now())),
            "status": ETL_STATUS.COMPLETE.value,
            "error_info": None,
            "task": SYNC_OBJECT.DATASHEET_WRAPPER_SYNC.value,
        }
        databook_etl_lock.change_status_to_complete_state(sync_run_log=sync_run_log)


def _update_datasheet_metadata_bulk(
    e2e_sync_run_id: UUID,
    client_id: int,
    datasheet_ids: list[UUID],
    sync_type: str | None = None,
) -> bool:
    """
    This function updates various metadata after successfully
    datasheet has been generated. It performs the following actions:

    1) Upsert the datasheet record in the DbkdPkdMap table.
    2) Sets the datasheet generated flag to true in the datasheet table.
    3) Sets the datasheet PK modified flag to true in the datasheet table.

    """
    knowledge_date = get_primary_kd(
        client_id=client_id, e2e_sync_run_id=e2e_sync_run_id, sync_type=sync_type
    )

    logger.info("BEGIN: datasheet ids for update datasheet_metadata: %s", datasheet_ids)
    try:
        update_dbkbd_map_bulk(
            client_id=client_id,
            datasheet_ids=datasheet_ids,
            knowledge_date=knowledge_date,
        )
        bulk_set_datasheet_generated(
            client_id=client_id,
            datasheet_ids=datasheet_ids,
            knowledge_date=knowledge_date,
        )

        bulk_datasheet_config_update(
            client_id=client_id,
            datasheet_ids=datasheet_ids,
            config_names_and_values={
                "is_pk_modified": False,
                "is_config_changed": False,
                "is_calc_field_changed": False,
            },
            knowledge_end_date=knowledge_date,
        )
        update_datasheet_metadata_failed = False
    except Exception:
        # If any exception occurs while updating datasheet metadata, we need to mark that sync as failed.
        logger.exception("Exception occurred while updating datasheet metadata's")
        update_datasheet_metadata_failed = True

    logger.info("END: datasheet ids for update datasheet_metadata: %s", datasheet_ids)
    return update_datasheet_metadata_failed
