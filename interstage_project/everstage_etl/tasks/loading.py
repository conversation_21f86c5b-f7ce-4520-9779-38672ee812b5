import logging
from datetime import datetime
from uuid import UUID

import numpy as np
import pandas as pd
from django.core.paginator import Paginator
from django.db import transaction
from django.db.models import QuerySet

from commission_engine.accessors.custom_object_accessor import CustomObjectAccessor
from commission_engine.accessors.etl_housekeeping_accessor import (
    ExtractionSyncLogAccessor,
    TransformationSyncLogAccessor,
)
from commission_engine.database.snowflake_query_utils import (
    snowflake_insert_records,
    snowflake_invalidate_records,
    snowflake_writer_handler,
)
from commission_engine.models.etl_housekeeping_models import (
    ExtractionSyncLog,
    LoadSyncLog,
    TransformationSyncLog,
)
from commission_engine.snowflake_accessors.custom_object_data_accessor import (
    get_records_with_snapshot_value,
)
from commission_engine.utils.databook_utils import resolve_snowflake_data_type
from commission_engine.utils.general_data import CustomObjectDataSource
from commission_engine.utils.general_utils import get_custom_object_data_table_name
from commission_engine.utils.log_utils import merge_log_context
from interstage_project.threadlocal_log_context import set_threadlocal_context

logger = logging.getLogger(__name__)


@transaction.atomic
def batch_upstream_loader(
    client_id: int,
    e2e_sync_run_id: UUID,
    sync_run_id: UUID,
    object_id: str,
    destination_object_id: str,
    primary_kd: datetime,
    sync_mode: str,
    destination_object_type: str,
    page_size: int,
) -> None:
    """
    Batch-load transformed records for the given SyncRun ID.
    """
    transformed_records_qs = TransformationSyncLogAccessor(
        client_id=client_id,
        e2e_sync_run_id=e2e_sync_run_id,
        sync_run_id=sync_run_id,
    ).get_query_set_for_object_name(object_name=destination_object_id)

    deleted_records_qs = ExtractionSyncLogAccessor(
        client_id=client_id,
        e2e_sync_run_id=e2e_sync_run_id,
        sync_run_id=sync_run_id,
    ).get_query_set_for_object_name(object_name=object_id, operation="delete")

    logger.info(
        "BEGIN: Batch loading for transformed records - %s",
        object_id,
    )

    paginated_transformed_records_qs = Paginator(transformed_records_qs, page_size)

    page_range = paginated_transformed_records_qs.page_range

    for page_num in page_range:
        logger.info(
            "Loading records - Page %d of %d; Processed %d records till now.",
            page_num,
            page_range.stop - 1,
            (page_num - 1) * page_size,
        )
        curr_page = paginated_transformed_records_qs.get_page(page_num)

        curr_page_transformed_records_qs: QuerySet[TransformationSyncLog] = (
            curr_page.object_list
        )

        transformed_data = [
            record.transformed_objects for record in curr_page_transformed_records_qs
        ]

        upstream_loader(
            client_id,
            e2e_sync_run_id,
            sync_run_id,
            object_id,
            destination_object_id,
            transformed_data,
            primary_kd,
            sync_mode,
            destination_object_type,
            [],
        )

    logger.info(
        "END: Batch loading for transformed records - %s",
        object_id,
    )

    if sync_mode == "changes":
        logger.info(
            "BEGIN: Batch loading for deleted records - %s",
            object_id,
        )

        paginated_deleted_records_qs = Paginator(deleted_records_qs, page_size)

        page_range = paginated_deleted_records_qs.page_range

        for page_num in page_range:
            logger.info(
                "Loading records - Page %d of %d; Processed %d records till now.",
                page_num,
                page_range.stop - 1,
                (page_num - 1) * page_size,
            )
            curr_page = paginated_deleted_records_qs.get_page(page_num)

            curr_page_deleted_records_qs: QuerySet[ExtractionSyncLog] = (
                curr_page.object_list
            )

            deleted_data = [
                record.extracted_objects for record in curr_page_deleted_records_qs
            ]

            upstream_loader(
                client_id,
                e2e_sync_run_id,
                sync_run_id,
                object_id,
                destination_object_id,
                [],
                primary_kd,
                sync_mode,
                destination_object_type,
                deleted_data,
            )

        logger.info(
            "END: Batch loading for deleted records - %s",
            object_id,
        )


def remove_nan_nat(df):
    df1 = df.replace({np.nan: None})  # removes NaT
    df1 = df1.where(pd.notnull(df1), None)  # removes nan
    return df1


def get_hash(key, data_list):
    result = {}
    for item in data_list:
        result[item[key]] = item
    return result


# deleted_records column used in 'changes' sync mode. It will be empty in 'all' sync mode
def upstream_loader(
    client_id,
    e2e_sync_run_id,
    sync_run_id,
    object_id,
    destination_object_id,
    trans_data,
    primary_kd,
    sync_mode,
    destination_object_type,  # pylint: disable=unused-argument
    deleted_records,
    log_context=None,
):
    log_context_local = {
        "client_id": client_id,
        "e2e_sync_run_id": e2e_sync_run_id,
        "sync_run_id": sync_run_id,
        "object_id": object_id,
    }
    log_context = merge_log_context(log_context, log_context_local)
    set_threadlocal_context(log_context)
    records = trans_data
    insert_objs = []
    delete_objs = []
    update_objs = []
    noc_objs = []
    snapshot_key_list = []
    del_ids = []
    update_ids = []
    pk_col = "row_key"

    if sync_mode == "all" and isinstance(records, list) and len(records) > 0:
        update_objs = records
    # For Changes Sync Mode
    elif sync_mode == "changes":
        # Sh process the changes alone
        if deleted_records:
            for rec in deleted_records:
                delete_objs.append({pk_col: rec["Id"]})
        # process insert and update
        for rec in records:
            update_objs.append(rec)
    elif sync_mode == "override":
        custom_object = CustomObjectAccessor(client_id).get_object_by_ids(
            destination_object_id
        )
        snapshot_key = custom_object[0].snapshot_key if custom_object else []  # type: ignore
        new_data = records
        group_new_data = dict()
        for record in new_data:
            snap_val = []
            for key in snapshot_key:  # type: ignore
                snap_val.append(str(record["data"][key]))
            snap_val = "##::##".join(snap_val)
            if snap_val:
                if snap_val in group_new_data:
                    group_new_data[snap_val].append(record)
                else:
                    group_new_data[snap_val] = [record]
        old_data = get_records_with_snapshot_value(
            client_id, list(group_new_data.keys())
        )
        group_old_data = {}
        for record in old_data:
            snap_val = []
            for key in snapshot_key:  # type: ignore
                snap_val.append(str(record["data"][key]))
            snap_val = "##::##".join(snap_val)
            if snap_val in group_old_data:
                group_old_data[snap_val].append(record)
            else:
                group_old_data[snap_val] = [record]

        snapshot_key_list = list(group_new_data.keys())

        for key in snapshot_key_list:
            insert_objs.extend(group_new_data[key])

    # Final DB Operations.....
    logger.info("No of INSERTs.. %s", len(insert_objs))
    logger.info("No of UPDATEs.. %s", len(update_objs))
    if sync_mode == "all":
        logger.info("Deleting all active records, as sync mode is ALL")
    else:
        logger.info("No of DELETEs.. %s", len(delete_objs))
    logger.info("No of NO CHANGE.. %s", len(noc_objs))
    load_sync_objs = []
    if insert_objs:
        for rec in insert_objs:
            load_sync_objs.append(
                LoadSyncLog(
                    client_id=client_id,
                    e2e_sync_run_id=e2e_sync_run_id,
                    sync_run_id=sync_run_id,
                    object_name=object_id,
                    object_id=rec[pk_col],
                    loaded_objects=rec,
                    operation="insert",
                )
            )
            rec["knowledge_begin_date"] = primary_kd
            rec["client_id"] = client_id

    if delete_objs:
        del_ids = []
        for rec in delete_objs:
            load_sync_objs.append(
                LoadSyncLog(
                    client_id=client_id,
                    e2e_sync_run_id=e2e_sync_run_id,
                    sync_run_id=sync_run_id,
                    object_name=object_id,
                    object_id=rec[pk_col],
                    loaded_objects=rec,
                    operation="delete",
                )
            )
            del_ids.append(rec[pk_col])
        logger.info("Objects getting deteled %s", del_ids)

    if update_objs:
        update_ids = []
        for rec in update_objs:
            load_sync_objs.append(
                LoadSyncLog(
                    client_id=client_id,
                    e2e_sync_run_id=e2e_sync_run_id,
                    sync_run_id=sync_run_id,
                    object_name=object_id,
                    object_id=rec[pk_col],
                    loaded_objects=rec,
                    operation="update",
                )
            )
            rec["knowledge_begin_date"] = primary_kd
            rec["client_id"] = client_id
            update_ids.append(rec[pk_col])

    _write_to_snowflake(
        client_id=client_id,
        e2e_sync_run_id=e2e_sync_run_id,
        sync_run_id=sync_run_id,
        sync_mode=sync_mode,
        object_id=object_id,
        load_sync_objs=load_sync_objs,
        snapshot_key_list=snapshot_key_list,
        destination_object_id=destination_object_id,
        insert_objs=insert_objs,
        del_ids=del_ids,
        update_ids=update_ids,
        update_objs=update_objs,
        primary_kd=primary_kd,
    )

    if noc_objs:
        pass


def _write_to_snowflake(**kwargs):
    client_id = kwargs.get("client_id")
    snapshot_key_list = kwargs.get("snapshot_key_list")
    destination_object_id = kwargs.get("destination_object_id")
    insert_objs = kwargs.get("insert_objs")
    sync_mode = kwargs.get("sync_mode")
    del_ids = kwargs.get("del_ids")
    update_ids = kwargs.get("update_ids")
    update_objs = kwargs.get("update_objs")
    primary_kd = kwargs.get("primary_kd")
    e2e_sync_run_id = kwargs.get("e2e_sync_run_id", None)
    sync_run_id = kwargs.get("sync_run_id", None)
    table_name = get_custom_object_data_table_name(
        client_id=client_id, custom_object_id=destination_object_id
    )

    logger.info("Write to snowflake: UPSTREAM SYNC")
    query_tag = {
        "client_id": client_id,
        "e2e_sync_run_id": e2e_sync_run_id,
        "sync_run_id": sync_run_id,
        "object_id": destination_object_id,
    }

    # TODO Always ensure the table_name is changed when deployment
    with snowflake_writer_handler(
        client_id=client_id, tag=query_tag
    ) as snowpark_session:
        if snapshot_key_list:
            snowflake_invalidate_records(
                snowpark_session=snowpark_session,
                table_name=table_name,
                client_id=client_id,
                keys=snapshot_key_list,
                key_col="snapshot_key",
                filters={"custom_object_id": destination_object_id},
                ked=primary_kd,
            )
        if insert_objs:
            snowflake_insert_records(
                table_name=table_name,
                records=insert_objs,
                snowpark_session=snowpark_session,
                client_id=client_id,
            )
        if del_ids:
            snowflake_invalidate_records(
                snowpark_session=snowpark_session,
                table_name=table_name,
                client_id=client_id,
                keys=del_ids,
                key_col="row_key",
                filters={"custom_object_id": destination_object_id},
                ked=primary_kd,
                is_deleted=True,
            )
        if sync_mode == "all":
            _invalidate_all_valid_custom_object_upstream_data_rows(
                snowpark_session=snowpark_session,
                table_name=table_name,
                client_id=client_id,
                object_id=destination_object_id,
                ked=primary_kd,
            )
        if update_objs:
            snowflake_invalidate_records(
                snowpark_session=snowpark_session,
                table_name=table_name,
                client_id=client_id,
                keys=update_ids,
                key_col="row_key",
                filters={"custom_object_id": destination_object_id},
                ked=primary_kd,
            )
            snowflake_insert_records(
                table_name=table_name,
                records=update_objs,
                snowpark_session=snowpark_session,
                client_id=client_id,
            )


def _invalidate_all_valid_custom_object_upstream_data_rows(
    snowpark_session,
    table_name,
    client_id,
    object_id,
    ked,
):
    """
    Invalidate all existing data for the particular custom object
    """
    logger.info(
        f"BEGIN: Invalidating all rows for custom object id {object_id} - snowflake"
    )
    snowflake_datetime = resolve_snowflake_data_type("DATE")
    sql = f"""UPDATE {table_name} SET knowledge_end_date='{ked}'::{snowflake_datetime}
    where client_id={client_id}  and custom_object_id={object_id}
    and is_deleted=false and knowledge_end_date is null and source = '{CustomObjectDataSource.UPSTREAM.value}'
    """

    snowpark_session.sql(sql).collect()
    logger.info("END: Invalidating all rows - snowflake")
