#! /bin/bash
source "$(dirname "${BASH_SOURCE[0]}")/vars.sh"

# Test specific overrides below

export EXECUTION_ENV=PYTEST
export USE_LOCAL_CACHE='true'
export EVERSTAGE_LOG_LEVEL=ERROR

export SLACK_CLIENT_ID=*************.*************

export SNOWFLAKE_USER=EVERSTAGE_TEST_ETL
export SNOWFLAKE_PASSWORD=Tes#Pwd3
export SNOWFLAKE_ACCOUNT=oea92247
export SNOWFLAKE_REGION=us-west-2
export SNOWFLAKE_WAREHOUSE=EVERSTAGE_TEST_ETL_LOCAL
export SNOWFLAKE_SCHEMA=PUBLIC
export SNOWFLAKE_ROLE=ACCOUNTADMIN

export STORMBREAKER_SNOWFLAKE_USER=$SNOWFLAKE_USER
export STORMBREAKER_SNOWFLAKE_PASSWORD=$SNOWFLAKE_PASSWORD
export STORMBREAKER_SNOWFLAKE_ACCOUNT=$SNOW<PERSON>AKE_ACCOUNT
export STORMBREAKER_SNOWFLAKE_REGION=$SNOWFLAKE_REGION
export STORMBREAKER_SNOWFLAKE_WAREHOUSE=$SNOWFLAKE_WAREHOUSE
export STORMBREAKER_SNOWFLAKE_SCHEMA=$SNOWFLAKE_SCHEMA
export STORMBREAKER_SNOWFLAKE_ROLE=$SNOWFLAKE_ROLE

export TYPESENSE_API_KEY='xyz'
export TYPESENSE_HOST='xxx.a1.typesense.net'

export EFS_BASE_PATH=snapshot

# Unset URL because local_variable.env sets it to localhost:3000,
# but Slack code tests expect URL to be None
unset URL