import uuid
from dataclasses import dataclass
from enum import Enum

from concurrency.fields import AutoIncVersionField
from django.core.serializers.json import DjangoJSONEncoder
from django.db import models
from django.db.models import F, Index, Q, UniqueConstraint

from commission_engine.models.client_models import Client
from commission_engine.models.common_models import MultiTenantTemporal, Temporal
from commission_engine.utils.report_utils import DataOrigin
from everstage_ddd.datasheet.enums import (
    TransformationType,
    multiple_source_transformation_registry,
)
from interstage_project.db.models import (
    EsArrayField,
    EsBooleanField,
    EsCharField,
    EsDateTimeField,
    EsEmailField,
    EsForeignKey,
    EsIntegerField,
    EsJSONField,
    EsTextField,
    EsUUIDField,
)


class AdjustmentScopeLevel(Enum):
    GLOBAL = "global"
    LOCAL = "local"


class Databook(MultiTenantTemporal):
    databook_id = EsUUIDField(default=uuid.uuid4, null=False, is_sensitive=False)
    name = EsCharField(max_length=254, null=True, is_sensitive=False)
    is_draft = EsBooleanField(null=False, default=True, is_sensitive=False)
    is_archived = EsBooleanField(null=False, default=False, is_sensitive=False)
    created_at = EsDateTimeField(null=False, is_sensitive=False)
    created_by = EsEmailField(null=False, is_sensitive=False)
    datasheet_order = EsJSONField(
        encoder=DjangoJSONEncoder, null=True, default=None, is_sensitive=False
    )

    class Meta(MultiTenantTemporal.Meta):
        db_table = "databook"
        app_label = "commission_engine"
        indexes = MultiTenantTemporal.Meta.indexes + [
            Index(
                fields=[
                    "client_id",
                    "is_deleted",
                    "-knowledge_end_date",
                    "databook_id",
                ],
                name="db_ked_dbi_idx",
            ),
        ]


@dataclass
class LinkDatasheet:
    datasheet_id: str
    crosslink: bool
    crosslink_databook_id: str


class Datasheet(MultiTenantTemporal):
    # the datasheet belongs (or is a constituent) of this databook
    databook_id = EsUUIDField(null=False, is_sensitive=False)
    datasheet_id = EsUUIDField(default=uuid.uuid4, null=False, is_sensitive=False)
    name = EsCharField(max_length=254, null=True, is_sensitive=False)
    description = EsTextField(
        null=True, is_sensitive=False
    )  # TODO: Change null to false. For now, validating in frontend
    source_type = EsCharField(max_length=254, null=True, is_sensitive=False)
    source_id = EsCharField(max_length=254, null=True, is_sensitive=False)
    # this column will be populated only if source_type as 'datasheet'
    source_databook_id = EsUUIDField(null=True, is_sensitive=False)
    order = EsIntegerField(null=True, is_sensitive=False)
    primary_key = EsJSONField(null=True, encoder=DjangoJSONEncoder, is_sensitive=False)
    additional_details = EsJSONField(
        null=True, encoder=DjangoJSONEncoder, is_sensitive=False
    )
    transformation_spec = EsJSONField(null=True, encoder=DjangoJSONEncoder)
    ordered_columns = EsArrayField(
        EsCharField(max_length=254), null=True, is_sensitive=False
    )
    hidden_columns = EsArrayField(
        EsCharField(max_length=254), null=True, is_sensitive=False
    )
    tags = EsJSONField(null=True, encoder=DjangoJSONEncoder, is_sensitive=False)
    # the below flag is set when primary key or datasheet variables get added/deleted/updated
    is_pk_modified = EsBooleanField(default=False, is_sensitive=False)
    is_datasheet_generated = EsBooleanField(default=False, is_sensitive=False)
    # this flag is set when any change happens to the transformation or ds variables
    is_config_changed = EsBooleanField(default=False, is_sensitive=False)
    # this flag is set when any change happens to calculated fields of the sheet
    is_calc_field_changed = EsBooleanField(default=False, is_sensitive=False)
    data_origin = EsCharField(
        max_length=254, default=DataOrigin.CUSTOM_OBJECT.value, is_sensitive=False
    )
    data_last_updated_at = EsDateTimeField(null=True, is_sensitive=False)
    is_force_skip = EsBooleanField(null=False, default=False, is_sensitive=False)

    @property
    def is_crosslinked(self) -> bool:
        return len(self.crosslinked_datasheet_set) != 0

    @property
    def crosslinked_datasheet_set(self) -> list[LinkDatasheet]:
        return [ld for ld in self.linked_datasheets if ld.crosslink is True]

    @property
    def linked_datasheets(self) -> list[LinkDatasheet]:
        """
        All datasheets that are linked to this datasheet.  The link can be to datasheets
        within the databook or cross-linked datasheets from other databooks.
        """
        linked_datasheets = []
        if self.source_type == "datasheet":
            source_datasheet = {
                "datasheet_id": self.source_id,
                "crosslink": False,
                "crosslink_databook_id": None,
            }
            if self.source_databook_id is not None:
                source_datasheet["crosslink"] = True
                source_datasheet["crosslink_databook_id"] = self.source_databook_id
            linked_datasheets.append(LinkDatasheet(**source_datasheet))

        if self.transformation_spec is not None:
            for transformation_dict in self.transformation_spec:
                if (
                    transformation_dict.get("type")
                    in multiple_source_transformation_registry
                ):
                    if (
                        transformation_dict.get("type")
                        == TransformationType.TEMPORAL_SPLICE.name
                    ):
                        for source in transformation_dict.get("meta"):
                            linked_datasheet = {}
                            if source["source_type"] == "datasheet":
                                with_id = source["source_id"]
                                if source.get("with_databook_id"):
                                    linked_datasheet_dict = {
                                        "datasheet_id": with_id,
                                        "crosslink": True,
                                        "crosslink_databook_id": source.get(
                                            "with_databook_id"
                                        ),
                                    }
                                else:
                                    linked_datasheet_dict = {
                                        "datasheet_id": with_id,
                                        "crosslink": False,
                                        "crosslink_databook_id": None,
                                    }
                                linked_datasheets.append(
                                    LinkDatasheet(**linked_datasheet_dict)
                                )

                    else:
                        datasheet_id = transformation_dict["with"]
                        linked_datasheet = {
                            "datasheet_id": datasheet_id,
                            "crosslink": False,
                            "crosslink_databook_id": None,
                        }
                        if transformation_dict.get("with_databook_id") is not None:
                            linked_datasheet["crosslink"] = True
                            linked_datasheet["crosslink_databook_id"] = (
                                transformation_dict.get("with_databook_id")
                            )
                        linked_datasheets.append(LinkDatasheet(**linked_datasheet))

        return linked_datasheets

    class Meta(MultiTenantTemporal.Meta):
        db_table = "datasheet"
        app_label = "commission_engine"
        constraints = [
            UniqueConstraint(
                fields=["client_id", "datasheet_id"],
                condition=Q(is_deleted=False) & Q(knowledge_end_date__isnull=True),
                name="unique_active_datasheet",
            )
        ]
        indexes = MultiTenantTemporal.Meta.indexes + [
            Index(
                fields=[
                    "client_id",
                    "is_deleted",
                    "-knowledge_end_date",
                    "databook_id",
                    "datasheet_id",
                ],
                name="ds_ked_dbi_dsi_idx",
            ),
            Index(
                fields=[
                    "client_id",
                    "is_deleted",
                    "knowledge_begin_date",
                    "-knowledge_end_date",
                    "databook_id",
                    "datasheet_id",
                ],
                name="ds_kbd_ked_dbi_dsi_idx",
            ),
            Index(
                "client_id",
                F("additional_details__analytics_default_dashboard_datasheet"),
                "is_deleted",
                F("knowledge_end_date").desc(),
                "databook_id",
                "datasheet_id",
                name="ds_hidden_ked_dbi_dsi_idx",
            ),
            Index(
                "client_id",
                F("additional_details__analytics_default_dashboard_datasheet"),
                "is_deleted",
                "knowledge_begin_date",
                F("knowledge_end_date").desc(),
                "databook_id",
                "datasheet_id",
                name="ds_hidden_kbd_ked_dbi_dsi_idx",
            ),
        ]


class DSFilterOperators(Temporal):
    id = EsUUIDField(default=uuid.uuid4, null=False, is_sensitive=False)
    name = EsCharField(max_length=254, null=False, blank=False, is_sensitive=False)
    alt_name = EsCharField(max_length=254, null=False, blank=False, is_sensitive=False)
    category = EsCharField(max_length=100, null=False, blank=False, is_sensitive=False)
    operand_type_ids = EsJSONField(null=True, blank=True, is_sensitive=False)
    output_type_ids = EsJSONField(null=True, blank=True, is_sensitive=False)
    needs_operand = EsBooleanField(null=False, default=True, is_sensitive=False)
    multi_valued = EsBooleanField(null=False, default=True, is_sensitive=False)

    class Meta(Temporal.Meta):
        db_table = "ds_filter_operators"
        app_label = "commission_engine"
        indexes = Temporal.Meta.indexes + [
            Index(
                fields=["id", "is_deleted", "-knowledge_end_date"], name="dsfo_ked_idx"
            )
        ]


class DatasheetAdjustments(MultiTenantTemporal):
    adjustment_id = EsUUIDField(default=uuid.uuid4, null=False, is_sensitive=False)
    adjustment_number = EsIntegerField(null=False, is_sensitive=False)
    sub_adjustment_number = EsCharField(max_length=254, null=True, is_sensitive=False)
    databook_id = EsUUIDField(null=False, is_sensitive=False)
    datasheet_id = EsUUIDField(null=False, is_sensitive=False)
    original_row_key = EsCharField(max_length=4096, null=True, is_sensitive=False)
    row_key = EsCharField(max_length=4096, null=True, is_sensitive=False)
    operation = EsCharField(max_length=254, null=True, is_sensitive=False)
    data = EsJSONField(null=True, encoder=DjangoJSONEncoder)
    comments = EsCharField(max_length=4096, null=True)
    is_global = EsBooleanField(default=False, null=False, is_sensitive=False)
    dangling_reason = EsCharField(max_length=254, null=True, is_sensitive=False)
    invalidated_at = EsDateTimeField(null=True, is_sensitive=False)

    class Meta(MultiTenantTemporal.Meta):
        db_table = "datasheet_adjustments"
        app_label = "commission_engine"
        indexes = MultiTenantTemporal.Meta.indexes + [
            Index(
                fields=[
                    "client_id",
                    "is_deleted",
                    "-knowledge_end_date",
                    "adjustment_id",
                    "adjustment_number",
                    "sub_adjustment_number",
                ],
                name="dsa_ked_adj_idx",
            ),
            Index(
                fields=[
                    "client_id",
                    "is_deleted",
                    "-knowledge_end_date",
                    "databook_id",
                    "datasheet_id",
                    "is_global",
                ],
                name="dsa_ked_glob_idx",
            ),
        ]


class DatasheetPermissions(MultiTenantTemporal):
    databook_id = EsUUIDField(null=False, is_sensitive=False)
    datasheet_id = EsUUIDField(null=False, is_sensitive=False)
    filter_list = EsJSONField(null=True, encoder=DjangoJSONEncoder)
    permission_set_id = EsUUIDField(null=False, is_sensitive=False)
    permission_set_name = EsCharField(max_length=254, null=True, is_sensitive=False)
    columns_to_be_hidden = EsArrayField(
        EsCharField(max_length=254, null=True),
        default=list,
        blank=True,
        is_sensitive=False,
    )

    class Meta(MultiTenantTemporal.Meta):
        db_table = "datasheet_permissions"
        app_label = "commission_engine"
        indexes = MultiTenantTemporal.Meta.indexes + [
            Index(
                fields=[
                    "client_id",
                    "is_deleted",
                    "-knowledge_end_date",
                    "databook_id",
                    "datasheet_id",
                ],
                name="dsp_ked_idx",
            ),
            Index(
                fields=[
                    "client_id",
                    "is_deleted",
                    "-knowledge_end_date",
                    "databook_id",
                    "datasheet_id",
                    "permission_set_id",
                ],
                name="dsp_ked_psi_idx",
            ),
            Index(
                fields=[
                    "client_id",
                    "is_deleted",
                    "-knowledge_end_date",
                    "databook_id",
                    "datasheet_id",
                    "permission_set_id",
                    "columns_to_be_hidden",
                ],
                name="dsp_ked_psi_ctbh_idx",
            ),
        ]


class DSPermissionsTarget(MultiTenantTemporal):
    target_type = EsCharField(max_length=254, null=True, is_sensitive=False)
    target = EsCharField(max_length=254, null=True, is_sensitive=False)
    permission_set_id = EsUUIDField(null=False, is_sensitive=False)

    class Meta(MultiTenantTemporal.Meta):
        db_table = "datasheet_permissions_target"
        app_label = "commission_engine"
        indexes = MultiTenantTemporal.Meta.indexes + [
            Index(
                fields=[
                    "client_id",
                    "is_deleted",
                    "-knowledge_end_date",
                    "permission_set_id",
                    "target_type",
                    "target",
                ],
                name="dpt_psi_idx",
            ),
            Index(
                fields=[
                    "client_id",
                    "is_deleted",
                    "-knowledge_end_date",
                    "permission_set_id",
                    "target_type",
                ],
                name="dpt_tt_idx",
            ),
            Index(
                fields=[
                    "client_id",
                    "is_deleted",
                    "-knowledge_end_date",
                    "permission_set_id",
                    "target",
                ],
                name="dpt_t_idx",
            ),
        ]


class DSSnapshotInfoLocal(models.Model):
    # There will be similar model called "ds_snapshot_info" in snowflake.
    # To avoid collisions, we are using same version of that model locally.
    # This is only used for local purposes. Please do test your code with remote snowflake table.
    client = EsForeignKey(Client, on_delete=models.DO_NOTHING, is_sensitive=False)
    databook_id = EsUUIDField(null=False, is_sensitive=False)
    datasheet_id = EsUUIDField(null=False, is_sensitive=False)
    generated_time = EsDateTimeField(null=False, is_sensitive=False)
    path = EsCharField(max_length=4096, null=False, is_sensitive=False)
    checksum = EsCharField(max_length=254, null=True, is_sensitive=False)

    class Meta:
        db_table = "ds_snapshot_info_local"
        app_label = "commission_engine"


class DbkdPkdMap(MultiTenantTemporal):
    primary_kd = EsDateTimeField(null=True, is_sensitive=False)
    databook_id = EsUUIDField(null=True, is_sensitive=False)
    datasheet_id = EsUUIDField(null=True, is_sensitive=False)

    class Meta(MultiTenantTemporal.Meta):
        db_table = "dbkd_pkd_map"
        app_label = "commission_engine"
        indexes = MultiTenantTemporal.Meta.indexes + [
            Index(
                fields=[
                    "client_id",
                    "is_deleted",
                    "-knowledge_end_date",
                    "databook_id",
                    "datasheet_id",
                ],
                name="dp_ked_dbi_dsi_idx",
            ),
        ]


class DatasheetTransformation(models.Model):

    client = EsForeignKey(
        Client, on_delete=models.DO_NOTHING, db_index=False, is_sensitive=False
    )
    datasheet_id = EsUUIDField(null=False, is_sensitive=False)
    transformation_id = EsUUIDField(null=False, default=uuid.uuid4, is_sensitive=False)
    spec = EsJSONField(null=False, encoder=DjangoJSONEncoder, is_sensitive=False)
    is_saved = EsBooleanField(default=False, is_sensitive=False)
    created_at = EsDateTimeField(auto_now_add=True, is_sensitive=False)
    updated_at = EsDateTimeField(auto_now=True, is_sensitive=False)
    order = EsIntegerField(null=False, is_sensitive=False)
    type = EsCharField(
        max_length=50,
        null=False,
        choices=[(type.value, type.name) for type in TransformationType],
        is_sensitive=False,
    )
    version = AutoIncVersionField()

    class Meta:
        db_table = "datasheet_transformations"
        indexes = [
            Index(
                fields=["client_id", "is_saved", "datasheet_id", "order"],
                name="client_datasheet_id_type_idx",
            ),
        ]
