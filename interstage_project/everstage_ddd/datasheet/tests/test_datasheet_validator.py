from unittest import mock
from uuid import UUID

import pytest
from django.test import TestCase

from everstage_ddd.datasheet.data_models import (
    DatasheetValidateRequest,
    DatasheetValidateResponse,
)
from everstage_ddd.datasheet.exceptions import DatasheetException
from everstage_ddd.datasheet.helpers.datasheet_validator import DatasheetValidator
from everstage_ddd.datasheet.selectors.datasheet_transformation_selector import (
    DatasheetTransformationSelector,
)
from everstage_ddd.datasheet.services.datasheet_service import validate_datasheet

from .datasheet_validator_data_set_up import (
    datasheet_validator_create_data,
    datasheet_validator_delete_data,
    get_adding_join_transformation_data,
    get_adding_more_aggregations_to_existing_group_by_data,
    get_flatten_transformation_data,
    get_group_by_transformation_data,
    get_renamed_flatten_variable_data,
    get_renaming_group_by_variable_data,
)

# ruff: noqa: SLF001

TEST_CLIENT_ID = 3024


@pytest.fixture(scope="module", autouse=True)
@pytest.mark.django_db
def setup_teardown(django_db_setup, django_db_blocker):  # noqa: ARG001
    with django_db_blocker.unblock():
        datasheet_validator_create_data()
        yield
        datasheet_validator_delete_data()


class TestDatasheetValidator(TestCase):

    def test_initilazing_validator(self):
        source_variables = [
            {
                "meta_data": None,
                "source_cf_meta_data": None,
                "source_id": 1,
                "variable_id": "co_1_name",
                "system_name": "co_1_name",
                "display_name": "Name",
                "data_type_id": 4,
                "source_variable_id": None,
                "source_type": "object",
                "is_selected": True,
                "is_primary": False,
                "field_order": 0,
            },
            {
                "meta_data": None,
                "source_cf_meta_data": None,
                "source_id": 1,
                "variable_id": "co_1_number",
                "system_name": "co_1_number",
                "display_name": "Number",
                "data_type_id": 1,
                "source_variable_id": None,
                "source_type": "object",
                "is_selected": True,
                "is_primary": True,
                "field_order": 0,
            },
            {
                "meta_data": None,
                "source_cf_meta_data": None,
                "source_id": 1,
                "variable_id": "co_1_boolean",
                "system_name": "co_1_boolean",
                "display_name": "Boolean",
                "data_type_id": 3,
                "source_variable_id": None,
                "source_type": "object",
                "is_selected": True,
                "is_primary": False,
                "field_order": 0,
            },
            {
                "meta_data": None,
                "source_cf_meta_data": None,
                "source_id": 1,
                "variable_id": "co_1_date_1",
                "system_name": "co_1_date_1",
                "display_name": "Date-1",
                "data_type_id": 2,
                "source_variable_id": None,
                "source_type": "object",
                "is_selected": True,
                "is_primary": False,
                "field_order": 0,
            },
            {
                "meta_data": None,
                "source_cf_meta_data": None,
                "source_id": 1,
                "variable_id": "co_1_email",
                "system_name": "co_1_email",
                "display_name": "Email",
                "data_type_id": 12,
                "source_variable_id": None,
                "source_type": "object",
                "is_selected": True,
                "is_primary": False,
                "field_order": 0,
            },
            {
                "meta_data": None,
                "source_cf_meta_data": None,
                "source_id": 1,
                "variable_id": "co_1_percentage",
                "system_name": "co_1_percentage",
                "display_name": "Percentage",
                "data_type_id": 6,
                "source_variable_id": None,
                "source_type": "object",
                "is_selected": True,
                "is_primary": False,
                "field_order": 0,
            },
            {
                "meta_data": None,
                "source_cf_meta_data": None,
                "source_id": 1,
                "variable_id": "co_1_date_2",
                "system_name": "co_1_date_2",
                "display_name": "Date-2",
                "data_type_id": 2,
                "source_variable_id": None,
                "source_type": "object",
                "is_selected": True,
                "is_primary": False,
                "field_order": 0,
            },
            {
                "meta_data": None,
                "source_cf_meta_data": None,
                "source_id": 1,
                "variable_id": "co_1_test",
                "system_name": "co_1_test",
                "display_name": "test",
                "data_type_id": 4,
                "source_variable_id": None,
                "source_type": "object",
                "is_selected": True,
                "is_primary": False,
                "field_order": 0,
            },
            {
                "meta_data": None,
                "source_cf_meta_data": None,
                "source_id": 1,
                "variable_id": "co_1_email_2",
                "system_name": "co_1_email_2",
                "display_name": "email-2",
                "data_type_id": 12,
                "source_variable_id": None,
                "source_type": "object",
                "is_selected": True,
                "is_primary": False,
                "field_order": 0,
            },
        ]
        variables = [
            {
                "meta_data": None,
                "source_cf_meta_data": None,
                "source_id": "1",
                "variable_id": "b95fcbc7-dcfb-4eb9-88fd-d7e267f00eb8",
                "system_name": "co_1_name",
                "display_name": "Name",
                "data_type_id": 4,
                "source_variable_id": "co_1_name",
                "source_type": "object",
                "is_selected": True,
                "is_primary": False,
                "field_order": 0,
            },
            {
                "meta_data": None,
                "source_cf_meta_data": None,
                "source_id": "1",
                "variable_id": "fe540c74-bade-4694-808a-0b4d967c8e8a",
                "system_name": "co_1_number",
                "display_name": "Number",
                "data_type_id": 1,
                "source_variable_id": "co_1_number",
                "source_type": "object",
                "is_selected": True,
                "is_primary": True,
                "field_order": 0,
            },
            {
                "meta_data": None,
                "source_cf_meta_data": None,
                "source_id": "1",
                "variable_id": "7c3cfe77-d2ee-4be6-87b0-03eccb15f4fe",
                "system_name": "co_1_boolean",
                "display_name": "Boolean",
                "data_type_id": 3,
                "source_variable_id": "co_1_boolean",
                "source_type": "object",
                "is_selected": True,
                "is_primary": False,
                "field_order": 0,
            },
            {
                "meta_data": None,
                "source_cf_meta_data": None,
                "source_id": "1",
                "variable_id": "f3c3d89a-b4c5-45a8-832e-8cecb775506b",
                "system_name": "co_1_date_1",
                "display_name": "Date-1",
                "data_type_id": 2,
                "source_variable_id": "co_1_date_1",
                "source_type": "object",
                "is_selected": True,
                "is_primary": False,
                "field_order": 0,
            },
            {
                "meta_data": None,
                "source_cf_meta_data": None,
                "source_id": "1",
                "variable_id": "65759d7a-66f4-48fd-a0ad-37f5dcdf1d4a",
                "system_name": "co_1_email",
                "display_name": "Email",
                "data_type_id": 12,
                "source_variable_id": "co_1_email",
                "source_type": "object",
                "is_selected": True,
                "is_primary": False,
                "field_order": 0,
            },
            {
                "meta_data": None,
                "source_cf_meta_data": None,
                "source_id": "1",
                "variable_id": "c21b0118-67c0-4935-abb8-c196018d96c4",
                "system_name": "co_1_percentage",
                "display_name": "Percentage",
                "data_type_id": 6,
                "source_variable_id": "co_1_percentage",
                "source_type": "object",
                "is_selected": True,
                "is_primary": False,
                "field_order": 0,
            },
            {
                "meta_data": None,
                "source_cf_meta_data": None,
                "source_id": "1",
                "variable_id": "3db40a59-3518-445c-8f4e-a275e1ba21a6",
                "system_name": "co_1_date_2",
                "display_name": "Date-2",
                "data_type_id": 2,
                "source_variable_id": "co_1_date_2",
                "source_type": "object",
                "is_selected": False,
                "is_primary": False,
                "field_order": 0,
            },
            {
                "meta_data": None,
                "source_cf_meta_data": None,
                "source_id": "1",
                "variable_id": "bfeec10c-cb28-4955-8835-0fa942d5abf8",
                "system_name": "co_1_test",
                "display_name": "test",
                "data_type_id": 4,
                "source_variable_id": "co_1_test",
                "source_type": "object",
                "is_selected": False,
                "is_primary": False,
                "field_order": 0,
            },
            {
                "meta_data": None,
                "source_cf_meta_data": None,
                "source_id": "1",
                "variable_id": "3146d848-2723-4c66-82a2-42322cd1923d",
                "system_name": "co_1_email_2",
                "display_name": "email-2",
                "data_type_id": 12,
                "source_variable_id": "co_1_email_2",
                "source_type": "object",
                "is_selected": False,
                "is_primary": False,
                "field_order": 0,
            },
            {
                "meta_data": None,
                "source_cf_meta_data": None,
                "source_id": "user",
                "variable_id": "a14e2421-b445-4341-9aba-a003c0d03bdb",
                "system_name": "up_co_1_email_current_date_employee_id_5fdb",
                "display_name": "Employee Id",
                "data_type_id": 4,
                "source_variable_id": "employee_id_user",
                "source_type": "report",
                "is_selected": True,
                "is_primary": False,
                "field_order": 0,
            },
            {
                "meta_data": None,
                "source_cf_meta_data": None,
                "source_id": "user",
                "variable_id": "9c351332-3c7e-4c5f-99b0-2e9533baf83d",
                "system_name": "up_co_1_email_current_date_joining_date_5fdb",
                "display_name": "Joining Date",
                "data_type_id": 2,
                "source_variable_id": "joining_date_user",
                "source_type": "report",
                "is_selected": True,
                "is_primary": False,
                "field_order": 0,
            },
            {
                "meta_data": {
                    "ast": {},
                    "criteria_type": "simple",
                    "evaluation_context": None,
                    "used_system_names": ["co_1_name"],
                    "infix": [
                        {
                            "token": {
                                "name": "Len(Name)",
                                "key": "Len(Name)",
                                "data_type": "Integer",
                                "function_name": "Len",
                                "args": [
                                    {
                                        "token_type": "DATASHEET_VARIABLES",
                                        "token": {
                                            "system_name": "co_1_name",
                                            "key": "co_1_name",
                                            "name": "Name",
                                        },
                                    }
                                ],
                                "type": "VARIABLE",
                                "token_category": "DYNAMIC",
                            },
                            "token_type": "FUNCTIONS",
                        }
                    ],
                },
                "source_cf_meta_data": None,
                "source_id": "c6a85425-750e-4d82-8274-0b2e7925b31a",
                "variable_id": "df922455-be68-4acd-992a-88ffff7185a3",
                "system_name": "cf_len",
                "display_name": "len",
                "data_type_id": 1,
                "source_variable_id": None,
                "source_type": "datasheet",
                "is_selected": True,
                "is_primary": False,
                "field_order": 0,
            },
        ]
        transformations = [
            {
                "key": "494fac12-90dd-4fea-9ac0-523ceb295fdb",
                "type": "GET_USER_PROPERTIES",
                "is_valid": True,
                "email_column": {
                    "value": "co_1_email",
                    "variable_id": "co_1_email",
                },
                "output_columns": [
                    {
                        "meta_data": None,
                        "source_id": 1,
                        "is_primary": False,
                        "field_order": 0,
                        "is_selected": True,
                        "source_type": "object",
                        "system_name": "co_1_name",
                        "variable_id": "co_1_name",
                        "data_type_id": 4,
                        "display_name": "Name",
                        "source_variable_id": None,
                        "source_cf_meta_data": None,
                    },
                    {
                        "meta_data": None,
                        "source_id": 1,
                        "is_primary": True,
                        "field_order": 0,
                        "is_selected": True,
                        "source_type": "object",
                        "system_name": "co_1_number",
                        "variable_id": "co_1_number",
                        "data_type_id": 1,
                        "display_name": "Number",
                        "source_variable_id": None,
                        "source_cf_meta_data": None,
                    },
                    {
                        "meta_data": None,
                        "source_id": 1,
                        "is_primary": False,
                        "field_order": 0,
                        "is_selected": True,
                        "source_type": "object",
                        "system_name": "co_1_boolean",
                        "variable_id": "co_1_boolean",
                        "data_type_id": 3,
                        "display_name": "Boolean",
                        "source_variable_id": None,
                        "source_cf_meta_data": None,
                    },
                    {
                        "meta_data": None,
                        "source_id": 1,
                        "is_primary": False,
                        "field_order": 0,
                        "is_selected": True,
                        "source_type": "object",
                        "system_name": "co_1_date_1",
                        "variable_id": "co_1_date_1",
                        "data_type_id": 2,
                        "display_name": "Date-1",
                        "source_variable_id": None,
                        "source_cf_meta_data": None,
                    },
                    {
                        "meta_data": None,
                        "source_id": 1,
                        "is_primary": False,
                        "field_order": 0,
                        "is_selected": True,
                        "source_type": "object",
                        "system_name": "co_1_email",
                        "variable_id": "co_1_email",
                        "data_type_id": 12,
                        "display_name": "Email",
                        "source_variable_id": None,
                        "source_cf_meta_data": None,
                    },
                    {
                        "meta_data": None,
                        "source_id": 1,
                        "is_primary": False,
                        "field_order": 0,
                        "is_selected": True,
                        "source_type": "object",
                        "system_name": "co_1_percentage",
                        "variable_id": "co_1_percentage",
                        "data_type_id": 6,
                        "display_name": "Percentage",
                        "source_variable_id": None,
                        "source_cf_meta_data": None,
                    },
                    {
                        "meta_data": None,
                        "source_id": 1,
                        "is_primary": False,
                        "field_order": 0,
                        "is_selected": True,
                        "source_type": "object",
                        "system_name": "co_1_date_2",
                        "variable_id": "co_1_date_2",
                        "data_type_id": 2,
                        "display_name": "Date-2",
                        "source_variable_id": None,
                        "source_cf_meta_data": None,
                    },
                    {
                        "meta_data": None,
                        "source_id": 1,
                        "is_primary": False,
                        "field_order": 0,
                        "is_selected": True,
                        "source_type": "object",
                        "system_name": "co_1_test",
                        "variable_id": "co_1_test",
                        "data_type_id": 4,
                        "display_name": "test",
                        "source_variable_id": None,
                        "source_cf_meta_data": None,
                    },
                    {
                        "meta_data": None,
                        "source_id": 1,
                        "is_primary": False,
                        "field_order": 0,
                        "is_selected": True,
                        "source_type": "object",
                        "system_name": "co_1_email_2",
                        "variable_id": "co_1_email_2",
                        "data_type_id": 12,
                        "display_name": "email-2",
                        "source_variable_id": None,
                        "source_cf_meta_data": None,
                    },
                    {
                        "meta_data": None,
                        "source_id": "user",
                        "is_primary": False,
                        "is_selected": True,
                        "source_type": "report",
                        "system_name": "up_co_1_email_current_date_employee_id_5fdb",
                        "variable_id": "employee_id_user",
                        "data_type_id": 4,
                        "display_name": "Employee Id",
                        "source_variable_id": None,
                        "source_cf_meta_data": None,
                    },
                    {
                        "meta_data": None,
                        "source_id": "user",
                        "is_primary": False,
                        "is_selected": True,
                        "source_type": "report",
                        "system_name": "up_co_1_email_current_date_joining_date_5fdb",
                        "variable_id": "joining_date_user",
                        "data_type_id": 2,
                        "display_name": "Joining Date",
                        "source_variable_id": None,
                        "source_cf_meta_data": None,
                    },
                ],
                "user_properties": [
                    {
                        "variable_id": "ecf70a37-23c9-4cb8-9b75-cb3111523546",
                        "data_type_id": 4,
                        "display_name": "Employee Id",
                        "user_property_system_name": "employee_id",
                        "output_variable_system_name": "up_co_1_email_current_date_employee_id_5fdb",
                    },
                    {
                        "variable_id": "a5edc77f-4709-4162-b2a1-eadde5bd4cc9",
                        "data_type_id": 2,
                        "display_name": "Joining Date",
                        "user_property_system_name": "joining_date",
                        "output_variable_system_name": "up_co_1_email_current_date_joining_date_5fdb",
                    },
                ],
                "as_of_date_column": {"value": "CURRENT_DATE", "variable_id": None},
                "transformation_id": "494fac12-90dd-4fea-9ac0-523ceb295fdb",
            }
        ]
        validator = DatasheetValidator(
            client_id=3024,
            datasheet_id="c6a85425-750e-4d82-8274-0b2e7925b31a",
            variables=variables,
            transformations=transformations,
            source_variables=source_variables,
            source_id="dd6075f9-7374-4f3e-962f-a4f88887d2e1",
            source_type="datasheet",
            has_source_changed=False,
        )
        self.assertEqual(
            validator.databook_id, UUID("cc48a2a9-2d6b-4fba-bde0-28b8a4c5a838")
        )
        # During initialization, the transformation_output_variables should be source variables
        self.assertEqual(validator.transformation_output_variables, source_variables)
        self.assertEqual(
            validator.selected_variables,
            [
                {
                    "meta_data": None,
                    "source_cf_meta_data": None,
                    "source_id": "1",
                    "variable_id": "b95fcbc7-dcfb-4eb9-88fd-d7e267f00eb8",
                    "system_name": "co_1_name",
                    "display_name": "Name",
                    "data_type_id": 4,
                    "source_variable_id": "co_1_name",
                    "source_type": "object",
                    "is_selected": True,
                    "is_primary": False,
                    "field_order": 0,
                },
                {
                    "meta_data": None,
                    "source_cf_meta_data": None,
                    "source_id": "1",
                    "variable_id": "fe540c74-bade-4694-808a-0b4d967c8e8a",
                    "system_name": "co_1_number",
                    "display_name": "Number",
                    "data_type_id": 1,
                    "source_variable_id": "co_1_number",
                    "source_type": "object",
                    "is_selected": True,
                    "is_primary": True,
                    "field_order": 0,
                },
                {
                    "meta_data": None,
                    "source_cf_meta_data": None,
                    "source_id": "1",
                    "variable_id": "7c3cfe77-d2ee-4be6-87b0-03eccb15f4fe",
                    "system_name": "co_1_boolean",
                    "display_name": "Boolean",
                    "data_type_id": 3,
                    "source_variable_id": "co_1_boolean",
                    "source_type": "object",
                    "is_selected": True,
                    "is_primary": False,
                    "field_order": 0,
                },
                {
                    "meta_data": None,
                    "source_cf_meta_data": None,
                    "source_id": "1",
                    "variable_id": "f3c3d89a-b4c5-45a8-832e-8cecb775506b",
                    "system_name": "co_1_date_1",
                    "display_name": "Date-1",
                    "data_type_id": 2,
                    "source_variable_id": "co_1_date_1",
                    "source_type": "object",
                    "is_selected": True,
                    "is_primary": False,
                    "field_order": 0,
                },
                {
                    "meta_data": None,
                    "source_cf_meta_data": None,
                    "source_id": "1",
                    "variable_id": "65759d7a-66f4-48fd-a0ad-37f5dcdf1d4a",
                    "system_name": "co_1_email",
                    "display_name": "Email",
                    "data_type_id": 12,
                    "source_variable_id": "co_1_email",
                    "source_type": "object",
                    "is_selected": True,
                    "is_primary": False,
                    "field_order": 0,
                },
                {
                    "meta_data": None,
                    "source_cf_meta_data": None,
                    "source_id": "1",
                    "variable_id": "c21b0118-67c0-4935-abb8-c196018d96c4",
                    "system_name": "co_1_percentage",
                    "display_name": "Percentage",
                    "data_type_id": 6,
                    "source_variable_id": "co_1_percentage",
                    "source_type": "object",
                    "is_selected": True,
                    "is_primary": False,
                    "field_order": 0,
                },
                {
                    "meta_data": None,
                    "source_cf_meta_data": None,
                    "source_id": "user",
                    "variable_id": "a14e2421-b445-4341-9aba-a003c0d03bdb",
                    "system_name": "up_co_1_email_current_date_employee_id_5fdb",
                    "display_name": "Employee Id",
                    "data_type_id": 4,
                    "source_variable_id": "employee_id_user",
                    "source_type": "report",
                    "is_selected": True,
                    "is_primary": False,
                    "field_order": 0,
                },
                {
                    "meta_data": None,
                    "source_cf_meta_data": None,
                    "source_id": "user",
                    "variable_id": "9c351332-3c7e-4c5f-99b0-2e9533baf83d",
                    "system_name": "up_co_1_email_current_date_joining_date_5fdb",
                    "display_name": "Joining Date",
                    "data_type_id": 2,
                    "source_variable_id": "joining_date_user",
                    "source_type": "report",
                    "is_selected": True,
                    "is_primary": False,
                    "field_order": 0,
                },
                {
                    "meta_data": {
                        "ast": {},
                        "criteria_type": "simple",
                        "evaluation_context": None,
                        "used_system_names": ["co_1_name"],
                        "infix": [
                            {
                                "token": {
                                    "name": "Len(Name)",
                                    "key": "Len(Name)",
                                    "data_type": "Integer",
                                    "function_name": "Len",
                                    "args": [
                                        {
                                            "token_type": "DATASHEET_VARIABLES",
                                            "token": {
                                                "system_name": "co_1_name",
                                                "key": "co_1_name",
                                                "name": "Name",
                                            },
                                        }
                                    ],
                                    "type": "VARIABLE",
                                    "token_category": "DYNAMIC",
                                },
                                "token_type": "FUNCTIONS",
                            }
                        ],
                    },
                    "source_cf_meta_data": None,
                    "source_id": "c6a85425-750e-4d82-8274-0b2e7925b31a",
                    "variable_id": "df922455-be68-4acd-992a-88ffff7185a3",
                    "system_name": "cf_len",
                    "display_name": "len",
                    "data_type_id": 1,
                    "source_variable_id": None,
                    "source_type": "datasheet",
                    "is_selected": True,
                    "is_primary": False,
                    "field_order": 0,
                },
            ],
        )
        self.assertEqual(
            validator.unselected_variables,
            [
                {
                    "meta_data": None,
                    "source_cf_meta_data": None,
                    "source_id": "1",
                    "variable_id": "3db40a59-3518-445c-8f4e-a275e1ba21a6",
                    "system_name": "co_1_date_2",
                    "display_name": "Date-2",
                    "data_type_id": 2,
                    "source_variable_id": "co_1_date_2",
                    "source_type": "object",
                    "is_selected": False,
                    "is_primary": False,
                    "field_order": 0,
                },
                {
                    "meta_data": None,
                    "source_cf_meta_data": None,
                    "source_id": "1",
                    "variable_id": "bfeec10c-cb28-4955-8835-0fa942d5abf8",
                    "system_name": "co_1_test",
                    "display_name": "test",
                    "data_type_id": 4,
                    "source_variable_id": "co_1_test",
                    "source_type": "object",
                    "is_selected": False,
                    "is_primary": False,
                    "field_order": 0,
                },
                {
                    "meta_data": None,
                    "source_cf_meta_data": None,
                    "source_id": "1",
                    "variable_id": "3146d848-2723-4c66-82a2-42322cd1923d",
                    "system_name": "co_1_email_2",
                    "display_name": "email-2",
                    "data_type_id": 12,
                    "source_variable_id": "co_1_email_2",
                    "source_type": "object",
                    "is_selected": False,
                    "is_primary": False,
                    "field_order": 0,
                },
            ],
        )
        self.assertEqual(
            validator.cf_variables,
            [
                {
                    "meta_data": {
                        "ast": {},
                        "criteria_type": "simple",
                        "evaluation_context": None,
                        "used_system_names": ["co_1_name"],
                        "infix": [
                            {
                                "token": {
                                    "name": "Len(Name)",
                                    "key": "Len(Name)",
                                    "data_type": "Integer",
                                    "function_name": "Len",
                                    "args": [
                                        {
                                            "token_type": "DATASHEET_VARIABLES",
                                            "token": {
                                                "system_name": "co_1_name",
                                                "key": "co_1_name",
                                                "name": "Name",
                                            },
                                        }
                                    ],
                                    "type": "VARIABLE",
                                    "token_category": "DYNAMIC",
                                },
                                "token_type": "FUNCTIONS",
                            }
                        ],
                    },
                    "source_cf_meta_data": None,
                    "source_id": "c6a85425-750e-4d82-8274-0b2e7925b31a",
                    "variable_id": "df922455-be68-4acd-992a-88ffff7185a3",
                    "system_name": "cf_len",
                    "display_name": "len",
                    "data_type_id": 1,
                    "source_variable_id": None,
                    "source_type": "datasheet",
                    "is_selected": True,
                    "is_primary": False,
                    "field_order": 0,
                }
            ],
        )
        self.assertEqual(validator.is_variable_added, False)
        self.assertEqual(validator.is_transformation_invalid, False)
        self.assertEqual(validator.flatten_system_name, "")
        self.assertEqual(validator.is_variable_reseted, False)

    def test_initilazing_validator_without_source_id(self):
        source_variables = [
            {
                "meta_data": None,
                "source_cf_meta_data": None,
                "source_id": 1,
                "variable_id": "co_1_name",
                "system_name": "co_1_name",
                "display_name": "Name",
                "data_type_id": 4,
                "source_variable_id": None,
                "source_type": "object",
                "is_selected": True,
                "is_primary": False,
                "field_order": 0,
            },
            {
                "meta_data": None,
                "source_cf_meta_data": None,
                "source_id": 1,
                "variable_id": "co_1_number",
                "system_name": "co_1_number",
                "display_name": "Number",
                "data_type_id": 1,
                "source_variable_id": None,
                "source_type": "object",
                "is_selected": True,
                "is_primary": True,
                "field_order": 0,
            },
            {
                "meta_data": None,
                "source_cf_meta_data": None,
                "source_id": 1,
                "variable_id": "co_1_boolean",
                "system_name": "co_1_boolean",
                "display_name": "Boolean",
                "data_type_id": 3,
                "source_variable_id": None,
                "source_type": "object",
                "is_selected": True,
                "is_primary": False,
                "field_order": 0,
            },
            {
                "meta_data": None,
                "source_cf_meta_data": None,
                "source_id": 1,
                "variable_id": "co_1_date_1",
                "system_name": "co_1_date_1",
                "display_name": "Date-1",
                "data_type_id": 2,
                "source_variable_id": None,
                "source_type": "object",
                "is_selected": True,
                "is_primary": False,
                "field_order": 0,
            },
            {
                "meta_data": None,
                "source_cf_meta_data": None,
                "source_id": 1,
                "variable_id": "co_1_email",
                "system_name": "co_1_email",
                "display_name": "Email",
                "data_type_id": 12,
                "source_variable_id": None,
                "source_type": "object",
                "is_selected": True,
                "is_primary": False,
                "field_order": 0,
            },
            {
                "meta_data": None,
                "source_cf_meta_data": None,
                "source_id": 1,
                "variable_id": "co_1_percentage",
                "system_name": "co_1_percentage",
                "display_name": "Percentage",
                "data_type_id": 6,
                "source_variable_id": None,
                "source_type": "object",
                "is_selected": True,
                "is_primary": False,
                "field_order": 0,
            },
            {
                "meta_data": None,
                "source_cf_meta_data": None,
                "source_id": 1,
                "variable_id": "co_1_date_2",
                "system_name": "co_1_date_2",
                "display_name": "Date-2",
                "data_type_id": 2,
                "source_variable_id": None,
                "source_type": "object",
                "is_selected": True,
                "is_primary": False,
                "field_order": 0,
            },
            {
                "meta_data": None,
                "source_cf_meta_data": None,
                "source_id": 1,
                "variable_id": "co_1_test",
                "system_name": "co_1_test",
                "display_name": "test",
                "data_type_id": 4,
                "source_variable_id": None,
                "source_type": "object",
                "is_selected": True,
                "is_primary": False,
                "field_order": 0,
            },
            {
                "meta_data": None,
                "source_cf_meta_data": None,
                "source_id": 1,
                "variable_id": "co_1_email_2",
                "system_name": "co_1_email_2",
                "display_name": "email-2",
                "data_type_id": 12,
                "source_variable_id": None,
                "source_type": "object",
                "is_selected": True,
                "is_primary": False,
                "field_order": 0,
            },
        ]
        variables = [
            {
                "meta_data": None,
                "source_cf_meta_data": None,
                "source_id": "1",
                "variable_id": "b95fcbc7-dcfb-4eb9-88fd-d7e267f00eb8",
                "system_name": "co_1_name",
                "display_name": "Name",
                "data_type_id": 4,
                "source_variable_id": "co_1_name",
                "source_type": "object",
                "is_selected": True,
                "is_primary": False,
                "field_order": 0,
            },
            {
                "meta_data": None,
                "source_cf_meta_data": None,
                "source_id": "1",
                "variable_id": "fe540c74-bade-4694-808a-0b4d967c8e8a",
                "system_name": "co_1_number",
                "display_name": "Number",
                "data_type_id": 1,
                "source_variable_id": "co_1_number",
                "source_type": "object",
                "is_selected": True,
                "is_primary": True,
                "field_order": 0,
            },
            {
                "meta_data": None,
                "source_cf_meta_data": None,
                "source_id": "1",
                "variable_id": "7c3cfe77-d2ee-4be6-87b0-03eccb15f4fe",
                "system_name": "co_1_boolean",
                "display_name": "Boolean",
                "data_type_id": 3,
                "source_variable_id": "co_1_boolean",
                "source_type": "object",
                "is_selected": True,
                "is_primary": False,
                "field_order": 0,
            },
            {
                "meta_data": None,
                "source_cf_meta_data": None,
                "source_id": "1",
                "variable_id": "f3c3d89a-b4c5-45a8-832e-8cecb775506b",
                "system_name": "co_1_date_1",
                "display_name": "Date-1",
                "data_type_id": 2,
                "source_variable_id": "co_1_date_1",
                "source_type": "object",
                "is_selected": True,
                "is_primary": False,
                "field_order": 0,
            },
            {
                "meta_data": None,
                "source_cf_meta_data": None,
                "source_id": "1",
                "variable_id": "65759d7a-66f4-48fd-a0ad-37f5dcdf1d4a",
                "system_name": "co_1_email",
                "display_name": "Email",
                "data_type_id": 12,
                "source_variable_id": "co_1_email",
                "source_type": "object",
                "is_selected": True,
                "is_primary": False,
                "field_order": 0,
            },
            {
                "meta_data": None,
                "source_cf_meta_data": None,
                "source_id": "1",
                "variable_id": "c21b0118-67c0-4935-abb8-c196018d96c4",
                "system_name": "co_1_percentage",
                "display_name": "Percentage",
                "data_type_id": 6,
                "source_variable_id": "co_1_percentage",
                "source_type": "object",
                "is_selected": True,
                "is_primary": False,
                "field_order": 0,
            },
            {
                "meta_data": None,
                "source_cf_meta_data": None,
                "source_id": "1",
                "variable_id": "3db40a59-3518-445c-8f4e-a275e1ba21a6",
                "system_name": "co_1_date_2",
                "display_name": "Date-2",
                "data_type_id": 2,
                "source_variable_id": "co_1_date_2",
                "source_type": "object",
                "is_selected": False,
                "is_primary": False,
                "field_order": 0,
            },
            {
                "meta_data": None,
                "source_cf_meta_data": None,
                "source_id": "1",
                "variable_id": "bfeec10c-cb28-4955-8835-0fa942d5abf8",
                "system_name": "co_1_test",
                "display_name": "test",
                "data_type_id": 4,
                "source_variable_id": "co_1_test",
                "source_type": "object",
                "is_selected": False,
                "is_primary": False,
                "field_order": 0,
            },
            {
                "meta_data": None,
                "source_cf_meta_data": None,
                "source_id": "1",
                "variable_id": "3146d848-2723-4c66-82a2-42322cd1923d",
                "system_name": "co_1_email_2",
                "display_name": "email-2",
                "data_type_id": 12,
                "source_variable_id": "co_1_email_2",
                "source_type": "object",
                "is_selected": False,
                "is_primary": False,
                "field_order": 0,
            },
            {
                "meta_data": None,
                "source_cf_meta_data": None,
                "source_id": "user",
                "variable_id": "a14e2421-b445-4341-9aba-a003c0d03bdb",
                "system_name": "up_co_1_email_current_date_employee_id_5fdb",
                "display_name": "Employee Id",
                "data_type_id": 4,
                "source_variable_id": "employee_id_user",
                "source_type": "report",
                "is_selected": True,
                "is_primary": False,
                "field_order": 0,
            },
            {
                "meta_data": None,
                "source_cf_meta_data": None,
                "source_id": "user",
                "variable_id": "9c351332-3c7e-4c5f-99b0-2e9533baf83d",
                "system_name": "up_co_1_email_current_date_joining_date_5fdb",
                "display_name": "Joining Date",
                "data_type_id": 2,
                "source_variable_id": "joining_date_user",
                "source_type": "report",
                "is_selected": True,
                "is_primary": False,
                "field_order": 0,
            },
            {
                "meta_data": {
                    "ast": {},
                    "criteria_type": "simple",
                    "evaluation_context": None,
                    "used_system_names": ["co_1_name"],
                    "infix": [
                        {
                            "token": {
                                "name": "Len(Name)",
                                "key": "Len(Name)",
                                "data_type": "Integer",
                                "function_name": "Len",
                                "args": [
                                    {
                                        "token_type": "DATASHEET_VARIABLES",
                                        "token": {
                                            "system_name": "co_1_name",
                                            "key": "co_1_name",
                                            "name": "Name",
                                        },
                                    }
                                ],
                                "type": "VARIABLE",
                                "token_category": "DYNAMIC",
                            },
                            "token_type": "FUNCTIONS",
                        }
                    ],
                },
                "source_cf_meta_data": None,
                "source_id": "c6a85425-750e-4d82-8274-0b2e7925b31a",
                "variable_id": "df922455-be68-4acd-992a-88ffff7185a3",
                "system_name": "cf_len",
                "display_name": "len",
                "data_type_id": 1,
                "source_variable_id": None,
                "source_type": "datasheet",
                "is_selected": True,
                "is_primary": False,
                "field_order": 0,
            },
        ]
        transformations = [
            {
                "key": "494fac12-90dd-4fea-9ac0-523ceb295fdb",
                "type": "GET_USER_PROPERTIES",
                "is_valid": True,
                "email_column": {
                    "value": "co_1_email",
                    "variable_id": "co_1_email",
                },
                "output_columns": [
                    {
                        "meta_data": None,
                        "source_id": 1,
                        "is_primary": False,
                        "field_order": 0,
                        "is_selected": True,
                        "source_type": "object",
                        "system_name": "co_1_name",
                        "variable_id": "co_1_name",
                        "data_type_id": 4,
                        "display_name": "Name",
                        "source_variable_id": None,
                        "source_cf_meta_data": None,
                    },
                    {
                        "meta_data": None,
                        "source_id": 1,
                        "is_primary": True,
                        "field_order": 0,
                        "is_selected": True,
                        "source_type": "object",
                        "system_name": "co_1_number",
                        "variable_id": "co_1_number",
                        "data_type_id": 1,
                        "display_name": "Number",
                        "source_variable_id": None,
                        "source_cf_meta_data": None,
                    },
                    {
                        "meta_data": None,
                        "source_id": 1,
                        "is_primary": False,
                        "field_order": 0,
                        "is_selected": True,
                        "source_type": "object",
                        "system_name": "co_1_boolean",
                        "variable_id": "co_1_boolean",
                        "data_type_id": 3,
                        "display_name": "Boolean",
                        "source_variable_id": None,
                        "source_cf_meta_data": None,
                    },
                    {
                        "meta_data": None,
                        "source_id": 1,
                        "is_primary": False,
                        "field_order": 0,
                        "is_selected": True,
                        "source_type": "object",
                        "system_name": "co_1_date_1",
                        "variable_id": "co_1_date_1",
                        "data_type_id": 2,
                        "display_name": "Date-1",
                        "source_variable_id": None,
                        "source_cf_meta_data": None,
                    },
                    {
                        "meta_data": None,
                        "source_id": 1,
                        "is_primary": False,
                        "field_order": 0,
                        "is_selected": True,
                        "source_type": "object",
                        "system_name": "co_1_email",
                        "variable_id": "co_1_email",
                        "data_type_id": 12,
                        "display_name": "Email",
                        "source_variable_id": None,
                        "source_cf_meta_data": None,
                    },
                    {
                        "meta_data": None,
                        "source_id": 1,
                        "is_primary": False,
                        "field_order": 0,
                        "is_selected": True,
                        "source_type": "object",
                        "system_name": "co_1_percentage",
                        "variable_id": "co_1_percentage",
                        "data_type_id": 6,
                        "display_name": "Percentage",
                        "source_variable_id": None,
                        "source_cf_meta_data": None,
                    },
                    {
                        "meta_data": None,
                        "source_id": 1,
                        "is_primary": False,
                        "field_order": 0,
                        "is_selected": True,
                        "source_type": "object",
                        "system_name": "co_1_date_2",
                        "variable_id": "co_1_date_2",
                        "data_type_id": 2,
                        "display_name": "Date-2",
                        "source_variable_id": None,
                        "source_cf_meta_data": None,
                    },
                    {
                        "meta_data": None,
                        "source_id": 1,
                        "is_primary": False,
                        "field_order": 0,
                        "is_selected": True,
                        "source_type": "object",
                        "system_name": "co_1_test",
                        "variable_id": "co_1_test",
                        "data_type_id": 4,
                        "display_name": "test",
                        "source_variable_id": None,
                        "source_cf_meta_data": None,
                    },
                    {
                        "meta_data": None,
                        "source_id": 1,
                        "is_primary": False,
                        "field_order": 0,
                        "is_selected": True,
                        "source_type": "object",
                        "system_name": "co_1_email_2",
                        "variable_id": "co_1_email_2",
                        "data_type_id": 12,
                        "display_name": "email-2",
                        "source_variable_id": None,
                        "source_cf_meta_data": None,
                    },
                    {
                        "meta_data": None,
                        "source_id": "user",
                        "is_primary": False,
                        "is_selected": True,
                        "source_type": "report",
                        "system_name": "up_co_1_email_current_date_employee_id_5fdb",
                        "variable_id": "employee_id_user",
                        "data_type_id": 4,
                        "display_name": "Employee Id",
                        "source_variable_id": None,
                        "source_cf_meta_data": None,
                    },
                    {
                        "meta_data": None,
                        "source_id": "user",
                        "is_primary": False,
                        "is_selected": True,
                        "source_type": "report",
                        "system_name": "up_co_1_email_current_date_joining_date_5fdb",
                        "variable_id": "joining_date_user",
                        "data_type_id": 2,
                        "display_name": "Joining Date",
                        "source_variable_id": None,
                        "source_cf_meta_data": None,
                    },
                ],
                "user_properties": [
                    {
                        "variable_id": "ecf70a37-23c9-4cb8-9b75-cb3111523546",
                        "data_type_id": 4,
                        "display_name": "Employee Id",
                        "user_property_system_name": "employee_id",
                        "output_variable_system_name": "up_co_1_email_current_date_employee_id_5fdb",
                    },
                    {
                        "variable_id": "a5edc77f-4709-4162-b2a1-eadde5bd4cc9",
                        "data_type_id": 2,
                        "display_name": "Joining Date",
                        "user_property_system_name": "joining_date",
                        "output_variable_system_name": "up_co_1_email_current_date_joining_date_5fdb",
                    },
                ],
                "as_of_date_column": {"value": "CURRENT_DATE", "variable_id": None},
                "transformation_id": "494fac12-90dd-4fea-9ac0-523ceb295fdb",
            }
        ]
        request_model = DatasheetValidateRequest(
            client_id=3024,
            datasheet_id="c6a85425-750e-4d82-8274-0b2e7925b31a",
            variables=variables,
            transformations=transformations,
            source_variables=source_variables,
            source_id=None,
            source_type="datasheet",
            has_source_changed=False,
            databook_id="869b4b9b-5e9f-49e1-b24c-e4c5cee825b0",
        )
        with self.assertRaises(DatasheetException):
            validate_datasheet(request_model)

    def test_validate_variable_id_uniqueness(self):
        variables = [
            {
                "meta_data": None,
                "source_cf_meta_data": None,
                "source_id": "1",
                "variable_id": "b95fcbc7-dcfb-4eb9-88fd-d7e267f00eb8",
                "system_name": "co_1_name",
                "display_name": "Name",
                "data_type_id": 4,
                "source_variable_id": "co_1_name",
                "source_type": "object",
                "is_selected": True,
                "is_primary": False,
                "field_order": 0,
            },
            {
                "meta_data": None,
                "source_cf_meta_data": None,
                "source_id": "1",
                "variable_id": "b95fcbc7-dcfb-4eb9-88fd-d7e267f00eb8",
                "system_name": "co_1_number",
                "display_name": "Number",
                "data_type_id": 1,
                "source_variable_id": "co_1_number",
                "source_type": "object",
                "is_selected": True,
                "is_primary": True,
                "field_order": 0,
            },
            {
                "meta_data": None,
                "source_cf_meta_data": None,
                "source_id": "1",
                "variable_id": "7c3cfe77-d2ee-4be6-87b0-03eccb15f4fe",
                "system_name": "co_1_boolean",
                "display_name": "Boolean",
                "data_type_id": 3,
                "source_variable_id": "co_1_boolean",
                "source_type": "object",
                "is_selected": True,
                "is_primary": False,
                "field_order": 0,
            },
            {
                "meta_data": None,
                "source_cf_meta_data": None,
                "source_id": "1",
                "variable_id": "f3c3d89a-b4c5-45a8-832e-8cecb775506b",
                "system_name": "co_1_date_1",
                "display_name": "Date-1",
                "data_type_id": 2,
                "source_variable_id": "co_1_date_1",
                "source_type": "object",
                "is_selected": True,
                "is_primary": False,
                "field_order": 0,
            },
            {
                "meta_data": None,
                "source_cf_meta_data": None,
                "source_id": "1",
                "variable_id": "65759d7a-66f4-48fd-a0ad-37f5dcdf1d4a",
                "system_name": "co_1_email",
                "display_name": "Email",
                "data_type_id": 12,
                "source_variable_id": "co_1_email",
                "source_type": "object",
                "is_selected": True,
                "is_primary": False,
                "field_order": 0,
            },
            {
                "meta_data": None,
                "source_cf_meta_data": None,
                "source_id": "1",
                "variable_id": "c21b0118-67c0-4935-abb8-c196018d96c4",
                "system_name": "co_1_percentage",
                "display_name": "Percentage",
                "data_type_id": 6,
                "source_variable_id": "co_1_percentage",
                "source_type": "object",
                "is_selected": True,
                "is_primary": False,
                "field_order": 0,
            },
            {
                "meta_data": None,
                "source_cf_meta_data": None,
                "source_id": "1",
                "variable_id": "3db40a59-3518-445c-8f4e-a275e1ba21a6",
                "system_name": "co_1_date_2",
                "display_name": "Date-2",
                "data_type_id": 2,
                "source_variable_id": "co_1_date_2",
                "source_type": "object",
                "is_selected": False,
                "is_primary": False,
                "field_order": 0,
            },
            {
                "meta_data": None,
                "source_cf_meta_data": None,
                "source_id": "1",
                "variable_id": "bfeec10c-cb28-4955-8835-0fa942d5abf8",
                "system_name": "co_1_test",
                "display_name": "test",
                "data_type_id": 4,
                "source_variable_id": "co_1_test",
                "source_type": "object",
                "is_selected": False,
                "is_primary": False,
                "field_order": 0,
            },
            {
                "meta_data": None,
                "source_cf_meta_data": None,
                "source_id": "1",
                "variable_id": "3146d848-2723-4c66-82a2-42322cd1923d",
                "system_name": "co_1_email_2",
                "display_name": "email-2",
                "data_type_id": 12,
                "source_variable_id": "co_1_email_2",
                "source_type": "object",
                "is_selected": False,
                "is_primary": False,
                "field_order": 0,
            },
            {
                "meta_data": None,
                "source_cf_meta_data": None,
                "source_id": "user",
                "variable_id": "a14e2421-b445-4341-9aba-a003c0d03bdb",
                "system_name": "up_co_1_email_current_date_employee_id_5fdb",
                "display_name": "Employee Id",
                "data_type_id": 4,
                "source_variable_id": "employee_id_user",
                "source_type": "report",
                "is_selected": True,
                "is_primary": False,
                "field_order": 0,
            },
            {
                "meta_data": None,
                "source_cf_meta_data": None,
                "source_id": "user",
                "variable_id": "9c351332-3c7e-4c5f-99b0-2e9533baf83d",
                "system_name": "up_co_1_email_current_date_joining_date_5fdb",
                "display_name": "Joining Date",
                "data_type_id": 2,
                "source_variable_id": "joining_date_user",
                "source_type": "report",
                "is_selected": True,
                "is_primary": False,
                "field_order": 0,
            },
            {
                "meta_data": {
                    "ast": {},
                    "criteria_type": "simple",
                    "evaluation_context": None,
                    "used_system_names": ["co_1_name"],
                    "infix": [
                        {
                            "token": {
                                "name": "Len(Name)",
                                "key": "Len(Name)",
                                "data_type": "Integer",
                                "function_name": "Len",
                                "args": [
                                    {
                                        "token_type": "DATASHEET_VARIABLES",
                                        "token": {
                                            "system_name": "co_1_name",
                                            "key": "co_1_name",
                                            "name": "Name",
                                        },
                                    }
                                ],
                                "type": "VARIABLE",
                                "token_category": "DYNAMIC",
                            },
                            "token_type": "FUNCTIONS",
                        }
                    ],
                },
                "source_cf_meta_data": None,
                "source_id": "c6a85425-750e-4d82-8274-0b2e7925b31a",
                "variable_id": "df922455-be68-4acd-992a-88ffff7185a3",
                "system_name": "cf_len",
                "display_name": "len",
                "data_type_id": 1,
                "source_variable_id": None,
                "source_type": "datasheet",
                "is_selected": True,
                "is_primary": False,
                "field_order": 0,
            },
        ]
        validator = DatasheetValidator(
            client_id=3024,
            datasheet_id="c6a85425-750e-4d82-8274-0b2e7925b31a",
            variables=variables,
            transformations=[],
            source_variables=variables,
            source_id="dd6075f9-7374-4f3e-962f-a4f88887d2e1",
            source_type="datasheet",
            has_source_changed=False,
        )
        with self.assertRaises(DatasheetException):
            validator._validate_variable_id_uniqueness()

    def test_validate_hierarchy_variables(self):
        validator = DatasheetValidator(
            client_id=3008,
            datasheet_id="daf224bc-0c76-476e-b8d7-056b762d55b7",
            variables=[
                {
                    "meta_data": {
                        "token_type": "FUNCTIONS",
                        "token": {
                            "name": "Hierarchy(hierarchy ref 1, name, manager, start date, end date, name, as of date)",
                            "key": "Hierarchy(hierarchy ref 1, name, manager, start date, end date, name, as of date)",
                            "function_name": "Hierarchy",
                            "data_type": "Hierarchy",
                            "type": "VARIABLE",
                            "token_category": "DYNAMIC",
                            "args": [
                                {
                                    "token_type": "DATASHEET_VARIABLES",
                                    "token": {
                                        "key": "co_183_name",
                                        "name": "name",
                                        "variable_id": "6060c02b-17b0-42c9-83fd-522c50b6110f",
                                    },
                                },
                                {
                                    "token_type": "DATASHEET_VARIABLES",
                                    "token": {
                                        "key": "co_183_asofdate",
                                        "name": "as of date",
                                        "variable_id": "3748229e-e872-4d00-998d-de503fa91b8f",
                                    },
                                },
                                {
                                    "name": "hierarchy_Copy",
                                    "value": "71b673b4-4e7a-4962-a9b4-707270ebaaea",
                                },
                                {
                                    "name": "hierarchy ref 1",
                                    "value": "04740d3a-c10d-44c5-9367-04de771a877c",
                                },
                                {
                                    "name": "name",
                                    "value": "co_182_name",
                                    "variable_id": "5112fa53-ee59-434a-ada3-b1ddc096842e",
                                },
                                {
                                    "name": "manager",
                                    "value": "co_182_manager",
                                    "variable_id": "32dd2894-058e-4f59-9a61-e77d72ecaf1e",
                                },
                                {
                                    "name": "start date",
                                    "value": "co_182_startdate",
                                    "variable_id": "9c3a5fe6-fe9a-4b7c-bb6f-9892e7bf3b95",
                                },
                                {
                                    "name": "end date",
                                    "value": "co_182_enddate",
                                    "variable_id": "2f40b6fa-f0c8-4663-93b7-508a0de10092",
                                },
                                {"name": "hierarchy_for_data_type_id", "value": 4},
                                {
                                    "name": "reference_sheet_data_origin",
                                    "value": "custom_object",
                                },
                            ],
                            "is_system_generated": True,
                        },
                    },
                    "source_variable_id": None,
                    "source_id": "daf224bc-0c76-476e-b8d7-056b762d55b7",
                    "variable_id": UUID("aa1b1dbb-bb72-48db-accc-494dbd6e0773"),
                    "is_selected": True,
                    "source_type": "datasheet",
                    "is_primary": False,
                    "field_order": 1,
                    "source_cf_meta_data": {
                        "datasheet_id": "daf224bc-0c76-476e-b8d7-056b762d55b7",
                        "hierarchy_for_data_type_id": 4,
                    },
                    "data_type_id": 15,
                    "system_name": "cf_hierarchycalc",
                    "display_name": "Hierarchy calc",
                },
                {
                    "meta_data": None,
                    "source_cf_meta_data": None,
                    "source_id": "183",
                    "variable_id": UUID("cb26e313-5c3f-4c78-894b-0ac1a7fe7206"),
                    "system_name": "co_183_id",
                    "display_name": "id",
                    "data_type_id": 1,
                    "source_variable_id": None,
                    "source_type": "object",
                    "is_selected": True,
                    "is_primary": True,
                    "field_order": 0,
                },
                {
                    "meta_data": None,
                    "source_cf_meta_data": None,
                    "source_id": "183",
                    "variable_id": UUID("e753f1bf-c9ae-4604-a1e7-cb86b82cf25c"),
                    "system_name": "co_183_name",
                    "display_name": "name",
                    "data_type_id": 4,
                    "source_variable_id": None,
                    "source_type": "object",
                    "is_selected": True,
                    "is_primary": False,
                    "field_order": 0,
                },
                {
                    "meta_data": None,
                    "source_cf_meta_data": None,
                    "source_id": "183",
                    "variable_id": UUID("2719b753-cdb7-4e0c-9cff-ffe4322f7114"),
                    "system_name": "co_183_email",
                    "display_name": "email",
                    "data_type_id": 12,
                    "source_variable_id": None,
                    "source_type": "object",
                    "is_selected": True,
                    "is_primary": False,
                    "field_order": 0,
                },
                {
                    "meta_data": None,
                    "source_cf_meta_data": None,
                    "source_id": "183",
                    "variable_id": UUID("0145f9d7-6d92-460e-9427-32ff83948dac"),
                    "system_name": "co_183_asofdate",
                    "display_name": "as of date",
                    "data_type_id": 2,
                    "source_variable_id": None,
                    "source_type": "object",
                    "is_selected": True,
                    "is_primary": False,
                    "field_order": 0,
                },
            ],
            transformations=[],
            source_variables=[
                {
                    "meta_data": {
                        "name": "Hierarchy(hierarchy ref 1, name, manager, start date, end date, name, as of date)",
                        "type": "VARIABLE",
                        "data_type": "Hierarchy",
                        "across_rows": True,
                        "function_name": "Hierarchy",
                        "token_category": "DYNAMIC",
                        "expression_string": "Hierarchy(hierarchy ref 1, name, manager, start date, end date, name, as of date)",
                        "evaluation_context": "ltd",
                        "is_system_generated": False,
                        "args": {
                            "hierarchy": {
                                "hierarchy_for_column": "co_183_name",
                                "as_of_date_column": "co_183_asofdate",
                                "reference_sheet": "04740d3a-c10d-44c5-9367-04de771a877c",
                                "child_column": "co_182_name",
                                "parent_column": "co_182_manager",
                                "start_time_column": "co_182_startdate",
                                "end_time_column": "co_182_enddate",
                                "reference_book": "71b673b4-4e7a-4962-a9b4-707270ebaaea",
                                "reference_sheet_data_origin": "custom_object",
                                "hierarchy_for_data_type_id": 4,
                            }
                        },
                        "hierarchy": {
                            "hierarchy_for_column": "co_183_name",
                            "as_of_date_column": "co_183_asofdate",
                            "reference_sheet": "04740d3a-c10d-44c5-9367-04de771a877c",
                            "child_column": "co_182_name",
                            "parent_column": "co_182_manager",
                            "start_time_column": "co_182_startdate",
                            "end_time_column": "co_182_enddate",
                            "reference_book": "71b673b4-4e7a-4962-a9b4-707270ebaaea",
                            "reference_sheet_data_origin": "custom_object",
                            "hierarchy_for_data_type_id": 4,
                        },
                    },
                    "source_cf_meta_data": {
                        "datasheet_id": UUID("daf224bc-0c76-476e-b8d7-056b762d55b7"),
                        "hierarchy_for_data_type_id": 4,
                    },
                    "source_id": "183",
                    "variable_id": UUID("b62b95ea-c62b-4490-9bb9-59293c54847b"),
                    "system_name": "cf_hierarchycalc",
                    "display_name": "Hierarchy calc",
                    "data_type_id": 15,
                    "source_variable_id": None,
                    "source_type": "object",
                    "is_selected": True,
                    "is_primary": False,
                    "field_order": 1,
                },
                {
                    "meta_data": None,
                    "source_cf_meta_data": None,
                    "source_id": "183",
                    "variable_id": UUID("cb26e313-5c3f-4c78-894b-0ac1a7fe7206"),
                    "system_name": "co_183_id",
                    "display_name": "id",
                    "data_type_id": 1,
                    "source_variable_id": None,
                    "source_type": "object",
                    "is_selected": True,
                    "is_primary": True,
                    "field_order": 0,
                },
                {
                    "meta_data": None,
                    "source_cf_meta_data": None,
                    "source_id": "183",
                    "variable_id": UUID("e753f1bf-c9ae-4604-a1e7-cb86b82cf25c"),
                    "system_name": "co_183_name",
                    "display_name": "name",
                    "data_type_id": 4,
                    "source_variable_id": None,
                    "source_type": "object",
                    "is_selected": True,
                    "is_primary": False,
                    "field_order": 0,
                },
                {
                    "meta_data": None,
                    "source_cf_meta_data": None,
                    "source_id": "183",
                    "variable_id": UUID("2719b753-cdb7-4e0c-9cff-ffe4322f7114"),
                    "system_name": "co_183_email",
                    "display_name": "email",
                    "data_type_id": 12,
                    "source_variable_id": None,
                    "source_type": "object",
                    "is_selected": True,
                    "is_primary": False,
                    "field_order": 0,
                },
                {
                    "meta_data": None,
                    "source_cf_meta_data": None,
                    "source_id": "183",
                    "variable_id": UUID("0145f9d7-6d92-460e-9427-32ff83948dac"),
                    "system_name": "co_183_asofdate",
                    "display_name": "as of date",
                    "data_type_id": 2,
                    "source_variable_id": None,
                    "source_type": "object",
                    "is_selected": True,
                    "is_primary": False,
                    "field_order": 0,
                },
            ],
            source_id=183,
            source_type="object",
            has_source_changed=False,
        )

        with self.assertRaises(DatasheetException):
            validator.validate_calculated_fields()

    @mock.patch.object(DatasheetValidator, "_append_source_history_to_variables")
    def test_validator_function(self, mock_append_source_history_to_variables):
        # Adding a additional variable in first transformations so that all the subsequent transformations
        # will be validated
        datasheet_id_2 = UUID("57cbfd39-b826-4829-89bf-e4e4b2225076")
        databook_id_1 = UUID("869b4b9b-5e9f-49e1-b24c-e4c5cee825b0")
        # return value should be arguments passed to _append_source_history_to_variables function
        mock_append_source_history_to_variables.side_effect = lambda x: x
        response = validate_datasheet(
            DatasheetValidateRequest(
                client_id=TEST_CLIENT_ID,
                databook_id=databook_id_1,
                datasheet_id=datasheet_id_2,
                initial_validation=True,
                source_variables=[
                    {
                        "client_id": 3024,
                        "datasheet_id": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                        "databook_id": "869b4b9b-5e9f-49e1-b24c-e4c5cee825b0",
                        "meta_data": None,
                        "variable_id": "a7f84b61-17d5-4d73-88e2-5ee9996649a6",
                        "source_cf_meta_data": None,
                        "source_id": "1",
                        "system_name": "co_1_name",
                        "display_name": "Name",
                        "data_type_id": 4,
                        "source_variable_id": "co_1_name",
                        "source_type": "object",
                        "is_selected": True,
                        "is_primary": False,
                        "field_order": 0,
                        "source_name": "custom_object_1",
                        "source_name_history": "Name << custom_object_1",
                        "is_dependent": False,
                    },
                    {
                        "client_id": 3024,
                        "datasheet_id": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                        "databook_id": "869b4b9b-5e9f-49e1-b24c-e4c5cee825b0",
                        "meta_data": None,
                        "variable_id": "8113951d-6ab2-48ff-af64-ab96579abd0b",
                        "source_cf_meta_data": None,
                        "source_id": "1",
                        "system_name": "co_1_number",
                        "display_name": "Number",
                        "data_type_id": 1,
                        "source_variable_id": "co_1_number",
                        "source_type": "object",
                        "is_selected": True,
                        "is_primary": True,
                        "field_order": 0,
                        "source_name": "custom_object_1",
                        "source_name_history": "Number << custom_object_1",
                        "is_dependent": True,
                    },
                    {
                        "client_id": 3024,
                        "datasheet_id": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                        "databook_id": "869b4b9b-5e9f-49e1-b24c-e4c5cee825b0",
                        "meta_data": None,
                        "variable_id": "c66f062b-4bb1-46c8-9f4d-d3b3663ee1f2",
                        "source_cf_meta_data": None,
                        "source_id": "1",
                        "system_name": "co_1_boolean",
                        "display_name": "Boolean",
                        "data_type_id": 3,
                        "source_variable_id": "co_1_boolean",
                        "source_type": "object",
                        "is_selected": True,
                        "is_primary": False,
                        "field_order": 0,
                        "source_name": "custom_object_1",
                        "source_name_history": "Boolean << custom_object_1",
                        "is_dependent": False,
                    },
                    {
                        "client_id": 3024,
                        "datasheet_id": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                        "databook_id": "869b4b9b-5e9f-49e1-b24c-e4c5cee825b0",
                        "meta_data": None,
                        "variable_id": "a8f6f7d8-9731-4dc8-bc1a-328d093b389b",
                        "source_cf_meta_data": None,
                        "source_id": "1",
                        "system_name": "co_1_date_1",
                        "display_name": "Date-1",
                        "data_type_id": 2,
                        "source_variable_id": "co_1_date_1",
                        "source_type": "object",
                        "is_selected": True,
                        "is_primary": False,
                        "field_order": 0,
                        "source_name": "custom_object_1",
                        "source_name_history": "Date-1 << custom_object_1",
                        "is_dependent": False,
                    },
                    {
                        "client_id": 3024,
                        "datasheet_id": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                        "databook_id": "869b4b9b-5e9f-49e1-b24c-e4c5cee825b0",
                        "meta_data": None,
                        "variable_id": "1b0b9972-764c-426a-891d-1d7338645519",
                        "source_cf_meta_data": None,
                        "source_id": "1",
                        "system_name": "co_1_email",
                        "display_name": "Email",
                        "data_type_id": 12,
                        "source_variable_id": "co_1_email",
                        "source_type": "object",
                        "is_selected": True,
                        "is_primary": False,
                        "field_order": 0,
                        "source_name": "custom_object_1",
                        "source_name_history": "Email << custom_object_1",
                        "is_dependent": False,
                    },
                    {
                        "client_id": 3024,
                        "datasheet_id": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                        "databook_id": "869b4b9b-5e9f-49e1-b24c-e4c5cee825b0",
                        "meta_data": None,
                        "variable_id": "c54ff1fc-43ae-4e06-935f-78527191f385",
                        "source_cf_meta_data": None,
                        "source_id": "1",
                        "system_name": "co_1_percentage",
                        "display_name": "Percentage",
                        "data_type_id": 6,
                        "source_variable_id": "co_1_percentage",
                        "source_type": "object",
                        "is_selected": True,
                        "is_primary": False,
                        "field_order": 0,
                        "source_name": "custom_object_1",
                        "source_name_history": "Percentage << custom_object_1",
                        "is_dependent": False,
                    },
                    {
                        "client_id": 3024,
                        "datasheet_id": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                        "databook_id": "869b4b9b-5e9f-49e1-b24c-e4c5cee825b0",
                        "meta_data": None,
                        "variable_id": "16bd069f-eaaf-4d5b-9b9b-afd42c02f631",
                        "source_cf_meta_data": None,
                        "source_id": "1",
                        "system_name": "co_1_date_2",
                        "display_name": "Date-2",
                        "data_type_id": 2,
                        "source_variable_id": "co_1_date_2",
                        "source_type": "object",
                        "is_selected": True,
                        "is_primary": False,
                        "field_order": 0,
                        "source_name": "custom_object_1",
                        "source_name_history": "Date-2 << custom_object_1",
                        "is_dependent": False,
                    },
                ],
                transformations=[
                    {
                        "on": {
                            "lhs": [
                                {
                                    "source_id": "1",
                                    "is_primary": True,
                                    "source_type": "object",
                                    "system_name": "co_1_number",
                                    "variable_id": "8113951d-6ab2-48ff-af64-ab96579abd0b",
                                    "data_type_id": 1,
                                    "display_name": "Number",
                                    "source_variable_id": "co_1_number",
                                    "source_cf_meta_data": None,
                                }
                            ],
                            "rhs": [
                                {
                                    "is_primary": True,
                                    "system_name": "co_1_number",
                                    "variable_id": "8113951d-6ab2-48ff-af64-ab96579abd0b",
                                    "data_type_id": 1,
                                    "display_name": "Number",
                                    "source_variable_id": "co_1_number",
                                    "source_cf_meta_data": None,
                                }
                            ],
                        },
                        "key": "be02df10-0484-490e-a7db-27d1d7cd12b8",
                        "type": "JOIN",
                        "with": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                        "columns": {
                            "lhs": [
                                {
                                    "source_id": "1",
                                    "is_primary": True,
                                    "source_type": "object",
                                    "system_name": "co_1_number",
                                    "variable_id": "8113951d-6ab2-48ff-af64-ab96579abd0b",
                                    "data_type_id": 1,
                                    "display_name": "Number",
                                    "system_generated": False,
                                    "source_variable_id": "co_1_number",
                                    "source_cf_meta_data": None,
                                },
                                {
                                    "source_id": "1",
                                    "is_primary": False,
                                    "source_type": "object",
                                    "system_name": "co_1_email",
                                    "variable_id": "1b0b9972-764c-426a-891d-1d7338645519",
                                    "data_type_id": 12,
                                    "display_name": "Email",
                                    "system_generated": False,
                                    "source_variable_id": "co_1_email",
                                    "source_cf_meta_data": None,
                                },
                            ],
                            "rhs": [
                                {
                                    "is_primary": True,
                                    "system_name": "co_1_number",
                                    "variable_id": "8113951d-6ab2-48ff-af64-ab96579abd0b",
                                    "data_type_id": 1,
                                    "display_name": "Number",
                                    "source_variable_id": "co_1_number",
                                    "source_cf_meta_data": None,
                                },
                                {
                                    "is_primary": False,
                                    "system_name": "co_1_boolean",
                                    "variable_id": "c66f062b-4bb1-46c8-9f4d-d3b3663ee1f2",
                                    "data_type_id": 3,
                                    "display_name": "Boolean",
                                    "source_variable_id": "co_1_boolean",
                                    "source_cf_meta_data": None,
                                },
                                {
                                    "is_primary": False,
                                    "system_name": "co_1_date_1",
                                    "variable_id": "a8f6f7d8-9731-4dc8-bc1a-328d093b389b",
                                    "data_type_id": 2,
                                    "display_name": "Date-1",
                                    "source_variable_id": "co_1_date_1",
                                    "source_cf_meta_data": None,
                                },
                            ],
                        },
                        "is_valid": True,
                        "join_type": "LEFT",
                        "output_columns": [
                            {
                                "source_id": "1",
                                "is_primary": True,
                                "source_type": "object",
                                "system_name": "lhs_co_1_number",
                                "variable_id": "8113951d-6ab2-48ff-af64-ab96579abd0b",
                                "data_type_id": 1,
                                "display_name": "Number",
                                "system_generated": False,
                                "source_variable_id": "co_1_number",
                                "source_cf_meta_data": None,
                            },
                            {
                                "source_id": "1",
                                "is_primary": False,
                                "source_type": "object",
                                "system_name": "lhs_co_1_email",
                                "variable_id": "1b0b9972-764c-426a-891d-1d7338645519",
                                "data_type_id": 12,
                                "display_name": "Email",
                                "system_generated": False,
                                "source_variable_id": "co_1_email",
                                "source_cf_meta_data": None,
                            },
                            {
                                "source_id": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                                "is_primary": True,
                                "source_type": "datasheet",
                                "system_name": "rhs_co_1_number",
                                "variable_id": "8113951d-6ab2-48ff-af64-ab96579abd0b__ss__1",
                                "data_type_id": 1,
                                "display_name": "Number",
                                "source_variable_id": "co_1_number",
                                "source_cf_meta_data": None,
                            },
                            {
                                "source_id": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                                "is_primary": False,
                                "source_type": "datasheet",
                                "system_name": "rhs_co_1_boolean",
                                "variable_id": "c66f062b-4bb1-46c8-9f4d-d3b3663ee1f2__ss__1",
                                "data_type_id": 3,
                                "display_name": "Boolean",
                                "source_variable_id": "co_1_boolean",
                                "source_cf_meta_data": None,
                            },
                            {
                                "source_id": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                                "is_primary": False,
                                "source_type": "datasheet",
                                "system_name": "rhs_co_1_date_1",
                                "variable_id": "a8f6f7d8-9731-4dc8-bc1a-328d093b389b__ss__1",
                                "data_type_id": 2,
                                "display_name": "Date-1",
                                "source_variable_id": "co_1_date_1",
                                "source_cf_meta_data": None,
                            },
                        ],
                        "with_databook_id": "869b4b9b-5e9f-49e1-b24c-e4c5cee825b0",
                        "transformation_id": "be02df10-0484-490e-a7db-27d1d7cd12b8",
                        "used_variable_ids": [
                            "c66f062b-4bb1-46c8-9f4d-d3b3663ee1f2",
                            "1b0b9972-764c-426a-891d-1d7338645519",
                            "8113951d-6ab2-48ff-af64-ab96579abd0b",
                            "a8f6f7d8-9731-4dc8-bc1a-328d093b389b",
                        ],
                        "transformation_source_map": {
                            "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb_datasheet": 1
                        },
                    },
                    {
                        "on": [
                            {
                                "lhs": {
                                    "source_id": "1",
                                    "is_primary": True,
                                    "source_type": "object",
                                    "system_name": "lhs_co_1_number",
                                    "variable_id": "8113951d-6ab2-48ff-af64-ab96579abd0b",
                                    "data_type_id": 1,
                                    "display_name": "Number",
                                    "system_generated": False,
                                    "source_variable_id": "co_1_number",
                                    "source_cf_meta_data": None,
                                },
                                "rhs": {
                                    "client_id": 3024,
                                    "meta_data": None,
                                    "source_id": "1",
                                    "is_primary": True,
                                    "databook_id": "869b4b9b-5e9f-49e1-b24c-e4c5cee825b0",
                                    "field_order": 0,
                                    "is_selected": True,
                                    "source_name": "custom_object_1",
                                    "source_type": "object",
                                    "system_name": "co_1_number",
                                    "variable_id": "8113951d-6ab2-48ff-af64-ab96579abd0b",
                                    "data_type_id": 1,
                                    "datasheet_id": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                                    "display_name": "Number",
                                    "is_dependent": True,
                                    "source_variable_id": "co_1_number",
                                    "source_cf_meta_data": None,
                                    "source_name_history": "Number << custom_object_1",
                                },
                                "col_name": "lhs_co_1_number",
                            },
                            {
                                "lhs": {
                                    "source_id": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                                    "is_primary": False,
                                    "source_type": "datasheet",
                                    "system_name": "rhs_co_1_boolean",
                                    "variable_id": "c66f062b-4bb1-46c8-9f4d-d3b3663ee1f2__ss__1",
                                    "data_type_id": 3,
                                    "display_name": "Boolean",
                                    "system_generated": False,
                                    "source_variable_id": "co_1_boolean",
                                    "source_cf_meta_data": None,
                                },
                                "rhs": {
                                    "client_id": 3024,
                                    "meta_data": None,
                                    "source_id": "1",
                                    "is_primary": False,
                                    "databook_id": "869b4b9b-5e9f-49e1-b24c-e4c5cee825b0",
                                    "field_order": 0,
                                    "is_selected": True,
                                    "source_name": "custom_object_1",
                                    "source_type": "object",
                                    "system_name": "co_1_boolean",
                                    "variable_id": "c66f062b-4bb1-46c8-9f4d-d3b3663ee1f2",
                                    "data_type_id": 3,
                                    "datasheet_id": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                                    "display_name": "Boolean",
                                    "is_dependent": True,
                                    "source_variable_id": "co_1_boolean",
                                    "source_cf_meta_data": None,
                                    "source_name_history": "Boolean << custom_object_1",
                                },
                                "col_name": "rhs_co_1_boolean",
                            },
                            {
                                "lhs": {
                                    "source_id": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                                    "is_primary": False,
                                    "source_type": "datasheet",
                                    "system_name": "rhs_co_1_date_1",
                                    "variable_id": "a8f6f7d8-9731-4dc8-bc1a-328d093b389b__ss__1",
                                    "data_type_id": 2,
                                    "display_name": "Date-1",
                                    "system_generated": False,
                                    "source_variable_id": "co_1_date_1",
                                    "source_cf_meta_data": None,
                                },
                                "rhs": {
                                    "client_id": 3024,
                                    "meta_data": None,
                                    "source_id": "1",
                                    "is_primary": False,
                                    "databook_id": "869b4b9b-5e9f-49e1-b24c-e4c5cee825b0",
                                    "field_order": 0,
                                    "is_selected": True,
                                    "source_name": "custom_object_1",
                                    "source_type": "object",
                                    "system_name": "co_1_date_1",
                                    "variable_id": "a8f6f7d8-9731-4dc8-bc1a-328d093b389b",
                                    "data_type_id": 2,
                                    "datasheet_id": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                                    "display_name": "Date-1",
                                    "is_dependent": True,
                                    "source_variable_id": "co_1_date_1",
                                    "source_cf_meta_data": None,
                                    "source_name_history": "Date-1 << custom_object_1",
                                },
                                "col_name": "rhs_co_1_date_1",
                            },
                            {
                                "lhs": {
                                    "source_id": "1",
                                    "is_primary": False,
                                    "source_type": "object",
                                    "system_name": "lhs_co_1_email",
                                    "variable_id": "1b0b9972-764c-426a-891d-1d7338645519",
                                    "data_type_id": 12,
                                    "display_name": "Email",
                                    "system_generated": False,
                                    "source_variable_id": "co_1_email",
                                    "source_cf_meta_data": None,
                                },
                                "rhs": {
                                    "client_id": 3024,
                                    "meta_data": None,
                                    "source_id": "1",
                                    "is_primary": False,
                                    "databook_id": "869b4b9b-5e9f-49e1-b24c-e4c5cee825b0",
                                    "field_order": 0,
                                    "is_selected": True,
                                    "source_name": "custom_object_1",
                                    "source_type": "object",
                                    "system_name": "co_1_email",
                                    "variable_id": "1b0b9972-764c-426a-891d-1d7338645519",
                                    "data_type_id": 12,
                                    "datasheet_id": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                                    "display_name": "Email",
                                    "is_dependent": True,
                                    "source_variable_id": "co_1_email",
                                    "source_cf_meta_data": None,
                                    "source_name_history": "Email << custom_object_1",
                                },
                                "col_name": "lhs_co_1_email",
                            },
                        ],
                        "key": "cff3ce0d-0395-4381-a610-ceea2dae4634",
                        "type": "UNION",
                        "with": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                        "is_valid": True,
                        "union_type": "DISTINCT",
                        "output_columns": [
                            {
                                "source_id": "1",
                                "is_primary": True,
                                "source_type": "object",
                                "system_name": "lhs_co_1_number",
                                "variable_id": "8113951d-6ab2-48ff-af64-ab96579abd0b",
                                "data_type_id": 1,
                                "display_name": "Number",
                                "system_generated": False,
                                "source_variable_id": "co_1_number",
                                "source_cf_meta_data": None,
                            },
                            {
                                "source_id": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                                "is_primary": True,
                                "source_type": "datasheet",
                                "system_name": "rhs_co_1_boolean",
                                "variable_id": "c66f062b-4bb1-46c8-9f4d-d3b3663ee1f2__ss__1",
                                "data_type_id": 3,
                                "display_name": "Boolean",
                                "system_generated": False,
                                "source_variable_id": "co_1_boolean",
                                "source_cf_meta_data": None,
                            },
                            {
                                "source_id": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                                "is_primary": True,
                                "source_type": "datasheet",
                                "system_name": "rhs_co_1_date_1",
                                "variable_id": "a8f6f7d8-9731-4dc8-bc1a-328d093b389b__ss__1",
                                "data_type_id": 2,
                                "display_name": "Date-1",
                                "system_generated": False,
                                "source_variable_id": "co_1_date_1",
                                "source_cf_meta_data": None,
                            },
                            {
                                "source_id": "1",
                                "is_primary": True,
                                "source_type": "object",
                                "system_name": "lhs_co_1_email",
                                "variable_id": "1b0b9972-764c-426a-891d-1d7338645519",
                                "data_type_id": 12,
                                "display_name": "Email",
                                "system_generated": False,
                                "source_variable_id": "co_1_email",
                                "source_cf_meta_data": None,
                            },
                        ],
                        "with_databook_id": "869b4b9b-5e9f-49e1-b24c-e4c5cee825b0",
                        "transformation_id": "cff3ce0d-0395-4381-a610-ceea2dae4634",
                        "used_variable_ids": [
                            "a8f6f7d8-9731-4dc8-bc1a-328d093b389b__ss__1",
                            "c66f062b-4bb1-46c8-9f4d-d3b3663ee1f2__ss__1",
                            "c66f062b-4bb1-46c8-9f4d-d3b3663ee1f2",
                            "1b0b9972-764c-426a-891d-1d7338645519",
                            "8113951d-6ab2-48ff-af64-ab96579abd0b",
                            "a8f6f7d8-9731-4dc8-bc1a-328d093b389b",
                        ],
                        "transformation_source_map": {
                            "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb_datasheet": 1
                        },
                    },
                    {
                        "key": "4631bb68-42a4-475e-b740-397b9d175eb9",
                        "transformation_id": "4631bb68-42a4-475e-b740-397b9d175eb9",
                        "columns": {
                            "lhs": [
                                {
                                    "system_name": "lhs_co_1_number",
                                    "display_name": "Number",
                                    "data_type_id": 1,
                                    "source_cf_meta_data": None,
                                    "variable_id": "8113951d-6ab2-48ff-af64-ab96579abd0b",
                                    "source_variable_id": "co_1_number",
                                    "is_primary": True,
                                },
                                {
                                    "system_name": "rhs_co_1_boolean",
                                    "display_name": "Boolean",
                                    "data_type_id": 3,
                                    "source_cf_meta_data": None,
                                    "variable_id": "c66f062b-4bb1-46c8-9f4d-d3b3663ee1f2__ss__1",
                                    "source_variable_id": "co_1_boolean",
                                    "is_primary": True,
                                },
                                {
                                    "system_name": "lhs_co_1_email",
                                    "display_name": "Email",
                                    "data_type_id": 12,
                                    "source_cf_meta_data": None,
                                    "variable_id": "1b0b9972-764c-426a-891d-1d7338645519",
                                    "source_variable_id": "co_1_email",
                                    "is_primary": True,
                                },
                            ],
                            "rhs": [
                                {
                                    "system_name": "co_1_number",
                                    "display_name": "Number",
                                    "data_type_id": 1,
                                    "source_cf_meta_data": None,
                                    "variable_id": "8113951d-6ab2-48ff-af64-ab96579abd0b",
                                    "source_variable_id": "co_1_number",
                                    "is_primary": True,
                                }
                            ],
                        },
                        "type": "JOIN",
                        "join_type": "LEFT",
                        "with_databook_id": "869b4b9b-5e9f-49e1-b24c-e4c5cee825b0",
                        "with": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                        "on": {
                            "lhs": [
                                {
                                    "system_name": "lhs_co_1_number",
                                    "display_name": "Number",
                                    "data_type_id": 1,
                                    "source_cf_meta_data": None,
                                    "variable_id": "8113951d-6ab2-48ff-af64-ab96579abd0b",
                                    "source_variable_id": "co_1_number",
                                    "is_primary": True,
                                }
                            ],
                            "rhs": [
                                {
                                    "system_name": "co_1_number",
                                    "display_name": "Number",
                                    "data_type_id": 1,
                                    "source_cf_meta_data": None,
                                    "variable_id": "8113951d-6ab2-48ff-af64-ab96579abd0b",
                                    "source_variable_id": "co_1_number",
                                    "is_primary": True,
                                }
                            ],
                        },
                        "is_valid": False,
                    },
                    {
                        "key": "ed80190e-fc64-4dee-b29c-4fda83066c46",
                        "meta": [
                            {
                                "source_id": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                                "source_type": "datasheet",
                                "input_columns": [
                                    {
                                        "system_name": "ts_1_lhs_lhs_co_1_number",
                                        "data_type_id": 1,
                                        "display_name": "Number",
                                    },
                                    {
                                        "system_name": "ts_1_lhs_rhs_co_1_boolean",
                                        "data_type_id": 3,
                                        "display_name": "Boolean",
                                    },
                                    {
                                        "system_name": "ts_1_lhs_lhs_co_1_email",
                                        "data_type_id": 12,
                                        "display_name": "Email",
                                    },
                                    {
                                        "system_name": "ts_1_lhs_count_rhs_co_1_date_1",
                                        "data_type_id": 1,
                                        "display_name": "COUNT::DATE-1",
                                    },
                                    {
                                        "system_name": "ts_1_rhs_co_1_number",
                                        "data_type_id": 1,
                                        "display_name": "Number",
                                    },
                                ],
                                "output_columns": [
                                    {
                                        "system_name": "ts_1_lhs_lhs_co_1_number",
                                        "data_type_id": 1,
                                        "display_name": "Number",
                                    },
                                    {
                                        "system_name": "ts_1_lhs_rhs_co_1_boolean",
                                        "data_type_id": 3,
                                        "display_name": "Boolean",
                                    },
                                    {
                                        "system_name": "ts_1_lhs_count_rhs_co_1_date_1",
                                        "data_type_id": 1,
                                        "display_name": "COUNT::DATE-1",
                                    },
                                    {
                                        "system_name": "ts_1_rhs_co_1_number",
                                        "data_type_id": 1,
                                        "display_name": "Number",
                                    },
                                ],
                                "email_id_column": {
                                    "value": "lhs_lhs_co_1_email",
                                    "variable_id": "1b0b9972-764c-426a-891d-1d7338645519",
                                },
                                "end_date_column": {"value": None, "variable_id": None},
                                "with_databook_id": "869b4b9b-5e9f-49e1-b24c-e4c5cee825b0",
                                "start_date_column": {
                                    "value": None,
                                    "variable_id": None,
                                },
                                "has_effective_dates": False,
                            },
                            {
                                "source_id": "user",
                                "source_type": "report",
                                "input_columns": [
                                    {
                                        "system_name": "ts_2_effective_start_date",
                                        "data_type_id": 2,
                                        "display_name": "Effective Start Date",
                                    },
                                    {
                                        "system_name": "ts_2_effective_end_date",
                                        "data_type_id": 2,
                                        "display_name": "Effective End Date",
                                    },
                                    {
                                        "system_name": "ts_2_employee_id",
                                        "data_type_id": 4,
                                        "display_name": "Employee Id",
                                    },
                                    {
                                        "system_name": "ts_2_joining_date",
                                        "data_type_id": 2,
                                        "display_name": "Joining Date",
                                    },
                                    {
                                        "system_name": "ts_2_time_zone",
                                        "data_type_id": 4,
                                        "display_name": "Current Time Zone",
                                    },
                                    {
                                        "system_name": "ts_2_designation",
                                        "data_type_id": 4,
                                        "display_name": "Designation",
                                    },
                                    {
                                        "system_name": "ts_2_employment_country",
                                        "data_type_id": 4,
                                        "display_name": "Employment Country",
                                    },
                                    {
                                        "system_name": "ts_2_payout_frequency",
                                        "data_type_id": 4,
                                        "display_name": "Payout Frequency",
                                    },
                                    {
                                        "system_name": "ts_2_pay_currency",
                                        "data_type_id": 4,
                                        "display_name": "Payout Currency",
                                    },
                                    {
                                        "system_name": "ts_2_fixed_pay",
                                        "data_type_id": 1,
                                        "display_name": "Fixed Pay",
                                    },
                                    {
                                        "system_name": "ts_2_payee_variable_pay",
                                        "data_type_id": 1,
                                        "display_name": "Variable Pay",
                                    },
                                    {
                                        "system_name": "ts_2_first_name",
                                        "data_type_id": 4,
                                        "display_name": "First Name",
                                    },
                                    {
                                        "system_name": "ts_2_last_name",
                                        "data_type_id": 4,
                                        "display_name": "Last Name",
                                    },
                                    {
                                        "system_name": "ts_2_user_role",
                                        "data_type_id": 4,
                                        "display_name": "Current User Role",
                                    },
                                    {
                                        "system_name": "ts_2_created_date",
                                        "data_type_id": 2,
                                        "display_name": "Created Date",
                                    },
                                    {
                                        "system_name": "ts_2_created_by",
                                        "data_type_id": 12,
                                        "display_name": "Created By",
                                    },
                                    {
                                        "system_name": "ts_2_exit_date",
                                        "data_type_id": 2,
                                        "display_name": "Exit Date",
                                    },
                                    {
                                        "system_name": "ts_2_last_commission_date",
                                        "data_type_id": 2,
                                        "display_name": "Exit Last Commission Date",
                                    },
                                    {
                                        "system_name": "ts_2_status",
                                        "data_type_id": 4,
                                        "display_name": "Status",
                                    },
                                    {
                                        "system_name": "ts_2_employee_email_id",
                                        "data_type_id": 12,
                                        "display_name": "Employee Email Id",
                                    },
                                    {
                                        "system_name": "ts_2_reporting_manager_email_id",
                                        "data_type_id": 12,
                                        "display_name": "Reporting Manager EmailId",
                                    },
                                    {
                                        "system_name": "ts_2_reporting_manager_name",
                                        "data_type_id": 4,
                                        "display_name": "Reporting Manager Name",
                                    },
                                    {
                                        "system_name": "ts_2_active_plan",
                                        "data_type_id": 4,
                                        "display_name": "Active Primary Plan",
                                    },
                                    {
                                        "system_name": "ts_2_active_spiffs",
                                        "data_type_id": 4,
                                        "display_name": "Active Spiff Plan",
                                    },
                                    {
                                        "system_name": "ts_2_active_quota_categories",
                                        "data_type_id": 4,
                                        "display_name": "Active Quota Categories",
                                    },
                                    {
                                        "system_name": "ts_2_payee_or_manager",
                                        "data_type_id": 4,
                                        "display_name": "Payee/Manager",
                                    },
                                ],
                                "output_columns": [
                                    {
                                        "system_name": "ts_2_effective_start_date",
                                        "data_type_id": 2,
                                        "display_name": "Effective Start Date",
                                    },
                                    {
                                        "system_name": "ts_2_effective_end_date",
                                        "data_type_id": 2,
                                        "display_name": "Effective End Date",
                                    },
                                    {
                                        "system_name": "ts_2_employee_id",
                                        "data_type_id": 4,
                                        "display_name": "Employee Id",
                                    },
                                    {
                                        "system_name": "ts_2_joining_date",
                                        "data_type_id": 2,
                                        "display_name": "Joining Date",
                                    },
                                    {
                                        "system_name": "ts_2_time_zone",
                                        "data_type_id": 4,
                                        "display_name": "Current Time Zone",
                                    },
                                    {
                                        "system_name": "ts_2_designation",
                                        "data_type_id": 4,
                                        "display_name": "Designation",
                                    },
                                    {
                                        "system_name": "ts_2_employment_country",
                                        "data_type_id": 4,
                                        "display_name": "Employment Country",
                                    },
                                    {
                                        "system_name": "ts_2_payout_frequency",
                                        "data_type_id": 4,
                                        "display_name": "Payout Frequency",
                                    },
                                    {
                                        "system_name": "ts_2_pay_currency",
                                        "data_type_id": 4,
                                        "display_name": "Payout Currency",
                                    },
                                    {
                                        "system_name": "ts_2_fixed_pay",
                                        "data_type_id": 1,
                                        "display_name": "Fixed Pay",
                                    },
                                    {
                                        "system_name": "ts_2_payee_variable_pay",
                                        "data_type_id": 1,
                                        "display_name": "Variable Pay",
                                    },
                                    {
                                        "system_name": "ts_2_first_name",
                                        "data_type_id": 4,
                                        "display_name": "First Name",
                                    },
                                    {
                                        "system_name": "ts_2_last_name",
                                        "data_type_id": 4,
                                        "display_name": "Last Name",
                                    },
                                    {
                                        "system_name": "ts_2_user_role",
                                        "data_type_id": 4,
                                        "display_name": "Current User Role",
                                    },
                                    {
                                        "system_name": "ts_2_created_date",
                                        "data_type_id": 2,
                                        "display_name": "Created Date",
                                    },
                                    {
                                        "system_name": "ts_2_created_by",
                                        "data_type_id": 12,
                                        "display_name": "Created By",
                                    },
                                    {
                                        "system_name": "ts_2_exit_date",
                                        "data_type_id": 2,
                                        "display_name": "Exit Date",
                                    },
                                    {
                                        "system_name": "ts_2_last_commission_date",
                                        "data_type_id": 2,
                                        "display_name": "Exit Last Commission Date",
                                    },
                                    {
                                        "system_name": "ts_2_status",
                                        "data_type_id": 4,
                                        "display_name": "Status",
                                    },
                                    {
                                        "system_name": "ts_2_reporting_manager_email_id",
                                        "data_type_id": 12,
                                        "display_name": "Reporting Manager EmailId",
                                    },
                                    {
                                        "system_name": "ts_2_reporting_manager_name",
                                        "data_type_id": 4,
                                        "display_name": "Reporting Manager Name",
                                    },
                                    {
                                        "system_name": "ts_2_active_plan",
                                        "data_type_id": 4,
                                        "display_name": "Active Primary Plan",
                                    },
                                    {
                                        "system_name": "ts_2_active_spiffs",
                                        "data_type_id": 4,
                                        "display_name": "Active Spiff Plan",
                                    },
                                    {
                                        "system_name": "ts_2_active_quota_categories",
                                        "data_type_id": 4,
                                        "display_name": "Active Quota Categories",
                                    },
                                    {
                                        "system_name": "ts_2_payee_or_manager",
                                        "data_type_id": 4,
                                        "display_name": "Payee/Manager",
                                    },
                                ],
                                "email_id_column": {
                                    "value": "employee_email_id",
                                    "variable_id": "employee_email_id_user",
                                },
                                "end_date_column": {"value": None, "variable_id": None},
                                "with_databook_id": None,
                                "start_date_column": {
                                    "value": None,
                                    "variable_id": None,
                                },
                                "has_effective_dates": False,
                            },
                        ],
                        "type": "TEMPORAL_SPLICE",
                        "is_valid": True,
                        "output_columns": [
                            {
                                "meta_data": None,
                                "source_id": "57cbfd39-b826-4829-89bf-e4e4b2225076",
                                "is_primary": True,
                                "is_selected": True,
                                "source_type": "datasheet",
                                "system_name": "ts_employee_email_id",
                                "variable_id": "970e849a-1240-4296-b15b-f37696ecf985",
                                "data_type_id": 12,
                                "display_name": "TS::Employee Email Id",
                                "system_generated": True,
                                "source_variable_id": None,
                                "source_cf_meta_data": None,
                            },
                            {
                                "meta_data": None,
                                "source_id": "57cbfd39-b826-4829-89bf-e4e4b2225076",
                                "is_primary": True,
                                "is_selected": True,
                                "source_type": "datasheet",
                                "system_name": "ts_effective_start_date",
                                "variable_id": "65b9fe83-6fa6-40ad-b1cf-a037e5517ae5",
                                "data_type_id": 2,
                                "display_name": "TS::Effective Start Date",
                                "system_generated": True,
                                "source_variable_id": None,
                                "source_cf_meta_data": None,
                            },
                            {
                                "meta_data": None,
                                "source_id": "57cbfd39-b826-4829-89bf-e4e4b2225076",
                                "is_primary": True,
                                "is_selected": True,
                                "source_type": "datasheet",
                                "system_name": "ts_effective_end_date",
                                "variable_id": "a0cb521b-2728-4b21-a4fc-62d56aa004b9",
                                "data_type_id": 2,
                                "display_name": "TS::Effective End Date",
                                "system_generated": True,
                                "source_variable_id": None,
                                "source_cf_meta_data": None,
                            },
                            {
                                "source_id": "1",
                                "is_primary": False,
                                "source_type": "object",
                                "system_name": "ts_1_lhs_lhs_co_1_number",
                                "variable_id": "8113951d-6ab2-48ff-af64-ab96579abd0b",
                                "data_type_id": 1,
                                "display_name": "Number",
                                "system_generated": False,
                                "source_variable_id": "co_1_number",
                                "source_cf_meta_data": None,
                            },
                            {
                                "source_id": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                                "is_primary": False,
                                "source_type": "datasheet",
                                "system_name": "ts_1_lhs_rhs_co_1_boolean",
                                "variable_id": "c66f062b-4bb1-46c8-9f4d-d3b3663ee1f2__ss__1",
                                "data_type_id": 3,
                                "display_name": "Boolean",
                                "system_generated": False,
                                "source_variable_id": "co_1_boolean",
                                "source_cf_meta_data": None,
                            },
                            {
                                "source_id": "57cbfd39-b826-4829-89bf-e4e4b2225076",
                                "is_primary": False,
                                "source_type": "datasheet",
                                "system_name": "ts_1_lhs_count_rhs_co_1_date_1",
                                "variable_id": "04da6570-5640-4fa5-840d-dd22f2dcc512",
                                "data_type_id": 1,
                                "display_name": "COUNT::DATE-1",
                                "system_generated": True,
                                "source_variable_id": None,
                                "source_cf_meta_data": None,
                            },
                            {
                                "source_id": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                                "is_primary": False,
                                "source_type": "datasheet",
                                "system_name": "ts_1_rhs_co_1_number",
                                "variable_id": "8113951d-6ab2-48ff-af64-ab96579abd0b__ss__2",
                                "data_type_id": 1,
                                "display_name": "Number",
                                "source_variable_id": "co_1_number",
                                "source_cf_meta_data": None,
                            },
                            {
                                "source_id": "user",
                                "is_primary": False,
                                "is_selected": True,
                                "source_name": "User",
                                "source_type": "report",
                                "system_name": "ts_2_effective_start_date",
                                "variable_id": "effective_start_date_user",
                                "data_type_id": 2,
                                "display_name": "Effective Start Date",
                                "source_variable_id": None,
                            },
                            {
                                "source_id": "user",
                                "is_primary": False,
                                "is_selected": True,
                                "source_name": "User",
                                "source_type": "report",
                                "system_name": "ts_2_effective_end_date",
                                "variable_id": "effective_end_date_user",
                                "data_type_id": 2,
                                "display_name": "Effective End Date",
                                "source_variable_id": None,
                            },
                            {
                                "source_id": "user",
                                "is_primary": False,
                                "is_selected": True,
                                "source_name": "User",
                                "source_type": "report",
                                "system_name": "ts_2_employee_id",
                                "variable_id": "employee_id_user",
                                "data_type_id": 4,
                                "display_name": "Employee Id",
                                "source_variable_id": None,
                            },
                            {
                                "source_id": "user",
                                "is_primary": False,
                                "is_selected": True,
                                "source_name": "User",
                                "source_type": "report",
                                "system_name": "ts_2_joining_date",
                                "variable_id": "joining_date_user",
                                "data_type_id": 2,
                                "display_name": "Joining Date",
                                "source_variable_id": None,
                            },
                            {
                                "source_id": "user",
                                "is_primary": False,
                                "is_selected": True,
                                "source_name": "User",
                                "source_type": "report",
                                "system_name": "ts_2_time_zone",
                                "variable_id": "time_zone_user",
                                "data_type_id": 4,
                                "display_name": "Current Time Zone",
                                "source_variable_id": None,
                            },
                            {
                                "source_id": "user",
                                "is_primary": False,
                                "is_selected": True,
                                "source_name": "User",
                                "source_type": "report",
                                "system_name": "ts_2_designation",
                                "variable_id": "designation_user",
                                "data_type_id": 4,
                                "display_name": "Designation",
                                "source_variable_id": None,
                            },
                            {
                                "source_id": "user",
                                "is_primary": False,
                                "is_selected": True,
                                "source_name": "User",
                                "source_type": "report",
                                "system_name": "ts_2_employment_country",
                                "variable_id": "employment_country_user",
                                "data_type_id": 4,
                                "display_name": "Employment Country",
                                "source_variable_id": None,
                            },
                            {
                                "source_id": "user",
                                "is_primary": False,
                                "is_selected": True,
                                "source_name": "User",
                                "source_type": "report",
                                "system_name": "ts_2_payout_frequency",
                                "variable_id": "payout_frequency_user",
                                "data_type_id": 4,
                                "display_name": "Payout Frequency",
                                "source_variable_id": None,
                            },
                            {
                                "source_id": "user",
                                "is_primary": False,
                                "is_selected": True,
                                "source_name": "User",
                                "source_type": "report",
                                "system_name": "ts_2_pay_currency",
                                "variable_id": "pay_currency_user",
                                "data_type_id": 4,
                                "display_name": "Payout Currency",
                                "source_variable_id": None,
                            },
                            {
                                "source_id": "user",
                                "is_primary": False,
                                "is_selected": True,
                                "source_name": "User",
                                "source_type": "report",
                                "system_name": "ts_2_fixed_pay",
                                "variable_id": "fixed_pay_user",
                                "data_type_id": 1,
                                "display_name": "Fixed Pay",
                                "source_variable_id": None,
                            },
                            {
                                "source_id": "user",
                                "is_primary": False,
                                "is_selected": True,
                                "source_name": "User",
                                "source_type": "report",
                                "system_name": "ts_2_payee_variable_pay",
                                "variable_id": "payee_variable_pay_user",
                                "data_type_id": 1,
                                "display_name": "Variable Pay",
                                "source_variable_id": None,
                            },
                            {
                                "source_id": "user",
                                "is_primary": False,
                                "is_selected": True,
                                "source_name": "User",
                                "source_type": "report",
                                "system_name": "ts_2_first_name",
                                "variable_id": "first_name_user",
                                "data_type_id": 4,
                                "display_name": "First Name",
                                "source_variable_id": None,
                            },
                            {
                                "source_id": "user",
                                "is_primary": False,
                                "is_selected": True,
                                "source_name": "User",
                                "source_type": "report",
                                "system_name": "ts_2_last_name",
                                "variable_id": "last_name_user",
                                "data_type_id": 4,
                                "display_name": "Last Name",
                                "source_variable_id": None,
                            },
                            {
                                "source_id": "user",
                                "is_primary": False,
                                "is_selected": True,
                                "source_name": "User",
                                "source_type": "report",
                                "system_name": "ts_2_user_role",
                                "variable_id": "user_role_user",
                                "data_type_id": 4,
                                "display_name": "Current User Role",
                                "source_variable_id": None,
                            },
                            {
                                "source_id": "user",
                                "is_primary": False,
                                "is_selected": True,
                                "source_name": "User",
                                "source_type": "report",
                                "system_name": "ts_2_created_date",
                                "variable_id": "created_date_user",
                                "data_type_id": 2,
                                "display_name": "Created Date",
                                "source_variable_id": None,
                            },
                            {
                                "source_id": "user",
                                "is_primary": False,
                                "is_selected": True,
                                "source_name": "User",
                                "source_type": "report",
                                "system_name": "ts_2_created_by",
                                "variable_id": "created_by_user",
                                "data_type_id": 12,
                                "display_name": "Created By",
                                "source_variable_id": None,
                            },
                            {
                                "source_id": "user",
                                "is_primary": False,
                                "is_selected": True,
                                "source_name": "User",
                                "source_type": "report",
                                "system_name": "ts_2_exit_date",
                                "variable_id": "exit_date_user",
                                "data_type_id": 2,
                                "display_name": "Exit Date",
                                "source_variable_id": None,
                            },
                            {
                                "source_id": "user",
                                "is_primary": False,
                                "is_selected": True,
                                "source_name": "User",
                                "source_type": "report",
                                "system_name": "ts_2_last_commission_date",
                                "variable_id": "last_commission_date_user",
                                "data_type_id": 2,
                                "display_name": "Exit Last Commission Date",
                                "source_variable_id": None,
                            },
                            {
                                "source_id": "user",
                                "is_primary": False,
                                "is_selected": True,
                                "source_name": "User",
                                "source_type": "report",
                                "system_name": "ts_2_status",
                                "variable_id": "status_user",
                                "data_type_id": 4,
                                "display_name": "Status",
                                "source_variable_id": None,
                            },
                            {
                                "source_id": "user",
                                "is_primary": False,
                                "is_selected": True,
                                "source_name": "User",
                                "source_type": "report",
                                "system_name": "ts_2_reporting_manager_email_id",
                                "variable_id": "reporting_manager_email_id_user",
                                "data_type_id": 12,
                                "display_name": "Reporting Manager EmailId",
                                "source_variable_id": None,
                            },
                            {
                                "source_id": "user",
                                "is_primary": False,
                                "is_selected": True,
                                "source_name": "User",
                                "source_type": "report",
                                "system_name": "ts_2_reporting_manager_name",
                                "variable_id": "reporting_manager_name_user",
                                "data_type_id": 4,
                                "display_name": "Reporting Manager Name",
                                "source_variable_id": None,
                            },
                            {
                                "source_id": "user",
                                "is_primary": False,
                                "is_selected": True,
                                "source_name": "User",
                                "source_type": "report",
                                "system_name": "ts_2_active_plan",
                                "variable_id": "active_plan_user",
                                "data_type_id": 4,
                                "display_name": "Active Primary Plan",
                                "source_variable_id": None,
                            },
                            {
                                "source_id": "user",
                                "is_primary": False,
                                "is_selected": True,
                                "source_name": "User",
                                "source_type": "report",
                                "system_name": "ts_2_active_spiffs",
                                "variable_id": "active_spiffs_user",
                                "data_type_id": 4,
                                "display_name": "Active Spiff Plan",
                                "source_variable_id": None,
                            },
                            {
                                "source_id": "user",
                                "is_primary": False,
                                "is_selected": True,
                                "source_name": "User",
                                "source_type": "report",
                                "system_name": "ts_2_active_quota_categories",
                                "variable_id": "active_quota_categories_user",
                                "data_type_id": 4,
                                "display_name": "Active Quota Categories",
                                "source_variable_id": None,
                            },
                            {
                                "source_id": "user",
                                "is_primary": False,
                                "is_selected": True,
                                "source_name": "User",
                                "source_type": "report",
                                "system_name": "ts_2_payee_or_manager",
                                "variable_id": "payee_or_manager_user",
                                "data_type_id": 4,
                                "display_name": "Payee/Manager",
                                "source_variable_id": None,
                            },
                        ],
                        "transformation_id": "ed80190e-fc64-4dee-b29c-4fda83066c46",
                        "used_variable_ids": [
                            "a8f6f7d8-9731-4dc8-bc1a-328d093b389b__ss__1",
                            "c66f062b-4bb1-46c8-9f4d-d3b3663ee1f2__ss__1",
                            "1b0b9972-764c-426a-891d-1d7338645519",
                            "c66f062b-4bb1-46c8-9f4d-d3b3663ee1f2",
                            "04da6570-5640-4fa5-840d-dd22f2dcc512",
                            "8113951d-6ab2-48ff-af64-ab96579abd0b",
                            "a8f6f7d8-9731-4dc8-bc1a-328d093b389b",
                        ],
                        "transformation_source_map": {
                            "user_report": 1,
                            "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb_datasheet": 3,
                        },
                    },
                    {
                        "key": "c4fdb86c-83a9-4a65-90e6-c759d2f7d805",
                        "type": "GET_USER_PROPERTIES",
                        "is_valid": True,
                        "email_column": {
                            "value": "ts_employee_email_id",
                            "variable_id": "970e849a-1240-4296-b15b-f37696ecf985",
                        },
                        "output_columns": [
                            {
                                "meta_data": None,
                                "source_id": "57cbfd39-b826-4829-89bf-e4e4b2225076",
                                "is_primary": True,
                                "is_selected": True,
                                "source_type": "datasheet",
                                "system_name": "ts_employee_email_id",
                                "variable_id": "970e849a-1240-4296-b15b-f37696ecf985",
                                "data_type_id": 12,
                                "display_name": "TS::Employee Email Id",
                                "system_generated": True,
                                "source_variable_id": None,
                                "source_cf_meta_data": None,
                            },
                            {
                                "meta_data": None,
                                "source_id": "57cbfd39-b826-4829-89bf-e4e4b2225076",
                                "is_primary": True,
                                "is_selected": True,
                                "source_type": "datasheet",
                                "system_name": "ts_effective_start_date",
                                "variable_id": "65b9fe83-6fa6-40ad-b1cf-a037e5517ae5",
                                "data_type_id": 2,
                                "display_name": "TS::Effective Start Date",
                                "system_generated": True,
                                "source_variable_id": None,
                                "source_cf_meta_data": None,
                            },
                            {
                                "meta_data": None,
                                "source_id": "57cbfd39-b826-4829-89bf-e4e4b2225076",
                                "is_primary": True,
                                "is_selected": True,
                                "source_type": "datasheet",
                                "system_name": "ts_effective_end_date",
                                "variable_id": "a0cb521b-2728-4b21-a4fc-62d56aa004b9",
                                "data_type_id": 2,
                                "display_name": "TS::Effective End Date",
                                "system_generated": True,
                                "source_variable_id": None,
                                "source_cf_meta_data": None,
                            },
                            {
                                "source_id": "1",
                                "is_primary": False,
                                "source_type": "object",
                                "system_name": "ts_1_lhs_lhs_co_1_number",
                                "variable_id": "8113951d-6ab2-48ff-af64-ab96579abd0b",
                                "data_type_id": 1,
                                "display_name": "Number",
                                "system_generated": False,
                                "source_variable_id": "co_1_number",
                                "source_cf_meta_data": None,
                            },
                            {
                                "source_id": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                                "is_primary": False,
                                "source_type": "datasheet",
                                "system_name": "ts_1_lhs_rhs_co_1_boolean",
                                "variable_id": "c66f062b-4bb1-46c8-9f4d-d3b3663ee1f2__ss__1",
                                "data_type_id": 3,
                                "display_name": "Boolean",
                                "system_generated": False,
                                "source_variable_id": "co_1_boolean",
                                "source_cf_meta_data": None,
                            },
                            {
                                "source_id": "57cbfd39-b826-4829-89bf-e4e4b2225076",
                                "is_primary": False,
                                "source_type": "datasheet",
                                "system_name": "ts_1_lhs_count_rhs_co_1_date_1",
                                "variable_id": "04da6570-5640-4fa5-840d-dd22f2dcc512",
                                "data_type_id": 1,
                                "display_name": "COUNT::DATE-1",
                                "system_generated": True,
                                "source_variable_id": None,
                                "source_cf_meta_data": None,
                            },
                            {
                                "source_id": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                                "is_primary": False,
                                "source_type": "datasheet",
                                "system_name": "ts_1_rhs_co_1_number",
                                "variable_id": "8113951d-6ab2-48ff-af64-ab96579abd0b__ss__2",
                                "data_type_id": 1,
                                "display_name": "Number",
                                "source_variable_id": "co_1_number",
                                "source_cf_meta_data": None,
                            },
                            {
                                "source_id": "user",
                                "is_primary": False,
                                "is_selected": True,
                                "source_name": "User",
                                "source_type": "report",
                                "system_name": "ts_2_effective_start_date",
                                "variable_id": "effective_start_date_user",
                                "data_type_id": 2,
                                "display_name": "Effective Start Date",
                                "source_variable_id": None,
                            },
                            {
                                "source_id": "user",
                                "is_primary": False,
                                "is_selected": True,
                                "source_name": "User",
                                "source_type": "report",
                                "system_name": "ts_2_effective_end_date",
                                "variable_id": "effective_end_date_user",
                                "data_type_id": 2,
                                "display_name": "Effective End Date",
                                "source_variable_id": None,
                            },
                            {
                                "source_id": "user",
                                "is_primary": False,
                                "is_selected": True,
                                "source_name": "User",
                                "source_type": "report",
                                "system_name": "ts_2_employee_id",
                                "variable_id": "employee_id_user",
                                "data_type_id": 4,
                                "display_name": "Employee Id",
                                "source_variable_id": None,
                            },
                            {
                                "source_id": "user",
                                "is_primary": False,
                                "is_selected": True,
                                "source_name": "User",
                                "source_type": "report",
                                "system_name": "ts_2_joining_date",
                                "variable_id": "joining_date_user",
                                "data_type_id": 2,
                                "display_name": "Joining Date",
                                "source_variable_id": None,
                            },
                            {
                                "source_id": "user",
                                "is_primary": False,
                                "is_selected": True,
                                "source_name": "User",
                                "source_type": "report",
                                "system_name": "ts_2_time_zone",
                                "variable_id": "time_zone_user",
                                "data_type_id": 4,
                                "display_name": "Current Time Zone",
                                "source_variable_id": None,
                            },
                            {
                                "source_id": "user",
                                "is_primary": False,
                                "is_selected": True,
                                "source_name": "User",
                                "source_type": "report",
                                "system_name": "ts_2_designation",
                                "variable_id": "designation_user",
                                "data_type_id": 4,
                                "display_name": "Designation",
                                "source_variable_id": None,
                            },
                            {
                                "source_id": "user",
                                "is_primary": False,
                                "is_selected": True,
                                "source_name": "User",
                                "source_type": "report",
                                "system_name": "ts_2_employment_country",
                                "variable_id": "employment_country_user",
                                "data_type_id": 4,
                                "display_name": "Employment Country",
                                "source_variable_id": None,
                            },
                            {
                                "source_id": "user",
                                "is_primary": False,
                                "is_selected": True,
                                "source_name": "User",
                                "source_type": "report",
                                "system_name": "ts_2_payout_frequency",
                                "variable_id": "payout_frequency_user",
                                "data_type_id": 4,
                                "display_name": "Payout Frequency",
                                "source_variable_id": None,
                            },
                            {
                                "source_id": "user",
                                "is_primary": False,
                                "is_selected": True,
                                "source_name": "User",
                                "source_type": "report",
                                "system_name": "ts_2_pay_currency",
                                "variable_id": "pay_currency_user",
                                "data_type_id": 4,
                                "display_name": "Payout Currency",
                                "source_variable_id": None,
                            },
                            {
                                "source_id": "user",
                                "is_primary": False,
                                "is_selected": True,
                                "source_name": "User",
                                "source_type": "report",
                                "system_name": "ts_2_fixed_pay",
                                "variable_id": "fixed_pay_user",
                                "data_type_id": 1,
                                "display_name": "Fixed Pay",
                                "source_variable_id": None,
                            },
                            {
                                "source_id": "user",
                                "is_primary": False,
                                "is_selected": True,
                                "source_name": "User",
                                "source_type": "report",
                                "system_name": "ts_2_payee_variable_pay",
                                "variable_id": "payee_variable_pay_user",
                                "data_type_id": 1,
                                "display_name": "Variable Pay",
                                "source_variable_id": None,
                            },
                            {
                                "source_id": "user",
                                "is_primary": False,
                                "is_selected": True,
                                "source_name": "User",
                                "source_type": "report",
                                "system_name": "ts_2_first_name",
                                "variable_id": "first_name_user",
                                "data_type_id": 4,
                                "display_name": "First Name",
                                "source_variable_id": None,
                            },
                            {
                                "source_id": "user",
                                "is_primary": False,
                                "is_selected": True,
                                "source_name": "User",
                                "source_type": "report",
                                "system_name": "ts_2_last_name",
                                "variable_id": "last_name_user",
                                "data_type_id": 4,
                                "display_name": "Last Name",
                                "source_variable_id": None,
                            },
                            {
                                "source_id": "user",
                                "is_primary": False,
                                "is_selected": True,
                                "source_name": "User",
                                "source_type": "report",
                                "system_name": "ts_2_user_role",
                                "variable_id": "user_role_user",
                                "data_type_id": 4,
                                "display_name": "Current User Role",
                                "source_variable_id": None,
                            },
                            {
                                "source_id": "user",
                                "is_primary": False,
                                "is_selected": True,
                                "source_name": "User",
                                "source_type": "report",
                                "system_name": "ts_2_created_date",
                                "variable_id": "created_date_user",
                                "data_type_id": 2,
                                "display_name": "Created Date",
                                "source_variable_id": None,
                            },
                            {
                                "source_id": "user",
                                "is_primary": False,
                                "is_selected": True,
                                "source_name": "User",
                                "source_type": "report",
                                "system_name": "ts_2_created_by",
                                "variable_id": "created_by_user",
                                "data_type_id": 12,
                                "display_name": "Created By",
                                "source_variable_id": None,
                            },
                            {
                                "source_id": "user",
                                "is_primary": False,
                                "is_selected": True,
                                "source_name": "User",
                                "source_type": "report",
                                "system_name": "ts_2_exit_date",
                                "variable_id": "exit_date_user",
                                "data_type_id": 2,
                                "display_name": "Exit Date",
                                "source_variable_id": None,
                            },
                            {
                                "source_id": "user",
                                "is_primary": False,
                                "is_selected": True,
                                "source_name": "User",
                                "source_type": "report",
                                "system_name": "ts_2_last_commission_date",
                                "variable_id": "last_commission_date_user",
                                "data_type_id": 2,
                                "display_name": "Exit Last Commission Date",
                                "source_variable_id": None,
                            },
                            {
                                "source_id": "user",
                                "is_primary": False,
                                "is_selected": True,
                                "source_name": "User",
                                "source_type": "report",
                                "system_name": "ts_2_status",
                                "variable_id": "status_user",
                                "data_type_id": 4,
                                "display_name": "Status",
                                "source_variable_id": None,
                            },
                            {
                                "source_id": "user",
                                "is_primary": False,
                                "is_selected": True,
                                "source_name": "User",
                                "source_type": "report",
                                "system_name": "ts_2_reporting_manager_email_id",
                                "variable_id": "reporting_manager_email_id_user",
                                "data_type_id": 12,
                                "display_name": "Reporting Manager EmailId",
                                "source_variable_id": None,
                            },
                            {
                                "source_id": "user",
                                "is_primary": False,
                                "is_selected": True,
                                "source_name": "User",
                                "source_type": "report",
                                "system_name": "ts_2_reporting_manager_name",
                                "variable_id": "reporting_manager_name_user",
                                "data_type_id": 4,
                                "display_name": "Reporting Manager Name",
                                "source_variable_id": None,
                            },
                            {
                                "source_id": "user",
                                "is_primary": False,
                                "is_selected": True,
                                "source_name": "User",
                                "source_type": "report",
                                "system_name": "ts_2_active_plan",
                                "variable_id": "active_plan_user",
                                "data_type_id": 4,
                                "display_name": "Active Primary Plan",
                                "source_variable_id": None,
                            },
                            {
                                "source_id": "user",
                                "is_primary": False,
                                "is_selected": True,
                                "source_name": "User",
                                "source_type": "report",
                                "system_name": "ts_2_active_spiffs",
                                "variable_id": "active_spiffs_user",
                                "data_type_id": 4,
                                "display_name": "Active Spiff Plan",
                                "source_variable_id": None,
                            },
                            {
                                "source_id": "user",
                                "is_primary": False,
                                "is_selected": True,
                                "source_name": "User",
                                "source_type": "report",
                                "system_name": "ts_2_active_quota_categories",
                                "variable_id": "active_quota_categories_user",
                                "data_type_id": 4,
                                "display_name": "Active Quota Categories",
                                "source_variable_id": None,
                            },
                            {
                                "source_id": "user",
                                "is_primary": False,
                                "is_selected": True,
                                "source_name": "User",
                                "source_type": "report",
                                "system_name": "ts_2_payee_or_manager",
                                "variable_id": "payee_or_manager_user",
                                "data_type_id": 4,
                                "display_name": "Payee/Manager",
                                "source_variable_id": None,
                            },
                            {
                                "meta_data": None,
                                "source_id": "user",
                                "is_primary": False,
                                "is_selected": True,
                                "source_type": "report",
                                "system_name": "up_ts_employee_email_id_ts_2_effective_end_date_payout_frequency_d805",
                                "variable_id": "payout_frequency_user",
                                "data_type_id": 4,
                                "display_name": "Payout Frequency",
                                "source_variable_id": None,
                                "source_cf_meta_data": None,
                            },
                            {
                                "meta_data": None,
                                "source_id": "user",
                                "is_primary": False,
                                "is_selected": True,
                                "source_type": "report",
                                "system_name": "up_ts_employee_email_id_ts_2_effective_end_date_employment_country_d805",
                                "variable_id": "employment_country_user",
                                "data_type_id": 4,
                                "display_name": "Employment Country",
                                "source_variable_id": None,
                                "source_cf_meta_data": None,
                            },
                        ],
                        "user_properties": [
                            {
                                "variable_id": "c578f2cd-6e02-4ff5-bebd-1304486043fc",
                                "data_type_id": 4,
                                "display_name": "Payout Frequency",
                                "user_property_system_name": "payout_frequency",
                                "output_variable_system_name": "up_ts_employee_email_id_ts_2_effective_end_date_payout_frequency_d805",
                            },
                            {
                                "variable_id": "04700c01-e2a5-44ff-b66e-ce50abc52956",
                                "data_type_id": 4,
                                "display_name": "Employment Country",
                                "user_property_system_name": "employment_country",
                                "output_variable_system_name": "up_ts_employee_email_id_ts_2_effective_end_date_employment_country_d805",
                            },
                        ],
                        "as_of_date_column": {
                            "value": "ts_2_effective_end_date",
                            "variable_id": "effective_end_date_user",
                        },
                        "transformation_id": "c4fdb86c-83a9-4a65-90e6-c759d2f7d805",
                        "used_variable_ids": [
                            "a8f6f7d8-9731-4dc8-bc1a-328d093b389b__ss__1",
                            "c66f062b-4bb1-46c8-9f4d-d3b3663ee1f2__ss__1",
                            "c66f062b-4bb1-46c8-9f4d-d3b3663ee1f2",
                            "04da6570-5640-4fa5-840d-dd22f2dcc512",
                            "970e849a-1240-4296-b15b-f37696ecf985",
                            "effective_end_date_user",
                            "1b0b9972-764c-426a-891d-1d7338645519",
                            "8113951d-6ab2-48ff-af64-ab96579abd0b",
                            "a8f6f7d8-9731-4dc8-bc1a-328d093b389b",
                        ],
                        "transformation_source_map": {
                            "user_report": 2,
                            "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb_datasheet": 3,
                        },
                    },
                    {
                        "on": {
                            "lhs": [
                                {
                                    "source_id": "user",
                                    "is_primary": False,
                                    "source_type": "report",
                                    "system_name": "ts_2_first_name",
                                    "variable_id": "first_name_user",
                                    "data_type_id": 4,
                                    "display_name": "First Name",
                                    "source_variable_id": None,
                                    "source_cf_meta_data": None,
                                }
                            ],
                            "rhs": [
                                {
                                    "is_primary": False,
                                    "system_name": "co_1_name",
                                    "variable_id": "a7f84b61-17d5-4d73-88e2-5ee9996649a6",
                                    "data_type_id": 4,
                                    "display_name": "Name",
                                    "source_variable_id": "co_1_name",
                                    "source_cf_meta_data": None,
                                }
                            ],
                        },
                        "key": "7f67c8fd-59d9-4aad-9e58-5b832b5ad228",
                        "type": "JOIN",
                        "with": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                        "columns": {
                            "lhs": [
                                {
                                    "source_id": "57cbfd39-b826-4829-89bf-e4e4b2225076",
                                    "is_primary": True,
                                    "source_type": "datasheet",
                                    "system_name": "ts_employee_email_id",
                                    "variable_id": "970e849a-1240-4296-b15b-f37696ecf985",
                                    "data_type_id": 12,
                                    "display_name": "TS::Employee Email Id",
                                    "system_generated": True,
                                    "source_variable_id": None,
                                    "source_cf_meta_data": None,
                                },
                                {
                                    "source_id": "57cbfd39-b826-4829-89bf-e4e4b2225076",
                                    "is_primary": True,
                                    "source_type": "datasheet",
                                    "system_name": "ts_effective_start_date",
                                    "variable_id": "65b9fe83-6fa6-40ad-b1cf-a037e5517ae5",
                                    "data_type_id": 2,
                                    "display_name": "TS::Effective Start Date",
                                    "system_generated": True,
                                    "source_variable_id": None,
                                    "source_cf_meta_data": None,
                                },
                                {
                                    "source_id": "57cbfd39-b826-4829-89bf-e4e4b2225076",
                                    "is_primary": True,
                                    "source_type": "datasheet",
                                    "system_name": "ts_effective_end_date",
                                    "variable_id": "a0cb521b-2728-4b21-a4fc-62d56aa004b9",
                                    "data_type_id": 2,
                                    "display_name": "TS::Effective End Date",
                                    "system_generated": True,
                                    "source_variable_id": None,
                                    "source_cf_meta_data": None,
                                },
                            ],
                            "rhs": [
                                {
                                    "is_primary": True,
                                    "system_name": "co_1_number",
                                    "variable_id": "8113951d-6ab2-48ff-af64-ab96579abd0b",
                                    "data_type_id": 1,
                                    "display_name": "Number",
                                    "source_variable_id": "co_1_number",
                                    "source_cf_meta_data": None,
                                }
                            ],
                        },
                        "is_valid": True,
                        "join_type": "LEFT",
                        "output_columns": [
                            {
                                "source_id": "57cbfd39-b826-4829-89bf-e4e4b2225076",
                                "is_primary": True,
                                "source_type": "datasheet",
                                "system_name": "lhs_ts_employee_email_id",
                                "variable_id": "970e849a-1240-4296-b15b-f37696ecf985",
                                "data_type_id": 12,
                                "display_name": "TS::Employee Email Id",
                                "system_generated": True,
                                "source_variable_id": None,
                                "source_cf_meta_data": None,
                            },
                            {
                                "source_id": "57cbfd39-b826-4829-89bf-e4e4b2225076",
                                "is_primary": True,
                                "source_type": "datasheet",
                                "system_name": "lhs_ts_effective_start_date",
                                "variable_id": "65b9fe83-6fa6-40ad-b1cf-a037e5517ae5",
                                "data_type_id": 2,
                                "display_name": "TS::Effective Start Date",
                                "system_generated": True,
                                "source_variable_id": None,
                                "source_cf_meta_data": None,
                            },
                            {
                                "source_id": "57cbfd39-b826-4829-89bf-e4e4b2225076",
                                "is_primary": True,
                                "source_type": "datasheet",
                                "system_name": "lhs_ts_effective_end_date",
                                "variable_id": "a0cb521b-2728-4b21-a4fc-62d56aa004b9",
                                "data_type_id": 2,
                                "display_name": "TS::Effective End Date",
                                "system_generated": True,
                                "source_variable_id": None,
                                "source_cf_meta_data": None,
                            },
                            {
                                "source_id": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                                "is_primary": True,
                                "source_type": "datasheet",
                                "system_name": "rhs_co_1_number",
                                "variable_id": "8113951d-6ab2-48ff-af64-ab96579abd0b__ss__4",
                                "data_type_id": 1,
                                "display_name": "Number",
                                "source_variable_id": "co_1_number",
                                "source_cf_meta_data": None,
                            },
                        ],
                        "with_databook_id": "869b4b9b-5e9f-49e1-b24c-e4c5cee825b0",
                        "transformation_id": "7f67c8fd-59d9-4aad-9e58-5b832b5ad228",
                        "used_variable_ids": [
                            "a8f6f7d8-9731-4dc8-bc1a-328d093b389b__ss__1",
                            "c66f062b-4bb1-46c8-9f4d-d3b3663ee1f2__ss__1",
                            "c66f062b-4bb1-46c8-9f4d-d3b3663ee1f2",
                            "04da6570-5640-4fa5-840d-dd22f2dcc512",
                            "a7f84b61-17d5-4d73-88e2-5ee9996649a6",
                            "970e849a-1240-4296-b15b-f37696ecf985",
                            "first_name_user",
                            "effective_end_date_user",
                            "1b0b9972-764c-426a-891d-1d7338645519",
                            "8113951d-6ab2-48ff-af64-ab96579abd0b",
                            "a8f6f7d8-9731-4dc8-bc1a-328d093b389b",
                            "a0cb521b-2728-4b21-a4fc-62d56aa004b9",
                            "65b9fe83-6fa6-40ad-b1cf-a037e5517ae5",
                        ],
                        "transformation_source_map": {
                            "user_report": 2,
                            "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb_datasheet": 4,
                        },
                    },
                ],
                variables=[
                    {
                        "client_id": 3024,
                        "datasheet_id": "57cbfd39-b826-4829-89bf-e4e4b2225076",
                        "databook_id": "869b4b9b-5e9f-49e1-b24c-e4c5cee825b0",
                        "meta_data": None,
                        "variable_id": "970e849a-1240-4296-b15b-f37696ecf985",
                        "source_cf_meta_data": None,
                        "source_id": "57cbfd39-b826-4829-89bf-e4e4b2225076",
                        "system_name": "lhs_ts_employee_email_id",
                        "display_name": "TS::Employee Email Id",
                        "data_type_id": 12,
                        "source_variable_id": None,
                        "source_type": "datasheet",
                        "is_selected": True,
                        "is_primary": True,
                        "field_order": 0,
                        "source_name": "test-derived-1",
                        "source_name_history": "TS::Employee Email Id",
                        "is_dependent": False,
                    },
                    {
                        "client_id": 3024,
                        "datasheet_id": "57cbfd39-b826-4829-89bf-e4e4b2225076",
                        "databook_id": "869b4b9b-5e9f-49e1-b24c-e4c5cee825b0",
                        "meta_data": None,
                        "variable_id": "65b9fe83-6fa6-40ad-b1cf-a037e5517ae5",
                        "source_cf_meta_data": None,
                        "source_id": "57cbfd39-b826-4829-89bf-e4e4b2225076",
                        "system_name": "lhs_ts_effective_start_date",
                        "display_name": "TS::Effective Start Date",
                        "data_type_id": 2,
                        "source_variable_id": None,
                        "source_type": "datasheet",
                        "is_selected": True,
                        "is_primary": True,
                        "field_order": 0,
                        "source_name": "test-derived-1",
                        "source_name_history": "TS::Effective Start Date",
                        "is_dependent": False,
                    },
                    {
                        "client_id": 3024,
                        "datasheet_id": "57cbfd39-b826-4829-89bf-e4e4b2225076",
                        "databook_id": "869b4b9b-5e9f-49e1-b24c-e4c5cee825b0",
                        "meta_data": None,
                        "variable_id": "a0cb521b-2728-4b21-a4fc-62d56aa004b9",
                        "source_cf_meta_data": None,
                        "source_id": "57cbfd39-b826-4829-89bf-e4e4b2225076",
                        "system_name": "lhs_ts_effective_end_date",
                        "display_name": "TS::Effective End Date",
                        "data_type_id": 2,
                        "source_variable_id": None,
                        "source_type": "datasheet",
                        "is_selected": True,
                        "is_primary": True,
                        "field_order": 0,
                        "source_name": "test-derived-1",
                        "source_name_history": "TS::Effective End Date",
                        "is_dependent": False,
                    },
                    {
                        "client_id": 3024,
                        "datasheet_id": "57cbfd39-b826-4829-89bf-e4e4b2225076",
                        "databook_id": "869b4b9b-5e9f-49e1-b24c-e4c5cee825b0",
                        "meta_data": None,
                        "variable_id": "338d2fb7-20a3-4a56-8a88-e71b09243b8f",
                        "source_cf_meta_data": None,
                        "source_id": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                        "system_name": "rhs_co_1_number",
                        "display_name": "Number",
                        "data_type_id": 1,
                        "source_variable_id": "8113951d-6ab2-48ff-af64-ab96579abd0b__ss__4",
                        "source_type": "datasheet",
                        "is_selected": True,
                        "is_primary": True,
                        "field_order": 0,
                        "source_name": "test",
                        "source_name_history": "Number << test << custom_object_1",
                        "is_dependent": False,
                    },
                ],
                source_id="869b4b9b-5e9f-49e1-b24c-e4c5cee825b0",
                source_type="datasheet",
                has_source_changed=False,
            )
        )

        result = response.model_dump(by_alias=True)
        expected_response = {
            "datasheet_id": UUID("57cbfd39-b826-4829-89bf-e4e4b2225076"),
            "has_config_changed": False,
            "variables": [
                {
                    "client_id": 3024,
                    "datasheet_id": UUID("57cbfd39-b826-4829-89bf-e4e4b2225076"),
                    "databook_id": UUID("869b4b9b-5e9f-49e1-b24c-e4c5cee825b0"),
                    "meta_data": None,
                    "variable_id": UUID("970e849a-1240-4296-b15b-f37696ecf985"),
                    "source_cf_meta_data": None,
                    "source_id": "57cbfd39-b826-4829-89bf-e4e4b2225076",
                    "warning": None,
                    "system_name": "lhs_ts_employee_email_id",
                    "display_name": "TS::Employee Email Id",
                    "data_type_id": 12,
                    "source_variable_id": None,
                    "source_type": "datasheet",
                    "is_selected": True,
                    "is_primary": True,
                    "field_order": 0,
                    "source_name": "test-derived-1",
                    "source_name_history": "TS::Employee Email Id",
                    "is_dependent": False,
                    "is_adjusted": False,
                },
                {
                    "client_id": 3024,
                    "datasheet_id": UUID("57cbfd39-b826-4829-89bf-e4e4b2225076"),
                    "databook_id": UUID("869b4b9b-5e9f-49e1-b24c-e4c5cee825b0"),
                    "meta_data": None,
                    "variable_id": UUID("65b9fe83-6fa6-40ad-b1cf-a037e5517ae5"),
                    "source_cf_meta_data": None,
                    "source_id": "57cbfd39-b826-4829-89bf-e4e4b2225076",
                    "warning": None,
                    "system_name": "lhs_ts_effective_start_date",
                    "display_name": "TS::Effective Start Date",
                    "data_type_id": 2,
                    "source_variable_id": None,
                    "source_type": "datasheet",
                    "is_selected": True,
                    "is_primary": True,
                    "field_order": 0,
                    "source_name": "test-derived-1",
                    "source_name_history": "TS::Effective Start Date",
                    "is_dependent": False,
                    "is_adjusted": False,
                },
                {
                    "client_id": 3024,
                    "datasheet_id": UUID("57cbfd39-b826-4829-89bf-e4e4b2225076"),
                    "databook_id": UUID("869b4b9b-5e9f-49e1-b24c-e4c5cee825b0"),
                    "meta_data": None,
                    "variable_id": UUID("a0cb521b-2728-4b21-a4fc-62d56aa004b9"),
                    "source_cf_meta_data": None,
                    "source_id": "57cbfd39-b826-4829-89bf-e4e4b2225076",
                    "warning": None,
                    "system_name": "lhs_ts_effective_end_date",
                    "display_name": "TS::Effective End Date",
                    "data_type_id": 2,
                    "source_variable_id": None,
                    "source_type": "datasheet",
                    "is_selected": True,
                    "is_primary": True,
                    "field_order": 0,
                    "source_name": "test-derived-1",
                    "source_name_history": "TS::Effective End Date",
                    "is_dependent": False,
                    "is_adjusted": False,
                },
                {
                    "client_id": 3024,
                    "datasheet_id": UUID("57cbfd39-b826-4829-89bf-e4e4b2225076"),
                    "databook_id": UUID("869b4b9b-5e9f-49e1-b24c-e4c5cee825b0"),
                    "meta_data": None,
                    "variable_id": UUID("338d2fb7-20a3-4a56-8a88-e71b09243b8f"),
                    "source_cf_meta_data": None,
                    "source_id": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                    "warning": None,
                    "system_name": "rhs_co_1_number",
                    "display_name": "Number",
                    "data_type_id": 1,
                    "source_variable_id": "8113951d-6ab2-48ff-af64-ab96579abd0b__ss__4",
                    "source_type": "datasheet",
                    "is_selected": True,
                    "is_primary": True,
                    "field_order": 0,
                    "source_name": "test",
                    "source_name_history": "Number << test << custom_object_1",
                    "is_dependent": False,
                    "is_adjusted": False,
                },
            ],
            "source_variables": [
                {
                    "meta_data": None,
                    "source_cf_meta_data": None,
                    "source_id": "1",
                    "variable_id": "a7f84b61-17d5-4d73-88e2-5ee9996649a6",
                    "system_name": "co_1_name",
                    "display_name": "Name",
                    "data_type_id": 4,
                    "source_variable_id": "co_1_name",
                    "source_type": "object",
                    "is_selected": True,
                    "is_primary": False,
                    "field_order": 0,
                    "source_name_history": "Name << custom_object_1",
                    "warning": None,
                },
                {
                    "meta_data": None,
                    "source_cf_meta_data": None,
                    "source_id": "1",
                    "variable_id": "8113951d-6ab2-48ff-af64-ab96579abd0b",
                    "system_name": "co_1_number",
                    "display_name": "Number",
                    "data_type_id": 1,
                    "source_variable_id": "co_1_number",
                    "source_type": "object",
                    "is_selected": True,
                    "is_primary": True,
                    "field_order": 0,
                    "source_name_history": "Number << custom_object_1",
                    "warning": None,
                },
                {
                    "meta_data": None,
                    "source_cf_meta_data": None,
                    "source_id": "1",
                    "variable_id": "c66f062b-4bb1-46c8-9f4d-d3b3663ee1f2",
                    "system_name": "co_1_boolean",
                    "display_name": "Boolean",
                    "data_type_id": 3,
                    "source_variable_id": "co_1_boolean",
                    "source_type": "object",
                    "is_selected": True,
                    "is_primary": False,
                    "field_order": 0,
                    "source_name_history": "Boolean << custom_object_1",
                    "warning": None,
                },
                {
                    "meta_data": None,
                    "source_cf_meta_data": None,
                    "source_id": "1",
                    "variable_id": "a8f6f7d8-9731-4dc8-bc1a-328d093b389b",
                    "system_name": "co_1_date_1",
                    "display_name": "Date-1",
                    "data_type_id": 2,
                    "source_variable_id": "co_1_date_1",
                    "source_type": "object",
                    "is_selected": True,
                    "is_primary": False,
                    "field_order": 0,
                    "source_name_history": "Date-1 << custom_object_1",
                    "warning": None,
                },
                {
                    "meta_data": None,
                    "source_cf_meta_data": None,
                    "source_id": "1",
                    "variable_id": "1b0b9972-764c-426a-891d-1d7338645519",
                    "system_name": "co_1_email",
                    "display_name": "Email",
                    "data_type_id": 12,
                    "source_variable_id": "co_1_email",
                    "source_type": "object",
                    "is_selected": True,
                    "is_primary": False,
                    "field_order": 0,
                    "source_name_history": "Email << custom_object_1",
                    "warning": None,
                },
                {
                    "meta_data": None,
                    "source_cf_meta_data": None,
                    "source_id": "1",
                    "variable_id": "c54ff1fc-43ae-4e06-935f-78527191f385",
                    "system_name": "co_1_percentage",
                    "display_name": "Percentage",
                    "data_type_id": 6,
                    "source_variable_id": "co_1_percentage",
                    "source_type": "object",
                    "is_selected": True,
                    "is_primary": False,
                    "field_order": 0,
                    "source_name_history": "Percentage << custom_object_1",
                    "warning": None,
                },
                {
                    "meta_data": None,
                    "source_cf_meta_data": None,
                    "source_id": "1",
                    "variable_id": "16bd069f-eaaf-4d5b-9b9b-afd42c02f631",
                    "system_name": "co_1_date_2",
                    "display_name": "Date-2",
                    "data_type_id": 2,
                    "source_variable_id": "co_1_date_2",
                    "source_type": "object",
                    "is_selected": True,
                    "is_primary": False,
                    "field_order": 0,
                    "source_name_history": "Date-2 << custom_object_1",
                    "warning": None,
                },
            ],
            "transformations": [
                {
                    "key": "be02df10-0484-490e-a7db-27d1d7cd12b8",
                    "type": "JOIN",
                    "transformation_id": UUID("be02df10-0484-490e-a7db-27d1d7cd12b8"),
                    "is_valid": True,
                    "output_columns": [
                        {
                            "source_id": "1",
                            "is_primary": True,
                            "source_type": "object",
                            "system_name": "lhs_co_1_number",
                            "variable_id": "8113951d-6ab2-48ff-af64-ab96579abd0b",
                            "data_type_id": 1,
                            "display_name": "Number",
                            "system_generated": False,
                            "source_variable_id": "co_1_number",
                            "source_cf_meta_data": None,
                        },
                        {
                            "source_id": "1",
                            "is_primary": False,
                            "source_type": "object",
                            "system_name": "lhs_co_1_email",
                            "variable_id": "1b0b9972-764c-426a-891d-1d7338645519",
                            "data_type_id": 12,
                            "display_name": "Email",
                            "system_generated": False,
                            "source_variable_id": "co_1_email",
                            "source_cf_meta_data": None,
                        },
                        {
                            "source_id": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                            "is_primary": True,
                            "source_type": "datasheet",
                            "system_name": "rhs_co_1_number",
                            "variable_id": "8113951d-6ab2-48ff-af64-ab96579abd0b__ss__1",
                            "data_type_id": 1,
                            "display_name": "Number",
                            "source_variable_id": "co_1_number",
                            "source_cf_meta_data": None,
                        },
                        {
                            "source_id": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                            "is_primary": False,
                            "source_type": "datasheet",
                            "system_name": "rhs_co_1_boolean",
                            "variable_id": "c66f062b-4bb1-46c8-9f4d-d3b3663ee1f2__ss__1",
                            "data_type_id": 3,
                            "display_name": "Boolean",
                            "source_variable_id": "co_1_boolean",
                            "source_cf_meta_data": None,
                        },
                        {
                            "source_id": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                            "is_primary": False,
                            "source_type": "datasheet",
                            "system_name": "rhs_co_1_date_1",
                            "variable_id": "a8f6f7d8-9731-4dc8-bc1a-328d093b389b__ss__1",
                            "data_type_id": 2,
                            "display_name": "Date-1",
                            "source_variable_id": "co_1_date_1",
                            "source_cf_meta_data": None,
                        },
                    ],
                    "transformation_source_map": {
                        "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb_datasheet": 1
                    },
                    "source_ids": [],
                    "used_variable_ids": [
                        "c66f062b-4bb1-46c8-9f4d-d3b3663ee1f2",
                        "1b0b9972-764c-426a-891d-1d7338645519",
                        "8113951d-6ab2-48ff-af64-ab96579abd0b",
                        "a8f6f7d8-9731-4dc8-bc1a-328d093b389b",
                    ],
                    "on": {
                        "lhs": [
                            {
                                "source_id": "1",
                                "is_primary": True,
                                "source_type": "object",
                                "system_name": "co_1_number",
                                "variable_id": "8113951d-6ab2-48ff-af64-ab96579abd0b",
                                "data_type_id": 1,
                                "display_name": "Number",
                                "source_variable_id": "co_1_number",
                                "source_cf_meta_data": None,
                            }
                        ],
                        "rhs": [
                            {
                                "is_primary": True,
                                "system_name": "co_1_number",
                                "variable_id": "8113951d-6ab2-48ff-af64-ab96579abd0b",
                                "data_type_id": 1,
                                "display_name": "Number",
                                "source_variable_id": "co_1_number",
                                "source_cf_meta_data": None,
                            }
                        ],
                    },
                    "columns": {
                        "lhs": [
                            {
                                "source_id": "1",
                                "is_primary": True,
                                "source_type": "object",
                                "system_name": "co_1_number",
                                "variable_id": "8113951d-6ab2-48ff-af64-ab96579abd0b",
                                "data_type_id": 1,
                                "display_name": "Number",
                                "system_generated": False,
                                "source_variable_id": "co_1_number",
                                "source_cf_meta_data": None,
                            },
                            {
                                "source_id": "1",
                                "is_primary": False,
                                "source_type": "object",
                                "system_name": "co_1_email",
                                "variable_id": "1b0b9972-764c-426a-891d-1d7338645519",
                                "data_type_id": 12,
                                "display_name": "Email",
                                "system_generated": False,
                                "source_variable_id": "co_1_email",
                                "source_cf_meta_data": None,
                            },
                        ],
                        "rhs": [
                            {
                                "is_primary": True,
                                "system_name": "co_1_number",
                                "variable_id": "8113951d-6ab2-48ff-af64-ab96579abd0b",
                                "data_type_id": 1,
                                "display_name": "Number",
                                "source_variable_id": "co_1_number",
                                "source_cf_meta_data": None,
                            },
                            {
                                "is_primary": False,
                                "system_name": "co_1_boolean",
                                "variable_id": "c66f062b-4bb1-46c8-9f4d-d3b3663ee1f2",
                                "data_type_id": 3,
                                "display_name": "Boolean",
                                "source_variable_id": "co_1_boolean",
                                "source_cf_meta_data": None,
                            },
                            {
                                "is_primary": False,
                                "system_name": "co_1_date_1",
                                "variable_id": "a8f6f7d8-9731-4dc8-bc1a-328d093b389b",
                                "data_type_id": 2,
                                "display_name": "Date-1",
                                "source_variable_id": "co_1_date_1",
                                "source_cf_meta_data": None,
                            },
                        ],
                    },
                    "join_type": "LEFT",
                    "with": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                    "with_databook_id": "869b4b9b-5e9f-49e1-b24c-e4c5cee825b0",
                },
                {
                    "key": "cff3ce0d-0395-4381-a610-ceea2dae4634",
                    "type": "UNION",
                    "transformation_id": UUID("cff3ce0d-0395-4381-a610-ceea2dae4634"),
                    "is_valid": True,
                    "output_columns": [
                        {
                            "source_id": "1",
                            "is_primary": True,
                            "source_type": "object",
                            "system_name": "lhs_co_1_number",
                            "variable_id": "8113951d-6ab2-48ff-af64-ab96579abd0b",
                            "data_type_id": 1,
                            "display_name": "Number",
                            "system_generated": False,
                            "source_variable_id": "co_1_number",
                            "source_cf_meta_data": None,
                        },
                        {
                            "source_id": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                            "is_primary": True,
                            "source_type": "datasheet",
                            "system_name": "rhs_co_1_boolean",
                            "variable_id": "c66f062b-4bb1-46c8-9f4d-d3b3663ee1f2__ss__1",
                            "data_type_id": 3,
                            "display_name": "Boolean",
                            "system_generated": False,
                            "source_variable_id": "co_1_boolean",
                            "source_cf_meta_data": None,
                        },
                        {
                            "source_id": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                            "is_primary": True,
                            "source_type": "datasheet",
                            "system_name": "rhs_co_1_date_1",
                            "variable_id": "a8f6f7d8-9731-4dc8-bc1a-328d093b389b__ss__1",
                            "data_type_id": 2,
                            "display_name": "Date-1",
                            "system_generated": False,
                            "source_variable_id": "co_1_date_1",
                            "source_cf_meta_data": None,
                        },
                        {
                            "source_id": "1",
                            "is_primary": True,
                            "source_type": "object",
                            "system_name": "lhs_co_1_email",
                            "variable_id": "1b0b9972-764c-426a-891d-1d7338645519",
                            "data_type_id": 12,
                            "display_name": "Email",
                            "system_generated": False,
                            "source_variable_id": "co_1_email",
                            "source_cf_meta_data": None,
                        },
                    ],
                    "transformation_source_map": {
                        "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb_datasheet": 1
                    },
                    "source_ids": [],
                    "used_variable_ids": [
                        "a8f6f7d8-9731-4dc8-bc1a-328d093b389b__ss__1",
                        "c66f062b-4bb1-46c8-9f4d-d3b3663ee1f2__ss__1",
                        "c66f062b-4bb1-46c8-9f4d-d3b3663ee1f2",
                        "1b0b9972-764c-426a-891d-1d7338645519",
                        "8113951d-6ab2-48ff-af64-ab96579abd0b",
                        "a8f6f7d8-9731-4dc8-bc1a-328d093b389b",
                    ],
                    "union_type": "DISTINCT",
                    "on": [
                        {
                            "lhs": {
                                "source_id": "1",
                                "is_primary": True,
                                "source_type": "object",
                                "system_name": "lhs_co_1_number",
                                "variable_id": "8113951d-6ab2-48ff-af64-ab96579abd0b",
                                "data_type_id": 1,
                                "display_name": "Number",
                                "system_generated": False,
                                "source_variable_id": "co_1_number",
                                "source_cf_meta_data": None,
                            },
                            "rhs": {
                                "client_id": 3024,
                                "meta_data": None,
                                "source_id": "1",
                                "is_primary": True,
                                "databook_id": "869b4b9b-5e9f-49e1-b24c-e4c5cee825b0",
                                "field_order": 0,
                                "is_selected": True,
                                "source_name": "custom_object_1",
                                "source_type": "object",
                                "system_name": "co_1_number",
                                "variable_id": "8113951d-6ab2-48ff-af64-ab96579abd0b",
                                "data_type_id": 1,
                                "datasheet_id": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                                "display_name": "Number",
                                "is_dependent": True,
                                "source_variable_id": "co_1_number",
                                "source_cf_meta_data": None,
                                "source_name_history": "Number << custom_object_1",
                            },
                            "col_name": "lhs_co_1_number",
                        },
                        {
                            "lhs": {
                                "source_id": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                                "is_primary": False,
                                "source_type": "datasheet",
                                "system_name": "rhs_co_1_boolean",
                                "variable_id": "c66f062b-4bb1-46c8-9f4d-d3b3663ee1f2__ss__1",
                                "data_type_id": 3,
                                "display_name": "Boolean",
                                "system_generated": False,
                                "source_variable_id": "co_1_boolean",
                                "source_cf_meta_data": None,
                            },
                            "rhs": {
                                "client_id": 3024,
                                "meta_data": None,
                                "source_id": "1",
                                "is_primary": False,
                                "databook_id": "869b4b9b-5e9f-49e1-b24c-e4c5cee825b0",
                                "field_order": 0,
                                "is_selected": True,
                                "source_name": "custom_object_1",
                                "source_type": "object",
                                "system_name": "co_1_boolean",
                                "variable_id": "c66f062b-4bb1-46c8-9f4d-d3b3663ee1f2",
                                "data_type_id": 3,
                                "datasheet_id": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                                "display_name": "Boolean",
                                "is_dependent": True,
                                "source_variable_id": "co_1_boolean",
                                "source_cf_meta_data": None,
                                "source_name_history": "Boolean << custom_object_1",
                            },
                            "col_name": "rhs_co_1_boolean",
                        },
                        {
                            "lhs": {
                                "source_id": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                                "is_primary": False,
                                "source_type": "datasheet",
                                "system_name": "rhs_co_1_date_1",
                                "variable_id": "a8f6f7d8-9731-4dc8-bc1a-328d093b389b__ss__1",
                                "data_type_id": 2,
                                "display_name": "Date-1",
                                "system_generated": False,
                                "source_variable_id": "co_1_date_1",
                                "source_cf_meta_data": None,
                            },
                            "rhs": {
                                "client_id": 3024,
                                "meta_data": None,
                                "source_id": "1",
                                "is_primary": False,
                                "databook_id": "869b4b9b-5e9f-49e1-b24c-e4c5cee825b0",
                                "field_order": 0,
                                "is_selected": True,
                                "source_name": "custom_object_1",
                                "source_type": "object",
                                "system_name": "co_1_date_1",
                                "variable_id": "a8f6f7d8-9731-4dc8-bc1a-328d093b389b",
                                "data_type_id": 2,
                                "datasheet_id": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                                "display_name": "Date-1",
                                "is_dependent": True,
                                "source_variable_id": "co_1_date_1",
                                "source_cf_meta_data": None,
                                "source_name_history": "Date-1 << custom_object_1",
                            },
                            "col_name": "rhs_co_1_date_1",
                        },
                        {
                            "lhs": {
                                "source_id": "1",
                                "is_primary": False,
                                "source_type": "object",
                                "system_name": "lhs_co_1_email",
                                "variable_id": "1b0b9972-764c-426a-891d-1d7338645519",
                                "data_type_id": 12,
                                "display_name": "Email",
                                "system_generated": False,
                                "source_variable_id": "co_1_email",
                                "source_cf_meta_data": None,
                            },
                            "rhs": {
                                "client_id": 3024,
                                "meta_data": None,
                                "source_id": "1",
                                "is_primary": False,
                                "databook_id": "869b4b9b-5e9f-49e1-b24c-e4c5cee825b0",
                                "field_order": 0,
                                "is_selected": True,
                                "source_name": "custom_object_1",
                                "source_type": "object",
                                "system_name": "co_1_email",
                                "variable_id": "1b0b9972-764c-426a-891d-1d7338645519",
                                "data_type_id": 12,
                                "datasheet_id": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                                "display_name": "Email",
                                "is_dependent": True,
                                "source_variable_id": "co_1_email",
                                "source_cf_meta_data": None,
                                "source_name_history": "Email << custom_object_1",
                            },
                            "col_name": "lhs_co_1_email",
                        },
                    ],
                    "with": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                    "with_databook_id": "869b4b9b-5e9f-49e1-b24c-e4c5cee825b0",
                },
                {
                    "key": "4631bb68-42a4-475e-b740-397b9d175eb9",
                    "type": "JOIN",
                    "transformation_id": UUID("4631bb68-42a4-475e-b740-397b9d175eb9"),
                    "is_valid": True,
                    "output_columns": [
                        {
                            "source_id": "1",
                            "is_primary": True,
                            "source_type": "object",
                            "system_name": "lhs_lhs_co_1_number",
                            "variable_id": "8113951d-6ab2-48ff-af64-ab96579abd0b",
                            "data_type_id": 1,
                            "display_name": "Number",
                            "system_generated": False,
                            "source_variable_id": "co_1_number",
                            "source_cf_meta_data": None,
                        },
                        {
                            "source_id": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                            "is_primary": True,
                            "source_type": "datasheet",
                            "system_name": "lhs_rhs_co_1_boolean",
                            "variable_id": "c66f062b-4bb1-46c8-9f4d-d3b3663ee1f2__ss__1",
                            "data_type_id": 3,
                            "display_name": "Boolean",
                            "system_generated": False,
                            "source_variable_id": "co_1_boolean",
                            "source_cf_meta_data": None,
                        },
                        {
                            "source_id": "1",
                            "is_primary": True,
                            "source_type": "object",
                            "system_name": "lhs_lhs_co_1_email",
                            "variable_id": "1b0b9972-764c-426a-891d-1d7338645519",
                            "data_type_id": 12,
                            "display_name": "Email",
                            "system_generated": False,
                            "source_variable_id": "co_1_email",
                            "source_cf_meta_data": None,
                        },
                        {
                            "source_id": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                            "is_primary": True,
                            "source_type": "datasheet",
                            "system_name": "rhs_co_1_number",
                            "variable_id": "8113951d-6ab2-48ff-af64-ab96579abd0b__ss__2",
                            "data_type_id": 1,
                            "display_name": "Number",
                            "source_variable_id": "co_1_number",
                            "source_cf_meta_data": None,
                        },
                    ],
                    "transformation_source_map": {
                        "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb_datasheet": 2
                    },
                    "source_ids": ["f962ea83-b58a-4ee4-983e-1dc07fe4d3fb"],
                    "used_variable_ids": [
                        "c66f062b-4bb1-46c8-9f4d-d3b3663ee1f2__ss__1",
                        "8113951d-6ab2-48ff-af64-ab96579abd0b",
                        "a8f6f7d8-9731-4dc8-bc1a-328d093b389b__ss__1",
                        "c66f062b-4bb1-46c8-9f4d-d3b3663ee1f2",
                        "1b0b9972-764c-426a-891d-1d7338645519",
                        "a8f6f7d8-9731-4dc8-bc1a-328d093b389b",
                    ],
                    "on": {
                        "lhs": [
                            {
                                "source_id": "1",
                                "is_primary": True,
                                "source_type": "object",
                                "system_name": "lhs_co_1_number",
                                "variable_id": "8113951d-6ab2-48ff-af64-ab96579abd0b",
                                "data_type_id": 1,
                                "display_name": "Number",
                                "source_variable_id": "co_1_number",
                                "source_cf_meta_data": None,
                            }
                        ],
                        "rhs": [
                            {
                                "is_primary": True,
                                "system_name": "co_1_number",
                                "variable_id": "8113951d-6ab2-48ff-af64-ab96579abd0b",
                                "data_type_id": 1,
                                "display_name": "Number",
                                "source_variable_id": "co_1_number",
                                "source_cf_meta_data": None,
                            }
                        ],
                    },
                    "columns": {
                        "lhs": [
                            {
                                "source_id": "1",
                                "is_primary": True,
                                "source_type": "object",
                                "system_name": "lhs_co_1_number",
                                "variable_id": "8113951d-6ab2-48ff-af64-ab96579abd0b",
                                "data_type_id": 1,
                                "display_name": "Number",
                                "system_generated": False,
                                "source_variable_id": "co_1_number",
                                "source_cf_meta_data": None,
                            },
                            {
                                "source_id": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                                "is_primary": True,
                                "source_type": "datasheet",
                                "system_name": "rhs_co_1_boolean",
                                "variable_id": "c66f062b-4bb1-46c8-9f4d-d3b3663ee1f2__ss__1",
                                "data_type_id": 3,
                                "display_name": "Boolean",
                                "system_generated": False,
                                "source_variable_id": "co_1_boolean",
                                "source_cf_meta_data": None,
                            },
                            {
                                "source_id": "1",
                                "is_primary": True,
                                "source_type": "object",
                                "system_name": "lhs_co_1_email",
                                "variable_id": "1b0b9972-764c-426a-891d-1d7338645519",
                                "data_type_id": 12,
                                "display_name": "Email",
                                "system_generated": False,
                                "source_variable_id": "co_1_email",
                                "source_cf_meta_data": None,
                            },
                        ],
                        "rhs": [
                            {
                                "is_primary": True,
                                "system_name": "co_1_number",
                                "variable_id": "8113951d-6ab2-48ff-af64-ab96579abd0b",
                                "data_type_id": 1,
                                "display_name": "Number",
                                "source_variable_id": "co_1_number",
                                "source_cf_meta_data": None,
                            }
                        ],
                    },
                    "join_type": "LEFT",
                    "with": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                    "with_databook_id": "869b4b9b-5e9f-49e1-b24c-e4c5cee825b0",
                },
                {
                    "key": "ed80190e-fc64-4dee-b29c-4fda83066c46",
                    "type": "TEMPORAL_SPLICE",
                    "transformation_id": UUID("ed80190e-fc64-4dee-b29c-4fda83066c46"),
                    "is_valid": True,
                    "output_columns": [
                        {
                            "meta_data": None,
                            "source_id": "57cbfd39-b826-4829-89bf-e4e4b2225076",
                            "is_primary": True,
                            "is_selected": True,
                            "source_type": "datasheet",
                            "system_name": "ts_employee_email_id",
                            "variable_id": "970e849a-1240-4296-b15b-f37696ecf985",
                            "data_type_id": 12,
                            "display_name": "TS::Employee Email Id",
                            "system_generated": True,
                            "source_variable_id": None,
                            "source_cf_meta_data": None,
                        },
                        {
                            "meta_data": None,
                            "source_id": "57cbfd39-b826-4829-89bf-e4e4b2225076",
                            "is_primary": True,
                            "is_selected": True,
                            "source_type": "datasheet",
                            "system_name": "ts_effective_start_date",
                            "variable_id": "65b9fe83-6fa6-40ad-b1cf-a037e5517ae5",
                            "data_type_id": 2,
                            "display_name": "TS::Effective Start Date",
                            "system_generated": True,
                            "source_variable_id": None,
                            "source_cf_meta_data": None,
                        },
                        {
                            "meta_data": None,
                            "source_id": "57cbfd39-b826-4829-89bf-e4e4b2225076",
                            "is_primary": True,
                            "is_selected": True,
                            "source_type": "datasheet",
                            "system_name": "ts_effective_end_date",
                            "variable_id": "a0cb521b-2728-4b21-a4fc-62d56aa004b9",
                            "data_type_id": 2,
                            "display_name": "TS::Effective End Date",
                            "system_generated": True,
                            "source_variable_id": None,
                            "source_cf_meta_data": None,
                        },
                        {
                            "source_id": "1",
                            "is_primary": False,
                            "source_type": "object",
                            "system_name": "ts_1_lhs_lhs_co_1_number",
                            "variable_id": "8113951d-6ab2-48ff-af64-ab96579abd0b",
                            "data_type_id": 1,
                            "display_name": "Number",
                            "system_generated": False,
                            "source_variable_id": "co_1_number",
                            "source_cf_meta_data": None,
                        },
                        {
                            "source_id": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                            "is_primary": False,
                            "source_type": "datasheet",
                            "system_name": "ts_1_lhs_rhs_co_1_boolean",
                            "variable_id": "c66f062b-4bb1-46c8-9f4d-d3b3663ee1f2__ss__1",
                            "data_type_id": 3,
                            "display_name": "Boolean",
                            "system_generated": False,
                            "source_variable_id": "co_1_boolean",
                            "source_cf_meta_data": None,
                        },
                        {
                            "source_id": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                            "is_primary": False,
                            "source_type": "datasheet",
                            "system_name": "ts_1_rhs_co_1_number",
                            "variable_id": "8113951d-6ab2-48ff-af64-ab96579abd0b__ss__2",
                            "data_type_id": 1,
                            "display_name": "Number",
                            "source_variable_id": "co_1_number",
                            "source_cf_meta_data": None,
                        },
                        {
                            "source_id": "user",
                            "is_primary": False,
                            "is_selected": True,
                            "source_name": "User",
                            "source_type": "report",
                            "is_enriched_variable": False,
                            "system_name": "ts_2_effective_start_date",
                            "variable_id": "effective_start_date_user",
                            "data_type_id": 2,
                            "display_name": "Effective Start Date",
                            "source_variable_id": None,
                        },
                        {
                            "source_id": "user",
                            "is_primary": False,
                            "is_selected": True,
                            "source_name": "User",
                            "source_type": "report",
                            "is_enriched_variable": False,
                            "system_name": "ts_2_effective_end_date",
                            "variable_id": "effective_end_date_user",
                            "data_type_id": 2,
                            "display_name": "Effective End Date",
                            "source_variable_id": None,
                        },
                        {
                            "source_id": "user",
                            "is_primary": False,
                            "is_selected": True,
                            "source_name": "User",
                            "source_type": "report",
                            "is_enriched_variable": False,
                            "system_name": "ts_2_employee_id",
                            "variable_id": "employee_id_user",
                            "data_type_id": 4,
                            "display_name": "Employee Id",
                            "source_variable_id": None,
                        },
                        {
                            "source_id": "user",
                            "is_primary": False,
                            "is_selected": True,
                            "source_name": "User",
                            "source_type": "report",
                            "is_enriched_variable": False,
                            "system_name": "ts_2_joining_date",
                            "variable_id": "joining_date_user",
                            "data_type_id": 2,
                            "display_name": "Joining Date",
                            "source_variable_id": None,
                        },
                        {
                            "source_id": "user",
                            "is_primary": False,
                            "is_selected": True,
                            "source_name": "User",
                            "source_type": "report",
                            "is_enriched_variable": False,
                            "system_name": "ts_2_time_zone",
                            "variable_id": "time_zone_user",
                            "data_type_id": 4,
                            "display_name": "Current Time Zone",
                            "source_variable_id": None,
                        },
                        {
                            "source_id": "user",
                            "is_primary": False,
                            "is_selected": True,
                            "source_name": "User",
                            "source_type": "report",
                            "is_enriched_variable": False,
                            "system_name": "ts_2_designation",
                            "variable_id": "designation_user",
                            "data_type_id": 4,
                            "display_name": "Designation",
                            "source_variable_id": None,
                        },
                        {
                            "source_id": "user",
                            "is_primary": False,
                            "is_selected": True,
                            "source_name": "User",
                            "source_type": "report",
                            "is_enriched_variable": False,
                            "system_name": "ts_2_employment_country",
                            "variable_id": "employment_country_user",
                            "data_type_id": 4,
                            "display_name": "Employment Country",
                            "source_variable_id": None,
                        },
                        {
                            "source_id": "user",
                            "is_primary": False,
                            "is_selected": True,
                            "source_name": "User",
                            "source_type": "report",
                            "is_enriched_variable": False,
                            "system_name": "ts_2_payout_frequency",
                            "variable_id": "payout_frequency_user",
                            "data_type_id": 4,
                            "display_name": "Payout Frequency",
                            "source_variable_id": None,
                        },
                        {
                            "source_id": "user",
                            "is_primary": False,
                            "is_selected": True,
                            "source_name": "User",
                            "source_type": "report",
                            "is_enriched_variable": False,
                            "system_name": "ts_2_pay_currency",
                            "variable_id": "pay_currency_user",
                            "data_type_id": 4,
                            "display_name": "Payout Currency",
                            "source_variable_id": None,
                        },
                        {
                            "source_id": "user",
                            "is_primary": False,
                            "is_selected": True,
                            "source_name": "User",
                            "source_type": "report",
                            "is_enriched_variable": False,
                            "system_name": "ts_2_fixed_pay",
                            "variable_id": "fixed_pay_user",
                            "data_type_id": 1,
                            "display_name": "Fixed Pay",
                            "source_variable_id": None,
                        },
                        {
                            "source_id": "user",
                            "is_primary": False,
                            "is_selected": True,
                            "source_name": "User",
                            "source_type": "report",
                            "is_enriched_variable": False,
                            "system_name": "ts_2_payee_variable_pay",
                            "variable_id": "payee_variable_pay_user",
                            "data_type_id": 1,
                            "display_name": "Variable Pay",
                            "source_variable_id": None,
                        },
                        {
                            "source_id": "user",
                            "is_primary": False,
                            "is_selected": True,
                            "source_name": "User",
                            "source_type": "report",
                            "is_enriched_variable": False,
                            "system_name": "ts_2_first_name",
                            "variable_id": "first_name_user",
                            "data_type_id": 4,
                            "display_name": "First Name",
                            "source_variable_id": None,
                        },
                        {
                            "source_id": "user",
                            "is_primary": False,
                            "is_selected": True,
                            "source_name": "User",
                            "source_type": "report",
                            "is_enriched_variable": False,
                            "system_name": "ts_2_last_name",
                            "variable_id": "last_name_user",
                            "data_type_id": 4,
                            "display_name": "Last Name",
                            "source_variable_id": None,
                        },
                        {
                            "source_id": "user",
                            "is_primary": False,
                            "is_selected": True,
                            "source_name": "User",
                            "source_type": "report",
                            "is_enriched_variable": False,
                            "system_name": "ts_2_user_role",
                            "variable_id": "user_role_user",
                            "data_type_id": 4,
                            "display_name": "Current User Role",
                            "source_variable_id": None,
                        },
                        {
                            "source_id": "user",
                            "is_primary": False,
                            "is_selected": True,
                            "source_name": "User",
                            "source_type": "report",
                            "is_enriched_variable": False,
                            "system_name": "ts_2_created_date",
                            "variable_id": "created_date_user",
                            "data_type_id": 2,
                            "display_name": "Created Date",
                            "source_variable_id": None,
                        },
                        {
                            "source_id": "user",
                            "is_primary": False,
                            "is_selected": True,
                            "source_name": "User",
                            "source_type": "report",
                            "is_enriched_variable": False,
                            "system_name": "ts_2_created_by",
                            "variable_id": "created_by_user",
                            "data_type_id": 12,
                            "display_name": "Created By",
                            "source_variable_id": None,
                        },
                        {
                            "source_id": "user",
                            "is_primary": False,
                            "is_selected": True,
                            "source_name": "User",
                            "source_type": "report",
                            "is_enriched_variable": False,
                            "system_name": "ts_2_exit_date",
                            "variable_id": "exit_date_user",
                            "data_type_id": 2,
                            "display_name": "Exit Date",
                            "source_variable_id": None,
                        },
                        {
                            "source_id": "user",
                            "is_primary": False,
                            "is_selected": True,
                            "source_name": "User",
                            "source_type": "report",
                            "is_enriched_variable": False,
                            "system_name": "ts_2_last_commission_date",
                            "variable_id": "last_commission_date_user",
                            "data_type_id": 2,
                            "display_name": "Exit Last Commission Date",
                            "source_variable_id": None,
                        },
                        {
                            "source_id": "user",
                            "is_primary": False,
                            "is_selected": True,
                            "source_name": "User",
                            "source_type": "report",
                            "is_enriched_variable": False,
                            "system_name": "ts_2_status",
                            "variable_id": "status_user",
                            "data_type_id": 4,
                            "display_name": "Status",
                            "source_variable_id": None,
                        },
                        {
                            "source_id": "user",
                            "is_primary": False,
                            "is_selected": True,
                            "source_name": "User",
                            "source_type": "report",
                            "is_enriched_variable": False,
                            "system_name": "ts_2_reporting_manager_email_id",
                            "variable_id": "reporting_manager_email_id_user",
                            "data_type_id": 12,
                            "display_name": "Reporting Manager EmailId",
                            "source_variable_id": None,
                        },
                        {
                            "source_id": "user",
                            "is_primary": False,
                            "is_selected": True,
                            "source_name": "User",
                            "source_type": "report",
                            "is_enriched_variable": False,
                            "system_name": "ts_2_reporting_manager_name",
                            "variable_id": "reporting_manager_name_user",
                            "data_type_id": 4,
                            "display_name": "Reporting Manager Name",
                            "source_variable_id": None,
                        },
                        {
                            "source_id": "user",
                            "is_primary": False,
                            "is_selected": True,
                            "source_name": "User",
                            "source_type": "report",
                            "is_enriched_variable": False,
                            "system_name": "ts_2_active_plan",
                            "variable_id": "active_plan_user",
                            "data_type_id": 4,
                            "display_name": "Active Primary Plan",
                            "source_variable_id": None,
                        },
                        {
                            "source_id": "user",
                            "is_primary": False,
                            "is_selected": True,
                            "source_name": "User",
                            "source_type": "report",
                            "is_enriched_variable": False,
                            "system_name": "ts_2_active_spiffs",
                            "variable_id": "active_spiffs_user",
                            "data_type_id": 4,
                            "display_name": "Active Spiff Plan",
                            "source_variable_id": None,
                        },
                        {
                            "source_id": "user",
                            "is_primary": False,
                            "is_selected": True,
                            "source_name": "User",
                            "source_type": "report",
                            "is_enriched_variable": False,
                            "system_name": "ts_2_active_quota_categories",
                            "variable_id": "active_quota_categories_user",
                            "data_type_id": 4,
                            "display_name": "Active Quota Categories",
                            "source_variable_id": None,
                        },
                        {
                            "source_id": "user",
                            "is_primary": False,
                            "is_selected": True,
                            "source_name": "User",
                            "source_type": "report",
                            "is_enriched_variable": False,
                            "system_name": "ts_2_payee_or_manager",
                            "variable_id": "payee_or_manager_user",
                            "data_type_id": 4,
                            "display_name": "Payee/Manager",
                            "source_variable_id": None,
                        },
                    ],
                    "transformation_source_map": {
                        "user_report": 1,
                        "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb_datasheet": 3,
                    },
                    "source_ids": ["f962ea83-b58a-4ee4-983e-1dc07fe4d3fb", "user"],
                    "used_variable_ids": [
                        "c66f062b-4bb1-46c8-9f4d-d3b3663ee1f2__ss__1",
                        "8113951d-6ab2-48ff-af64-ab96579abd0b",
                        "a8f6f7d8-9731-4dc8-bc1a-328d093b389b__ss__1",
                        "c66f062b-4bb1-46c8-9f4d-d3b3663ee1f2",
                        "1b0b9972-764c-426a-891d-1d7338645519",
                        "a8f6f7d8-9731-4dc8-bc1a-328d093b389b",
                    ],
                    "meta": [
                        {
                            "source_id": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                            "source_type": "datasheet",
                            "input_columns": [
                                {
                                    "system_name": "ts_1_lhs_lhs_co_1_number",
                                    "data_type_id": 1,
                                    "display_name": "Number",
                                },
                                {
                                    "system_name": "ts_1_lhs_rhs_co_1_boolean",
                                    "data_type_id": 3,
                                    "display_name": "Boolean",
                                },
                                {
                                    "system_name": "ts_1_lhs_lhs_co_1_email",
                                    "data_type_id": 12,
                                    "display_name": "Email",
                                },
                                {
                                    "system_name": "ts_1_rhs_co_1_number",
                                    "data_type_id": 1,
                                    "display_name": "Number",
                                },
                            ],
                            "output_columns": [
                                {
                                    "system_name": "ts_1_lhs_lhs_co_1_number",
                                    "data_type_id": 1,
                                    "display_name": "Number",
                                },
                                {
                                    "system_name": "ts_1_lhs_rhs_co_1_boolean",
                                    "data_type_id": 3,
                                    "display_name": "Boolean",
                                },
                                {
                                    "system_name": "ts_1_rhs_co_1_number",
                                    "data_type_id": 1,
                                    "display_name": "Number",
                                },
                            ],
                            "email_id_column": {
                                "value": "lhs_lhs_co_1_email",
                                "variable_id": "1b0b9972-764c-426a-891d-1d7338645519",
                            },
                            "with_databook_id": "869b4b9b-5e9f-49e1-b24c-e4c5cee825b0",
                            "end_date_column": {"value": None, "variable_id": None},
                            "start_date_column": {"value": None, "variable_id": None},
                            "has_effective_dates": False,
                        },
                        {
                            "source_id": "user",
                            "source_type": "report",
                            "input_columns": [
                                {
                                    "system_name": "ts_2_effective_start_date",
                                    "data_type_id": 2,
                                    "display_name": "Effective Start Date",
                                },
                                {
                                    "system_name": "ts_2_effective_end_date",
                                    "data_type_id": 2,
                                    "display_name": "Effective End Date",
                                },
                                {
                                    "system_name": "ts_2_employee_id",
                                    "data_type_id": 4,
                                    "display_name": "Employee Id",
                                },
                                {
                                    "system_name": "ts_2_joining_date",
                                    "data_type_id": 2,
                                    "display_name": "Joining Date",
                                },
                                {
                                    "system_name": "ts_2_time_zone",
                                    "data_type_id": 4,
                                    "display_name": "Current Time Zone",
                                },
                                {
                                    "system_name": "ts_2_designation",
                                    "data_type_id": 4,
                                    "display_name": "Designation",
                                },
                                {
                                    "system_name": "ts_2_employment_country",
                                    "data_type_id": 4,
                                    "display_name": "Employment Country",
                                },
                                {
                                    "system_name": "ts_2_payout_frequency",
                                    "data_type_id": 4,
                                    "display_name": "Payout Frequency",
                                },
                                {
                                    "system_name": "ts_2_pay_currency",
                                    "data_type_id": 4,
                                    "display_name": "Payout Currency",
                                },
                                {
                                    "system_name": "ts_2_fixed_pay",
                                    "data_type_id": 1,
                                    "display_name": "Fixed Pay",
                                },
                                {
                                    "system_name": "ts_2_payee_variable_pay",
                                    "data_type_id": 1,
                                    "display_name": "Variable Pay",
                                },
                                {
                                    "system_name": "ts_2_first_name",
                                    "data_type_id": 4,
                                    "display_name": "First Name",
                                },
                                {
                                    "system_name": "ts_2_last_name",
                                    "data_type_id": 4,
                                    "display_name": "Last Name",
                                },
                                {
                                    "system_name": "ts_2_user_role",
                                    "data_type_id": 4,
                                    "display_name": "Current User Role",
                                },
                                {
                                    "system_name": "ts_2_created_date",
                                    "data_type_id": 2,
                                    "display_name": "Created Date",
                                },
                                {
                                    "system_name": "ts_2_created_by",
                                    "data_type_id": 12,
                                    "display_name": "Created By",
                                },
                                {
                                    "system_name": "ts_2_exit_date",
                                    "data_type_id": 2,
                                    "display_name": "Exit Date",
                                },
                                {
                                    "system_name": "ts_2_last_commission_date",
                                    "data_type_id": 2,
                                    "display_name": "Exit Last Commission Date",
                                },
                                {
                                    "system_name": "ts_2_status",
                                    "data_type_id": 4,
                                    "display_name": "Status",
                                },
                                {
                                    "system_name": "ts_2_employee_email_id",
                                    "data_type_id": 12,
                                    "display_name": "Employee Email Id",
                                },
                                {
                                    "system_name": "ts_2_reporting_manager_email_id",
                                    "data_type_id": 12,
                                    "display_name": "Reporting Manager EmailId",
                                },
                                {
                                    "system_name": "ts_2_reporting_manager_name",
                                    "data_type_id": 4,
                                    "display_name": "Reporting Manager Name",
                                },
                                {
                                    "system_name": "ts_2_active_plan",
                                    "data_type_id": 4,
                                    "display_name": "Active Primary Plan",
                                },
                                {
                                    "system_name": "ts_2_active_spiffs",
                                    "data_type_id": 4,
                                    "display_name": "Active Spiff Plan",
                                },
                                {
                                    "system_name": "ts_2_active_quota_categories",
                                    "data_type_id": 4,
                                    "display_name": "Active Quota Categories",
                                },
                                {
                                    "system_name": "ts_2_payee_or_manager",
                                    "data_type_id": 4,
                                    "display_name": "Payee/Manager",
                                },
                            ],
                            "output_columns": [
                                {
                                    "system_name": "ts_2_effective_start_date",
                                    "data_type_id": 2,
                                    "display_name": "Effective Start Date",
                                },
                                {
                                    "system_name": "ts_2_effective_end_date",
                                    "data_type_id": 2,
                                    "display_name": "Effective End Date",
                                },
                                {
                                    "system_name": "ts_2_employee_id",
                                    "data_type_id": 4,
                                    "display_name": "Employee Id",
                                },
                                {
                                    "system_name": "ts_2_joining_date",
                                    "data_type_id": 2,
                                    "display_name": "Joining Date",
                                },
                                {
                                    "system_name": "ts_2_time_zone",
                                    "data_type_id": 4,
                                    "display_name": "Current Time Zone",
                                },
                                {
                                    "system_name": "ts_2_designation",
                                    "data_type_id": 4,
                                    "display_name": "Designation",
                                },
                                {
                                    "system_name": "ts_2_employment_country",
                                    "data_type_id": 4,
                                    "display_name": "Employment Country",
                                },
                                {
                                    "system_name": "ts_2_payout_frequency",
                                    "data_type_id": 4,
                                    "display_name": "Payout Frequency",
                                },
                                {
                                    "system_name": "ts_2_pay_currency",
                                    "data_type_id": 4,
                                    "display_name": "Payout Currency",
                                },
                                {
                                    "system_name": "ts_2_fixed_pay",
                                    "data_type_id": 1,
                                    "display_name": "Fixed Pay",
                                },
                                {
                                    "system_name": "ts_2_payee_variable_pay",
                                    "data_type_id": 1,
                                    "display_name": "Variable Pay",
                                },
                                {
                                    "system_name": "ts_2_first_name",
                                    "data_type_id": 4,
                                    "display_name": "First Name",
                                },
                                {
                                    "system_name": "ts_2_last_name",
                                    "data_type_id": 4,
                                    "display_name": "Last Name",
                                },
                                {
                                    "system_name": "ts_2_user_role",
                                    "data_type_id": 4,
                                    "display_name": "Current User Role",
                                },
                                {
                                    "system_name": "ts_2_created_date",
                                    "data_type_id": 2,
                                    "display_name": "Created Date",
                                },
                                {
                                    "system_name": "ts_2_created_by",
                                    "data_type_id": 12,
                                    "display_name": "Created By",
                                },
                                {
                                    "system_name": "ts_2_exit_date",
                                    "data_type_id": 2,
                                    "display_name": "Exit Date",
                                },
                                {
                                    "system_name": "ts_2_last_commission_date",
                                    "data_type_id": 2,
                                    "display_name": "Exit Last Commission Date",
                                },
                                {
                                    "system_name": "ts_2_status",
                                    "data_type_id": 4,
                                    "display_name": "Status",
                                },
                                {
                                    "system_name": "ts_2_reporting_manager_email_id",
                                    "data_type_id": 12,
                                    "display_name": "Reporting Manager EmailId",
                                },
                                {
                                    "system_name": "ts_2_reporting_manager_name",
                                    "data_type_id": 4,
                                    "display_name": "Reporting Manager Name",
                                },
                                {
                                    "system_name": "ts_2_active_plan",
                                    "data_type_id": 4,
                                    "display_name": "Active Primary Plan",
                                },
                                {
                                    "system_name": "ts_2_active_spiffs",
                                    "data_type_id": 4,
                                    "display_name": "Active Spiff Plan",
                                },
                                {
                                    "system_name": "ts_2_active_quota_categories",
                                    "data_type_id": 4,
                                    "display_name": "Active Quota Categories",
                                },
                                {
                                    "system_name": "ts_2_payee_or_manager",
                                    "data_type_id": 4,
                                    "display_name": "Payee/Manager",
                                },
                            ],
                            "email_id_column": {
                                "value": "employee_email_id",
                                "variable_id": "employee_email_id_user",
                            },
                            "with_databook_id": None,
                            "end_date_column": {"value": None, "variable_id": None},
                            "start_date_column": {"value": None, "variable_id": None},
                            "has_effective_dates": False,
                        },
                    ],
                },
                {
                    "key": "c4fdb86c-83a9-4a65-90e6-c759d2f7d805",
                    "type": "GET_USER_PROPERTIES",
                    "transformation_id": UUID("c4fdb86c-83a9-4a65-90e6-c759d2f7d805"),
                    "is_valid": True,
                    "output_columns": [
                        {
                            "meta_data": None,
                            "source_id": "57cbfd39-b826-4829-89bf-e4e4b2225076",
                            "is_primary": True,
                            "is_selected": True,
                            "source_type": "datasheet",
                            "system_name": "ts_employee_email_id",
                            "variable_id": "970e849a-1240-4296-b15b-f37696ecf985",
                            "data_type_id": 12,
                            "display_name": "TS::Employee Email Id",
                            "system_generated": True,
                            "source_variable_id": None,
                            "source_cf_meta_data": None,
                        },
                        {
                            "meta_data": None,
                            "source_id": "57cbfd39-b826-4829-89bf-e4e4b2225076",
                            "is_primary": True,
                            "is_selected": True,
                            "source_type": "datasheet",
                            "system_name": "ts_effective_start_date",
                            "variable_id": "65b9fe83-6fa6-40ad-b1cf-a037e5517ae5",
                            "data_type_id": 2,
                            "display_name": "TS::Effective Start Date",
                            "system_generated": True,
                            "source_variable_id": None,
                            "source_cf_meta_data": None,
                        },
                        {
                            "meta_data": None,
                            "source_id": "57cbfd39-b826-4829-89bf-e4e4b2225076",
                            "is_primary": True,
                            "is_selected": True,
                            "source_type": "datasheet",
                            "system_name": "ts_effective_end_date",
                            "variable_id": "a0cb521b-2728-4b21-a4fc-62d56aa004b9",
                            "data_type_id": 2,
                            "display_name": "TS::Effective End Date",
                            "system_generated": True,
                            "source_variable_id": None,
                            "source_cf_meta_data": None,
                        },
                        {
                            "source_id": "1",
                            "is_primary": False,
                            "source_type": "object",
                            "system_name": "ts_1_lhs_lhs_co_1_number",
                            "variable_id": "8113951d-6ab2-48ff-af64-ab96579abd0b",
                            "data_type_id": 1,
                            "display_name": "Number",
                            "system_generated": False,
                            "source_variable_id": "co_1_number",
                            "source_cf_meta_data": None,
                        },
                        {
                            "source_id": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                            "is_primary": False,
                            "source_type": "datasheet",
                            "system_name": "ts_1_lhs_rhs_co_1_boolean",
                            "variable_id": "c66f062b-4bb1-46c8-9f4d-d3b3663ee1f2__ss__1",
                            "data_type_id": 3,
                            "display_name": "Boolean",
                            "system_generated": False,
                            "source_variable_id": "co_1_boolean",
                            "source_cf_meta_data": None,
                        },
                        {
                            "source_id": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                            "is_primary": False,
                            "source_type": "datasheet",
                            "system_name": "ts_1_rhs_co_1_number",
                            "variable_id": "8113951d-6ab2-48ff-af64-ab96579abd0b__ss__2",
                            "data_type_id": 1,
                            "display_name": "Number",
                            "source_variable_id": "co_1_number",
                            "source_cf_meta_data": None,
                        },
                        {
                            "source_id": "user",
                            "is_primary": False,
                            "is_selected": True,
                            "source_name": "User",
                            "source_type": "report",
                            "is_enriched_variable": False,
                            "system_name": "ts_2_effective_start_date",
                            "variable_id": "effective_start_date_user",
                            "data_type_id": 2,
                            "display_name": "Effective Start Date",
                            "source_variable_id": None,
                        },
                        {
                            "source_id": "user",
                            "is_primary": False,
                            "is_selected": True,
                            "source_name": "User",
                            "source_type": "report",
                            "is_enriched_variable": False,
                            "system_name": "ts_2_effective_end_date",
                            "variable_id": "effective_end_date_user",
                            "data_type_id": 2,
                            "display_name": "Effective End Date",
                            "source_variable_id": None,
                        },
                        {
                            "source_id": "user",
                            "is_primary": False,
                            "is_selected": True,
                            "source_name": "User",
                            "source_type": "report",
                            "is_enriched_variable": False,
                            "system_name": "ts_2_employee_id",
                            "variable_id": "employee_id_user",
                            "data_type_id": 4,
                            "display_name": "Employee Id",
                            "source_variable_id": None,
                        },
                        {
                            "source_id": "user",
                            "is_primary": False,
                            "is_selected": True,
                            "source_name": "User",
                            "source_type": "report",
                            "is_enriched_variable": False,
                            "system_name": "ts_2_joining_date",
                            "variable_id": "joining_date_user",
                            "data_type_id": 2,
                            "display_name": "Joining Date",
                            "source_variable_id": None,
                        },
                        {
                            "source_id": "user",
                            "is_primary": False,
                            "is_selected": True,
                            "source_name": "User",
                            "source_type": "report",
                            "is_enriched_variable": False,
                            "system_name": "ts_2_time_zone",
                            "variable_id": "time_zone_user",
                            "data_type_id": 4,
                            "display_name": "Current Time Zone",
                            "source_variable_id": None,
                        },
                        {
                            "source_id": "user",
                            "is_primary": False,
                            "is_selected": True,
                            "source_name": "User",
                            "source_type": "report",
                            "is_enriched_variable": False,
                            "system_name": "ts_2_designation",
                            "variable_id": "designation_user",
                            "data_type_id": 4,
                            "display_name": "Designation",
                            "source_variable_id": None,
                        },
                        {
                            "source_id": "user",
                            "is_primary": False,
                            "is_selected": True,
                            "source_name": "User",
                            "source_type": "report",
                            "is_enriched_variable": False,
                            "system_name": "ts_2_employment_country",
                            "variable_id": "employment_country_user",
                            "data_type_id": 4,
                            "display_name": "Employment Country",
                            "source_variable_id": None,
                        },
                        {
                            "source_id": "user",
                            "is_primary": False,
                            "is_selected": True,
                            "source_name": "User",
                            "source_type": "report",
                            "is_enriched_variable": False,
                            "system_name": "ts_2_payout_frequency",
                            "variable_id": "payout_frequency_user",
                            "data_type_id": 4,
                            "display_name": "Payout Frequency",
                            "source_variable_id": None,
                        },
                        {
                            "source_id": "user",
                            "is_primary": False,
                            "is_selected": True,
                            "source_name": "User",
                            "source_type": "report",
                            "is_enriched_variable": False,
                            "system_name": "ts_2_pay_currency",
                            "variable_id": "pay_currency_user",
                            "data_type_id": 4,
                            "display_name": "Payout Currency",
                            "source_variable_id": None,
                        },
                        {
                            "source_id": "user",
                            "is_primary": False,
                            "is_selected": True,
                            "source_name": "User",
                            "source_type": "report",
                            "is_enriched_variable": False,
                            "system_name": "ts_2_fixed_pay",
                            "variable_id": "fixed_pay_user",
                            "data_type_id": 1,
                            "display_name": "Fixed Pay",
                            "source_variable_id": None,
                        },
                        {
                            "source_id": "user",
                            "is_primary": False,
                            "is_selected": True,
                            "source_name": "User",
                            "source_type": "report",
                            "is_enriched_variable": False,
                            "system_name": "ts_2_payee_variable_pay",
                            "variable_id": "payee_variable_pay_user",
                            "data_type_id": 1,
                            "display_name": "Variable Pay",
                            "source_variable_id": None,
                        },
                        {
                            "source_id": "user",
                            "is_primary": False,
                            "is_selected": True,
                            "source_name": "User",
                            "source_type": "report",
                            "is_enriched_variable": False,
                            "system_name": "ts_2_first_name",
                            "variable_id": "first_name_user",
                            "data_type_id": 4,
                            "display_name": "First Name",
                            "source_variable_id": None,
                        },
                        {
                            "source_id": "user",
                            "is_primary": False,
                            "is_selected": True,
                            "source_name": "User",
                            "source_type": "report",
                            "is_enriched_variable": False,
                            "system_name": "ts_2_last_name",
                            "variable_id": "last_name_user",
                            "data_type_id": 4,
                            "display_name": "Last Name",
                            "source_variable_id": None,
                        },
                        {
                            "source_id": "user",
                            "is_primary": False,
                            "is_selected": True,
                            "source_name": "User",
                            "source_type": "report",
                            "is_enriched_variable": False,
                            "system_name": "ts_2_user_role",
                            "variable_id": "user_role_user",
                            "data_type_id": 4,
                            "display_name": "Current User Role",
                            "source_variable_id": None,
                        },
                        {
                            "source_id": "user",
                            "is_primary": False,
                            "is_selected": True,
                            "source_name": "User",
                            "source_type": "report",
                            "is_enriched_variable": False,
                            "system_name": "ts_2_created_date",
                            "variable_id": "created_date_user",
                            "data_type_id": 2,
                            "display_name": "Created Date",
                            "source_variable_id": None,
                        },
                        {
                            "source_id": "user",
                            "is_primary": False,
                            "is_selected": True,
                            "source_name": "User",
                            "source_type": "report",
                            "is_enriched_variable": False,
                            "system_name": "ts_2_created_by",
                            "variable_id": "created_by_user",
                            "data_type_id": 12,
                            "display_name": "Created By",
                            "source_variable_id": None,
                        },
                        {
                            "source_id": "user",
                            "is_primary": False,
                            "is_selected": True,
                            "source_name": "User",
                            "source_type": "report",
                            "is_enriched_variable": False,
                            "system_name": "ts_2_exit_date",
                            "variable_id": "exit_date_user",
                            "data_type_id": 2,
                            "display_name": "Exit Date",
                            "source_variable_id": None,
                        },
                        {
                            "source_id": "user",
                            "is_primary": False,
                            "is_selected": True,
                            "source_name": "User",
                            "source_type": "report",
                            "is_enriched_variable": False,
                            "system_name": "ts_2_last_commission_date",
                            "variable_id": "last_commission_date_user",
                            "data_type_id": 2,
                            "display_name": "Exit Last Commission Date",
                            "source_variable_id": None,
                        },
                        {
                            "source_id": "user",
                            "is_primary": False,
                            "is_selected": True,
                            "source_name": "User",
                            "source_type": "report",
                            "is_enriched_variable": False,
                            "system_name": "ts_2_status",
                            "variable_id": "status_user",
                            "data_type_id": 4,
                            "display_name": "Status",
                            "source_variable_id": None,
                        },
                        {
                            "source_id": "user",
                            "is_primary": False,
                            "is_selected": True,
                            "source_name": "User",
                            "source_type": "report",
                            "is_enriched_variable": False,
                            "system_name": "ts_2_reporting_manager_email_id",
                            "variable_id": "reporting_manager_email_id_user",
                            "data_type_id": 12,
                            "display_name": "Reporting Manager EmailId",
                            "source_variable_id": None,
                        },
                        {
                            "source_id": "user",
                            "is_primary": False,
                            "is_selected": True,
                            "source_name": "User",
                            "source_type": "report",
                            "is_enriched_variable": False,
                            "system_name": "ts_2_reporting_manager_name",
                            "variable_id": "reporting_manager_name_user",
                            "data_type_id": 4,
                            "display_name": "Reporting Manager Name",
                            "source_variable_id": None,
                        },
                        {
                            "source_id": "user",
                            "is_primary": False,
                            "is_selected": True,
                            "source_name": "User",
                            "source_type": "report",
                            "is_enriched_variable": False,
                            "system_name": "ts_2_active_plan",
                            "variable_id": "active_plan_user",
                            "data_type_id": 4,
                            "display_name": "Active Primary Plan",
                            "source_variable_id": None,
                        },
                        {
                            "source_id": "user",
                            "is_primary": False,
                            "is_selected": True,
                            "source_name": "User",
                            "source_type": "report",
                            "is_enriched_variable": False,
                            "system_name": "ts_2_active_spiffs",
                            "variable_id": "active_spiffs_user",
                            "data_type_id": 4,
                            "display_name": "Active Spiff Plan",
                            "source_variable_id": None,
                        },
                        {
                            "source_id": "user",
                            "is_primary": False,
                            "is_selected": True,
                            "source_name": "User",
                            "source_type": "report",
                            "is_enriched_variable": False,
                            "system_name": "ts_2_active_quota_categories",
                            "variable_id": "active_quota_categories_user",
                            "data_type_id": 4,
                            "display_name": "Active Quota Categories",
                            "source_variable_id": None,
                        },
                        {
                            "source_id": "user",
                            "is_primary": False,
                            "is_selected": True,
                            "source_name": "User",
                            "source_type": "report",
                            "is_enriched_variable": False,
                            "system_name": "ts_2_payee_or_manager",
                            "variable_id": "payee_or_manager_user",
                            "data_type_id": 4,
                            "display_name": "Payee/Manager",
                            "source_variable_id": None,
                        },
                        {
                            "meta_data": None,
                            "source_id": "user",
                            "is_primary": False,
                            "is_selected": True,
                            "source_type": "report",
                            "system_name": "up_ts_employee_email_id_ts_2_effective_end_date_payout_frequency_d805",
                            "variable_id": "payout_frequency_user__ss__2",
                            "data_type_id": 4,
                            "display_name": "Payout Frequency",
                            "source_variable_id": None,
                            "source_cf_meta_data": None,
                        },
                        {
                            "meta_data": None,
                            "source_id": "user",
                            "is_primary": False,
                            "is_selected": True,
                            "source_type": "report",
                            "system_name": "up_ts_employee_email_id_ts_2_effective_end_date_employment_country_d805",
                            "variable_id": "employment_country_user__ss__2",
                            "data_type_id": 4,
                            "display_name": "Employment Country",
                            "source_variable_id": None,
                            "source_cf_meta_data": None,
                        },
                    ],
                    "transformation_source_map": {
                        "user_report": 2,
                        "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb_datasheet": 3,
                    },
                    "source_ids": ["f962ea83-b58a-4ee4-983e-1dc07fe4d3fb", "user"],
                    "used_variable_ids": [
                        "c66f062b-4bb1-46c8-9f4d-d3b3663ee1f2__ss__1",
                        "8113951d-6ab2-48ff-af64-ab96579abd0b",
                        "a8f6f7d8-9731-4dc8-bc1a-328d093b389b__ss__1",
                        "c66f062b-4bb1-46c8-9f4d-d3b3663ee1f2",
                        "970e849a-1240-4296-b15b-f37696ecf985",
                        "effective_end_date_user",
                        "1b0b9972-764c-426a-891d-1d7338645519",
                        "a8f6f7d8-9731-4dc8-bc1a-328d093b389b",
                    ],
                    "email_column": {
                        "value": "ts_employee_email_id",
                        "variable_id": "970e849a-1240-4296-b15b-f37696ecf985",
                    },
                    "as_of_date_column": {
                        "value": "ts_2_effective_end_date",
                        "variable_id": "effective_end_date_user",
                    },
                    "user_properties": [
                        {
                            "user_property_system_name": "payout_frequency",
                            "data_type_id": 4,
                            "output_variable_system_name": "up_ts_employee_email_id_ts_2_effective_end_date_payout_frequency_d805",
                            "display_name": "Payout Frequency",
                            "variable_id": "c578f2cd-6e02-4ff5-bebd-1304486043fc",
                        },
                        {
                            "user_property_system_name": "employment_country",
                            "data_type_id": 4,
                            "output_variable_system_name": "up_ts_employee_email_id_ts_2_effective_end_date_employment_country_d805",
                            "display_name": "Employment Country",
                            "variable_id": "04700c01-e2a5-44ff-b66e-ce50abc52956",
                        },
                    ],
                },
                {
                    "key": "7f67c8fd-59d9-4aad-9e58-5b832b5ad228",
                    "type": "JOIN",
                    "transformation_id": UUID("7f67c8fd-59d9-4aad-9e58-5b832b5ad228"),
                    "is_valid": True,
                    "output_columns": [
                        {
                            "source_id": "57cbfd39-b826-4829-89bf-e4e4b2225076",
                            "is_primary": True,
                            "source_type": "datasheet",
                            "system_name": "lhs_ts_employee_email_id",
                            "variable_id": "970e849a-1240-4296-b15b-f37696ecf985",
                            "data_type_id": 12,
                            "display_name": "TS::Employee Email Id",
                            "system_generated": True,
                            "source_variable_id": None,
                            "source_cf_meta_data": None,
                        },
                        {
                            "source_id": "57cbfd39-b826-4829-89bf-e4e4b2225076",
                            "is_primary": True,
                            "source_type": "datasheet",
                            "system_name": "lhs_ts_effective_start_date",
                            "variable_id": "65b9fe83-6fa6-40ad-b1cf-a037e5517ae5",
                            "data_type_id": 2,
                            "display_name": "TS::Effective Start Date",
                            "system_generated": True,
                            "source_variable_id": None,
                            "source_cf_meta_data": None,
                        },
                        {
                            "source_id": "57cbfd39-b826-4829-89bf-e4e4b2225076",
                            "is_primary": True,
                            "source_type": "datasheet",
                            "system_name": "lhs_ts_effective_end_date",
                            "variable_id": "a0cb521b-2728-4b21-a4fc-62d56aa004b9",
                            "data_type_id": 2,
                            "display_name": "TS::Effective End Date",
                            "system_generated": True,
                            "source_variable_id": None,
                            "source_cf_meta_data": None,
                        },
                        {
                            "source_id": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                            "is_primary": True,
                            "source_type": "datasheet",
                            "system_name": "rhs_co_1_number",
                            "variable_id": "8113951d-6ab2-48ff-af64-ab96579abd0b__ss__4",
                            "data_type_id": 1,
                            "display_name": "Number",
                            "source_variable_id": "co_1_number",
                            "source_cf_meta_data": None,
                        },
                    ],
                    "transformation_source_map": {
                        "user_report": 2,
                        "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb_datasheet": 4,
                    },
                    "source_ids": ["f962ea83-b58a-4ee4-983e-1dc07fe4d3fb", "user"],
                    "used_variable_ids": [
                        "c66f062b-4bb1-46c8-9f4d-d3b3663ee1f2__ss__1",
                        "8113951d-6ab2-48ff-af64-ab96579abd0b",
                        "a8f6f7d8-9731-4dc8-bc1a-328d093b389b__ss__1",
                        "65b9fe83-6fa6-40ad-b1cf-a037e5517ae5",
                        "970e849a-1240-4296-b15b-f37696ecf985",
                        "1b0b9972-764c-426a-891d-1d7338645519",
                        "a8f6f7d8-9731-4dc8-bc1a-328d093b389b",
                        "first_name_user",
                        "a7f84b61-17d5-4d73-88e2-5ee9996649a6",
                        "a0cb521b-2728-4b21-a4fc-62d56aa004b9",
                        "c66f062b-4bb1-46c8-9f4d-d3b3663ee1f2",
                        "effective_end_date_user",
                    ],
                    "on": {
                        "lhs": [
                            {
                                "source_id": "user",
                                "is_primary": False,
                                "source_type": "report",
                                "system_name": "ts_2_first_name",
                                "variable_id": "first_name_user",
                                "data_type_id": 4,
                                "display_name": "First Name",
                                "source_variable_id": None,
                                "source_cf_meta_data": None,
                            }
                        ],
                        "rhs": [
                            {
                                "is_primary": False,
                                "system_name": "co_1_name",
                                "variable_id": "a7f84b61-17d5-4d73-88e2-5ee9996649a6",
                                "data_type_id": 4,
                                "display_name": "Name",
                                "source_variable_id": "co_1_name",
                                "source_cf_meta_data": None,
                            }
                        ],
                    },
                    "columns": {
                        "lhs": [
                            {
                                "source_id": "57cbfd39-b826-4829-89bf-e4e4b2225076",
                                "is_primary": True,
                                "source_type": "datasheet",
                                "system_name": "ts_employee_email_id",
                                "variable_id": "970e849a-1240-4296-b15b-f37696ecf985",
                                "data_type_id": 12,
                                "display_name": "TS::Employee Email Id",
                                "system_generated": True,
                                "source_variable_id": None,
                                "source_cf_meta_data": None,
                            },
                            {
                                "source_id": "57cbfd39-b826-4829-89bf-e4e4b2225076",
                                "is_primary": True,
                                "source_type": "datasheet",
                                "system_name": "ts_effective_start_date",
                                "variable_id": "65b9fe83-6fa6-40ad-b1cf-a037e5517ae5",
                                "data_type_id": 2,
                                "display_name": "TS::Effective Start Date",
                                "system_generated": True,
                                "source_variable_id": None,
                                "source_cf_meta_data": None,
                            },
                            {
                                "source_id": "57cbfd39-b826-4829-89bf-e4e4b2225076",
                                "is_primary": True,
                                "source_type": "datasheet",
                                "system_name": "ts_effective_end_date",
                                "variable_id": "a0cb521b-2728-4b21-a4fc-62d56aa004b9",
                                "data_type_id": 2,
                                "display_name": "TS::Effective End Date",
                                "system_generated": True,
                                "source_variable_id": None,
                                "source_cf_meta_data": None,
                            },
                        ],
                        "rhs": [
                            {
                                "is_primary": True,
                                "system_name": "co_1_number",
                                "variable_id": "8113951d-6ab2-48ff-af64-ab96579abd0b",
                                "data_type_id": 1,
                                "display_name": "Number",
                                "source_variable_id": "co_1_number",
                                "source_cf_meta_data": None,
                            }
                        ],
                    },
                    "join_type": "LEFT",
                    "with": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                    "with_databook_id": "869b4b9b-5e9f-49e1-b24c-e4c5cee825b0",
                },
            ],
            "source_id": "869b4b9b-5e9f-49e1-b24c-e4c5cee825b0",
            "source_type": "datasheet",
        }
        # Removing used_variable_ids from both result and expected_response for comparison
        # since variable ids will be generated in runtime
        for transformation in result["transformations"]:
            transformation.pop("used_variable_ids")
            result_source_ids = transformation.pop("source_ids")
            for column in transformation["output_columns"]:
                if column.get("source_name_history"):
                    column.pop("source_name_history")

        for transformation in expected_response["transformations"]:
            transformation.pop("used_variable_ids")
            expected_source_ids = transformation.pop("source_ids")
            for column in transformation["output_columns"]:
                if column.get("source_name_history"):
                    column.pop("source_name_history")

        for variable in result["variables"]:
            variable.pop("source_name_history")

        for variable in expected_response["variables"]:
            variable.pop("source_name_history")

        self.maxDiff = None
        # since order of source_ids in list is not fixed, using assertCountEqual
        self.assertCountEqual(result_source_ids, expected_source_ids)
        self.assertDictEqual(result, expected_response)

    def _assert_variables_name(self, response, expected_variables_name):
        for var in response["variables"]:
            if var["system_name"] in expected_variables_name:
                assert (
                    var["display_name"] == expected_variables_name[var["system_name"]]
                )

    @mock.patch.object(DatasheetValidator, "_append_source_history_to_variables")
    def test_group_by_validator_function(self, mock_append_source_history_to_variables):
        """
        Test that variable display names are correctly maintained across multiple transformations.

        This test verifies that when applying a sequence of transformations (group by,
        renaming, adding aggregations, and joins), the display names of variables are
        properly preserved and updated according to the transformations applied.

        The test follows this sequence:
        1. Apply group by transformation and verify initial variable names
        2. Rename a group by variable and verify the name change is retained
        3. Add more aggregations to existing group by and verify new variable names
        4. Add join transformation and verify all variable names are correct

        This ensures that the datasheet validator correctly handles variable name
        persistence across complex transformation chains.
        """
        datasheet_id = UUID("382929d2-6294-4803-acc8-256ada17acec")
        databook_id = UUID("869b4b9b-5e9f-49e1-b24c-e4c5cee825b0")
        # return value should be arguments passed to _append_source_history_to_variables function
        mock_append_source_history_to_variables.side_effect = lambda x: x

        expected_variables_name = {
            "co_1_email": "Email",
            "co_1_name": "Name",
            "sum_co_1_number": "SUM::Number",
        }
        # 1. Group by transformation added
        response = validate_datasheet(
            DatasheetValidateRequest(
                client_id=TEST_CLIENT_ID,
                databook_id=databook_id,
                datasheet_id=datasheet_id,
                **get_group_by_transformation_data(),
            )
        ).model_dump(by_alias=True)

        self._assert_variables_name(response, expected_variables_name)

        # 2. Renaming group by variable
        expected_variables_name.update(
            {
                "sum_co_1_number": "Renamed sum of Number",
            }
        )
        response = validate_datasheet(
            DatasheetValidateRequest(
                client_id=TEST_CLIENT_ID,
                databook_id=databook_id,
                **get_renaming_group_by_variable_data(response),
            )
        ).model_dump(by_alias=True)

        self._assert_variables_name(response, expected_variables_name)

        # 3. Adding more aggregations to existing group by
        expected_variables_name.update(
            {
                "count_co_1_percentage": "COUNT::Percentage",
            }
        )
        response = validate_datasheet(
            DatasheetValidateRequest(
                client_id=TEST_CLIENT_ID,
                databook_id=databook_id,
                **get_adding_more_aggregations_to_existing_group_by_data(response),
            )
        ).model_dump(by_alias=True)

        self._assert_variables_name(response, expected_variables_name)

        # 4. Adding join transformation
        expected_variables_name = {
            "lhs_co_1_name": "Name",
            "lhs_co_1_email": "Email",
            "lhs_sum_co_1_number": "Renamed sum of Number",
            "lhs_count_co_1_percentage": "COUNT::Percentage",
            "rhs_effective_start_date": "Effective Start Date",
            "rhs_effective_end_date": "Effective End Date",
            "rhs_employee_email_id": "Employee Email Id",
        }

        response = validate_datasheet(
            DatasheetValidateRequest(
                client_id=TEST_CLIENT_ID,
                databook_id=databook_id,
                **get_adding_join_transformation_data(databook_id, response),
            )
        ).model_dump(by_alias=True)

        self._assert_variables_name(response, expected_variables_name)

    @mock.patch.object(DatasheetValidator, "_append_source_history_to_variables")
    def test_flatten_transformation_validator_function(
        self, mock_append_source_history_to_variables
    ):
        """
        Test that flatten transformation validator function is working as expected.
        """
        datasheet_id = UUID("7c5b57c2-109e-4e73-bf94-b2ab2c4f1aa6")
        databook_id = UUID("869b4b9b-5e9f-49e1-b24c-e4c5cee825b0")
        # return value should be arguments passed to _append_source_history_to_variables function
        mock_append_source_history_to_variables.side_effect = lambda x: x

        # 1. Add flatten transformation and validate
        response = validate_datasheet(
            DatasheetValidateRequest(
                client_id=TEST_CLIENT_ID,
                databook_id=databook_id,
                datasheet_id=datasheet_id,
                **get_flatten_transformation_data(),
            )
        ).model_dump(by_alias=True)

        expected_variables_name = {
            "cf_hierarchy_field_email_47c3": "Hierarchy Field as Email-flattened",
        }
        self._assert_variables_name(response, expected_variables_name)

        # 2. Rename flattened variable and validate
        response = validate_datasheet(
            DatasheetValidateRequest(
                client_id=TEST_CLIENT_ID,
                databook_id=databook_id,
                **get_renamed_flatten_variable_data(response),
            )
        ).model_dump(by_alias=True)

        expected_variables_name.update(
            {
                "cf_hierarchy_field_email_47c3": "HF Email flattened",
            }
        )
        self._assert_variables_name(response, expected_variables_name)

    def test_selected_variables_after_transformation(self):
        datasheet_validator_request_model = DatasheetValidateRequest(
            **{
                "client_id": TEST_CLIENT_ID,
                "datasheet_id": UUID("1090d6c2-0ef8-4990-bf48-359d45cb1dec"),
                "databook_id": UUID("869b4b9b-5e9f-49e1-b24c-e4c5cee825b0"),
                "initial_validation": True,
                "variables": [
                    {
                        "meta_data": None,
                        "source_cf_meta_data": None,
                        "source_id": "user",
                        "variable_id": "3524e64b-9003-4a03-b7c3-178ed39ad678",
                        "system_name": "effective_start_date",
                        "display_name": "Effective Start Date",
                        "data_type_id": 2,
                        "source_variable_id": "effective_start_date_user",
                        "source_type": "report",
                        "is_selected": True,
                        "is_primary": True,
                        "field_order": 0,
                    },
                    {
                        "meta_data": None,
                        "source_cf_meta_data": None,
                        "source_id": "user",
                        "variable_id": "02f933cd-ab07-4cb1-ac4b-0dcb005ec181",
                        "system_name": "effective_end_date",
                        "display_name": "Effective End Date",
                        "data_type_id": 2,
                        "source_variable_id": "effective_end_date_user",
                        "source_type": "report",
                        "is_selected": True,
                        "is_primary": True,
                        "field_order": 0,
                    },
                    {
                        "meta_data": None,
                        "source_cf_meta_data": None,
                        "source_id": "user",
                        "variable_id": "3904ae88-bdcf-40b0-ba99-16bc20bdf30e",
                        "system_name": "employee_id",
                        "display_name": "Employee Id",
                        "data_type_id": 4,
                        "source_variable_id": "employee_id_user",
                        "source_type": "report",
                        "is_selected": True,
                        "is_primary": False,
                        "field_order": 0,
                    },
                    {
                        "meta_data": None,
                        "source_cf_meta_data": None,
                        "source_id": "user",
                        "variable_id": "3e075f3c-6b0c-48fc-90e1-898b83d48f26",
                        "system_name": "joining_date",
                        "display_name": "Joining Date",
                        "data_type_id": 2,
                        "source_variable_id": "joining_date_user",
                        "source_type": "report",
                        "is_selected": True,
                        "is_primary": False,
                        "field_order": 0,
                    },
                    {
                        "meta_data": None,
                        "source_cf_meta_data": None,
                        "source_id": "user",
                        "variable_id": "10dd5a0d-7057-4aca-9120-9be77840da60",
                        "system_name": "time_zone",
                        "display_name": "Current Time Zone",
                        "data_type_id": 4,
                        "source_variable_id": "time_zone_user",
                        "source_type": "report",
                        "is_selected": False,
                        "is_primary": False,
                        "field_order": 0,
                    },
                    {
                        "meta_data": None,
                        "source_cf_meta_data": None,
                        "source_id": "user",
                        "variable_id": "49de7dca-ae52-41d2-9470-5717636bc533",
                        "system_name": "designation",
                        "display_name": "Designation",
                        "data_type_id": 4,
                        "source_variable_id": "designation_user",
                        "source_type": "report",
                        "is_selected": True,
                        "is_primary": False,
                        "field_order": 0,
                    },
                    {
                        "meta_data": None,
                        "source_cf_meta_data": None,
                        "source_id": "user",
                        "variable_id": "d898705d-db51-44cd-958c-e3a6013b2cf9",
                        "system_name": "employment_country",
                        "display_name": "Employment Country",
                        "data_type_id": 4,
                        "source_variable_id": "employment_country_user",
                        "source_type": "report",
                        "is_selected": True,
                        "is_primary": False,
                        "field_order": 0,
                    },
                    {
                        "meta_data": None,
                        "source_cf_meta_data": None,
                        "source_id": "user",
                        "variable_id": "8f0f6669-f0d0-4496-b906-d0c726c15639",
                        "system_name": "payout_frequency",
                        "display_name": "Payout Frequency",
                        "data_type_id": 4,
                        "source_variable_id": "payout_frequency_user",
                        "source_type": "report",
                        "is_selected": True,
                        "is_primary": False,
                        "field_order": 0,
                    },
                    {
                        "meta_data": None,
                        "source_cf_meta_data": None,
                        "source_id": "user",
                        "variable_id": "38632bfa-0759-47e7-a17b-98b3d5335aca",
                        "system_name": "pay_currency",
                        "display_name": "Payout Currency",
                        "data_type_id": 4,
                        "source_variable_id": "pay_currency_user",
                        "source_type": "report",
                        "is_selected": True,
                        "is_primary": False,
                        "field_order": 0,
                    },
                    {
                        "meta_data": None,
                        "source_cf_meta_data": None,
                        "source_id": "user",
                        "variable_id": "44278393-a436-4e0d-afbe-f543ff7cc02e",
                        "system_name": "fixed_pay",
                        "display_name": "Fixed Pay",
                        "data_type_id": 1,
                        "source_variable_id": "fixed_pay_user",
                        "source_type": "report",
                        "is_selected": True,
                        "is_primary": False,
                        "field_order": 0,
                    },
                    {
                        "meta_data": None,
                        "source_cf_meta_data": None,
                        "source_id": "user",
                        "variable_id": "4c2385f5-8b2f-45fa-8d77-cb6958a7a180",
                        "system_name": "payee_variable_pay",
                        "display_name": "Variable Pay",
                        "data_type_id": 1,
                        "source_variable_id": "payee_variable_pay_user",
                        "source_type": "report",
                        "is_selected": True,
                        "is_primary": False,
                        "field_order": 0,
                    },
                    {
                        "meta_data": None,
                        "source_cf_meta_data": None,
                        "source_id": "user",
                        "variable_id": "556aa3b4-c4b6-435d-96a2-dbfdbffe844b",
                        "system_name": "first_name",
                        "display_name": "First Name",
                        "data_type_id": 4,
                        "source_variable_id": "first_name_user",
                        "source_type": "report",
                        "is_selected": True,
                        "is_primary": False,
                        "field_order": 0,
                    },
                    {
                        "meta_data": None,
                        "source_cf_meta_data": None,
                        "source_id": "user",
                        "variable_id": "18625303-151e-40af-865b-aa30e8fc04ff",
                        "system_name": "last_name",
                        "display_name": "Last Name",
                        "data_type_id": 4,
                        "source_variable_id": "last_name_user",
                        "source_type": "report",
                        "is_selected": True,
                        "is_primary": False,
                        "field_order": 0,
                    },
                    {
                        "meta_data": None,
                        "source_cf_meta_data": None,
                        "source_id": "user",
                        "variable_id": "6d706ce0-46d4-4d9b-bdfc-f9303a5dbf39",
                        "system_name": "user_role",
                        "display_name": "Current User Role",
                        "data_type_id": 4,
                        "source_variable_id": "user_role_user",
                        "source_type": "report",
                        "is_selected": True,
                        "is_primary": False,
                        "field_order": 0,
                    },
                    {
                        "meta_data": None,
                        "source_cf_meta_data": None,
                        "source_id": "user",
                        "variable_id": "758c8f82-170e-4094-85f2-e29dcb3a903b",
                        "system_name": "created_date",
                        "display_name": "Created Date",
                        "data_type_id": 2,
                        "source_variable_id": "created_date_user",
                        "source_type": "report",
                        "is_selected": True,
                        "is_primary": False,
                        "field_order": 0,
                    },
                    {
                        "meta_data": None,
                        "source_cf_meta_data": None,
                        "source_id": "user",
                        "variable_id": "37790973-59ea-4f5e-b57c-165b17f7b9c7",
                        "system_name": "created_by",
                        "display_name": "Created By",
                        "data_type_id": 12,
                        "source_variable_id": "created_by_user",
                        "source_type": "report",
                        "is_selected": True,
                        "is_primary": False,
                        "field_order": 0,
                    },
                    {
                        "meta_data": None,
                        "source_cf_meta_data": None,
                        "source_id": "user",
                        "variable_id": "440e564c-6565-42d3-b334-7d9ebe7170d1",
                        "system_name": "exit_date",
                        "display_name": "Exit Date",
                        "data_type_id": 2,
                        "source_variable_id": "exit_date_user",
                        "source_type": "report",
                        "is_selected": True,
                        "is_primary": False,
                        "field_order": 0,
                    },
                    {
                        "meta_data": None,
                        "source_cf_meta_data": None,
                        "source_id": "user",
                        "variable_id": "1862a8af-58e2-41ba-86b0-f8654ab385e9",
                        "system_name": "last_commission_date",
                        "display_name": "Exit Last Commission Date",
                        "data_type_id": 2,
                        "source_variable_id": "last_commission_date_user",
                        "source_type": "report",
                        "is_selected": True,
                        "is_primary": False,
                        "field_order": 0,
                    },
                    {
                        "meta_data": None,
                        "source_cf_meta_data": None,
                        "source_id": "user",
                        "variable_id": "23fe6cc2-9872-4869-ac78-da43f1f1cdd6",
                        "system_name": "status",
                        "display_name": "Status",
                        "data_type_id": 4,
                        "source_variable_id": "status_user",
                        "source_type": "report",
                        "is_selected": True,
                        "is_primary": False,
                        "field_order": 0,
                    },
                    {
                        "meta_data": None,
                        "source_cf_meta_data": None,
                        "source_id": "user",
                        "variable_id": "effa65a1-1343-49bc-81ab-59b7000fd4c0",
                        "system_name": "employee_email_id",
                        "display_name": "Employee Email Id",
                        "data_type_id": 12,
                        "source_variable_id": "employee_email_id_user",
                        "source_type": "report",
                        "is_selected": True,
                        "is_primary": True,
                        "field_order": 0,
                    },
                    {
                        "meta_data": None,
                        "source_cf_meta_data": None,
                        "source_id": "user",
                        "variable_id": "92eb784e-d9e0-46be-89fb-91ff0d4acdc2",
                        "system_name": "reporting_manager_email_id",
                        "display_name": "Reporting Manager EmailId",
                        "data_type_id": 12,
                        "source_variable_id": "reporting_manager_email_id_user",
                        "source_type": "report",
                        "is_selected": True,
                        "is_primary": False,
                        "field_order": 0,
                    },
                    {
                        "meta_data": None,
                        "source_cf_meta_data": None,
                        "source_id": "user",
                        "variable_id": "115538cf-fb87-461b-bc72-246e4386719d",
                        "system_name": "reporting_manager_name",
                        "display_name": "Reporting Manager Name",
                        "data_type_id": 4,
                        "source_variable_id": "reporting_manager_name_user",
                        "source_type": "report",
                        "is_selected": True,
                        "is_primary": False,
                        "field_order": 0,
                    },
                    {
                        "meta_data": None,
                        "source_cf_meta_data": None,
                        "source_id": "user",
                        "variable_id": "cf399817-e61c-4d3c-b026-c89101fe6224",
                        "system_name": "active_plan",
                        "display_name": "Active Primary Plan",
                        "data_type_id": 4,
                        "source_variable_id": "active_plan_user",
                        "source_type": "report",
                        "is_selected": True,
                        "is_primary": False,
                        "field_order": 0,
                    },
                    {
                        "meta_data": None,
                        "source_cf_meta_data": None,
                        "source_id": "user",
                        "variable_id": "feeb76cf-4f21-4bbe-a9f8-e2acaa533434",
                        "system_name": "active_spiffs",
                        "display_name": "Active Spiff Plan",
                        "data_type_id": 4,
                        "source_variable_id": "active_spiffs_user",
                        "source_type": "report",
                        "is_selected": True,
                        "is_primary": False,
                        "field_order": 0,
                    },
                    {
                        "meta_data": None,
                        "source_cf_meta_data": None,
                        "source_id": "user",
                        "variable_id": "8f0e184d-4456-4c9f-9147-737169f4fd04",
                        "system_name": "active_quota_categories",
                        "display_name": "Active Quota Categories",
                        "data_type_id": 4,
                        "source_variable_id": "active_quota_categories_user",
                        "source_type": "report",
                        "is_selected": True,
                        "is_primary": False,
                        "field_order": 0,
                    },
                    {
                        "meta_data": None,
                        "source_cf_meta_data": None,
                        "source_id": "user",
                        "variable_id": "d6b4be43-8f5f-43fe-ac30-900e9d3eee8b",
                        "system_name": "payee_or_manager",
                        "display_name": "Payee/Manager",
                        "data_type_id": 4,
                        "source_variable_id": "payee_or_manager_user",
                        "source_type": "report",
                        "is_selected": True,
                        "is_primary": False,
                        "field_order": 0,
                    },
                ],
                "source_variables": [
                    {
                        "meta_data": None,
                        "source_cf_meta_data": None,
                        "source_id": "user",
                        "variable_id": "effective_start_date_user",
                        "system_name": "effective_start_date",
                        "display_name": "Effective Start Date",
                        "data_type_id": 2,
                        "source_variable_id": None,
                        "source_type": "report",
                        "is_selected": True,
                        "is_primary": True,
                        "field_order": 0,
                    },
                    {
                        "meta_data": None,
                        "source_cf_meta_data": None,
                        "source_id": "user",
                        "variable_id": "effective_end_date_user",
                        "system_name": "effective_end_date",
                        "display_name": "Effective End Date",
                        "data_type_id": 2,
                        "source_variable_id": None,
                        "source_type": "report",
                        "is_selected": True,
                        "is_primary": True,
                        "field_order": 0,
                    },
                    {
                        "meta_data": None,
                        "source_cf_meta_data": None,
                        "source_id": "user",
                        "variable_id": "employee_id_user",
                        "system_name": "employee_id",
                        "display_name": "Employee Id",
                        "data_type_id": 4,
                        "source_variable_id": None,
                        "source_type": "report",
                        "is_selected": True,
                        "is_primary": False,
                        "field_order": 0,
                    },
                    {
                        "meta_data": None,
                        "source_cf_meta_data": None,
                        "source_id": "user",
                        "variable_id": "joining_date_user",
                        "system_name": "joining_date",
                        "display_name": "Joining Date",
                        "data_type_id": 2,
                        "source_variable_id": None,
                        "source_type": "report",
                        "is_selected": True,
                        "is_primary": False,
                        "field_order": 0,
                    },
                    {
                        "meta_data": None,
                        "source_cf_meta_data": None,
                        "source_id": "user",
                        "variable_id": "time_zone_user",
                        "system_name": "time_zone",
                        "display_name": "Current Time Zone",
                        "data_type_id": 4,
                        "source_variable_id": None,
                        "source_type": "report",
                        "is_selected": True,
                        "is_primary": False,
                        "field_order": 0,
                    },
                    {
                        "meta_data": None,
                        "source_cf_meta_data": None,
                        "source_id": "user",
                        "variable_id": "designation_user",
                        "system_name": "designation",
                        "display_name": "Designation",
                        "data_type_id": 4,
                        "source_variable_id": None,
                        "source_type": "report",
                        "is_selected": True,
                        "is_primary": False,
                        "field_order": 0,
                    },
                    {
                        "meta_data": None,
                        "source_cf_meta_data": None,
                        "source_id": "user",
                        "variable_id": "employment_country_user",
                        "system_name": "employment_country",
                        "display_name": "Employment Country",
                        "data_type_id": 4,
                        "source_variable_id": None,
                        "source_type": "report",
                        "is_selected": True,
                        "is_primary": False,
                        "field_order": 0,
                    },
                    {
                        "meta_data": None,
                        "source_cf_meta_data": None,
                        "source_id": "user",
                        "variable_id": "payout_frequency_user",
                        "system_name": "payout_frequency",
                        "display_name": "Payout Frequency",
                        "data_type_id": 4,
                        "source_variable_id": None,
                        "source_type": "report",
                        "is_selected": True,
                        "is_primary": False,
                        "field_order": 0,
                    },
                    {
                        "meta_data": None,
                        "source_cf_meta_data": None,
                        "source_id": "user",
                        "variable_id": "pay_currency_user",
                        "system_name": "pay_currency",
                        "display_name": "Payout Currency",
                        "data_type_id": 4,
                        "source_variable_id": None,
                        "source_type": "report",
                        "is_selected": True,
                        "is_primary": False,
                        "field_order": 0,
                    },
                    {
                        "meta_data": None,
                        "source_cf_meta_data": None,
                        "source_id": "user",
                        "variable_id": "fixed_pay_user",
                        "system_name": "fixed_pay",
                        "display_name": "Fixed Pay",
                        "data_type_id": 1,
                        "source_variable_id": None,
                        "source_type": "report",
                        "is_selected": True,
                        "is_primary": False,
                        "field_order": 0,
                    },
                    {
                        "meta_data": None,
                        "source_cf_meta_data": None,
                        "source_id": "user",
                        "variable_id": "payee_variable_pay_user",
                        "system_name": "payee_variable_pay",
                        "display_name": "Variable Pay",
                        "data_type_id": 1,
                        "source_variable_id": None,
                        "source_type": "report",
                        "is_selected": True,
                        "is_primary": False,
                        "field_order": 0,
                    },
                    {
                        "meta_data": None,
                        "source_cf_meta_data": None,
                        "source_id": "user",
                        "variable_id": "first_name_user",
                        "system_name": "first_name",
                        "display_name": "First Name",
                        "data_type_id": 4,
                        "source_variable_id": None,
                        "source_type": "report",
                        "is_selected": True,
                        "is_primary": False,
                        "field_order": 0,
                    },
                    {
                        "meta_data": None,
                        "source_cf_meta_data": None,
                        "source_id": "user",
                        "variable_id": "last_name_user",
                        "system_name": "last_name",
                        "display_name": "Last Name",
                        "data_type_id": 4,
                        "source_variable_id": None,
                        "source_type": "report",
                        "is_selected": True,
                        "is_primary": False,
                        "field_order": 0,
                    },
                    {
                        "meta_data": None,
                        "source_cf_meta_data": None,
                        "source_id": "user",
                        "variable_id": "user_role_user",
                        "system_name": "user_role",
                        "display_name": "Current User Role",
                        "data_type_id": 4,
                        "source_variable_id": None,
                        "source_type": "report",
                        "is_selected": True,
                        "is_primary": False,
                        "field_order": 0,
                    },
                    {
                        "meta_data": None,
                        "source_cf_meta_data": None,
                        "source_id": "user",
                        "variable_id": "created_date_user",
                        "system_name": "created_date",
                        "display_name": "Created Date",
                        "data_type_id": 2,
                        "source_variable_id": None,
                        "source_type": "report",
                        "is_selected": True,
                        "is_primary": False,
                        "field_order": 0,
                    },
                    {
                        "meta_data": None,
                        "source_cf_meta_data": None,
                        "source_id": "user",
                        "variable_id": "created_by_user",
                        "system_name": "created_by",
                        "display_name": "Created By",
                        "data_type_id": 12,
                        "source_variable_id": None,
                        "source_type": "report",
                        "is_selected": True,
                        "is_primary": False,
                        "field_order": 0,
                    },
                    {
                        "meta_data": None,
                        "source_cf_meta_data": None,
                        "source_id": "user",
                        "variable_id": "exit_date_user",
                        "system_name": "exit_date",
                        "display_name": "Exit Date",
                        "data_type_id": 2,
                        "source_variable_id": None,
                        "source_type": "report",
                        "is_selected": True,
                        "is_primary": False,
                        "field_order": 0,
                    },
                    {
                        "meta_data": None,
                        "source_cf_meta_data": None,
                        "source_id": "user",
                        "variable_id": "last_commission_date_user",
                        "system_name": "last_commission_date",
                        "display_name": "Exit Last Commission Date",
                        "data_type_id": 2,
                        "source_variable_id": None,
                        "source_type": "report",
                        "is_selected": True,
                        "is_primary": False,
                        "field_order": 0,
                    },
                    {
                        "meta_data": None,
                        "source_cf_meta_data": None,
                        "source_id": "user",
                        "variable_id": "status_user",
                        "system_name": "status",
                        "display_name": "Status",
                        "data_type_id": 4,
                        "source_variable_id": None,
                        "source_type": "report",
                        "is_selected": True,
                        "is_primary": False,
                        "field_order": 0,
                    },
                    {
                        "meta_data": None,
                        "source_cf_meta_data": None,
                        "source_id": "user",
                        "variable_id": "employee_email_id_user",
                        "system_name": "employee_email_id",
                        "display_name": "Employee Email Id",
                        "data_type_id": 12,
                        "source_variable_id": None,
                        "source_type": "report",
                        "is_selected": True,
                        "is_primary": True,
                        "field_order": 0,
                    },
                    {
                        "meta_data": None,
                        "source_cf_meta_data": None,
                        "source_id": "user",
                        "variable_id": "reporting_manager_email_id_user",
                        "system_name": "reporting_manager_email_id",
                        "display_name": "Reporting Manager EmailId",
                        "data_type_id": 12,
                        "source_variable_id": None,
                        "source_type": "report",
                        "is_selected": True,
                        "is_primary": False,
                        "field_order": 0,
                    },
                    {
                        "meta_data": None,
                        "source_cf_meta_data": None,
                        "source_id": "user",
                        "variable_id": "reporting_manager_name_user",
                        "system_name": "reporting_manager_name",
                        "display_name": "Reporting Manager Name",
                        "data_type_id": 4,
                        "source_variable_id": None,
                        "source_type": "report",
                        "is_selected": True,
                        "is_primary": False,
                        "field_order": 0,
                    },
                    {
                        "meta_data": None,
                        "source_cf_meta_data": None,
                        "source_id": "user",
                        "variable_id": "active_plan_user",
                        "system_name": "active_plan",
                        "display_name": "Active Primary Plan",
                        "data_type_id": 4,
                        "source_variable_id": None,
                        "source_type": "report",
                        "is_selected": True,
                        "is_primary": False,
                        "field_order": 0,
                    },
                    {
                        "meta_data": None,
                        "source_cf_meta_data": None,
                        "source_id": "user",
                        "variable_id": "active_spiffs_user",
                        "system_name": "active_spiffs",
                        "display_name": "Active Spiff Plan",
                        "data_type_id": 4,
                        "source_variable_id": None,
                        "source_type": "report",
                        "is_selected": True,
                        "is_primary": False,
                        "field_order": 0,
                    },
                    {
                        "meta_data": None,
                        "source_cf_meta_data": None,
                        "source_id": "user",
                        "variable_id": "active_quota_categories_user",
                        "system_name": "active_quota_categories",
                        "display_name": "Active Quota Categories",
                        "data_type_id": 4,
                        "source_variable_id": None,
                        "source_type": "report",
                        "is_selected": True,
                        "is_primary": False,
                        "field_order": 0,
                    },
                    {
                        "meta_data": None,
                        "source_cf_meta_data": None,
                        "source_id": "user",
                        "variable_id": "payee_or_manager_user",
                        "system_name": "payee_or_manager",
                        "display_name": "Payee/Manager",
                        "data_type_id": 4,
                        "source_variable_id": None,
                        "source_type": "report",
                        "is_selected": True,
                        "is_primary": False,
                        "field_order": 0,
                    },
                ],
                "transformations": [
                    {
                        "key": "fae0d839-2fed-4b92-94a1-4c6754562bb1",
                        "type": "JOIN",
                        "transformation_id": UUID(
                            "fae0d839-2fed-4b92-94a1-4c6754562bb1"
                        ),
                        "is_valid": False,
                        "output_columns": [],
                        "transformation_source_map": {},
                        "used_variable_ids": [],
                        "on": {
                            "lhs": [
                                {
                                    "system_name": "employee_email_id",
                                    "display_name": "Employee Email Id",
                                    "data_type_id": 12,
                                    "source_cf_meta_data": None,
                                    "variable_id": "employee_email_id_user",
                                    "source_variable_id": None,
                                    "is_primary": True,
                                }
                            ],
                            "rhs": [
                                {
                                    "system_name": "co_1_email",
                                    "display_name": "Email",
                                    "data_type_id": 12,
                                    "source_cf_meta_data": None,
                                    "variable_id": "1b0b9972-764c-426a-891d-1d7338645519",
                                    "source_variable_id": "co_1_email",
                                    "is_primary": False,
                                }
                            ],
                        },
                        "columns": {
                            "lhs": [
                                {
                                    "system_name": "effective_start_date",
                                    "display_name": "Effective Start Date",
                                    "data_type_id": 2,
                                    "source_cf_meta_data": None,
                                    "variable_id": "effective_start_date_user",
                                    "source_variable_id": None,
                                    "is_primary": True,
                                },
                                {
                                    "system_name": "effective_end_date",
                                    "display_name": "Effective End Date",
                                    "data_type_id": 2,
                                    "source_cf_meta_data": None,
                                    "variable_id": "effective_end_date_user",
                                    "source_variable_id": None,
                                    "is_primary": True,
                                },
                                {
                                    "system_name": "employee_email_id",
                                    "display_name": "Employee Email Id",
                                    "data_type_id": 12,
                                    "source_cf_meta_data": None,
                                    "variable_id": "employee_email_id_user",
                                    "source_variable_id": None,
                                    "is_primary": True,
                                },
                                {
                                    "system_name": "employee_id",
                                    "display_name": "Employee Id",
                                    "data_type_id": 4,
                                    "source_cf_meta_data": None,
                                    "variable_id": "employee_id_user",
                                    "source_variable_id": None,
                                    "is_primary": False,
                                },
                                {
                                    "system_name": "joining_date",
                                    "display_name": "Joining Date",
                                    "data_type_id": 2,
                                    "source_cf_meta_data": None,
                                    "variable_id": "joining_date_user",
                                    "source_variable_id": None,
                                    "is_primary": False,
                                },
                                {
                                    "system_name": "time_zone",
                                    "display_name": "Current Time Zone",
                                    "data_type_id": 4,
                                    "source_cf_meta_data": None,
                                    "variable_id": "time_zone_user",
                                    "source_variable_id": None,
                                    "is_primary": False,
                                },
                            ],
                            "rhs": [
                                {
                                    "system_name": "co_1_number",
                                    "display_name": "Number",
                                    "data_type_id": 1,
                                    "source_cf_meta_data": None,
                                    "variable_id": "8113951d-6ab2-48ff-af64-ab96579abd0b",
                                    "source_variable_id": "co_1_number",
                                    "is_primary": True,
                                }
                            ],
                        },
                        "join_type": "LEFT",
                        "with": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                        "with_databook_id": "869b4b9b-5e9f-49e1-b24c-e4c5cee825b0",
                    }
                ],
                "source_id": "user",
                "source_type": "report",
                "has_source_changed": False,
            }
        )
        response = validate_datasheet(request_model=datasheet_validator_request_model)
        result = response.model_dump(by_alias=True)
        expected_response = [
            {
                "client_id": 3024,
                "datasheet_id": UUID("1090d6c2-0ef8-4990-bf48-359d45cb1dec"),
                "databook_id": UUID("869b4b9b-5e9f-49e1-b24c-e4c5cee825b0"),
                "meta_data": None,
                "variable_id": UUID("10dd5a0d-7057-4aca-9120-9be77840da60"),
                "source_cf_meta_data": None,
                "source_id": "user",
                "warning": None,
                "system_name": "lhs_time_zone",
                "display_name": "Current Time Zone",
                "data_type_id": 4,
                "source_variable_id": "time_zone_user",
                "source_type": "report",
                "is_selected": False,
                "is_primary": False,
                "field_order": 0,
                "source_name": "User",
                "source_name_history": "Current Time Zone << User",
                "is_dependent": False,
                "is_adjusted": False,
            },
            {
                "client_id": 3024,
                "datasheet_id": UUID("1090d6c2-0ef8-4990-bf48-359d45cb1dec"),
                "databook_id": UUID("869b4b9b-5e9f-49e1-b24c-e4c5cee825b0"),
                "meta_data": None,
                "source_cf_meta_data": None,
                "source_id": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                "warning": None,
                "system_name": "rhs_co_1_number",
                "display_name": "Number",
                "data_type_id": 1,
                "source_variable_id": "8113951d-6ab2-48ff-af64-ab96579abd0b",
                "source_type": "datasheet",
                "is_selected": True,
                "is_primary": True,
                "field_order": 0,
                "source_name": "test",
                "source_name_history": "Number << test << custom_object_1",
                "is_dependent": False,
                "is_adjusted": False,
            },
            {
                "client_id": 3024,
                "datasheet_id": UUID("1090d6c2-0ef8-4990-bf48-359d45cb1dec"),
                "databook_id": UUID("869b4b9b-5e9f-49e1-b24c-e4c5cee825b0"),
                "meta_data": None,
                "variable_id": UUID("3524e64b-9003-4a03-b7c3-178ed39ad678"),
                "source_cf_meta_data": None,
                "source_id": "user",
                "warning": None,
                "system_name": "lhs_effective_start_date",
                "display_name": "Effective Start Date",
                "data_type_id": 2,
                "source_variable_id": "effective_start_date_user",
                "source_type": "report",
                "is_selected": True,
                "is_primary": True,
                "field_order": 0,
                "source_name": "User",
                "source_name_history": "Effective Start Date << User",
                "is_dependent": False,
                "is_adjusted": False,
            },
            {
                "client_id": 3024,
                "datasheet_id": UUID("1090d6c2-0ef8-4990-bf48-359d45cb1dec"),
                "databook_id": UUID("869b4b9b-5e9f-49e1-b24c-e4c5cee825b0"),
                "meta_data": None,
                "variable_id": UUID("02f933cd-ab07-4cb1-ac4b-0dcb005ec181"),
                "source_cf_meta_data": None,
                "source_id": "user",
                "warning": None,
                "system_name": "lhs_effective_end_date",
                "display_name": "Effective End Date",
                "data_type_id": 2,
                "source_variable_id": "effective_end_date_user",
                "source_type": "report",
                "is_selected": True,
                "is_primary": True,
                "field_order": 0,
                "source_name": "User",
                "source_name_history": "Effective End Date << User",
                "is_dependent": False,
                "is_adjusted": False,
            },
            {
                "client_id": 3024,
                "datasheet_id": UUID("1090d6c2-0ef8-4990-bf48-359d45cb1dec"),
                "databook_id": UUID("869b4b9b-5e9f-49e1-b24c-e4c5cee825b0"),
                "meta_data": None,
                "variable_id": UUID("effa65a1-1343-49bc-81ab-59b7000fd4c0"),
                "source_cf_meta_data": None,
                "source_id": "user",
                "warning": None,
                "system_name": "lhs_employee_email_id",
                "display_name": "Employee Email Id",
                "data_type_id": 12,
                "source_variable_id": "employee_email_id_user",
                "source_type": "report",
                "is_selected": True,
                "is_primary": True,
                "field_order": 0,
                "source_name": "User",
                "source_name_history": "Employee Email Id << User",
                "is_dependent": False,
                "is_adjusted": False,
            },
            {
                "client_id": 3024,
                "datasheet_id": UUID("1090d6c2-0ef8-4990-bf48-359d45cb1dec"),
                "databook_id": UUID("869b4b9b-5e9f-49e1-b24c-e4c5cee825b0"),
                "meta_data": None,
                "variable_id": UUID("3904ae88-bdcf-40b0-ba99-16bc20bdf30e"),
                "source_cf_meta_data": None,
                "source_id": "user",
                "warning": None,
                "system_name": "lhs_employee_id",
                "display_name": "Employee Id",
                "data_type_id": 4,
                "source_variable_id": "employee_id_user",
                "source_type": "report",
                "is_selected": True,
                "is_primary": False,
                "field_order": 0,
                "source_name": "User",
                "source_name_history": "Employee Id << User",
                "is_dependent": False,
                "is_adjusted": False,
            },
            {
                "client_id": 3024,
                "datasheet_id": UUID("1090d6c2-0ef8-4990-bf48-359d45cb1dec"),
                "databook_id": UUID("869b4b9b-5e9f-49e1-b24c-e4c5cee825b0"),
                "meta_data": None,
                "variable_id": UUID("3e075f3c-6b0c-48fc-90e1-898b83d48f26"),
                "source_cf_meta_data": None,
                "source_id": "user",
                "warning": None,
                "system_name": "lhs_joining_date",
                "display_name": "Joining Date",
                "data_type_id": 2,
                "source_variable_id": "joining_date_user",
                "source_type": "report",
                "is_selected": True,
                "is_primary": False,
                "field_order": 0,
                "source_name": "User",
                "source_name_history": "Joining Date << User",
                "is_dependent": False,
                "is_adjusted": False,
            },
        ]

        for variable in result["variables"]:
            # Removing variable id from the result for rhs variable since it's dynamically generated
            if variable["system_name"] == "rhs_co_1_number":
                variable.pop("variable_id")

        self.assertCountEqual(result["variables"], expected_response)

    def test_same_source_rhs_join_variable(self):
        # when variable id has __ss__ , remove it while mapping , validation should be successful
        validate_request_details = {
            "client_id": 3024,
            "datasheet_id": UUID("b2dd92d5-d46c-44b6-a23a-ae712407f38a"),
            "databook_id": UUID("869b4b9b-5e9f-49e1-b24c-e4c5cee825b0"),
            "variables": [
                {
                    "meta_data": None,
                    "source_cf_meta_data": None,
                    "source_id": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                    "variable_id": "5652ef0e-9321-4d52-95a8-905a7c353744",
                    "system_name": "lhs_lhs_co_1_number",
                    "display_name": "Number",
                    "data_type_id": 1,
                    "source_variable_id": "8113951d-6ab2-48ff-af64-ab96579abd0b",
                    "source_type": "datasheet",
                    "is_selected": True,
                    "is_primary": True,
                    "field_order": 0,
                    "warning": None,
                },
                {
                    "meta_data": None,
                    "source_cf_meta_data": None,
                    "source_id": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                    "variable_id": "9956ec66-e98f-4955-a459-0d1c5eb9b0f6",
                    "system_name": "lhs_rhs_co_1_number",
                    "display_name": "test :: Number",
                    "data_type_id": 1,
                    "source_variable_id": "8113951d-6ab2-48ff-af64-ab96579abd0b__ss__1",
                    "source_type": "datasheet",
                    "is_selected": True,
                    "is_primary": True,
                    "field_order": 0,
                    "warning": None,
                },
                {
                    "meta_data": None,
                    "source_cf_meta_data": None,
                    "source_id": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                    "variable_id": "67d0e6a6-ddda-4527-a725-0568146804f4",
                    "system_name": "rhs_co_1_number",
                    "display_name": "test :: test :: Number",
                    "data_type_id": 1,
                    "source_variable_id": "8113951d-6ab2-48ff-af64-ab96579abd0b__ss__2",
                    "source_type": "datasheet",
                    "is_selected": True,
                    "is_primary": True,
                    "field_order": 0,
                    "warning": None,
                },
            ],
            "transformations": [
                {
                    "key": "67260bc4-f59c-475b-a042-020b384bb8de",
                    "type": "JOIN",
                    "transformation_id": UUID("67260bc4-f59c-475b-a042-020b384bb8de"),
                    "is_valid": False,
                    "output_columns": [],
                    "transformation_source_map": {},
                    "used_variable_ids": [],
                    "source_ids": [],
                    "on": {
                        "lhs": [
                            {
                                "system_name": "co_1_name",
                                "display_name": "Name",
                                "data_type_id": 4,
                                "source_cf_meta_data": None,
                                "variable_id": "a7f84b61-17d5-4d73-88e2-5ee9996649a6",
                                "source_variable_id": "co_1_name",
                                "is_primary": False,
                                "source_name_history": "Name << custom_object_1",
                                "source_id": "1",
                                "source_type": "object",
                            }
                        ],
                        "rhs": [
                            {
                                "system_name": "co_1_name",
                                "display_name": "Name",
                                "data_type_id": 4,
                                "source_cf_meta_data": None,
                                "variable_id": "a7f84b61-17d5-4d73-88e2-5ee9996649a6",
                                "source_variable_id": "co_1_name",
                                "is_primary": False,
                                "source_name_history": "Name << custom_object_1",
                                "source_id": "1",
                                "source_type": "object",
                            }
                        ],
                    },
                    "columns": {
                        "lhs": [
                            {
                                "system_name": "co_1_number",
                                "display_name": "Number",
                                "data_type_id": 1,
                                "source_cf_meta_data": None,
                                "variable_id": "8113951d-6ab2-48ff-af64-ab96579abd0b",
                                "source_variable_id": "co_1_number",
                                "is_primary": True,
                                "source_name_history": "Number << custom_object_1",
                                "source_id": "1",
                                "source_type": "object",
                            }
                        ],
                        "rhs": [
                            {
                                "system_name": "co_1_number",
                                "display_name": "Number",
                                "data_type_id": 1,
                                "source_cf_meta_data": None,
                                "variable_id": "8113951d-6ab2-48ff-af64-ab96579abd0b",
                                "source_variable_id": "co_1_number",
                                "is_primary": True,
                                "source_name_history": "Number << custom_object_1",
                                "source_id": "1",
                                "source_type": "object",
                            }
                        ],
                    },
                    "join_type": "LEFT",
                    "with": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                    "with_databook_id": "869b4b9b-5e9f-49e1-b24c-e4c5cee825b0",
                },
                {
                    "key": "cf612b52-3957-4c96-91f8-34a397c83082",
                    "type": "JOIN",
                    "transformation_id": UUID("cf612b52-3957-4c96-91f8-34a397c83082"),
                    "is_valid": True,
                    "output_columns": [
                        {
                            "warning": None,
                            "client_id": 3024,
                            "meta_data": None,
                            "source_id": "1",
                            "is_primary": True,
                            "databook_id": "869b4b9b-5e9f-49e1-b24c-e4c5cee825b0",
                            "field_order": 0,
                            "is_adjusted": False,
                            "is_selected": True,
                            "source_name": "custom_object_1",
                            "source_type": "object",
                            "system_name": "lhs_lhs_co_1_number",
                            "variable_id": "8113951d-6ab2-48ff-af64-ab96579abd0b",
                            "data_type_id": 1,
                            "datasheet_id": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                            "display_name": "Number",
                            "is_dependent": True,
                            "source_variable_id": "co_1_number",
                            "source_cf_meta_data": None,
                            "source_name_history": "Number << custom_object_1",
                        },
                        {
                            "warning": None,
                            "client_id": 3024,
                            "meta_data": None,
                            "source_id": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                            "is_primary": True,
                            "databook_id": "869b4b9b-5e9f-49e1-b24c-e4c5cee825b0",
                            "field_order": 0,
                            "is_adjusted": False,
                            "is_selected": True,
                            "source_name": "custom_object_1",
                            "source_type": "datasheet",
                            "system_name": "lhs_rhs_co_1_number",
                            "variable_id": "8113951d-6ab2-48ff-af64-ab96579abd0b__ss__1",
                            "data_type_id": 1,
                            "datasheet_id": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                            "display_name": "Number",
                            "is_dependent": True,
                            "source_variable_id": "co_1_number",
                            "source_cf_meta_data": None,
                            "source_name_history": "Number << custom_object_1",
                        },
                        {
                            "warning": None,
                            "client_id": 3024,
                            "meta_data": None,
                            "source_id": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                            "is_primary": True,
                            "databook_id": "869b4b9b-5e9f-49e1-b24c-e4c5cee825b0",
                            "field_order": 0,
                            "is_adjusted": False,
                            "is_selected": True,
                            "source_name": "custom_object_1",
                            "source_type": "datasheet",
                            "system_name": "rhs_co_1_number",
                            "variable_id": "8113951d-6ab2-48ff-af64-ab96579abd0b__ss__2",
                            "data_type_id": 1,
                            "datasheet_id": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                            "display_name": "Number",
                            "is_dependent": True,
                            "source_variable_id": "co_1_number",
                            "source_cf_meta_data": None,
                            "source_name_history": "Number << custom_object_1",
                        },
                    ],
                    "transformation_source_map": {
                        "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb_datasheet": 2
                    },
                    "used_variable_ids": [
                        "8113951d-6ab2-48ff-af64-ab96579abd0b__ss__2",
                        "8113951d-6ab2-48ff-af64-ab96579abd0b",
                        "a7f84b61-17d5-4d73-88e2-5ee9996649a6",
                        "8113951d-6ab2-48ff-af64-ab96579abd0b__ss__1",
                    ],
                    "source_ids": ["f962ea83-b58a-4ee4-983e-1dc07fe4d3fb"],
                    "on": {
                        "lhs": [
                            {
                                "warning": None,
                                "client_id": 3024,
                                "meta_data": None,
                                "source_id": "1",
                                "is_primary": True,
                                "databook_id": "869b4b9b-5e9f-49e1-b24c-e4c5cee825b0",
                                "field_order": 0,
                                "is_adjusted": False,
                                "is_selected": True,
                                "source_name": "custom_object_1",
                                "source_type": "object",
                                "system_name": "lhs_co_1_number",
                                "variable_id": "8113951d-6ab2-48ff-af64-ab96579abd0b",
                                "data_type_id": 1,
                                "datasheet_id": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                                "display_name": "Number",
                                "is_dependent": True,
                                "source_variable_id": "co_1_number",
                                "source_cf_meta_data": None,
                                "source_name_history": "Number << custom_object_1",
                            }
                        ],
                        "rhs": [
                            {
                                "warning": None,
                                "client_id": 3024,
                                "meta_data": None,
                                "source_id": "1",
                                "is_primary": True,
                                "databook_id": "869b4b9b-5e9f-49e1-b24c-e4c5cee825b0",
                                "field_order": 0,
                                "is_adjusted": False,
                                "is_selected": True,
                                "source_name": "custom_object_1",
                                "source_type": "object",
                                "system_name": "co_1_number",
                                "variable_id": "8113951d-6ab2-48ff-af64-ab96579abd0b",
                                "data_type_id": 1,
                                "datasheet_id": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                                "display_name": "Number",
                                "is_dependent": True,
                                "source_variable_id": "co_1_number",
                                "source_cf_meta_data": None,
                                "source_name_history": "Number << custom_object_1",
                            }
                        ],
                    },
                    "columns": {
                        "lhs": [
                            {
                                "warning": None,
                                "client_id": 3024,
                                "meta_data": None,
                                "source_id": "1",
                                "is_primary": True,
                                "databook_id": "869b4b9b-5e9f-49e1-b24c-e4c5cee825b0",
                                "field_order": 0,
                                "is_adjusted": False,
                                "is_selected": True,
                                "source_name": "custom_object_1",
                                "source_type": "object",
                                "system_name": "lhs_co_1_number",
                                "variable_id": "8113951d-6ab2-48ff-af64-ab96579abd0b",
                                "data_type_id": 1,
                                "datasheet_id": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                                "display_name": "Number",
                                "is_dependent": True,
                                "source_variable_id": "co_1_number",
                                "source_cf_meta_data": None,
                                "source_name_history": "Number << custom_object_1",
                            },
                            {
                                "warning": None,
                                "client_id": 3024,
                                "meta_data": None,
                                "source_id": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                                "is_primary": True,
                                "databook_id": "869b4b9b-5e9f-49e1-b24c-e4c5cee825b0",
                                "field_order": 0,
                                "is_adjusted": False,
                                "is_selected": True,
                                "source_name": "custom_object_1",
                                "source_type": "datasheet",
                                "system_name": "rhs_co_1_number",
                                "variable_id": "8113951d-6ab2-48ff-af64-ab96579abd0b__ss__1",
                                "data_type_id": 1,
                                "datasheet_id": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                                "display_name": "Number",
                                "is_dependent": True,
                                "source_variable_id": "co_1_number",
                                "source_cf_meta_data": None,
                                "source_name_history": "Number << custom_object_1",
                            },
                        ],
                        "rhs": [
                            {
                                "warning": None,
                                "client_id": 3024,
                                "meta_data": None,
                                "source_id": "1",
                                "is_primary": True,
                                "databook_id": "869b4b9b-5e9f-49e1-b24c-e4c5cee825b0",
                                "field_order": 0,
                                "is_adjusted": False,
                                "is_selected": True,
                                "source_name": "custom_object_1",
                                "source_type": "object",
                                "system_name": "co_1_number",
                                "variable_id": "8113951d-6ab2-48ff-af64-ab96579abd0b__ss__2",
                                "data_type_id": 1,
                                "datasheet_id": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                                "display_name": "Number",
                                "is_dependent": True,
                                "source_variable_id": "co_1_number",
                                "source_cf_meta_data": None,
                                "source_name_history": "Number << custom_object_1",
                            }
                        ],
                    },
                    "join_type": "LEFT",
                    "with": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                    "with_databook_id": "869b4b9b-5e9f-49e1-b24c-e4c5cee825b0",
                },
            ],
            "initial_validation": True,
            "source_type": "datasheet",
            "source_id": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
            "source_variables": [
                {
                    "meta_data": None,
                    "source_cf_meta_data": None,
                    "source_id": "1",
                    "variable_id": "a7f84b61-17d5-4d73-88e2-5ee9996649a6",
                    "system_name": "co_1_name",
                    "display_name": "Name",
                    "data_type_id": 4,
                    "source_variable_id": "co_1_name",
                    "source_type": "object",
                    "is_selected": True,
                    "is_primary": False,
                    "field_order": 0,
                    "warning": None,
                    "source_name_history": "Name << custom_object_1",
                },
                {
                    "meta_data": None,
                    "source_cf_meta_data": None,
                    "source_id": "1",
                    "variable_id": "8113951d-6ab2-48ff-af64-ab96579abd0b",
                    "system_name": "co_1_number",
                    "display_name": "Number",
                    "data_type_id": 1,
                    "source_variable_id": "co_1_number",
                    "source_type": "object",
                    "is_selected": True,
                    "is_primary": True,
                    "field_order": 0,
                    "warning": None,
                    "source_name_history": "Number << custom_object_1",
                },
                {
                    "meta_data": None,
                    "source_cf_meta_data": None,
                    "source_id": "1",
                    "variable_id": "c66f062b-4bb1-46c8-9f4d-d3b3663ee1f2",
                    "system_name": "co_1_boolean",
                    "display_name": "Boolean",
                    "data_type_id": 3,
                    "source_variable_id": "co_1_boolean",
                    "source_type": "object",
                    "is_selected": True,
                    "is_primary": False,
                    "field_order": 0,
                    "warning": None,
                    "source_name_history": "Boolean << custom_object_1",
                },
                {
                    "meta_data": None,
                    "source_cf_meta_data": None,
                    "source_id": "1",
                    "variable_id": "a8f6f7d8-9731-4dc8-bc1a-328d093b389b",
                    "system_name": "co_1_date_1",
                    "display_name": "Date-1",
                    "data_type_id": 2,
                    "source_variable_id": "co_1_date_1",
                    "source_type": "object",
                    "is_selected": True,
                    "is_primary": False,
                    "field_order": 0,
                    "warning": None,
                    "source_name_history": "Date-1 << custom_object_1",
                },
                {
                    "meta_data": None,
                    "source_cf_meta_data": None,
                    "source_id": "1",
                    "variable_id": "1b0b9972-764c-426a-891d-1d7338645519",
                    "system_name": "co_1_email",
                    "display_name": "Email",
                    "data_type_id": 12,
                    "source_variable_id": "co_1_email",
                    "source_type": "object",
                    "is_selected": True,
                    "is_primary": False,
                    "field_order": 0,
                    "warning": None,
                    "source_name_history": "Email << custom_object_1",
                },
                {
                    "meta_data": None,
                    "source_cf_meta_data": None,
                    "source_id": "1",
                    "variable_id": "c54ff1fc-43ae-4e06-935f-78527191f385",
                    "system_name": "co_1_percentage",
                    "display_name": "Percentage",
                    "data_type_id": 6,
                    "source_variable_id": "co_1_percentage",
                    "source_type": "object",
                    "is_selected": True,
                    "is_primary": False,
                    "field_order": 0,
                    "warning": None,
                    "source_name_history": "Percentage << custom_object_1",
                },
                {
                    "meta_data": None,
                    "source_cf_meta_data": None,
                    "source_id": "1",
                    "variable_id": "16bd069f-eaaf-4d5b-9b9b-afd42c02f631",
                    "system_name": "co_1_date_2",
                    "display_name": "Date-2",
                    "data_type_id": 2,
                    "source_variable_id": "co_1_date_2",
                    "source_type": "object",
                    "is_selected": True,
                    "is_primary": False,
                    "field_order": 0,
                    "warning": None,
                    "source_name_history": "Date-2 << custom_object_1",
                },
            ],
            "has_source_changed": False,
        }
        response = validate_datasheet(
            DatasheetValidateRequest(**validate_request_details)
        )
        self.assertTrue(isinstance(response, DatasheetValidateResponse))
        result = response.model_dump(by_alias=True)
        join_transformation = result["transformations"][1]
        for column in join_transformation["columns"]["rhs"]:
            self.assertTrue("__ss__" not in column["variable_id"])
        for output_column in join_transformation["output_columns"]:
            # starts with rhs
            if output_column["system_name"][:3] == "rhs":
                self.assertTrue("__ss__" in output_column["variable_id"])

    def test_source_change_validation_with_cf_and_invalid_transformation(self):
        validate_request_details = {
            "client_id": 3024,
            "datasheet_id": UUID("934c99c7-bcbe-4706-b99f-a1e4b4695ca7"),
            "databook_id": UUID("869b4b9b-5e9f-49e1-b24c-e4c5cee825b0"),
            "variables": [
                {
                    "meta_data": None,
                    "source_cf_meta_data": None,
                    "source_id": "1",
                    "variable_id": "1a807b04-f4dc-4d75-a341-b501fdc08b51",
                    "system_name": "lhs_co_1_number",
                    "display_name": "Number",
                    "data_type_id": 1,
                    "source_variable_id": "co_1_number",
                    "source_type": "object",
                    "is_selected": True,
                    "is_primary": True,
                    "field_order": 0,
                    "warning": None,
                },
                {
                    "meta_data": None,
                    "source_cf_meta_data": None,
                    "source_id": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                    "variable_id": "4800c1f5-4560-4d6e-8a07-4772185a3c09",
                    "system_name": "rhs_co_1_number",
                    "display_name": "test :: Number",
                    "data_type_id": 1,
                    "source_variable_id": "8113951d-6ab2-48ff-af64-ab96579abd0b",
                    "source_type": "datasheet",
                    "is_selected": True,
                    "is_primary": True,
                    "field_order": 0,
                    "warning": None,
                },
            ],
            "transformations": [
                {
                    "key": "13d235a9-0423-498f-a48b-1f2f3caffd85",
                    "type": "JOIN",
                    "transformation_id": UUID("13d235a9-0423-498f-a48b-1f2f3caffd85"),
                    "is_valid": True,
                    "output_columns": [
                        {
                            "source_id": 1,
                            "is_primary": True,
                            "source_type": "object",
                            "system_name": "lhs_co_1_number",
                            "variable_id": "co_1_number",
                            "data_type_id": 1,
                            "display_name": "Number",
                            "system_generated": False,
                            "source_variable_id": None,
                            "source_cf_meta_data": None,
                            "source_name_history": "Number << custom_object_1",
                        },
                        {
                            "source_id": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                            "is_primary": True,
                            "source_type": "datasheet",
                            "system_name": "rhs_co_1_number",
                            "variable_id": "8113951d-6ab2-48ff-af64-ab96579abd0b",
                            "data_type_id": 1,
                            "display_name": "Number",
                            "source_variable_id": "co_1_number",
                            "source_cf_meta_data": None,
                            "source_name_history": "Number << custom_object_1",
                        },
                    ],
                    "transformation_source_map": {
                        "1_object": 0,
                        "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb_datasheet": 0,
                    },
                    "used_variable_ids": [
                        "co_1_number",
                        "8113951d-6ab2-48ff-af64-ab96579abd0b",
                        "a7f84b61-17d5-4d73-88e2-5ee9996649a6",
                        "co_1_name",
                    ],
                    "source_ids": ["f962ea83-b58a-4ee4-983e-1dc07fe4d3fb"],
                    "on": {
                        "lhs": [
                            {
                                "source_id": 1,
                                "is_primary": False,
                                "source_type": "object",
                                "system_name": "co_1_name",
                                "variable_id": "co_1_name",
                                "data_type_id": 4,
                                "display_name": "Name",
                                "source_variable_id": None,
                                "source_cf_meta_data": None,
                                "source_name_history": "Name << custom_object_1",
                            }
                        ],
                        "rhs": [
                            {
                                "source_id": "1",
                                "is_primary": False,
                                "source_type": "object",
                                "system_name": "co_1_name",
                                "variable_id": "a7f84b61-17d5-4d73-88e2-5ee9996649a6",
                                "data_type_id": 4,
                                "display_name": "Name",
                                "source_variable_id": "co_1_name",
                                "source_cf_meta_data": None,
                                "source_name_history": "Name << custom_object_1",
                            }
                        ],
                    },
                    "columns": {
                        "lhs": [
                            {
                                "source_id": 1,
                                "is_primary": True,
                                "source_type": "object",
                                "system_name": "co_1_number",
                                "variable_id": "co_1_number",
                                "data_type_id": 1,
                                "display_name": "Number",
                                "system_generated": False,
                                "source_variable_id": None,
                                "source_cf_meta_data": None,
                                "source_name_history": "Number << custom_object_1",
                            }
                        ],
                        "rhs": [
                            {
                                "source_id": "1",
                                "is_primary": True,
                                "source_type": "object",
                                "system_name": "co_1_number",
                                "variable_id": "8113951d-6ab2-48ff-af64-ab96579abd0b",
                                "data_type_id": 1,
                                "display_name": "Number",
                                "source_variable_id": "co_1_number",
                                "source_cf_meta_data": None,
                                "source_name_history": "Number << custom_object_1",
                            }
                        ],
                    },
                    "join_type": "LEFT",
                    "with": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                    "with_databook_id": "869b4b9b-5e9f-49e1-b24c-e4c5cee825b0",
                }
            ],
            "initial_validation": True,
            "source_type": "datasheet",
            "source_id": "b12af5c1-35f0-4b8f-b22d-991ea4395ef3",
            "source_variables": [
                {
                    "meta_data": None,
                    "source_cf_meta_data": None,
                    "source_id": "1",
                    "variable_id": UUID("0e70b50b-c30d-4ff7-b8cf-752d456973b5"),
                    "system_name": "co_1_number",
                    "display_name": "Number",
                    "data_type_id": 1,
                    "source_variable_id": "co_1_number",
                    "source_type": "object",
                    "is_selected": True,
                    "is_primary": True,
                    "field_order": 0,
                    "warning": None,
                    "source_name_history": "Number << custom_object_1",
                },
                {
                    "meta_data": None,
                    "source_cf_meta_data": None,
                    "source_id": "b12af5c1-35f0-4b8f-b22d-991ea4395ef3",
                    "variable_id": UUID("d01263a6-b93d-46b4-bb02-d6217bd27555"),
                    "system_name": "cf_num",
                    "display_name": "num",
                    "data_type_id": 1,
                    "source_variable_id": None,
                    "source_type": "datasheet",
                    "is_selected": True,
                    "is_primary": False,
                    "field_order": 1,
                    "warning": None,
                    "source_name_history": "num << calc-field",
                },
            ],
            "has_source_changed": True,
        }
        response = validate_datasheet(
            DatasheetValidateRequest(**validate_request_details)
        )
        self.assertTrue(isinstance(response, DatasheetValidateResponse))
        result = response.model_dump(by_alias=True)
        result_transformation_spec = [
            {
                "key": "13d235a9-0423-498f-a48b-1f2f3caffd85",
                "type": "JOIN",
                "transformation_id": UUID("13d235a9-0423-498f-a48b-1f2f3caffd85"),
                "is_valid": False,
                "output_columns": [],
                "transformation_source_map": {
                    "1_object": 0,
                    "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb_datasheet": 0,
                },
                "used_variable_ids": [
                    "co_1_number",
                    "8113951d-6ab2-48ff-af64-ab96579abd0b",
                    "a7f84b61-17d5-4d73-88e2-5ee9996649a6",
                    "co_1_name",
                ],
                "source_ids": ["f962ea83-b58a-4ee4-983e-1dc07fe4d3fb"],
                "on": {"lhs": [], "rhs": []},
                "columns": {"lhs": [], "rhs": []},
                "join_type": "LEFT",
                "with": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                "with_databook_id": "869b4b9b-5e9f-49e1-b24c-e4c5cee825b0",
            }
        ]
        self.assertEqual(result["transformations"], result_transformation_spec)
        self.assertTrue(
            all(variable["field_order"] == 0 for variable in result["variables"])
        )
        transformation_spec = list(
            DatasheetTransformationSelector(
                client_id=3024,
                datasheet_id=UUID("934c99c7-bcbe-4706-b99f-a1e4b4695ca7"),
            )
            .get_transformations()
            .values()
        )
        self.assertEqual(transformation_spec, [])


# Define fixtures for mocks to make the test more maintainable
@pytest.fixture
def mock_view_selector():
    with mock.patch(
        "everstage_ddd.datasheet.helpers.datasheet_validator.DatasheetViewSelector"
    ) as mock_view_selector:
        yield mock_view_selector


@pytest.fixture
def mock_create_ast():
    with mock.patch(
        "everstage_ddd.datasheet.helpers.datasheet_validator.create_ast"
    ) as mock_create_ast:
        yield mock_create_ast


@pytest.fixture
def mock_variable_extractor():
    with mock.patch(
        "everstage_ddd.datasheet.helpers.datasheet_validator.VariableExtractor"
    ) as mock_variable_extractor:
        yield mock_variable_extractor


@pytest.fixture
def mock_datasheet_expression_box_utils():
    with mock.patch(
        "everstage_ddd.datasheet.helpers.datasheet_validator.DatasheetExpressionBoxUtils"
    ) as mock_datasheet_expression_box_utils:
        yield mock_datasheet_expression_box_utils


@pytest.fixture
def mock_get_datasheet_record():
    with mock.patch.object(
        DatasheetValidator, "get_datasheet_record"
    ) as mock_get_datasheet_record:
        yield mock_get_datasheet_record


@pytest.mark.parametrize(
    "test_case,variable_selections,expected_unselected,should_call_handle_invalid,has_pivot",
    [
        # Test case 1: Some variables are unselected and used in filter functions
        (
            "unselected_variables_in_filter",
            # Variable selections (True = selected, False = unselected)
            {
                "14017122-43c7-41d1-9dfc-a960dfa3717f": True,  # employee_id
                "fe76eb14-f2a6-4f28-a91f-fdbcfd3ac8fd": False,  # designation
                "b37238df-7774-4b29-92df-437fe9dd4467": False,  # employment_country
                "f282e821-f065-4f6d-944a-5aed1d7440c1": True,  # fixed_pay
                "f0f95d73-69ea-4443-99cc-4162a358bb01": True,  # payee_variable_pay
            },
            # Expected unselected variables
            {"designation", "employment_country"},
            # Should call _handle_invalid_variables
            True,
            # No pivot view
            False,
        ),
        # Test case 2: All variables used in filter are selected
        (
            "all_variables_selected",
            # All variables are selected
            {
                "14017122-43c7-41d1-9dfc-a960dfa3717f": True,  # employee_id
                "fe76eb14-f2a6-4f28-a91f-fdbcfd3ac8fd": True,  # designation
                "b37238df-7774-4b29-92df-437fe9dd4467": True,  # employment_country
                "f282e821-f065-4f6d-944a-5aed1d7440c1": True,  # fixed_pay
                "f0f95d73-69ea-4443-99cc-4162a358bb01": True,  # payee_variable_pay
            },
            # No unselected variables expected
            set(),
            # Should not call _handle_invalid_variables
            False,
            # No pivot view
            False,
        ),
        # Test case 3: Unselected variables in pivot view
        (
            "unselected_variables_in_pivot",
            # Variable selections (True = selected, False = unselected)
            {
                "14017122-43c7-41d1-9dfc-a960dfa3717f": True,  # employee_id
                "fe76eb14-f2a6-4f28-a91f-fdbcfd3ac8fd": False,  # designation - unselected but used in pivot
                "b37238df-7774-4b29-92df-437fe9dd4467": True,  # employment_country
                "f282e821-f065-4f6d-944a-5aed1d7440c1": True,  # fixed_pay
                "f0f95d73-69ea-4443-99cc-4162a358bb01": False,  # payee_variable_pay - unselected but used in pivot
            },
            # Expected unselected variables
            {"designation", "payee_variable_pay"},
            # Should call _handle_invalid_variables
            True,
            # Has pivot view
            True,
        ),
    ],
)
@mock.patch.object(DatasheetValidator, "get_datasheet_record")
@mock.patch.object(DatasheetValidator, "_populate_databook_id")
def test_variables_in_filter_views(  # noqa: PLR0913
    mock_populate_databook_id,
    mock_get_datasheet_record,
    mock_datasheet_expression_box_utils,
    mock_variable_extractor,
    mock_create_ast,
    mock_view_selector,
    test_case,
    variable_selections,
    expected_unselected,
    should_call_handle_invalid,
    has_pivot,
):
    """
    Test that variables used in filter functions and pivot views are correctly identified.

    This test verifies that the _is_unselected_variables_used_in_views method
    correctly identifies unselected variables that are used in filter functions,
    such as Contains() and IsNotEmpty(), as well as unselected variables used in pivot views.

    Args:
        test_case: Name of the test case for better error messages
        variable_selections: Dictionary mapping variable IDs to selection status
        expected_unselected: Set of system names expected to be identified as unselected
        should_call_handle_invalid: Whether _handle_invalid_variables should be called
        has_pivot: Whether the test should include a pivot view
    """
    # Set up variables with selection status from the test parameters
    variables = [
        {
            "variable_id": "14017122-43c7-41d1-9dfc-a960dfa3717f",
            "system_name": "employee_id",
            "display_name": "Employee Id",
            "is_selected": variable_selections["14017122-43c7-41d1-9dfc-a960dfa3717f"],
            "source_id": "source-1",  # Added source_id
            "meta_data": None,  # Added meta_data
            "source_variable_id": "employee_id",  # Added source_variable_id
            "source_type": "object",  # Added source_type
            "data_type_id": 4,  # Added data_type_id
            "source_cf_meta_data": None,  # Added source_cf_meta_data
        },
        {
            "variable_id": "fe76eb14-f2a6-4f28-a91f-fdbcfd3ac8fd",
            "system_name": "designation",
            "display_name": "Designation",
            "is_selected": variable_selections["fe76eb14-f2a6-4f28-a91f-fdbcfd3ac8fd"],
            "source_id": "source-1",  # Added source_id
            "meta_data": None,  # Added meta_data
            "source_variable_id": "designation",  # Added source_variable_id
            "source_type": "object",  # Added source_type
            "data_type_id": 4,  # Added data_type_id
            "source_cf_meta_data": None,  # Added source_cf_meta_data
        },
        {
            "variable_id": "b37238df-7774-4b29-92df-437fe9dd4467",
            "system_name": "employment_country",
            "display_name": "Employment Country",
            "is_selected": variable_selections["b37238df-7774-4b29-92df-437fe9dd4467"],
            "source_id": "source-1",  # Added source_id
            "meta_data": None,  # Added meta_data
            "source_variable_id": "employment_country",  # Added source_variable_id
            "source_type": "object",  # Added source_type
            "data_type_id": 4,  # Added data_type_id
            "source_cf_meta_data": None,  # Added source_cf_meta_data
        },
        {
            "variable_id": "f282e821-f065-4f6d-944a-5aed1d7440c1",
            "system_name": "fixed_pay",
            "display_name": "Fixed Pay",
            "is_selected": variable_selections["f282e821-f065-4f6d-944a-5aed1d7440c1"],
            "source_id": "source-1",  # Added source_id
            "meta_data": None,  # Added meta_data
            "source_variable_id": "fixed_pay",  # Added source_variable_id
            "source_type": "object",  # Added source_type
            "data_type_id": 1,  # Added data_type_id
            "source_cf_meta_data": None,  # Added source_cf_meta_data
        },
        {
            "variable_id": "f0f95d73-69ea-4443-99cc-4162a358bb01",
            "system_name": "payee_variable_pay",
            "display_name": "Variable Pay",
            "is_selected": variable_selections["f0f95d73-69ea-4443-99cc-4162a358bb01"],
            "source_id": "source-1",  # Added source_id
            "meta_data": None,  # Added meta_data
            "source_variable_id": "payee_variable_pay",  # Added source_variable_id
            "source_type": "object",  # Added source_type
            "data_type_id": 1,  # Added data_type_id
            "source_cf_meta_data": None,  # Added source_cf_meta_data
        },
    ]

    # Sample filter data with variables in function arguments
    filter_data = [
        {
            "token": {
                "key": "Contains(Employee Id, Designation)",
                "args": [
                    {
                        "token": {
                            "key": "employee_id",
                            "name": "Employee Id",
                            "path": "Employee Id << User",
                            "system_name": "employee_id",
                            "variable_id": "14017122-43c7-41d1-9dfc-a960dfa3717f",
                        },
                        "token_type": "DATASHEET_VARIABLES",
                    },
                    {
                        "token": {
                            "key": "designation",
                            "name": "Designation",
                            "path": "Designation << User",
                            "system_name": "designation",
                            "variable_id": "fe76eb14-f2a6-4f28-a91f-fdbcfd3ac8fd",
                        },
                        "token_type": "DATASHEET_VARIABLES",
                    },
                ],
                "name": "Contains(Employee Id, Designation)",
                "type": "VARIABLE",
                "data_type": "Boolean",
                "function_name": "Contains",
                "token_category": "DYNAMIC",
            },
            "token_type": "FUNCTIONS",
        },
        {"token": {"key": "AND", "name": "AND"}, "token_type": "OPERATORS"},
        {
            "token": {
                "key": "IsNotEmpty(Employment Country)",
                "args": [
                    {
                        "token": {
                            "key": "employment_country",
                            "name": "Employment Country",
                            "path": "Employment Country << User",
                            "system_name": "employment_country",
                            "variable_id": "b37238df-7774-4b29-92df-437fe9dd4467",
                        },
                        "token_type": "DATASHEET_VARIABLES",
                    }
                ],
                "name": "IsNotEmpty(Employment Country)",
                "type": "VARIABLE",
                "data_type": "Boolean",
                "function_name": "IsNotEmpty",
                "token_category": "DYNAMIC",
            },
            "token_type": "FUNCTIONS",
        },
        {"token": {"key": "AND", "name": "AND"}, "token_type": "OPERATORS"},
        {
            "token": {
                "key": "fixed_pay",
                "name": "Fixed Pay",
                "path": "Fixed Pay << User",
                "system_name": "fixed_pay",
                "variable_id": "f282e821-f065-4f6d-944a-5aed1d7440c1",
            },
            "token_type": "DATASHEET_VARIABLES",
        },
        {"token": {"key": "==", "name": "=="}, "token_type": "OPERATORS"},
        {
            "token": {
                "key": "payee_variable_pay",
                "name": "Variable Pay",
                "path": "Variable Pay << User",
                "system_name": "payee_variable_pay",
                "variable_id": "f0f95d73-69ea-4443-99cc-4162a358bb01",
            },
            "token_type": "DATASHEET_VARIABLES",
        },
    ]

    # Set up mocks
    mock_view_selector_instance = mock.MagicMock()
    mock_view_selector.return_value = mock_view_selector_instance

    # Create a mock view with a filter and possibly a pivot
    mock_view = mock.MagicMock()
    mock_view.filter_id = "test-filter-id"
    mock_view.pivot_id = "test-pivot-id" if has_pivot else None
    mock_view_selector_instance.get_datasheet_views.return_value = [mock_view]

    # Mock the filter data
    mock_view_selector_instance.get_filter_data.return_value = filter_data

    # Mock pivot data if needed
    if has_pivot:
        # Create pivot data with unselected variables
        pivot_data = {
            "index": ["designation"],  # Unselected variable
            "columns": [],
            "values": ["payee_variable_pay"],  # Unselected variable
            "aggfunc": {"payee_variable_pay": "sum"},
        }
        mock_view_selector_instance.get_pivot_data.return_value = pivot_data

    # Mock the AST creation
    mock_ast = {"ast": "mock_ast"}
    mock_create_ast.return_value = mock_ast

    # Mock the variable extractor
    mock_variable_extractor_instance = mock.MagicMock()
    mock_variable_extractor.return_value = mock_variable_extractor_instance
    mock_variable_extractor_instance.get_variables_used.return_value = [
        "employee_id",
        "designation",
        "employment_country",
    ]

    # Mock the DatasheetExpressionBoxUtils
    mock_expression_box_utils_instance = mock.MagicMock()
    mock_datasheet_expression_box_utils.return_value = (
        mock_expression_box_utils_instance
    )
    mock_expression_box_utils_instance.extract_variable_ids.return_value = {
        "14017122-43c7-41d1-9dfc-a960dfa3717f",  # employee_id
        "fe76eb14-f2a6-4f28-a91f-fdbcfd3ac8fd",  # designation
        "b37238df-7774-4b29-92df-437fe9dd4467",  # employment_country
        "f282e821-f065-4f6d-944a-5aed1d7440c1",  # fixed_pay
        "f0f95d73-69ea-4443-99cc-4162a358bb01",  # payee_variable_pay
    }

    # Set up mocks to prevent database access
    mock_datasheet = mock.MagicMock()
    mock_datasheet.databook_id = UUID("cc48a2a9-2d6b-4fba-bde0-28b8a4c5a838")
    mock_get_datasheet_record.return_value = mock_datasheet
    mock_populate_databook_id.return_value = UUID(
        "cc48a2a9-2d6b-4fba-bde0-28b8a4c5a838"
    )

    # Create the validator instance with our test data
    validator = DatasheetValidator(
        client_id=TEST_CLIENT_ID,
        datasheet_id="test-datasheet-id",
        variables=variables,
        transformations=[],  # Empty list for transformations
        source_variables=[],  # Empty list for source variables
        source_id="test-source-id",  # Mock source ID
        source_type="test-source-type",  # Mock source type
        has_source_changed=False,  # Default to False for testing
    )

    # Mock the _handle_invalid_variables method to capture the invalid variables
    validator._handle_invalid_variables = mock.MagicMock()

    # Call the method under test
    validator._is_unselected_variables_used_in_views()

    # Verify the behavior based on the test case
    if should_call_handle_invalid:
        # Verify that _handle_invalid_variables was called with the correct unselected variables
        validator._handle_invalid_variables.assert_called_once()
        args, _ = validator._handle_invalid_variables.call_args

        # The first argument should be the set of unselected variables
        unselected_vars = args[0]
        assert (
            set(unselected_vars) == expected_unselected
        ), f"Test case '{test_case}' failed: Expected unselected variables {expected_unselected}, got {unselected_vars}"

        # Verify that the context and source were passed correctly
        assert args[1] == "DELETE"  # Default context
        assert args[2] == "views"
    else:
        # Verify that _handle_invalid_variables was NOT called
        validator._handle_invalid_variables.assert_not_called()
