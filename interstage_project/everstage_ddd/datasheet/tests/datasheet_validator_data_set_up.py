import uuid
from uuid import UUI<PERSON>

import pytest
from django.utils import timezone

from everstage_ddd.datasheet.models import Databook, Datasheet, DatasheetVariable

# ruff: noqa: S104

TEST_CLIENT_ID = 3024
datasheet_id_1 = UUID(
    "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb"
)  # Base sheet with custom object as source
datasheet_id_2 = UUID(
    "57cbfd39-b826-4829-89bf-e4e4b2225076"
)  # Derived sheet with all transformations
datasheet_id_3 = UUID(
    "1090d6c2-0ef8-4990-bf48-359d45cb1dec"
)  # Base sheet with user report as source
datasheet_id_4 = UUID("934c99c7-bcbe-4706-b99f-a1e4b4695ca7")
# Base sheet with join transformation
datasheet_id_5 = UUID("46974ee7-edf0-4bce-84e8-d1fe6a9a8cce")
# Base sheet with join transformation as datasheet source

datasheet_id_6 = UUID("b2dd92d5-d46c-44b6-a23a-ae712407f38a")
# Base sheet with same source join transformation(two times)

datasheet_id_7 = UUID("b12af5c1-35f0-4b8f-b22d-991ea4395ef3")
# Base sheet with calculated field

datasheet_id_8 = UUID("382929d2-6294-4803-acc8-256ada17acec")

datasheet_id_9 = UUID("9cb6cc1f-52a0-44ac-97ee-35f7a7be47c3")
# Base sheet with hierarchy calculated field

datasheet_id_10 = UUID("7c5b57c2-109e-4e73-bf94-b2ab2c4f1aa6")
# Derived sheet from datasheet_id_9 with flatten transformation


class TestDatasheetConstructor:
    """
    Class to create datasheet objects and variables.
    """

    def __init__(
        self, datasheet_id: UUID, databook_id: UUID, datasheet_name: str
    ) -> None:
        self.client_id = TEST_CLIENT_ID
        self.datasheet_id = datasheet_id
        self.databook_id = databook_id
        self.datasheet_name = datasheet_name
        self.source_id = None
        self.source_type = None

    def create_datasheet_variables(
        self,
        system_name_to_variable_map: dict,
        calculated_fields: list,
    ):
        """
        Create datasheet variables for a given datasheet.
        """
        datasheet_variables = []

        for system_name, variable_details in system_name_to_variable_map.items():

            datasheet_variables.append(
                DatasheetVariable(
                    client_id=self.client_id,
                    databook_id=self.databook_id,
                    datasheet_id=self.datasheet_id,
                    variable_id=variable_details["variable_id"],
                    source_variable_id=variable_details["source_variable_id"],
                    display_name=variable_details["display_name"],
                    source_id=self.source_id,
                    source_type=self.source_type,
                    system_name=system_name,
                    data_type_id=variable_details["data_type_id"],
                    knowledge_begin_date=timezone.now(),
                )
            )

        if calculated_fields:
            datasheet_variables.extend(calculated_fields)

        DatasheetVariable.objects.bulk_create(datasheet_variables)

    def create_sheet_obj(
        self,
        ordered_columns: list[str],
        primary_key,
    ):
        """
        Create a datasheet object.
        """
        Datasheet.objects.create(
            client_id=TEST_CLIENT_ID,
            databook_id=self.databook_id,
            datasheet_id=self.datasheet_id,
            name=self.datasheet_name,
            source_type=self.source_type,
            source_id=self.source_id,
            order=1,
            primary_key=primary_key,
            additional_details={"updated_by": "<EMAIL>"},
            transformation_spec=[],
            ordered_columns=f"{{{','.join(ordered_columns)}}}",
            data_origin="custom_object",
            knowledge_begin_date=timezone.now(),
        )

    def create_sheet(
        self,
        source_id: int | str,
        source_type: str,
        system_name_to_variable_map: dict,
        calculated_fields: list | None = None,
    ):
        """
        Create a datasheet object.
        """
        self.source_id = source_id
        self.source_type = source_type

        if calculated_fields is None:
            calculated_fields = []

        variables = list(system_name_to_variable_map.keys())

        self.create_sheet_obj(variables, ["co_1_number"])

        self.create_datasheet_variables(system_name_to_variable_map, calculated_fields)


@pytest.mark.django_db
def datasheet_validator_create_data():
    databook_id_1 = UUID("869b4b9b-5e9f-49e1-b24c-e4c5cee825b0")

    Databook.objects.create(
        client_id=TEST_CLIENT_ID,
        databook_id=databook_id_1,
        name="Same Source",
        knowledge_begin_date=timezone.now(),
        is_archived=False,
        is_draft=False,
        created_by="<EMAIL>",
        created_at=timezone.now(),
    )

    tdc = TestDatasheetConstructor(
        datasheet_id=datasheet_id_1,
        databook_id=databook_id_1,
        datasheet_name="test",
    )

    system_name_to_variable_map = get_system_name_to_variable_details(datasheet_id_1)
    tdc.create_sheet(1, "object", system_name_to_variable_map)

    tdc = TestDatasheetConstructor(
        datasheet_id=datasheet_id_8,
        databook_id=databook_id_1,
        datasheet_name="SHEET-8",
    )

    system_name_to_variable_map = get_system_name_to_variable_details(datasheet_id_8)
    tdc.create_sheet(1, "object", system_name_to_variable_map)

    tdc = TestDatasheetConstructor(
        datasheet_id=datasheet_id_9,
        databook_id=databook_id_1,
        datasheet_name="SHEET-9",
    )

    system_name_to_variable_map = get_system_name_to_variable_details(datasheet_id_9)
    tdc.create_sheet(
        1,
        "object",
        system_name_to_variable_map,
        calculated_fields=[
            DatasheetVariable(
                client_id=3024,
                datasheet_id=datasheet_id_9,
                databook_id=databook_id_1,
                meta_data={
                    "token": {
                        "name": "GenerateHierarchy(Email, CURRENT_DATE, adjustments, user, Employee Email Id, Reporting Manager EmailId, Effective Start Date, Effective End Date)",
                        "key": "GenerateHierarchy(Email, CURRENT_DATE, adjustments, user, Employee Email Id, Reporting Manager EmailId, Effective Start Date, Effective End Date)",
                        "function_name": "Hierarchy",
                        "data_type": "Hierarchy",
                        "type": "VARIABLE",
                        "token_category": "DYNAMIC",
                        "args": [
                            {
                                "token_type": "DATASHEET_VARIABLES",
                                "token": {
                                    "key": "co_1_email",
                                    "name": "Email",
                                    "variable_id": "be7946eb-ec31-48cd-a6d8-a5a120f5b1fa",
                                },
                            },
                            "CURRENT_DATE",
                            {
                                "name": "adjustments",
                                "value": "9605d772-f376-4cdc-b9a6-40ab8d2e2645",
                            },
                            {
                                "name": "user",
                                "value": "f6b34a0a-f83d-43b5-b1cb-5ffbeae1417a",
                            },
                            {
                                "name": "Employee Email Id",
                                "value": "employee_email_id",
                                "variable_id": "cdab3669-3df3-4cc3-a939-9b36dc30c6df",
                            },
                            {
                                "name": "Reporting Manager EmailId",
                                "value": "reporting_manager_email_id",
                                "variable_id": "536f0903-c143-4810-8e4a-1c49efcf5c05",
                            },
                            {
                                "name": "Effective Start Date",
                                "value": "effective_start_date",
                                "variable_id": "668ac693-5de1-4ad2-bbec-9a7d42040a03",
                            },
                            {
                                "name": "Effective End Date",
                                "value": "effective_end_date",
                                "variable_id": "a964c4d1-c28b-42ce-bcff-e30917e1a65e",
                            },
                            {"name": "hierarchy_for_data_type_id", "value": 12},
                            {
                                "name": "reference_sheet_data_origin",
                                "value": "system_object",
                            },
                        ],
                        "is_system_generated": True,
                    },
                    "token_type": "FUNCTIONS",
                    "used_system_names": [],
                },
                variable_id="95af3a89-a40e-44fc-bf4b-9d4692644251",
                source_cf_meta_data=None,
                source_id=datasheet_id_9,
                warning=None,
                system_name="cf_hierarchy_field",
                display_name="Hierarchy Field",
                data_type_id=15,
                source_variable_id=None,
                source_type="datasheet",
                is_selected=True,
                is_primary=False,
                field_order=1,
                knowledge_begin_date=timezone.now(),
            )
        ],
    )

    tdc = TestDatasheetConstructor(
        datasheet_id=datasheet_id_10,
        databook_id=databook_id_1,
        datasheet_name="SHEET-10",
    )

    system_name_to_variable_map = get_system_name_to_variable_details(datasheet_id_10)
    tdc.create_sheet(
        source_id=str(datasheet_id_9),
        source_type="datasheet",
        system_name_to_variable_map=system_name_to_variable_map,
        calculated_fields=[
            DatasheetVariable(
                client_id=3024,
                datasheet_id=datasheet_id_10,
                databook_id=databook_id_1,
                meta_data=None,
                variable_id="1df46bfd-126b-41c0-aa85-0cafb8f0b8e9",
                source_cf_meta_data={
                    "datasheet_id": datasheet_id_9,
                    "hierarchy_for_data_type_id": 12,
                },
                source_id=datasheet_id_9,
                warning=None,
                system_name="cf_hierarchy_field",
                display_name="Hierarchy Field",
                data_type_id=15,
                source_variable_id=None,
                source_type="datasheet",
                is_selected=True,
                is_primary=False,
                field_order=1,
                knowledge_begin_date=timezone.now(),
            )
        ],
    )

    Datasheet.objects.create(
        client_id=TEST_CLIENT_ID,
        databook_id=databook_id_1,
        datasheet_id=datasheet_id_2,
        name="test-derived-1",
        source_type="datasheet",
        source_id=datasheet_id_1,
        order=2,
        primary_key=[
            "lhs_ts_employee_email_id",
            "lhs_ts_effective_start_date",
            "lhs_ts_effective_end_date",
            "rhs_co_1_number",
        ],
        transformation_spec=[
            {
                "on": {
                    "lhs": [
                        {
                            "source_id": "1",
                            "is_primary": True,
                            "source_type": "object",
                            "system_name": "co_1_number",
                            "variable_id": "8113951d-6ab2-48ff-af64-ab96579abd0b",
                            "data_type_id": 1,
                            "display_name": "Number",
                            "source_variable_id": "co_1_number",
                            "source_cf_meta_data": None,
                        }
                    ],
                    "rhs": [
                        {
                            "is_primary": True,
                            "system_name": "co_1_number",
                            "variable_id": "8113951d-6ab2-48ff-af64-ab96579abd0b",
                            "data_type_id": 1,
                            "display_name": "Number",
                            "source_variable_id": "co_1_number",
                            "source_cf_meta_data": None,
                        }
                    ],
                },
                "key": "be02df10-0484-490e-a7db-27d1d7cd12b8",
                "type": "JOIN",
                "with": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                "columns": {
                    "lhs": [
                        {
                            "source_id": "1",
                            "is_primary": True,
                            "source_type": "object",
                            "system_name": "co_1_number",
                            "variable_id": "8113951d-6ab2-48ff-af64-ab96579abd0b",
                            "data_type_id": 1,
                            "display_name": "Number",
                            "system_generated": False,
                            "source_variable_id": "co_1_number",
                            "source_cf_meta_data": None,
                        },
                        {
                            "source_id": "1",
                            "is_primary": False,
                            "source_type": "object",
                            "system_name": "co_1_email",
                            "variable_id": "1b0b9972-764c-426a-891d-1d7338645519",
                            "data_type_id": 12,
                            "display_name": "Email",
                            "system_generated": False,
                            "source_variable_id": "co_1_email",
                            "source_cf_meta_data": None,
                        },
                    ],
                    "rhs": [
                        {
                            "is_primary": True,
                            "system_name": "co_1_number",
                            "variable_id": "8113951d-6ab2-48ff-af64-ab96579abd0b",
                            "data_type_id": 1,
                            "display_name": "Number",
                            "source_variable_id": "co_1_number",
                            "source_cf_meta_data": None,
                        },
                        {
                            "is_primary": False,
                            "system_name": "co_1_boolean",
                            "variable_id": "c66f062b-4bb1-46c8-9f4d-d3b3663ee1f2",
                            "data_type_id": 3,
                            "display_name": "Boolean",
                            "source_variable_id": "co_1_boolean",
                            "source_cf_meta_data": None,
                        },
                        {
                            "is_primary": False,
                            "system_name": "co_1_date_1",
                            "variable_id": "a8f6f7d8-9731-4dc8-bc1a-328d093b389b",
                            "data_type_id": 2,
                            "display_name": "Date-1",
                            "source_variable_id": "co_1_date_1",
                            "source_cf_meta_data": None,
                        },
                    ],
                },
                "is_valid": True,
                "join_type": "LEFT",
                "output_columns": [
                    {
                        "source_id": "1",
                        "is_primary": True,
                        "source_type": "object",
                        "system_name": "lhs_co_1_number",
                        "variable_id": "8113951d-6ab2-48ff-af64-ab96579abd0b",
                        "data_type_id": 1,
                        "display_name": "Number",
                        "system_generated": False,
                        "source_variable_id": "co_1_number",
                        "source_cf_meta_data": None,
                    },
                    {
                        "source_id": "1",
                        "is_primary": False,
                        "source_type": "object",
                        "system_name": "lhs_co_1_email",
                        "variable_id": "1b0b9972-764c-426a-891d-1d7338645519",
                        "data_type_id": 12,
                        "display_name": "Email",
                        "system_generated": False,
                        "source_variable_id": "co_1_email",
                        "source_cf_meta_data": None,
                    },
                    {
                        "source_id": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                        "is_primary": True,
                        "source_type": "datasheet",
                        "system_name": "rhs_co_1_number",
                        "variable_id": "8113951d-6ab2-48ff-af64-ab96579abd0b__ss__1",
                        "data_type_id": 1,
                        "display_name": "Number",
                        "source_variable_id": "co_1_number",
                        "source_cf_meta_data": None,
                    },
                    {
                        "source_id": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                        "is_primary": False,
                        "source_type": "datasheet",
                        "system_name": "rhs_co_1_boolean",
                        "variable_id": "c66f062b-4bb1-46c8-9f4d-d3b3663ee1f2__ss__1",
                        "data_type_id": 3,
                        "display_name": "Boolean",
                        "source_variable_id": "co_1_boolean",
                        "source_cf_meta_data": None,
                    },
                    {
                        "source_id": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                        "is_primary": False,
                        "source_type": "datasheet",
                        "system_name": "rhs_co_1_date_1",
                        "variable_id": "a8f6f7d8-9731-4dc8-bc1a-328d093b389b__ss__1",
                        "data_type_id": 2,
                        "display_name": "Date-1",
                        "source_variable_id": "co_1_date_1",
                        "source_cf_meta_data": None,
                    },
                ],
                "with_databook_id": "869b4b9b-5e9f-49e1-b24c-e4c5cee825b0",
                "transformation_id": "be02df10-0484-490e-a7db-27d1d7cd12b8",
                "used_variable_ids": [
                    "c66f062b-4bb1-46c8-9f4d-d3b3663ee1f2",
                    "1b0b9972-764c-426a-891d-1d7338645519",
                    "8113951d-6ab2-48ff-af64-ab96579abd0b",
                    "a8f6f7d8-9731-4dc8-bc1a-328d093b389b",
                ],
                "transformation_source_map": {
                    "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb_datasheet": 1
                },
            },
            {
                "on": [
                    {
                        "lhs": {
                            "source_id": "1",
                            "is_primary": True,
                            "source_type": "object",
                            "system_name": "lhs_co_1_number",
                            "variable_id": "8113951d-6ab2-48ff-af64-ab96579abd0b",
                            "data_type_id": 1,
                            "display_name": "Number",
                            "system_generated": False,
                            "source_variable_id": "co_1_number",
                            "source_cf_meta_data": None,
                        },
                        "rhs": {
                            "client_id": 3024,
                            "meta_data": None,
                            "source_id": "1",
                            "is_primary": True,
                            "databook_id": UUID("306d881e-bb1a-4c6f-bb95-b0475d50eb2c"),
                            "field_order": 0,
                            "is_selected": True,
                            "source_name": "custom_object_1",
                            "source_type": "object",
                            "system_name": "co_1_number",
                            "variable_id": "8113951d-6ab2-48ff-af64-ab96579abd0b",
                            "data_type_id": 1,
                            "datasheet_id": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                            "display_name": "Number",
                            "is_dependent": True,
                            "source_variable_id": "co_1_number",
                            "source_cf_meta_data": None,
                            "source_name_history": "Number << custom_object_1",
                        },
                        "col_name": "lhs_co_1_number",
                    },
                    {
                        "lhs": {
                            "source_id": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                            "is_primary": False,
                            "source_type": "datasheet",
                            "system_name": "rhs_co_1_boolean",
                            "variable_id": "c66f062b-4bb1-46c8-9f4d-d3b3663ee1f2__ss__1",
                            "data_type_id": 3,
                            "display_name": "Boolean",
                            "system_generated": False,
                            "source_variable_id": "co_1_boolean",
                            "source_cf_meta_data": None,
                        },
                        "rhs": {
                            "client_id": 3024,
                            "meta_data": None,
                            "source_id": "1",
                            "is_primary": False,
                            "databook_id": UUID("306d881e-bb1a-4c6f-bb95-b0475d50eb2c"),
                            "field_order": 0,
                            "is_selected": True,
                            "source_name": "custom_object_1",
                            "source_type": "object",
                            "system_name": "co_1_boolean",
                            "variable_id": "c66f062b-4bb1-46c8-9f4d-d3b3663ee1f2",
                            "data_type_id": 3,
                            "datasheet_id": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                            "display_name": "Boolean",
                            "is_dependent": True,
                            "source_variable_id": "co_1_boolean",
                            "source_cf_meta_data": None,
                            "source_name_history": "Boolean << custom_object_1",
                        },
                        "col_name": "rhs_co_1_boolean",
                    },
                    {
                        "lhs": {
                            "source_id": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                            "is_primary": False,
                            "source_type": "datasheet",
                            "system_name": "rhs_co_1_date_1",
                            "variable_id": "a8f6f7d8-9731-4dc8-bc1a-328d093b389b__ss__1",
                            "data_type_id": 2,
                            "display_name": "Date-1",
                            "system_generated": False,
                            "source_variable_id": "co_1_date_1",
                            "source_cf_meta_data": None,
                        },
                        "rhs": {
                            "client_id": 3024,
                            "meta_data": None,
                            "source_id": "1",
                            "is_primary": False,
                            "databook_id": UUID("306d881e-bb1a-4c6f-bb95-b0475d50eb2c"),
                            "field_order": 0,
                            "is_selected": True,
                            "source_name": "custom_object_1",
                            "source_type": "object",
                            "system_name": "co_1_date_1",
                            "variable_id": "a8f6f7d8-9731-4dc8-bc1a-328d093b389b",
                            "data_type_id": 2,
                            "datasheet_id": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                            "display_name": "Date-1",
                            "is_dependent": True,
                            "source_variable_id": "co_1_date_1",
                            "source_cf_meta_data": None,
                            "source_name_history": "Date-1 << custom_object_1",
                        },
                        "col_name": "rhs_co_1_date_1",
                    },
                    {
                        "lhs": {
                            "source_id": "1",
                            "is_primary": False,
                            "source_type": "object",
                            "system_name": "lhs_co_1_email",
                            "variable_id": "1b0b9972-764c-426a-891d-1d7338645519",
                            "data_type_id": 12,
                            "display_name": "Email",
                            "system_generated": False,
                            "source_variable_id": "co_1_email",
                            "source_cf_meta_data": None,
                        },
                        "rhs": {
                            "client_id": 3024,
                            "meta_data": None,
                            "source_id": "1",
                            "is_primary": False,
                            "databook_id": UUID("306d881e-bb1a-4c6f-bb95-b0475d50eb2c"),
                            "field_order": 0,
                            "is_selected": True,
                            "source_name": "custom_object_1",
                            "source_type": "object",
                            "system_name": "co_1_email",
                            "variable_id": "1b0b9972-764c-426a-891d-1d7338645519",
                            "data_type_id": 12,
                            "datasheet_id": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                            "display_name": "Email",
                            "is_dependent": True,
                            "source_variable_id": "co_1_email",
                            "source_cf_meta_data": None,
                            "source_name_history": "Email << custom_object_1",
                        },
                        "col_name": "lhs_co_1_email",
                    },
                ],
                "key": "cff3ce0d-0395-4381-a610-ceea2dae4634",
                "type": "UNION",
                "with": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                "is_valid": True,
                "union_type": "DISTINCT",
                "output_columns": [
                    {
                        "source_id": "1",
                        "is_primary": True,
                        "source_type": "object",
                        "system_name": "lhs_co_1_number",
                        "variable_id": "8113951d-6ab2-48ff-af64-ab96579abd0b",
                        "data_type_id": 1,
                        "display_name": "Number",
                        "system_generated": False,
                        "source_variable_id": "co_1_number",
                        "source_cf_meta_data": None,
                    },
                    {
                        "source_id": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                        "is_primary": True,
                        "source_type": "datasheet",
                        "system_name": "rhs_co_1_boolean",
                        "variable_id": "c66f062b-4bb1-46c8-9f4d-d3b3663ee1f2__ss__1",
                        "data_type_id": 3,
                        "display_name": "Boolean",
                        "system_generated": False,
                        "source_variable_id": "co_1_boolean",
                        "source_cf_meta_data": None,
                    },
                    {
                        "source_id": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                        "is_primary": True,
                        "source_type": "datasheet",
                        "system_name": "rhs_co_1_date_1",
                        "variable_id": "a8f6f7d8-9731-4dc8-bc1a-328d093b389b__ss__1",
                        "data_type_id": 2,
                        "display_name": "Date-1",
                        "system_generated": False,
                        "source_variable_id": "co_1_date_1",
                        "source_cf_meta_data": None,
                    },
                    {
                        "source_id": "1",
                        "is_primary": True,
                        "source_type": "object",
                        "system_name": "lhs_co_1_email",
                        "variable_id": "1b0b9972-764c-426a-891d-1d7338645519",
                        "data_type_id": 12,
                        "display_name": "Email",
                        "system_generated": False,
                        "source_variable_id": "co_1_email",
                        "source_cf_meta_data": None,
                    },
                ],
                "with_databook_id": "869b4b9b-5e9f-49e1-b24c-e4c5cee825b0",
                "transformation_id": "cff3ce0d-0395-4381-a610-ceea2dae4634",
                "used_variable_ids": [
                    "a8f6f7d8-9731-4dc8-bc1a-328d093b389b__ss__1",
                    "c66f062b-4bb1-46c8-9f4d-d3b3663ee1f2__ss__1",
                    "c66f062b-4bb1-46c8-9f4d-d3b3663ee1f2",
                    "1b0b9972-764c-426a-891d-1d7338645519",
                    "8113951d-6ab2-48ff-af64-ab96579abd0b",
                    "a8f6f7d8-9731-4dc8-bc1a-328d093b389b",
                ],
                "transformation_source_map": {
                    "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb_datasheet": 1
                },
            },
            {
                "by": [
                    {
                        "source_id": "1",
                        "is_primary": True,
                        "source_type": "object",
                        "system_name": "lhs_co_1_number",
                        "variable_id": "8113951d-6ab2-48ff-af64-ab96579abd0b",
                        "data_type_id": 1,
                        "display_name": "Number",
                        "system_generated": False,
                        "source_variable_id": "co_1_number",
                        "source_cf_meta_data": None,
                    },
                    {
                        "source_id": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                        "is_primary": True,
                        "source_type": "datasheet",
                        "system_name": "rhs_co_1_boolean",
                        "variable_id": "c66f062b-4bb1-46c8-9f4d-d3b3663ee1f2__ss__1",
                        "data_type_id": 3,
                        "display_name": "Boolean",
                        "system_generated": False,
                        "source_variable_id": "co_1_boolean",
                        "source_cf_meta_data": None,
                    },
                    {
                        "source_id": "1",
                        "is_primary": True,
                        "source_type": "object",
                        "system_name": "lhs_co_1_email",
                        "variable_id": "1b0b9972-764c-426a-891d-1d7338645519",
                        "data_type_id": 12,
                        "display_name": "Email",
                        "system_generated": False,
                        "source_variable_id": "co_1_email",
                        "source_cf_meta_data": None,
                    },
                ],
                "key": "fcbbc2f2-2aa6-4e4b-9bfc-b944ae9022c0",
                "type": "GROUP_BY",
                "is_valid": True,
                "aggregations": [
                    {
                        "of": "rhs_co_1_date_1",
                        "col_name": "count_rhs_co_1_date_1",
                        "function": "COUNT",
                        "variable_id": "a8f6f7d8-9731-4dc8-bc1a-328d093b389b__ss__1",
                        "data_type_id": 1,
                    }
                ],
                "output_columns": [
                    {
                        "source_id": "1",
                        "is_primary": True,
                        "source_type": "object",
                        "system_name": "lhs_co_1_number",
                        "variable_id": "8113951d-6ab2-48ff-af64-ab96579abd0b",
                        "data_type_id": 1,
                        "display_name": "Number",
                        "system_generated": False,
                        "source_variable_id": "co_1_number",
                        "source_cf_meta_data": None,
                    },
                    {
                        "source_id": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                        "is_primary": True,
                        "source_type": "datasheet",
                        "system_name": "rhs_co_1_boolean",
                        "variable_id": "c66f062b-4bb1-46c8-9f4d-d3b3663ee1f2__ss__1",
                        "data_type_id": 3,
                        "display_name": "Boolean",
                        "system_generated": False,
                        "source_variable_id": "co_1_boolean",
                        "source_cf_meta_data": None,
                    },
                    {
                        "source_id": "1",
                        "is_primary": True,
                        "source_type": "object",
                        "system_name": "lhs_co_1_email",
                        "variable_id": "1b0b9972-764c-426a-891d-1d7338645519",
                        "data_type_id": 12,
                        "display_name": "Email",
                        "system_generated": False,
                        "source_variable_id": "co_1_email",
                        "source_cf_meta_data": None,
                    },
                    {
                        "meta_data": None,
                        "source_id": "57cbfd39-b826-4829-89bf-e4e4b2225076",
                        "is_primary": False,
                        "column_name": "count_rhs_co_1_date_1",
                        "is_selected": True,
                        "source_type": "datasheet",
                        "system_name": "count_rhs_co_1_date_1",
                        "variable_id": "04da6570-5640-4fa5-840d-dd22f2dcc512",
                        "data_type_id": 1,
                        "display_name": "COUNT::DATE-1",
                        "system_generated": True,
                        "source_variable_id": None,
                        "source_cf_meta_data": None,
                    },
                ],
                "transformation_id": "fcbbc2f2-2aa6-4e4b-9bfc-b944ae9022c0",
                "used_variable_ids": [
                    "a8f6f7d8-9731-4dc8-bc1a-328d093b389b__ss__1",
                    "c66f062b-4bb1-46c8-9f4d-d3b3663ee1f2__ss__1",
                    "c66f062b-4bb1-46c8-9f4d-d3b3663ee1f2",
                    "1b0b9972-764c-426a-891d-1d7338645519",
                    "8113951d-6ab2-48ff-af64-ab96579abd0b",
                    "a8f6f7d8-9731-4dc8-bc1a-328d093b389b",
                ],
                "transformation_source_map": {
                    "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb_datasheet": 1
                },
            },
            {
                "on": {
                    "lhs": [
                        {
                            "source_id": "1",
                            "is_primary": True,
                            "source_type": "object",
                            "system_name": "lhs_co_1_number",
                            "variable_id": "8113951d-6ab2-48ff-af64-ab96579abd0b",
                            "data_type_id": 1,
                            "display_name": "Number",
                            "source_variable_id": "co_1_number",
                            "source_cf_meta_data": None,
                        }
                    ],
                    "rhs": [
                        {
                            "is_primary": True,
                            "system_name": "co_1_number",
                            "variable_id": "8113951d-6ab2-48ff-af64-ab96579abd0b",
                            "data_type_id": 1,
                            "display_name": "Number",
                            "source_variable_id": "co_1_number",
                            "source_cf_meta_data": None,
                        }
                    ],
                },
                "key": "4631bb68-42a4-475e-b740-397b9d175eb9",
                "type": "JOIN",
                "with": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                "columns": {
                    "lhs": [
                        {
                            "source_id": "1",
                            "is_primary": True,
                            "source_type": "object",
                            "system_name": "lhs_co_1_number",
                            "variable_id": "8113951d-6ab2-48ff-af64-ab96579abd0b",
                            "data_type_id": 1,
                            "display_name": "Number",
                            "system_generated": False,
                            "source_variable_id": "co_1_number",
                            "source_cf_meta_data": None,
                        },
                        {
                            "source_id": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                            "is_primary": True,
                            "source_type": "datasheet",
                            "system_name": "rhs_co_1_boolean",
                            "variable_id": "c66f062b-4bb1-46c8-9f4d-d3b3663ee1f2__ss__1",
                            "data_type_id": 3,
                            "display_name": "Boolean",
                            "system_generated": False,
                            "source_variable_id": "co_1_boolean",
                            "source_cf_meta_data": None,
                        },
                        {
                            "source_id": "1",
                            "is_primary": True,
                            "source_type": "object",
                            "system_name": "lhs_co_1_email",
                            "variable_id": "1b0b9972-764c-426a-891d-1d7338645519",
                            "data_type_id": 12,
                            "display_name": "Email",
                            "system_generated": False,
                            "source_variable_id": "co_1_email",
                            "source_cf_meta_data": None,
                        },
                        {
                            "source_id": "57cbfd39-b826-4829-89bf-e4e4b2225076",
                            "is_primary": False,
                            "source_type": "datasheet",
                            "system_name": "count_rhs_co_1_date_1",
                            "variable_id": "04da6570-5640-4fa5-840d-dd22f2dcc512",
                            "data_type_id": 1,
                            "display_name": "COUNT::DATE-1",
                            "system_generated": True,
                            "source_variable_id": None,
                            "source_cf_meta_data": None,
                        },
                    ],
                    "rhs": [
                        {
                            "is_primary": True,
                            "system_name": "co_1_number",
                            "variable_id": "8113951d-6ab2-48ff-af64-ab96579abd0b",
                            "data_type_id": 1,
                            "display_name": "Number",
                            "source_variable_id": "co_1_number",
                            "source_cf_meta_data": None,
                        }
                    ],
                },
                "is_valid": True,
                "join_type": "LEFT",
                "output_columns": [
                    {
                        "source_id": "1",
                        "is_primary": True,
                        "source_type": "object",
                        "system_name": "lhs_lhs_co_1_number",
                        "variable_id": "8113951d-6ab2-48ff-af64-ab96579abd0b",
                        "data_type_id": 1,
                        "display_name": "Number",
                        "system_generated": False,
                        "source_variable_id": "co_1_number",
                        "source_cf_meta_data": None,
                    },
                    {
                        "source_id": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                        "is_primary": True,
                        "source_type": "datasheet",
                        "system_name": "lhs_rhs_co_1_boolean",
                        "variable_id": "c66f062b-4bb1-46c8-9f4d-d3b3663ee1f2__ss__1",
                        "data_type_id": 3,
                        "display_name": "Boolean",
                        "system_generated": False,
                        "source_variable_id": "co_1_boolean",
                        "source_cf_meta_data": None,
                    },
                    {
                        "source_id": "1",
                        "is_primary": True,
                        "source_type": "object",
                        "system_name": "lhs_lhs_co_1_email",
                        "variable_id": "1b0b9972-764c-426a-891d-1d7338645519",
                        "data_type_id": 12,
                        "display_name": "Email",
                        "system_generated": False,
                        "source_variable_id": "co_1_email",
                        "source_cf_meta_data": None,
                    },
                    {
                        "source_id": "57cbfd39-b826-4829-89bf-e4e4b2225076",
                        "is_primary": False,
                        "source_type": "datasheet",
                        "system_name": "lhs_count_rhs_co_1_date_1",
                        "variable_id": "04da6570-5640-4fa5-840d-dd22f2dcc512",
                        "data_type_id": 1,
                        "display_name": "COUNT::DATE-1",
                        "system_generated": True,
                        "source_variable_id": None,
                        "source_cf_meta_data": None,
                    },
                    {
                        "source_id": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                        "is_primary": True,
                        "source_type": "datasheet",
                        "system_name": "rhs_co_1_number",
                        "variable_id": "8113951d-6ab2-48ff-af64-ab96579abd0b__ss__2",
                        "data_type_id": 1,
                        "display_name": "Number",
                        "source_variable_id": "co_1_number",
                        "source_cf_meta_data": None,
                    },
                ],
                "with_databook_id": "869b4b9b-5e9f-49e1-b24c-e4c5cee825b0",
                "transformation_id": "4631bb68-42a4-475e-b740-397b9d175eb9",
                "used_variable_ids": [
                    "a8f6f7d8-9731-4dc8-bc1a-328d093b389b__ss__1",
                    "c66f062b-4bb1-46c8-9f4d-d3b3663ee1f2__ss__1",
                    "1b0b9972-764c-426a-891d-1d7338645519",
                    "c66f062b-4bb1-46c8-9f4d-d3b3663ee1f2",
                    "04da6570-5640-4fa5-840d-dd22f2dcc512",
                    "8113951d-6ab2-48ff-af64-ab96579abd0b",
                    "a8f6f7d8-9731-4dc8-bc1a-328d093b389b",
                ],
                "transformation_source_map": {
                    "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb_datasheet": 2
                },
            },
            {
                "key": "ed80190e-fc64-4dee-b29c-4fda83066c46",
                "meta": [
                    {
                        "source_id": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                        "source_type": "datasheet",
                        "input_columns": [
                            {
                                "system_name": "ts_1_lhs_lhs_co_1_number",
                                "data_type_id": 1,
                                "display_name": "Number",
                            },
                            {
                                "system_name": "ts_1_lhs_rhs_co_1_boolean",
                                "data_type_id": 3,
                                "display_name": "Boolean",
                            },
                            {
                                "system_name": "ts_1_lhs_lhs_co_1_email",
                                "data_type_id": 12,
                                "display_name": "Email",
                            },
                            {
                                "system_name": "ts_1_lhs_count_rhs_co_1_date_1",
                                "data_type_id": 1,
                                "display_name": "COUNT::DATE-1",
                            },
                            {
                                "system_name": "ts_1_rhs_co_1_number",
                                "data_type_id": 1,
                                "display_name": "Number",
                            },
                        ],
                        "output_columns": [
                            {
                                "system_name": "ts_1_lhs_lhs_co_1_number",
                                "data_type_id": 1,
                                "display_name": "Number",
                            },
                            {
                                "system_name": "ts_1_lhs_rhs_co_1_boolean",
                                "data_type_id": 3,
                                "display_name": "Boolean",
                            },
                            {
                                "system_name": "ts_1_lhs_count_rhs_co_1_date_1",
                                "data_type_id": 1,
                                "display_name": "COUNT::DATE-1",
                            },
                            {
                                "system_name": "ts_1_rhs_co_1_number",
                                "data_type_id": 1,
                                "display_name": "Number",
                            },
                        ],
                        "email_id_column": {
                            "value": "lhs_lhs_co_1_email",
                            "variable_id": "1b0b9972-764c-426a-891d-1d7338645519",
                        },
                        "end_date_column": {"value": None, "variable_id": None},
                        "with_databook_id": "869b4b9b-5e9f-49e1-b24c-e4c5cee825b0",
                        "start_date_column": {"value": None, "variable_id": None},
                        "has_effective_dates": False,
                    },
                    {
                        "source_id": "user",
                        "source_type": "report",
                        "input_columns": [
                            {
                                "system_name": "ts_2_effective_start_date",
                                "data_type_id": 2,
                                "display_name": "Effective Start Date",
                            },
                            {
                                "system_name": "ts_2_effective_end_date",
                                "data_type_id": 2,
                                "display_name": "Effective End Date",
                            },
                            {
                                "system_name": "ts_2_employee_id",
                                "data_type_id": 4,
                                "display_name": "Employee Id",
                            },
                            {
                                "system_name": "ts_2_joining_date",
                                "data_type_id": 2,
                                "display_name": "Joining Date",
                            },
                            {
                                "system_name": "ts_2_time_zone",
                                "data_type_id": 4,
                                "display_name": "Current Time Zone",
                            },
                            {
                                "system_name": "ts_2_designation",
                                "data_type_id": 4,
                                "display_name": "Designation",
                            },
                            {
                                "system_name": "ts_2_employment_country",
                                "data_type_id": 4,
                                "display_name": "Employment Country",
                            },
                            {
                                "system_name": "ts_2_payout_frequency",
                                "data_type_id": 4,
                                "display_name": "Payout Frequency",
                            },
                            {
                                "system_name": "ts_2_pay_currency",
                                "data_type_id": 4,
                                "display_name": "Payout Currency",
                            },
                            {
                                "system_name": "ts_2_fixed_pay",
                                "data_type_id": 1,
                                "display_name": "Fixed Pay",
                            },
                            {
                                "system_name": "ts_2_payee_variable_pay",
                                "data_type_id": 1,
                                "display_name": "Variable Pay",
                            },
                            {
                                "system_name": "ts_2_first_name",
                                "data_type_id": 4,
                                "display_name": "First Name",
                            },
                            {
                                "system_name": "ts_2_last_name",
                                "data_type_id": 4,
                                "display_name": "Last Name",
                            },
                            {
                                "system_name": "ts_2_user_role",
                                "data_type_id": 4,
                                "display_name": "Current User Role",
                            },
                            {
                                "system_name": "ts_2_created_date",
                                "data_type_id": 2,
                                "display_name": "Created Date",
                            },
                            {
                                "system_name": "ts_2_created_by",
                                "data_type_id": 12,
                                "display_name": "Created By",
                            },
                            {
                                "system_name": "ts_2_exit_date",
                                "data_type_id": 2,
                                "display_name": "Exit Date",
                            },
                            {
                                "system_name": "ts_2_last_commission_date",
                                "data_type_id": 2,
                                "display_name": "Exit Last Commission Date",
                            },
                            {
                                "system_name": "ts_2_status",
                                "data_type_id": 4,
                                "display_name": "Status",
                            },
                            {
                                "system_name": "ts_2_employee_email_id",
                                "data_type_id": 12,
                                "display_name": "Employee Email Id",
                            },
                            {
                                "system_name": "ts_2_reporting_manager_email_id",
                                "data_type_id": 12,
                                "display_name": "Reporting Manager EmailId",
                            },
                            {
                                "system_name": "ts_2_reporting_manager_name",
                                "data_type_id": 4,
                                "display_name": "Reporting Manager Name",
                            },
                            {
                                "system_name": "ts_2_active_plan",
                                "data_type_id": 4,
                                "display_name": "Active Primary Plan",
                            },
                            {
                                "system_name": "ts_2_active_spiffs",
                                "data_type_id": 4,
                                "display_name": "Active Spiff Plan",
                            },
                            {
                                "system_name": "ts_2_active_quota_categories",
                                "data_type_id": 4,
                                "display_name": "Active Quota Categories",
                            },
                            {
                                "system_name": "ts_2_payee_or_manager",
                                "data_type_id": 4,
                                "display_name": "Payee/Manager",
                            },
                        ],
                        "output_columns": [
                            {
                                "system_name": "ts_2_effective_start_date",
                                "data_type_id": 2,
                                "display_name": "Effective Start Date",
                            },
                            {
                                "system_name": "ts_2_effective_end_date",
                                "data_type_id": 2,
                                "display_name": "Effective End Date",
                            },
                            {
                                "system_name": "ts_2_employee_id",
                                "data_type_id": 4,
                                "display_name": "Employee Id",
                            },
                            {
                                "system_name": "ts_2_joining_date",
                                "data_type_id": 2,
                                "display_name": "Joining Date",
                            },
                            {
                                "system_name": "ts_2_time_zone",
                                "data_type_id": 4,
                                "display_name": "Current Time Zone",
                            },
                            {
                                "system_name": "ts_2_designation",
                                "data_type_id": 4,
                                "display_name": "Designation",
                            },
                            {
                                "system_name": "ts_2_employment_country",
                                "data_type_id": 4,
                                "display_name": "Employment Country",
                            },
                            {
                                "system_name": "ts_2_payout_frequency",
                                "data_type_id": 4,
                                "display_name": "Payout Frequency",
                            },
                            {
                                "system_name": "ts_2_pay_currency",
                                "data_type_id": 4,
                                "display_name": "Payout Currency",
                            },
                            {
                                "system_name": "ts_2_fixed_pay",
                                "data_type_id": 1,
                                "display_name": "Fixed Pay",
                            },
                            {
                                "system_name": "ts_2_payee_variable_pay",
                                "data_type_id": 1,
                                "display_name": "Variable Pay",
                            },
                            {
                                "system_name": "ts_2_first_name",
                                "data_type_id": 4,
                                "display_name": "First Name",
                            },
                            {
                                "system_name": "ts_2_last_name",
                                "data_type_id": 4,
                                "display_name": "Last Name",
                            },
                            {
                                "system_name": "ts_2_user_role",
                                "data_type_id": 4,
                                "display_name": "Current User Role",
                            },
                            {
                                "system_name": "ts_2_created_date",
                                "data_type_id": 2,
                                "display_name": "Created Date",
                            },
                            {
                                "system_name": "ts_2_created_by",
                                "data_type_id": 12,
                                "display_name": "Created By",
                            },
                            {
                                "system_name": "ts_2_exit_date",
                                "data_type_id": 2,
                                "display_name": "Exit Date",
                            },
                            {
                                "system_name": "ts_2_last_commission_date",
                                "data_type_id": 2,
                                "display_name": "Exit Last Commission Date",
                            },
                            {
                                "system_name": "ts_2_status",
                                "data_type_id": 4,
                                "display_name": "Status",
                            },
                            {
                                "system_name": "ts_2_reporting_manager_email_id",
                                "data_type_id": 12,
                                "display_name": "Reporting Manager EmailId",
                            },
                            {
                                "system_name": "ts_2_reporting_manager_name",
                                "data_type_id": 4,
                                "display_name": "Reporting Manager Name",
                            },
                            {
                                "system_name": "ts_2_active_plan",
                                "data_type_id": 4,
                                "display_name": "Active Primary Plan",
                            },
                            {
                                "system_name": "ts_2_active_spiffs",
                                "data_type_id": 4,
                                "display_name": "Active Spiff Plan",
                            },
                            {
                                "system_name": "ts_2_active_quota_categories",
                                "data_type_id": 4,
                                "display_name": "Active Quota Categories",
                            },
                            {
                                "system_name": "ts_2_payee_or_manager",
                                "data_type_id": 4,
                                "display_name": "Payee/Manager",
                            },
                        ],
                        "email_id_column": {
                            "value": "employee_email_id",
                            "variable_id": "employee_email_id_user",
                        },
                        "end_date_column": {"value": None, "variable_id": None},
                        "with_databook_id": None,
                        "start_date_column": {"value": None, "variable_id": None},
                        "has_effective_dates": False,
                    },
                ],
                "type": "TEMPORAL_SPLICE",
                "is_valid": True,
                "output_columns": [
                    {
                        "meta_data": None,
                        "source_id": "57cbfd39-b826-4829-89bf-e4e4b2225076",
                        "is_primary": True,
                        "is_selected": True,
                        "source_type": "datasheet",
                        "system_name": "ts_employee_email_id",
                        "variable_id": "970e849a-1240-4296-b15b-f37696ecf985",
                        "data_type_id": 12,
                        "display_name": "TS::Employee Email Id",
                        "system_generated": True,
                        "source_variable_id": None,
                        "source_cf_meta_data": None,
                    },
                    {
                        "meta_data": None,
                        "source_id": "57cbfd39-b826-4829-89bf-e4e4b2225076",
                        "is_primary": True,
                        "is_selected": True,
                        "source_type": "datasheet",
                        "system_name": "ts_effective_start_date",
                        "variable_id": "65b9fe83-6fa6-40ad-b1cf-a037e5517ae5",
                        "data_type_id": 2,
                        "display_name": "TS::Effective Start Date",
                        "system_generated": True,
                        "source_variable_id": None,
                        "source_cf_meta_data": None,
                    },
                    {
                        "meta_data": None,
                        "source_id": "57cbfd39-b826-4829-89bf-e4e4b2225076",
                        "is_primary": True,
                        "is_selected": True,
                        "source_type": "datasheet",
                        "system_name": "ts_effective_end_date",
                        "variable_id": "a0cb521b-2728-4b21-a4fc-62d56aa004b9",
                        "data_type_id": 2,
                        "display_name": "TS::Effective End Date",
                        "system_generated": True,
                        "source_variable_id": None,
                        "source_cf_meta_data": None,
                    },
                    {
                        "source_id": "1",
                        "is_primary": False,
                        "source_type": "object",
                        "system_name": "ts_1_lhs_lhs_co_1_number",
                        "variable_id": "8113951d-6ab2-48ff-af64-ab96579abd0b",
                        "data_type_id": 1,
                        "display_name": "Number",
                        "system_generated": False,
                        "source_variable_id": "co_1_number",
                        "source_cf_meta_data": None,
                    },
                    {
                        "source_id": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                        "is_primary": False,
                        "source_type": "datasheet",
                        "system_name": "ts_1_lhs_rhs_co_1_boolean",
                        "variable_id": "c66f062b-4bb1-46c8-9f4d-d3b3663ee1f2__ss__1",
                        "data_type_id": 3,
                        "display_name": "Boolean",
                        "system_generated": False,
                        "source_variable_id": "co_1_boolean",
                        "source_cf_meta_data": None,
                    },
                    {
                        "source_id": "57cbfd39-b826-4829-89bf-e4e4b2225076",
                        "is_primary": False,
                        "source_type": "datasheet",
                        "system_name": "ts_1_lhs_count_rhs_co_1_date_1",
                        "variable_id": "04da6570-5640-4fa5-840d-dd22f2dcc512",
                        "data_type_id": 1,
                        "display_name": "COUNT::DATE-1",
                        "system_generated": True,
                        "source_variable_id": None,
                        "source_cf_meta_data": None,
                    },
                    {
                        "source_id": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                        "is_primary": False,
                        "source_type": "datasheet",
                        "system_name": "ts_1_rhs_co_1_number",
                        "variable_id": "8113951d-6ab2-48ff-af64-ab96579abd0b__ss__2",
                        "data_type_id": 1,
                        "display_name": "Number",
                        "source_variable_id": "co_1_number",
                        "source_cf_meta_data": None,
                    },
                    {
                        "source_id": "user",
                        "is_primary": False,
                        "is_selected": True,
                        "source_name": "User",
                        "source_type": "report",
                        "system_name": "ts_2_effective_start_date",
                        "variable_id": "effective_start_date_user",
                        "data_type_id": 2,
                        "display_name": "Effective Start Date",
                        "source_variable_id": None,
                    },
                    {
                        "source_id": "user",
                        "is_primary": False,
                        "is_selected": True,
                        "source_name": "User",
                        "source_type": "report",
                        "system_name": "ts_2_effective_end_date",
                        "variable_id": "effective_end_date_user",
                        "data_type_id": 2,
                        "display_name": "Effective End Date",
                        "source_variable_id": None,
                    },
                    {
                        "source_id": "user",
                        "is_primary": False,
                        "is_selected": True,
                        "source_name": "User",
                        "source_type": "report",
                        "system_name": "ts_2_employee_id",
                        "variable_id": "employee_id_user",
                        "data_type_id": 4,
                        "display_name": "Employee Id",
                        "source_variable_id": None,
                    },
                    {
                        "source_id": "user",
                        "is_primary": False,
                        "is_selected": True,
                        "source_name": "User",
                        "source_type": "report",
                        "system_name": "ts_2_joining_date",
                        "variable_id": "joining_date_user",
                        "data_type_id": 2,
                        "display_name": "Joining Date",
                        "source_variable_id": None,
                    },
                    {
                        "source_id": "user",
                        "is_primary": False,
                        "is_selected": True,
                        "source_name": "User",
                        "source_type": "report",
                        "system_name": "ts_2_time_zone",
                        "variable_id": "time_zone_user",
                        "data_type_id": 4,
                        "display_name": "Current Time Zone",
                        "source_variable_id": None,
                    },
                    {
                        "source_id": "user",
                        "is_primary": False,
                        "is_selected": True,
                        "source_name": "User",
                        "source_type": "report",
                        "system_name": "ts_2_designation",
                        "variable_id": "designation_user",
                        "data_type_id": 4,
                        "display_name": "Designation",
                        "source_variable_id": None,
                    },
                    {
                        "source_id": "user",
                        "is_primary": False,
                        "is_selected": True,
                        "source_name": "User",
                        "source_type": "report",
                        "system_name": "ts_2_employment_country",
                        "variable_id": "employment_country_user",
                        "data_type_id": 4,
                        "display_name": "Employment Country",
                        "source_variable_id": None,
                    },
                    {
                        "source_id": "user",
                        "is_primary": False,
                        "is_selected": True,
                        "source_name": "User",
                        "source_type": "report",
                        "system_name": "ts_2_payout_frequency",
                        "variable_id": "payout_frequency_user",
                        "data_type_id": 4,
                        "display_name": "Payout Frequency",
                        "source_variable_id": None,
                    },
                    {
                        "source_id": "user",
                        "is_primary": False,
                        "is_selected": True,
                        "source_name": "User",
                        "source_type": "report",
                        "system_name": "ts_2_pay_currency",
                        "variable_id": "pay_currency_user",
                        "data_type_id": 4,
                        "display_name": "Payout Currency",
                        "source_variable_id": None,
                    },
                    {
                        "source_id": "user",
                        "is_primary": False,
                        "is_selected": True,
                        "source_name": "User",
                        "source_type": "report",
                        "system_name": "ts_2_fixed_pay",
                        "variable_id": "fixed_pay_user",
                        "data_type_id": 1,
                        "display_name": "Fixed Pay",
                        "source_variable_id": None,
                    },
                    {
                        "source_id": "user",
                        "is_primary": False,
                        "is_selected": True,
                        "source_name": "User",
                        "source_type": "report",
                        "system_name": "ts_2_payee_variable_pay",
                        "variable_id": "payee_variable_pay_user",
                        "data_type_id": 1,
                        "display_name": "Variable Pay",
                        "source_variable_id": None,
                    },
                    {
                        "source_id": "user",
                        "is_primary": False,
                        "is_selected": True,
                        "source_name": "User",
                        "source_type": "report",
                        "system_name": "ts_2_first_name",
                        "variable_id": "first_name_user",
                        "data_type_id": 4,
                        "display_name": "First Name",
                        "source_variable_id": None,
                    },
                    {
                        "source_id": "user",
                        "is_primary": False,
                        "is_selected": True,
                        "source_name": "User",
                        "source_type": "report",
                        "system_name": "ts_2_last_name",
                        "variable_id": "last_name_user",
                        "data_type_id": 4,
                        "display_name": "Last Name",
                        "source_variable_id": None,
                    },
                    {
                        "source_id": "user",
                        "is_primary": False,
                        "is_selected": True,
                        "source_name": "User",
                        "source_type": "report",
                        "system_name": "ts_2_user_role",
                        "variable_id": "user_role_user",
                        "data_type_id": 4,
                        "display_name": "Current User Role",
                        "source_variable_id": None,
                    },
                    {
                        "source_id": "user",
                        "is_primary": False,
                        "is_selected": True,
                        "source_name": "User",
                        "source_type": "report",
                        "system_name": "ts_2_created_date",
                        "variable_id": "created_date_user",
                        "data_type_id": 2,
                        "display_name": "Created Date",
                        "source_variable_id": None,
                    },
                    {
                        "source_id": "user",
                        "is_primary": False,
                        "is_selected": True,
                        "source_name": "User",
                        "source_type": "report",
                        "system_name": "ts_2_created_by",
                        "variable_id": "created_by_user",
                        "data_type_id": 12,
                        "display_name": "Created By",
                        "source_variable_id": None,
                    },
                    {
                        "source_id": "user",
                        "is_primary": False,
                        "is_selected": True,
                        "source_name": "User",
                        "source_type": "report",
                        "system_name": "ts_2_exit_date",
                        "variable_id": "exit_date_user",
                        "data_type_id": 2,
                        "display_name": "Exit Date",
                        "source_variable_id": None,
                    },
                    {
                        "source_id": "user",
                        "is_primary": False,
                        "is_selected": True,
                        "source_name": "User",
                        "source_type": "report",
                        "system_name": "ts_2_last_commission_date",
                        "variable_id": "last_commission_date_user",
                        "data_type_id": 2,
                        "display_name": "Exit Last Commission Date",
                        "source_variable_id": None,
                    },
                    {
                        "source_id": "user",
                        "is_primary": False,
                        "is_selected": True,
                        "source_name": "User",
                        "source_type": "report",
                        "system_name": "ts_2_status",
                        "variable_id": "status_user",
                        "data_type_id": 4,
                        "display_name": "Status",
                        "source_variable_id": None,
                    },
                    {
                        "source_id": "user",
                        "is_primary": False,
                        "is_selected": True,
                        "source_name": "User",
                        "source_type": "report",
                        "system_name": "ts_2_reporting_manager_email_id",
                        "variable_id": "reporting_manager_email_id_user",
                        "data_type_id": 12,
                        "display_name": "Reporting Manager EmailId",
                        "source_variable_id": None,
                    },
                    {
                        "source_id": "user",
                        "is_primary": False,
                        "is_selected": True,
                        "source_name": "User",
                        "source_type": "report",
                        "system_name": "ts_2_reporting_manager_name",
                        "variable_id": "reporting_manager_name_user",
                        "data_type_id": 4,
                        "display_name": "Reporting Manager Name",
                        "source_variable_id": None,
                    },
                    {
                        "source_id": "user",
                        "is_primary": False,
                        "is_selected": True,
                        "source_name": "User",
                        "source_type": "report",
                        "system_name": "ts_2_active_plan",
                        "variable_id": "active_plan_user",
                        "data_type_id": 4,
                        "display_name": "Active Primary Plan",
                        "source_variable_id": None,
                    },
                    {
                        "source_id": "user",
                        "is_primary": False,
                        "is_selected": True,
                        "source_name": "User",
                        "source_type": "report",
                        "system_name": "ts_2_active_spiffs",
                        "variable_id": "active_spiffs_user",
                        "data_type_id": 4,
                        "display_name": "Active Spiff Plan",
                        "source_variable_id": None,
                    },
                    {
                        "source_id": "user",
                        "is_primary": False,
                        "is_selected": True,
                        "source_name": "User",
                        "source_type": "report",
                        "system_name": "ts_2_active_quota_categories",
                        "variable_id": "active_quota_categories_user",
                        "data_type_id": 4,
                        "display_name": "Active Quota Categories",
                        "source_variable_id": None,
                    },
                    {
                        "source_id": "user",
                        "is_primary": False,
                        "is_selected": True,
                        "source_name": "User",
                        "source_type": "report",
                        "system_name": "ts_2_payee_or_manager",
                        "variable_id": "payee_or_manager_user",
                        "data_type_id": 4,
                        "display_name": "Payee/Manager",
                        "source_variable_id": None,
                    },
                ],
                "transformation_id": "ed80190e-fc64-4dee-b29c-4fda83066c46",
                "used_variable_ids": [
                    "a8f6f7d8-9731-4dc8-bc1a-328d093b389b__ss__1",
                    "c66f062b-4bb1-46c8-9f4d-d3b3663ee1f2__ss__1",
                    "1b0b9972-764c-426a-891d-1d7338645519",
                    "c66f062b-4bb1-46c8-9f4d-d3b3663ee1f2",
                    "04da6570-5640-4fa5-840d-dd22f2dcc512",
                    "8113951d-6ab2-48ff-af64-ab96579abd0b",
                    "a8f6f7d8-9731-4dc8-bc1a-328d093b389b",
                ],
                "transformation_source_map": {
                    "user_report": 1,
                    "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb_datasheet": 3,
                },
            },
            {
                "key": "c4fdb86c-83a9-4a65-90e6-c759d2f7d805",
                "type": "GET_USER_PROPERTIES",
                "is_valid": True,
                "email_column": {
                    "value": "ts_employee_email_id",
                    "variable_id": "970e849a-1240-4296-b15b-f37696ecf985",
                },
                "output_columns": [
                    {
                        "meta_data": None,
                        "source_id": "57cbfd39-b826-4829-89bf-e4e4b2225076",
                        "is_primary": True,
                        "is_selected": True,
                        "source_type": "datasheet",
                        "system_name": "ts_employee_email_id",
                        "variable_id": "970e849a-1240-4296-b15b-f37696ecf985",
                        "data_type_id": 12,
                        "display_name": "TS::Employee Email Id",
                        "system_generated": True,
                        "source_variable_id": None,
                        "source_cf_meta_data": None,
                    },
                    {
                        "meta_data": None,
                        "source_id": "57cbfd39-b826-4829-89bf-e4e4b2225076",
                        "is_primary": True,
                        "is_selected": True,
                        "source_type": "datasheet",
                        "system_name": "ts_effective_start_date",
                        "variable_id": "65b9fe83-6fa6-40ad-b1cf-a037e5517ae5",
                        "data_type_id": 2,
                        "display_name": "TS::Effective Start Date",
                        "system_generated": True,
                        "source_variable_id": None,
                        "source_cf_meta_data": None,
                    },
                    {
                        "meta_data": None,
                        "source_id": "57cbfd39-b826-4829-89bf-e4e4b2225076",
                        "is_primary": True,
                        "is_selected": True,
                        "source_type": "datasheet",
                        "system_name": "ts_effective_end_date",
                        "variable_id": "a0cb521b-2728-4b21-a4fc-62d56aa004b9",
                        "data_type_id": 2,
                        "display_name": "TS::Effective End Date",
                        "system_generated": True,
                        "source_variable_id": None,
                        "source_cf_meta_data": None,
                    },
                    {
                        "source_id": "1",
                        "is_primary": False,
                        "source_type": "object",
                        "system_name": "ts_1_lhs_lhs_co_1_number",
                        "variable_id": "8113951d-6ab2-48ff-af64-ab96579abd0b",
                        "data_type_id": 1,
                        "display_name": "Number",
                        "system_generated": False,
                        "source_variable_id": "co_1_number",
                        "source_cf_meta_data": None,
                    },
                    {
                        "source_id": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                        "is_primary": False,
                        "source_type": "datasheet",
                        "system_name": "ts_1_lhs_rhs_co_1_boolean",
                        "variable_id": "c66f062b-4bb1-46c8-9f4d-d3b3663ee1f2__ss__1",
                        "data_type_id": 3,
                        "display_name": "Boolean",
                        "system_generated": False,
                        "source_variable_id": "co_1_boolean",
                        "source_cf_meta_data": None,
                    },
                    {
                        "source_id": "57cbfd39-b826-4829-89bf-e4e4b2225076",
                        "is_primary": False,
                        "source_type": "datasheet",
                        "system_name": "ts_1_lhs_count_rhs_co_1_date_1",
                        "variable_id": "04da6570-5640-4fa5-840d-dd22f2dcc512",
                        "data_type_id": 1,
                        "display_name": "COUNT::DATE-1",
                        "system_generated": True,
                        "source_variable_id": None,
                        "source_cf_meta_data": None,
                    },
                    {
                        "source_id": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                        "is_primary": False,
                        "source_type": "datasheet",
                        "system_name": "ts_1_rhs_co_1_number",
                        "variable_id": "8113951d-6ab2-48ff-af64-ab96579abd0b__ss__2",
                        "data_type_id": 1,
                        "display_name": "Number",
                        "source_variable_id": "co_1_number",
                        "source_cf_meta_data": None,
                    },
                    {
                        "source_id": "user",
                        "is_primary": False,
                        "is_selected": True,
                        "source_name": "User",
                        "source_type": "report",
                        "system_name": "ts_2_effective_start_date",
                        "variable_id": "effective_start_date_user",
                        "data_type_id": 2,
                        "display_name": "Effective Start Date",
                        "source_variable_id": None,
                    },
                    {
                        "source_id": "user",
                        "is_primary": False,
                        "is_selected": True,
                        "source_name": "User",
                        "source_type": "report",
                        "system_name": "ts_2_effective_end_date",
                        "variable_id": "effective_end_date_user",
                        "data_type_id": 2,
                        "display_name": "Effective End Date",
                        "source_variable_id": None,
                    },
                    {
                        "source_id": "user",
                        "is_primary": False,
                        "is_selected": True,
                        "source_name": "User",
                        "source_type": "report",
                        "system_name": "ts_2_employee_id",
                        "variable_id": "employee_id_user",
                        "data_type_id": 4,
                        "display_name": "Employee Id",
                        "source_variable_id": None,
                    },
                    {
                        "source_id": "user",
                        "is_primary": False,
                        "is_selected": True,
                        "source_name": "User",
                        "source_type": "report",
                        "system_name": "ts_2_joining_date",
                        "variable_id": "joining_date_user",
                        "data_type_id": 2,
                        "display_name": "Joining Date",
                        "source_variable_id": None,
                    },
                    {
                        "source_id": "user",
                        "is_primary": False,
                        "is_selected": True,
                        "source_name": "User",
                        "source_type": "report",
                        "system_name": "ts_2_time_zone",
                        "variable_id": "time_zone_user",
                        "data_type_id": 4,
                        "display_name": "Current Time Zone",
                        "source_variable_id": None,
                    },
                    {
                        "source_id": "user",
                        "is_primary": False,
                        "is_selected": True,
                        "source_name": "User",
                        "source_type": "report",
                        "system_name": "ts_2_designation",
                        "variable_id": "designation_user",
                        "data_type_id": 4,
                        "display_name": "Designation",
                        "source_variable_id": None,
                    },
                    {
                        "source_id": "user",
                        "is_primary": False,
                        "is_selected": True,
                        "source_name": "User",
                        "source_type": "report",
                        "system_name": "ts_2_employment_country",
                        "variable_id": "employment_country_user",
                        "data_type_id": 4,
                        "display_name": "Employment Country",
                        "source_variable_id": None,
                    },
                    {
                        "source_id": "user",
                        "is_primary": False,
                        "is_selected": True,
                        "source_name": "User",
                        "source_type": "report",
                        "system_name": "ts_2_payout_frequency",
                        "variable_id": "payout_frequency_user",
                        "data_type_id": 4,
                        "display_name": "Payout Frequency",
                        "source_variable_id": None,
                    },
                    {
                        "source_id": "user",
                        "is_primary": False,
                        "is_selected": True,
                        "source_name": "User",
                        "source_type": "report",
                        "system_name": "ts_2_pay_currency",
                        "variable_id": "pay_currency_user",
                        "data_type_id": 4,
                        "display_name": "Payout Currency",
                        "source_variable_id": None,
                    },
                    {
                        "source_id": "user",
                        "is_primary": False,
                        "is_selected": True,
                        "source_name": "User",
                        "source_type": "report",
                        "system_name": "ts_2_fixed_pay",
                        "variable_id": "fixed_pay_user",
                        "data_type_id": 1,
                        "display_name": "Fixed Pay",
                        "source_variable_id": None,
                    },
                    {
                        "source_id": "user",
                        "is_primary": False,
                        "is_selected": True,
                        "source_name": "User",
                        "source_type": "report",
                        "system_name": "ts_2_payee_variable_pay",
                        "variable_id": "payee_variable_pay_user",
                        "data_type_id": 1,
                        "display_name": "Variable Pay",
                        "source_variable_id": None,
                    },
                    {
                        "source_id": "user",
                        "is_primary": False,
                        "is_selected": True,
                        "source_name": "User",
                        "source_type": "report",
                        "system_name": "ts_2_first_name",
                        "variable_id": "first_name_user",
                        "data_type_id": 4,
                        "display_name": "First Name",
                        "source_variable_id": None,
                    },
                    {
                        "source_id": "user",
                        "is_primary": False,
                        "is_selected": True,
                        "source_name": "User",
                        "source_type": "report",
                        "system_name": "ts_2_last_name",
                        "variable_id": "last_name_user",
                        "data_type_id": 4,
                        "display_name": "Last Name",
                        "source_variable_id": None,
                    },
                    {
                        "source_id": "user",
                        "is_primary": False,
                        "is_selected": True,
                        "source_name": "User",
                        "source_type": "report",
                        "system_name": "ts_2_user_role",
                        "variable_id": "user_role_user",
                        "data_type_id": 4,
                        "display_name": "Current User Role",
                        "source_variable_id": None,
                    },
                    {
                        "source_id": "user",
                        "is_primary": False,
                        "is_selected": True,
                        "source_name": "User",
                        "source_type": "report",
                        "system_name": "ts_2_created_date",
                        "variable_id": "created_date_user",
                        "data_type_id": 2,
                        "display_name": "Created Date",
                        "source_variable_id": None,
                    },
                    {
                        "source_id": "user",
                        "is_primary": False,
                        "is_selected": True,
                        "source_name": "User",
                        "source_type": "report",
                        "system_name": "ts_2_created_by",
                        "variable_id": "created_by_user",
                        "data_type_id": 12,
                        "display_name": "Created By",
                        "source_variable_id": None,
                    },
                    {
                        "source_id": "user",
                        "is_primary": False,
                        "is_selected": True,
                        "source_name": "User",
                        "source_type": "report",
                        "system_name": "ts_2_exit_date",
                        "variable_id": "exit_date_user",
                        "data_type_id": 2,
                        "display_name": "Exit Date",
                        "source_variable_id": None,
                    },
                    {
                        "source_id": "user",
                        "is_primary": False,
                        "is_selected": True,
                        "source_name": "User",
                        "source_type": "report",
                        "system_name": "ts_2_last_commission_date",
                        "variable_id": "last_commission_date_user",
                        "data_type_id": 2,
                        "display_name": "Exit Last Commission Date",
                        "source_variable_id": None,
                    },
                    {
                        "source_id": "user",
                        "is_primary": False,
                        "is_selected": True,
                        "source_name": "User",
                        "source_type": "report",
                        "system_name": "ts_2_status",
                        "variable_id": "status_user",
                        "data_type_id": 4,
                        "display_name": "Status",
                        "source_variable_id": None,
                    },
                    {
                        "source_id": "user",
                        "is_primary": False,
                        "is_selected": True,
                        "source_name": "User",
                        "source_type": "report",
                        "system_name": "ts_2_reporting_manager_email_id",
                        "variable_id": "reporting_manager_email_id_user",
                        "data_type_id": 12,
                        "display_name": "Reporting Manager EmailId",
                        "source_variable_id": None,
                    },
                    {
                        "source_id": "user",
                        "is_primary": False,
                        "is_selected": True,
                        "source_name": "User",
                        "source_type": "report",
                        "system_name": "ts_2_reporting_manager_name",
                        "variable_id": "reporting_manager_name_user",
                        "data_type_id": 4,
                        "display_name": "Reporting Manager Name",
                        "source_variable_id": None,
                    },
                    {
                        "source_id": "user",
                        "is_primary": False,
                        "is_selected": True,
                        "source_name": "User",
                        "source_type": "report",
                        "system_name": "ts_2_active_plan",
                        "variable_id": "active_plan_user",
                        "data_type_id": 4,
                        "display_name": "Active Primary Plan",
                        "source_variable_id": None,
                    },
                    {
                        "source_id": "user",
                        "is_primary": False,
                        "is_selected": True,
                        "source_name": "User",
                        "source_type": "report",
                        "system_name": "ts_2_active_spiffs",
                        "variable_id": "active_spiffs_user",
                        "data_type_id": 4,
                        "display_name": "Active Spiff Plan",
                        "source_variable_id": None,
                    },
                    {
                        "source_id": "user",
                        "is_primary": False,
                        "is_selected": True,
                        "source_name": "User",
                        "source_type": "report",
                        "system_name": "ts_2_active_quota_categories",
                        "variable_id": "active_quota_categories_user",
                        "data_type_id": 4,
                        "display_name": "Active Quota Categories",
                        "source_variable_id": None,
                    },
                    {
                        "source_id": "user",
                        "is_primary": False,
                        "is_selected": True,
                        "source_name": "User",
                        "source_type": "report",
                        "system_name": "ts_2_payee_or_manager",
                        "variable_id": "payee_or_manager_user",
                        "data_type_id": 4,
                        "display_name": "Payee/Manager",
                        "source_variable_id": None,
                    },
                    {
                        "meta_data": None,
                        "source_id": "user",
                        "is_primary": False,
                        "is_selected": True,
                        "source_type": "report",
                        "system_name": "up_ts_employee_email_id_ts_2_effective_end_date_payout_frequency_d805",
                        "variable_id": "payout_frequency_user",
                        "data_type_id": 4,
                        "display_name": "Payout Frequency",
                        "source_variable_id": None,
                        "source_cf_meta_data": None,
                    },
                    {
                        "meta_data": None,
                        "source_id": "user",
                        "is_primary": False,
                        "is_selected": True,
                        "source_type": "report",
                        "system_name": "up_ts_employee_email_id_ts_2_effective_end_date_employment_country_d805",
                        "variable_id": "employment_country_user",
                        "data_type_id": 4,
                        "display_name": "Employment Country",
                        "source_variable_id": None,
                        "source_cf_meta_data": None,
                    },
                ],
                "user_properties": [
                    {
                        "variable_id": "c578f2cd-6e02-4ff5-bebd-1304486043fc",
                        "data_type_id": 4,
                        "display_name": "Payout Frequency",
                        "user_property_system_name": "payout_frequency",
                        "output_variable_system_name": "up_ts_employee_email_id_ts_2_effective_end_date_payout_frequency_d805",
                    },
                    {
                        "variable_id": "04700c01-e2a5-44ff-b66e-ce50abc52956",
                        "data_type_id": 4,
                        "display_name": "Employment Country",
                        "user_property_system_name": "employment_country",
                        "output_variable_system_name": "up_ts_employee_email_id_ts_2_effective_end_date_employment_country_d805",
                    },
                ],
                "as_of_date_column": {
                    "value": "ts_2_effective_end_date",
                    "variable_id": "effective_end_date_user",
                },
                "transformation_id": "c4fdb86c-83a9-4a65-90e6-c759d2f7d805",
                "used_variable_ids": [
                    "a8f6f7d8-9731-4dc8-bc1a-328d093b389b__ss__1",
                    "c66f062b-4bb1-46c8-9f4d-d3b3663ee1f2__ss__1",
                    "c66f062b-4bb1-46c8-9f4d-d3b3663ee1f2",
                    "04da6570-5640-4fa5-840d-dd22f2dcc512",
                    "970e849a-1240-4296-b15b-f37696ecf985",
                    "effective_end_date_user",
                    "1b0b9972-764c-426a-891d-1d7338645519",
                    "8113951d-6ab2-48ff-af64-ab96579abd0b",
                    "a8f6f7d8-9731-4dc8-bc1a-328d093b389b",
                ],
                "transformation_source_map": {
                    "user_report": 2,
                    "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb_datasheet": 3,
                },
            },
            {
                "on": {
                    "lhs": [
                        {
                            "source_id": "user",
                            "is_primary": False,
                            "source_type": "report",
                            "system_name": "ts_2_first_name",
                            "variable_id": "first_name_user",
                            "data_type_id": 4,
                            "display_name": "First Name",
                            "source_variable_id": None,
                            "source_cf_meta_data": None,
                        }
                    ],
                    "rhs": [
                        {
                            "is_primary": False,
                            "system_name": "co_1_name",
                            "variable_id": "a7f84b61-17d5-4d73-88e2-5ee9996649a6",
                            "data_type_id": 4,
                            "display_name": "Name",
                            "source_variable_id": "co_1_name",
                            "source_cf_meta_data": None,
                        }
                    ],
                },
                "key": "7f67c8fd-59d9-4aad-9e58-5b832b5ad228",
                "type": "JOIN",
                "with": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                "columns": {
                    "lhs": [
                        {
                            "source_id": "57cbfd39-b826-4829-89bf-e4e4b2225076",
                            "is_primary": True,
                            "source_type": "datasheet",
                            "system_name": "ts_employee_email_id",
                            "variable_id": "970e849a-1240-4296-b15b-f37696ecf985",
                            "data_type_id": 12,
                            "display_name": "TS::Employee Email Id",
                            "system_generated": True,
                            "source_variable_id": None,
                            "source_cf_meta_data": None,
                        },
                        {
                            "source_id": "57cbfd39-b826-4829-89bf-e4e4b2225076",
                            "is_primary": True,
                            "source_type": "datasheet",
                            "system_name": "ts_effective_start_date",
                            "variable_id": "65b9fe83-6fa6-40ad-b1cf-a037e5517ae5",
                            "data_type_id": 2,
                            "display_name": "TS::Effective Start Date",
                            "system_generated": True,
                            "source_variable_id": None,
                            "source_cf_meta_data": None,
                        },
                        {
                            "source_id": "57cbfd39-b826-4829-89bf-e4e4b2225076",
                            "is_primary": True,
                            "source_type": "datasheet",
                            "system_name": "ts_effective_end_date",
                            "variable_id": "a0cb521b-2728-4b21-a4fc-62d56aa004b9",
                            "data_type_id": 2,
                            "display_name": "TS::Effective End Date",
                            "system_generated": True,
                            "source_variable_id": None,
                            "source_cf_meta_data": None,
                        },
                    ],
                    "rhs": [
                        {
                            "is_primary": True,
                            "system_name": "co_1_number",
                            "variable_id": "8113951d-6ab2-48ff-af64-ab96579abd0b",
                            "data_type_id": 1,
                            "display_name": "Number",
                            "source_variable_id": "co_1_number",
                            "source_cf_meta_data": None,
                        }
                    ],
                },
                "is_valid": True,
                "join_type": "LEFT",
                "output_columns": [
                    {
                        "source_id": "57cbfd39-b826-4829-89bf-e4e4b2225076",
                        "is_primary": True,
                        "source_type": "datasheet",
                        "system_name": "lhs_ts_employee_email_id",
                        "variable_id": "970e849a-1240-4296-b15b-f37696ecf985",
                        "data_type_id": 12,
                        "display_name": "TS::Employee Email Id",
                        "system_generated": True,
                        "source_variable_id": None,
                        "source_cf_meta_data": None,
                    },
                    {
                        "source_id": "57cbfd39-b826-4829-89bf-e4e4b2225076",
                        "is_primary": True,
                        "source_type": "datasheet",
                        "system_name": "lhs_ts_effective_start_date",
                        "variable_id": "65b9fe83-6fa6-40ad-b1cf-a037e5517ae5",
                        "data_type_id": 2,
                        "display_name": "TS::Effective Start Date",
                        "system_generated": True,
                        "source_variable_id": None,
                        "source_cf_meta_data": None,
                    },
                    {
                        "source_id": "57cbfd39-b826-4829-89bf-e4e4b2225076",
                        "is_primary": True,
                        "source_type": "datasheet",
                        "system_name": "lhs_ts_effective_end_date",
                        "variable_id": "a0cb521b-2728-4b21-a4fc-62d56aa004b9",
                        "data_type_id": 2,
                        "display_name": "TS::Effective End Date",
                        "system_generated": True,
                        "source_variable_id": None,
                        "source_cf_meta_data": None,
                    },
                    {
                        "source_id": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                        "is_primary": True,
                        "source_type": "datasheet",
                        "system_name": "rhs_co_1_number",
                        "variable_id": "8113951d-6ab2-48ff-af64-ab96579abd0b__ss__4",
                        "data_type_id": 1,
                        "display_name": "Number",
                        "source_variable_id": "co_1_number",
                        "source_cf_meta_data": None,
                    },
                ],
                "with_databook_id": "869b4b9b-5e9f-49e1-b24c-e4c5cee825b0",
                "transformation_id": "7f67c8fd-59d9-4aad-9e58-5b832b5ad228",
                "used_variable_ids": [
                    "a8f6f7d8-9731-4dc8-bc1a-328d093b389b__ss__1",
                    "c66f062b-4bb1-46c8-9f4d-d3b3663ee1f2__ss__1",
                    "c66f062b-4bb1-46c8-9f4d-d3b3663ee1f2",
                    "04da6570-5640-4fa5-840d-dd22f2dcc512",
                    "a7f84b61-17d5-4d73-88e2-5ee9996649a6",
                    "970e849a-1240-4296-b15b-f37696ecf985",
                    "first_name_user",
                    "effective_end_date_user",
                    "1b0b9972-764c-426a-891d-1d7338645519",
                    "8113951d-6ab2-48ff-af64-ab96579abd0b",
                    "a8f6f7d8-9731-4dc8-bc1a-328d093b389b",
                    "a0cb521b-2728-4b21-a4fc-62d56aa004b9",
                    "65b9fe83-6fa6-40ad-b1cf-a037e5517ae5",
                ],
                "transformation_source_map": {
                    "user_report": 2,
                    "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb_datasheet": 4,
                },
            },
        ],
        ordered_columns="{co_1_name,co_1_number,co_1_boolean,co_1_date_1,co_1_email,co_1_percentage,co_1_date_2}",
        data_origin="system_object",
        source_databook_id=databook_id_1,
        knowledge_begin_date=timezone.now(),
    )

    DatasheetVariable.objects.bulk_create(
        [
            DatasheetVariable(
                client_id=TEST_CLIENT_ID,
                databook_id=databook_id_1,
                datasheet_id=datasheet_id_2,
                variable_id="970e849a-1240-4296-b15b-f37696ecf985",
                source_variable_id=None,
                display_name="TS::Employee Email Id",
                source_id=datasheet_id_2,
                source_type="datasheet",
                system_name="lhs_ts_employee_email_id",
                data_type_id=12,
                knowledge_begin_date=timezone.now(),
            ),
            DatasheetVariable(
                client_id=TEST_CLIENT_ID,
                databook_id=databook_id_1,
                datasheet_id=datasheet_id_2,
                variable_id="65b9fe83-6fa6-40ad-b1cf-a037e5517ae5",
                source_variable_id=None,
                display_name="TS::Effective Start Date",
                source_id=datasheet_id_2,
                source_type="datasheet",
                system_name="lhs_ts_effective_start_date",
                data_type_id=2,
                knowledge_begin_date=timezone.now(),
            ),
            DatasheetVariable(
                client_id=TEST_CLIENT_ID,
                databook_id=databook_id_1,
                datasheet_id=datasheet_id_2,
                variable_id="a0cb521b-2728-4b21-a4fc-62d56aa004b9",
                source_variable_id=None,
                display_name="TS::Effective End Date",
                source_id=datasheet_id_2,
                source_type="datasheet",
                system_name="lhs_ts_effective_end_date",
                data_type_id=2,
                knowledge_begin_date=timezone.now(),
            ),
            DatasheetVariable(
                client_id=TEST_CLIENT_ID,
                databook_id=databook_id_1,
                datasheet_id=datasheet_id_2,
                variable_id="338d2fb7-20a3-4a56-8a88-e71b09243b8f",
                source_variable_id="8113951d-6ab2-48ff-af64-ab96579abd0b__ss__4",
                display_name="Number",
                source_id=datasheet_id_1,
                source_type="datasheet",
                system_name="rhs_co_1_number",
                data_type_id=1,
                knowledge_begin_date=timezone.now(),
            ),
        ]
    )

    Datasheet.objects.create(
        client_id=TEST_CLIENT_ID,
        databook_id=databook_id_1,
        datasheet_id=datasheet_id_3,
        name="test-user-report",
        source_type="report",
        source_id="user",
        order=1,
        primary_key=["employee_email_id", "effective_start_date", "effective_end_date"],
        transformation_spec=[],
        ordered_columns="{effective_start_date,effective_end_date,employee_id,joining_date,time_zone,designation,employment_country,payout_frequency,pay_currency,fixed_pay,payee_variable_pay,first_name,last_name,user_role,created_date,created_by,exit_date,last_commission_date,status,employee_email_id,reporting_manager_email_id,reporting_manager_name,active_plan,active_spiffs,active_quota_categories,payee_or_manager}",
        knowledge_begin_date=timezone.now(),
    )

    DatasheetVariable.objects.bulk_create(
        [
            DatasheetVariable(
                client_id=TEST_CLIENT_ID,
                databook_id=databook_id_1,
                datasheet_id=datasheet_id_3,
                variable_id="3524e64b-9003-4a03-b7c3-178ed39ad678",
                source_variable_id="effective_start_date_user",
                display_name="Effective Start Date",
                source_id="user",
                source_type="report",
                system_name="effective_start_date",
                data_type_id=2,
                knowledge_begin_date=timezone.now(),
            ),
            DatasheetVariable(
                client_id=TEST_CLIENT_ID,
                databook_id=databook_id_1,
                datasheet_id=datasheet_id_3,
                variable_id="02f933cd-ab07-4cb1-ac4b-0dcb005ec181",
                source_variable_id="effective_end_date_user",
                display_name="Effective End Date",
                source_id="user",
                source_type="report",
                system_name="effective_end_date",
                data_type_id=2,
                knowledge_begin_date=timezone.now(),
            ),
            DatasheetVariable(
                client_id=TEST_CLIENT_ID,
                databook_id=databook_id_1,
                datasheet_id=datasheet_id_3,
                variable_id="3904ae88-bdcf-40b0-ba99-16bc20bdf30e",
                source_variable_id="employee_id_user",
                display_name="Employee Id",
                source_id="user",
                source_type="report",
                system_name="employee_id",
                data_type_id=2,
                knowledge_begin_date=timezone.now(),
            ),
            DatasheetVariable(
                client_id=TEST_CLIENT_ID,
                databook_id=databook_id_1,
                datasheet_id=datasheet_id_3,
                variable_id="3e075f3c-6b0c-48fc-90e1-898b83d48f26",
                source_variable_id="joining_date_user",
                display_name="Joining Date",
                source_id="user",
                source_type="report",
                system_name="joining_date",
                data_type_id=2,
                knowledge_begin_date=timezone.now(),
            ),
            DatasheetVariable(
                client_id=TEST_CLIENT_ID,
                databook_id=databook_id_1,
                datasheet_id=datasheet_id_3,
                variable_id="10dd5a0d-7057-4aca-9120-9be77840da60",
                source_variable_id="time_zone_user",
                display_name="Current Time Zone",
                source_id="user",
                source_type="report",
                system_name="time_zone",
                data_type_id=2,
                knowledge_begin_date=timezone.now(),
            ),
            DatasheetVariable(
                client_id=TEST_CLIENT_ID,
                databook_id=databook_id_1,
                datasheet_id=datasheet_id_3,
                variable_id="49de7dca-ae52-41d2-9470-5717636bc533",
                source_variable_id="designation_user",
                display_name="Designation",
                source_id="user",
                source_type="report",
                system_name="designation",
                data_type_id=2,
                knowledge_begin_date=timezone.now(),
            ),
            DatasheetVariable(
                client_id=TEST_CLIENT_ID,
                databook_id=databook_id_1,
                datasheet_id=datasheet_id_3,
                variable_id="d898705d-db51-44cd-958c-e3a6013b2cf9",
                source_variable_id="employment_country_user",
                display_name="Employment Country",
                source_id="user",
                source_type="report",
                system_name="employment_country",
                data_type_id=2,
                knowledge_begin_date=timezone.now(),
            ),
            DatasheetVariable(
                client_id=TEST_CLIENT_ID,
                databook_id=databook_id_1,
                datasheet_id=datasheet_id_3,
                variable_id="8f0f6669-f0d0-4496-b906-d0c726c15639",
                source_variable_id="payout_frequency_user",
                display_name="Payout Frequency",
                source_id="user",
                source_type="report",
                system_name="payout_frequency",
                data_type_id=2,
                knowledge_begin_date=timezone.now(),
            ),
            DatasheetVariable(
                client_id=TEST_CLIENT_ID,
                databook_id=databook_id_1,
                datasheet_id=datasheet_id_3,
                variable_id="38632bfa-0759-47e7-a17b-98b3d5335aca",
                source_variable_id="pay_currency_user",
                display_name="Payout Currency",
                source_id="user",
                source_type="report",
                system_name="pay_currency",
                data_type_id=2,
                knowledge_begin_date=timezone.now(),
            ),
            DatasheetVariable(
                client_id=TEST_CLIENT_ID,
                databook_id=databook_id_1,
                datasheet_id=datasheet_id_3,
                variable_id="44278393-a436-4e0d-afbe-f543ff7cc02e",
                source_variable_id="fixed_pay_user",
                display_name="Fixed Pay",
                source_id="user",
                source_type="report",
                system_name="fixed_pay",
                data_type_id=2,
                knowledge_begin_date=timezone.now(),
            ),
            DatasheetVariable(
                client_id=TEST_CLIENT_ID,
                databook_id=databook_id_1,
                datasheet_id=datasheet_id_3,
                variable_id="4c2385f5-8b2f-45fa-8d77-cb6958a7a180",
                source_variable_id="payee_variable_pay_user",
                display_name="Variable Pay",
                source_id="user",
                source_type="report",
                system_name="payee_variable_pay",
                data_type_id=2,
                knowledge_begin_date=timezone.now(),
            ),
            DatasheetVariable(
                client_id=TEST_CLIENT_ID,
                databook_id=databook_id_1,
                datasheet_id=datasheet_id_3,
                variable_id="556aa3b4-c4b6-435d-96a2-dbfdbffe844b",
                source_variable_id="first_name_user",
                display_name="First Name",
                source_id="user",
                source_type="report",
                system_name="first_name",
                data_type_id=2,
                knowledge_begin_date=timezone.now(),
            ),
            DatasheetVariable(
                client_id=TEST_CLIENT_ID,
                databook_id=databook_id_1,
                datasheet_id=datasheet_id_3,
                variable_id="18625303-151e-40af-865b-aa30e8fc04ff",
                source_variable_id="last_name_user",
                display_name="Last Name",
                source_id="user",
                source_type="report",
                system_name="last_name",
                data_type_id=2,
                knowledge_begin_date=timezone.now(),
            ),
            DatasheetVariable(
                client_id=TEST_CLIENT_ID,
                databook_id=databook_id_1,
                datasheet_id=datasheet_id_3,
                variable_id="6d706ce0-46d4-4d9b-bdfc-f9303a5dbf39",
                source_variable_id="user_role_user",
                display_name="Current User Role",
                source_id="user",
                source_type="report",
                system_name="user_role",
                data_type_id=2,
                knowledge_begin_date=timezone.now(),
            ),
            DatasheetVariable(
                client_id=TEST_CLIENT_ID,
                databook_id=databook_id_1,
                datasheet_id=datasheet_id_3,
                variable_id="758c8f82-170e-4094-85f2-e29dcb3a903b",
                source_variable_id="created_date_user",
                display_name="Created Date",
                source_id="user",
                source_type="report",
                system_name="created_date",
                data_type_id=2,
                knowledge_begin_date=timezone.now(),
            ),
            DatasheetVariable(
                client_id=TEST_CLIENT_ID,
                databook_id=databook_id_1,
                datasheet_id=datasheet_id_3,
                variable_id="37790973-59ea-4f5e-b57c-165b17f7b9c7",
                source_variable_id="created_by_user",
                display_name="Created By",
                source_id="user",
                source_type="report",
                system_name="created_by",
                data_type_id=2,
                knowledge_begin_date=timezone.now(),
            ),
            DatasheetVariable(
                client_id=TEST_CLIENT_ID,
                databook_id=databook_id_1,
                datasheet_id=datasheet_id_3,
                variable_id="440e564c-6565-42d3-b334-7d9ebe7170d1",
                source_variable_id="exit_date_user",
                display_name="Exit Date",
                source_id="user",
                source_type="report",
                system_name="exit_date",
                data_type_id=2,
                knowledge_begin_date=timezone.now(),
            ),
            DatasheetVariable(
                client_id=TEST_CLIENT_ID,
                databook_id=databook_id_1,
                datasheet_id=datasheet_id_3,
                variable_id="1862a8af-58e2-41ba-86b0-f8654ab385e9",
                source_variable_id="last_commission_date_user",
                display_name="Exit Last Commission Date",
                source_id="user",
                source_type="report",
                system_name="last_commission_date",
                data_type_id=2,
                knowledge_begin_date=timezone.now(),
            ),
            DatasheetVariable(
                client_id=TEST_CLIENT_ID,
                databook_id=databook_id_1,
                datasheet_id=datasheet_id_3,
                variable_id="23fe6cc2-9872-4869-ac78-da43f1f1cdd6",
                source_variable_id="status_user",
                display_name="Status",
                source_id="user",
                source_type="report",
                system_name="status",
                data_type_id=2,
                knowledge_begin_date=timezone.now(),
            ),
            DatasheetVariable(
                client_id=TEST_CLIENT_ID,
                databook_id=databook_id_1,
                datasheet_id=datasheet_id_3,
                variable_id="effa65a1-1343-49bc-81ab-59b7000fd4c0",
                source_variable_id="employee_email_id_user",
                display_name="Employee Email Id",
                source_id="user",
                source_type="report",
                system_name="employee_email_id",
                data_type_id=2,
                knowledge_begin_date=timezone.now(),
            ),
            DatasheetVariable(
                client_id=TEST_CLIENT_ID,
                databook_id=databook_id_1,
                datasheet_id=datasheet_id_3,
                variable_id="92eb784e-d9e0-46be-89fb-91ff0d4acdc2",
                source_variable_id="reporting_manager_email_id_user",
                display_name="Reporting Manager EmailId",
                source_id="user",
                source_type="report",
                system_name="reporting_manager_email_id",
                data_type_id=2,
                knowledge_begin_date=timezone.now(),
            ),
            DatasheetVariable(
                client_id=TEST_CLIENT_ID,
                databook_id=databook_id_1,
                datasheet_id=datasheet_id_3,
                variable_id="115538cf-fb87-461b-bc72-246e4386719d",
                source_variable_id="reporting_manager_name_user",
                display_name="Reporting Manager Name",
                source_id="user",
                source_type="report",
                system_name="reporting_manager_name",
                data_type_id=2,
                knowledge_begin_date=timezone.now(),
            ),
            DatasheetVariable(
                client_id=TEST_CLIENT_ID,
                databook_id=databook_id_1,
                datasheet_id=datasheet_id_3,
                variable_id="cf399817-e61c-4d3c-b026-c89101fe6224",
                source_variable_id="active_plan_user",
                display_name="Active Primary Plan",
                source_id="user",
                source_type="report",
                system_name="active_plan",
                data_type_id=2,
                knowledge_begin_date=timezone.now(),
            ),
            DatasheetVariable(
                client_id=TEST_CLIENT_ID,
                databook_id=databook_id_1,
                datasheet_id=datasheet_id_3,
                variable_id="feeb76cf-4f21-4bbe-a9f8-e2acaa533434",
                source_variable_id="active_spiffs_user",
                display_name="Active Spiff Plan",
                source_id="user",
                source_type="report",
                system_name="active_spiffs",
                data_type_id=2,
                knowledge_begin_date=timezone.now(),
            ),
            DatasheetVariable(
                client_id=TEST_CLIENT_ID,
                databook_id=databook_id_1,
                datasheet_id=datasheet_id_3,
                variable_id="8f0e184d-4456-4c9f-9147-737169f4fd04",
                source_variable_id="active_quota_categories_user",
                display_name="Active Quota Categories",
                source_id="user",
                source_type="report",
                system_name="active_quota_categories",
                data_type_id=2,
                knowledge_begin_date=timezone.now(),
            ),
            DatasheetVariable(
                client_id=TEST_CLIENT_ID,
                databook_id=databook_id_1,
                datasheet_id=datasheet_id_3,
                variable_id="d6b4be43-8f5f-43fe-ac30-900e9d3eee8b",
                source_variable_id="payee_or_manager_user",
                display_name="Payee/Manager",
                source_id="user",
                source_type="report",
                system_name="payee_or_manager",
                data_type_id=2,
                knowledge_begin_date=timezone.now(),
            ),
        ]
    )

    Datasheet.objects.create(
        client_id=TEST_CLIENT_ID,
        databook_id=databook_id_1,
        datasheet_id=datasheet_id_4,
        name="test-with-join",
        source_type="object",
        source_id=1,
        order=2,
        primary_key=["lhs_co_1_number", "rhs_co_1_number"],
        additional_details={
            "server": "0.0.0.0",
            "updated_by": "<EMAIL>",
            "prohibited_usage___login_user": "<EMAIL>",
        },
        transformation_spec=[
            {
                "on": {
                    "lhs": [
                        {
                            "source_id": 1,
                            "is_primary": False,
                            "source_type": "object",
                            "system_name": "co_1_name",
                            "variable_id": "co_1_name",
                            "data_type_id": 4,
                            "display_name": "Name",
                            "source_variable_id": None,
                            "source_cf_meta_data": None,
                            "source_name_history": "Name << custom_object_1",
                        }
                    ],
                    "rhs": [
                        {
                            "source_id": "1",
                            "is_primary": False,
                            "source_type": "object",
                            "system_name": "co_1_name",
                            "variable_id": "a7f84b61-17d5-4d73-88e2-5ee9996649a6",
                            "data_type_id": 4,
                            "display_name": "Name",
                            "source_variable_id": "co_1_name",
                            "source_cf_meta_data": None,
                            "source_name_history": "Name << custom_object_1",
                        }
                    ],
                },
                "key": "13d235a9-0423-498f-a48b-1f2f3caffd85",
                "type": "JOIN",
                "with": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                "columns": {
                    "lhs": [
                        {
                            "source_id": 1,
                            "is_primary": True,
                            "source_type": "object",
                            "system_name": "co_1_number",
                            "variable_id": "co_1_number",
                            "data_type_id": 1,
                            "display_name": "Number",
                            "system_generated": False,
                            "source_variable_id": None,
                            "source_cf_meta_data": None,
                            "source_name_history": "Number << custom_object_1",
                        }
                    ],
                    "rhs": [
                        {
                            "source_id": "1",
                            "is_primary": True,
                            "source_type": "object",
                            "system_name": "co_1_number",
                            "variable_id": "8113951d-6ab2-48ff-af64-ab96579abd0b",
                            "data_type_id": 1,
                            "display_name": "Number",
                            "source_variable_id": "co_1_number",
                            "source_cf_meta_data": None,
                            "source_name_history": "Number << custom_object_1",
                        }
                    ],
                },
                "is_valid": True,
                "join_type": "LEFT",
                "source_ids": ["f962ea83-b58a-4ee4-983e-1dc07fe4d3fb"],
                "output_columns": [
                    {
                        "source_id": 1,
                        "is_primary": True,
                        "source_type": "object",
                        "system_name": "lhs_co_1_number",
                        "variable_id": "co_1_number",
                        "data_type_id": 1,
                        "display_name": "Number",
                        "system_generated": False,
                        "source_variable_id": None,
                        "source_cf_meta_data": None,
                        "source_name_history": "Number << custom_object_1",
                    },
                    {
                        "source_id": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                        "is_primary": True,
                        "source_type": "datasheet",
                        "system_name": "rhs_co_1_number",
                        "variable_id": "8113951d-6ab2-48ff-af64-ab96579abd0b",
                        "data_type_id": 1,
                        "display_name": "Number",
                        "source_variable_id": "co_1_number",
                        "source_cf_meta_data": None,
                        "source_name_history": "Number << custom_object_1",
                    },
                ],
                "with_databook_id": "869b4b9b-5e9f-49e1-b24c-e4c5cee825b0",
                "transformation_id": "13d235a9-0423-498f-a48b-1f2f3caffd85",
                "used_variable_ids": [
                    "co_1_number",
                    "8113951d-6ab2-48ff-af64-ab96579abd0b",
                    "a7f84b61-17d5-4d73-88e2-5ee9996649a6",
                    "co_1_name",
                ],
                "transformation_source_map": {
                    "1_object": 0,
                    "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb_datasheet": 0,
                },
            }
        ],
        ordered_columns="{lhs_co_1_number,rhs_co_1_number}",
        data_origin="custom_object",
        knowledge_begin_date=timezone.now(),
    )

    DatasheetVariable.objects.bulk_create(
        [
            DatasheetVariable(
                client_id=TEST_CLIENT_ID,
                databook_id=databook_id_1,
                datasheet_id=datasheet_id_4,
                variable_id="1a807b04-f4dc-4d75-a341-b501fdc08b51",
                source_variable_id="co_1_number",
                display_name="Number",
                source_id=1,
                source_type="object",
                system_name="lhs_co_1_number",
                data_type_id=1,
                knowledge_begin_date=timezone.now(),
            ),
            DatasheetVariable(
                client_id=TEST_CLIENT_ID,
                databook_id=databook_id_1,
                datasheet_id=datasheet_id_4,
                variable_id="4800c1f5-4560-4d6e-8a07-4772185a3c09",
                source_variable_id="8113951d-6ab2-48ff-af64-ab96579abd0b",
                display_name="test :: Number",
                source_id="f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                source_type="datasheet",
                system_name="rhs_co_1_number",
                data_type_id=1,
                knowledge_begin_date=timezone.now(),
            ),
        ]
    )

    Datasheet.objects.create(
        client_id=TEST_CLIENT_ID,
        databook_id=databook_id_1,
        datasheet_id=datasheet_id_5,
        name="test-with-same-join",
        source_type="datasheet",
        source_id=datasheet_id_1,
        knowledge_begin_date=timezone.now(),
        order=2,
        primary_key=["lhs_co_1_number", "rhs_co_1_number"],
        additional_details={
            "server": "0.0.0.0",
            "updated_by": "<EMAIL>",
            "prohibited_usage___login_user": "<EMAIL>",
        },
        transformation_spec=[
            {
                "on": {
                    "lhs": [
                        {
                            "source_id": "1",
                            "is_primary": False,
                            "source_type": "object",
                            "system_name": "co_1_name",
                            "variable_id": "a7f84b61-17d5-4d73-88e2-5ee9996649a6",
                            "data_type_id": 4,
                            "display_name": "Name",
                            "source_variable_id": "co_1_name",
                            "source_cf_meta_data": None,
                            "source_name_history": "Name << custom_object_1",
                        }
                    ],
                    "rhs": [
                        {
                            "source_id": "1",
                            "is_primary": False,
                            "source_type": "object",
                            "system_name": "co_1_name",
                            "variable_id": "a7f84b61-17d5-4d73-88e2-5ee9996649a6",
                            "data_type_id": 4,
                            "display_name": "Name",
                            "source_variable_id": "co_1_name",
                            "source_cf_meta_data": None,
                            "source_name_history": "Name << custom_object_1",
                        }
                    ],
                },
                "key": "67260bc4-f59c-475b-a042-020b384bb8de",
                "type": "JOIN",
                "with": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                "columns": {
                    "lhs": [
                        {
                            "source_id": "1",
                            "is_primary": True,
                            "source_type": "object",
                            "system_name": "co_1_number",
                            "variable_id": "8113951d-6ab2-48ff-af64-ab96579abd0b",
                            "data_type_id": 1,
                            "display_name": "Number",
                            "system_generated": False,
                            "source_variable_id": "co_1_number",
                            "source_cf_meta_data": None,
                            "source_name_history": "Number << custom_object_1",
                        }
                    ],
                    "rhs": [
                        {
                            "source_id": "1",
                            "is_primary": True,
                            "source_type": "object",
                            "system_name": "co_1_number",
                            "variable_id": "8113951d-6ab2-48ff-af64-ab96579abd0b",
                            "data_type_id": 1,
                            "display_name": "Number",
                            "source_variable_id": "co_1_number",
                            "source_cf_meta_data": None,
                            "source_name_history": "Number << custom_object_1",
                        }
                    ],
                },
                "is_valid": True,
                "join_type": "LEFT",
                "source_ids": ["f962ea83-b58a-4ee4-983e-1dc07fe4d3fb"],
                "output_columns": [
                    {
                        "source_id": "1",
                        "is_primary": True,
                        "source_type": "object",
                        "system_name": "lhs_co_1_number",
                        "variable_id": "8113951d-6ab2-48ff-af64-ab96579abd0b",
                        "data_type_id": 1,
                        "display_name": "Number",
                        "system_generated": False,
                        "source_variable_id": "co_1_number",
                        "source_cf_meta_data": None,
                        "source_name_history": "Number << custom_object_1",
                    },
                    {
                        "source_id": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                        "is_primary": True,
                        "source_type": "datasheet",
                        "system_name": "rhs_co_1_number",
                        "variable_id": "8113951d-6ab2-48ff-af64-ab96579abd0b__ss__1",
                        "data_type_id": 1,
                        "display_name": "Number",
                        "source_variable_id": "co_1_number",
                        "source_cf_meta_data": None,
                        "source_name_history": "Number << custom_object_1",
                    },
                ],
                "with_databook_id": "869b4b9b-5e9f-49e1-b24c-e4c5cee825b0",
                "transformation_id": "67260bc4-f59c-475b-a042-020b384bb8de",
                "used_variable_ids": [
                    "a7f84b61-17d5-4d73-88e2-5ee9996649a6",
                    "8113951d-6ab2-48ff-af64-ab96579abd0b",
                ],
                "transformation_source_map": {
                    "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb_datasheet": 1
                },
            }
        ],
        ordered_columns="{lhs_co_1_number,rhs_co_1_number}",
        data_origin="custom_object",
    )

    DatasheetVariable.objects.bulk_create(
        [
            DatasheetVariable(
                client_id=TEST_CLIENT_ID,
                databook_id=databook_id_1,
                datasheet_id=datasheet_id_5,
                variable_id="51671ef6-d78a-4649-8f34-baf1ed295ae4",
                source_variable_id="8113951d-6ab2-48ff-af64-ab96579abd0b",
                display_name="Number",
                source_id="f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                source_type="datasheet",
                system_name="lhs_co_1_number",
                data_type_id=1,
                knowledge_begin_date=timezone.now(),
            ),
            DatasheetVariable(
                client_id=TEST_CLIENT_ID,
                databook_id=databook_id_1,
                datasheet_id=datasheet_id_5,
                variable_id="81c78864-a241-44f7-8b86-5db6e5e22fe6",
                source_variable_id="8113951d-6ab2-48ff-af64-ab96579abd0b__ss__1",
                display_name="Number",
                source_id="f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                source_type="datasheet",
                system_name="rhs_co_1_number",
                data_type_id=1,
                knowledge_begin_date=timezone.now(),
            ),
        ]
    )

    Datasheet.objects.create(
        client_id=TEST_CLIENT_ID,
        databook_id=databook_id_1,
        datasheet_id=datasheet_id_6,
        name="test-with-same-join-2",
        source_type="datasheet",
        source_id=datasheet_id_1,
        knowledge_begin_date=timezone.now(),
        order=2,
        primary_key=["lhs_lhs_co_1_number", "lhs_rhs_co_1_number", "rhs_co_1_number"],
        additional_details={
            "server": "0.0.0.0",
            "updated_by": "<EMAIL>",
            "prohibited_usage___login_user": "<EMAIL>",
        },
        transformation_spec=[
            {
                "on": {
                    "lhs": [
                        {
                            "warning": None,
                            "client_id": 3024,
                            "meta_data": None,
                            "source_id": "1",
                            "is_primary": False,
                            "databook_id": "869b4b9b-5e9f-49e1-b24c-e4c5cee825b0",
                            "field_order": 0,
                            "is_adjusted": False,
                            "is_selected": True,
                            "source_name": "custom_object_1",
                            "source_type": "object",
                            "system_name": "co_1_name",
                            "variable_id": "a7f84b61-17d5-4d73-88e2-5ee9996649a6",
                            "data_type_id": 4,
                            "datasheet_id": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                            "display_name": "Name",
                            "is_dependent": False,
                            "source_variable_id": "co_1_name",
                            "source_cf_meta_data": None,
                            "source_name_history": "Name << custom_object_1",
                        }
                    ],
                    "rhs": [
                        {
                            "warning": None,
                            "client_id": 3024,
                            "meta_data": None,
                            "source_id": "1",
                            "is_primary": False,
                            "databook_id": "869b4b9b-5e9f-49e1-b24c-e4c5cee825b0",
                            "field_order": 0,
                            "is_adjusted": False,
                            "is_selected": True,
                            "source_name": "custom_object_1",
                            "source_type": "object",
                            "system_name": "co_1_name",
                            "variable_id": "a7f84b61-17d5-4d73-88e2-5ee9996649a6",
                            "data_type_id": 4,
                            "datasheet_id": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                            "display_name": "Name",
                            "is_dependent": False,
                            "source_variable_id": "co_1_name",
                            "source_cf_meta_data": None,
                            "source_name_history": "Name << custom_object_1",
                        }
                    ],
                },
                "key": "67260bc4-f59c-475b-a042-020b384bb8de",
                "type": "JOIN",
                "with": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                "columns": {
                    "lhs": [
                        {
                            "warning": None,
                            "client_id": 3024,
                            "meta_data": None,
                            "source_id": "1",
                            "is_primary": True,
                            "databook_id": "869b4b9b-5e9f-49e1-b24c-e4c5cee825b0",
                            "field_order": 0,
                            "is_adjusted": False,
                            "is_selected": True,
                            "source_name": "custom_object_1",
                            "source_type": "object",
                            "system_name": "co_1_number",
                            "variable_id": "8113951d-6ab2-48ff-af64-ab96579abd0b",
                            "data_type_id": 1,
                            "datasheet_id": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                            "display_name": "Number",
                            "is_dependent": True,
                            "source_variable_id": "co_1_number",
                            "source_cf_meta_data": None,
                            "source_name_history": "Number << custom_object_1",
                        }
                    ],
                    "rhs": [
                        {
                            "warning": None,
                            "client_id": 3024,
                            "meta_data": None,
                            "source_id": "1",
                            "is_primary": True,
                            "databook_id": "869b4b9b-5e9f-49e1-b24c-e4c5cee825b0",
                            "field_order": 0,
                            "is_adjusted": False,
                            "is_selected": True,
                            "source_name": "custom_object_1",
                            "source_type": "object",
                            "system_name": "co_1_number",
                            "variable_id": "8113951d-6ab2-48ff-af64-ab96579abd0b__ss__1",
                            "data_type_id": 1,
                            "datasheet_id": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                            "display_name": "Number",
                            "is_dependent": True,
                            "source_variable_id": "co_1_number",
                            "source_cf_meta_data": None,
                            "source_name_history": "Number << custom_object_1",
                        }
                    ],
                },
                "is_valid": True,
                "join_type": "LEFT",
                "source_ids": ["f962ea83-b58a-4ee4-983e-1dc07fe4d3fb"],
                "output_columns": [
                    {
                        "warning": None,
                        "client_id": 3024,
                        "meta_data": None,
                        "source_id": "1",
                        "is_primary": True,
                        "databook_id": "869b4b9b-5e9f-49e1-b24c-e4c5cee825b0",
                        "field_order": 0,
                        "is_adjusted": False,
                        "is_selected": True,
                        "source_name": "custom_object_1",
                        "source_type": "object",
                        "system_name": "lhs_co_1_number",
                        "variable_id": "8113951d-6ab2-48ff-af64-ab96579abd0b",
                        "data_type_id": 1,
                        "datasheet_id": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                        "display_name": "Number",
                        "is_dependent": True,
                        "source_variable_id": "co_1_number",
                        "source_cf_meta_data": None,
                        "source_name_history": "Number << custom_object_1",
                    },
                    {
                        "warning": None,
                        "client_id": 3024,
                        "meta_data": None,
                        "source_id": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                        "is_primary": True,
                        "databook_id": "869b4b9b-5e9f-49e1-b24c-e4c5cee825b0",
                        "field_order": 0,
                        "is_adjusted": False,
                        "is_selected": True,
                        "source_name": "custom_object_1",
                        "source_type": "datasheet",
                        "system_name": "rhs_co_1_number",
                        "variable_id": "8113951d-6ab2-48ff-af64-ab96579abd0b__ss__1",
                        "data_type_id": 1,
                        "datasheet_id": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                        "display_name": "Number",
                        "is_dependent": True,
                        "source_variable_id": "co_1_number",
                        "source_cf_meta_data": None,
                        "source_name_history": "Number << custom_object_1",
                    },
                ],
                "with_databook_id": "869b4b9b-5e9f-49e1-b24c-e4c5cee825b0",
                "transformation_id": "67260bc4-f59c-475b-a042-020b384bb8de",
                "used_variable_ids": [
                    "8113951d-6ab2-48ff-af64-ab96579abd0b",
                    "a7f84b61-17d5-4d73-88e2-5ee9996649a6",
                    "8113951d-6ab2-48ff-af64-ab96579abd0b__ss__1",
                ],
                "transformation_source_map": {
                    "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb_datasheet": 1
                },
            },
            {
                "on": {
                    "lhs": [
                        {
                            "warning": None,
                            "client_id": 3024,
                            "meta_data": None,
                            "source_id": "1",
                            "is_primary": True,
                            "databook_id": "869b4b9b-5e9f-49e1-b24c-e4c5cee825b0",
                            "field_order": 0,
                            "is_adjusted": False,
                            "is_selected": True,
                            "source_name": "custom_object_1",
                            "source_type": "object",
                            "system_name": "lhs_co_1_number",
                            "variable_id": "8113951d-6ab2-48ff-af64-ab96579abd0b",
                            "data_type_id": 1,
                            "datasheet_id": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                            "display_name": "Number",
                            "is_dependent": True,
                            "source_variable_id": "co_1_number",
                            "source_cf_meta_data": None,
                            "source_name_history": "Number << custom_object_1",
                        }
                    ],
                    "rhs": [
                        {
                            "warning": None,
                            "client_id": 3024,
                            "meta_data": None,
                            "source_id": "1",
                            "is_primary": True,
                            "databook_id": "869b4b9b-5e9f-49e1-b24c-e4c5cee825b0",
                            "field_order": 0,
                            "is_adjusted": False,
                            "is_selected": True,
                            "source_name": "custom_object_1",
                            "source_type": "object",
                            "system_name": "co_1_number",
                            "variable_id": "8113951d-6ab2-48ff-af64-ab96579abd0b",
                            "data_type_id": 1,
                            "datasheet_id": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                            "display_name": "Number",
                            "is_dependent": True,
                            "source_variable_id": "co_1_number",
                            "source_cf_meta_data": None,
                            "source_name_history": "Number << custom_object_1",
                        }
                    ],
                },
                "key": "cf612b52-3957-4c96-91f8-34a397c83082",
                "type": "JOIN",
                "with": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                "columns": {
                    "lhs": [
                        {
                            "warning": None,
                            "client_id": 3024,
                            "meta_data": None,
                            "source_id": "1",
                            "is_primary": True,
                            "databook_id": "869b4b9b-5e9f-49e1-b24c-e4c5cee825b0",
                            "field_order": 0,
                            "is_adjusted": False,
                            "is_selected": True,
                            "source_name": "custom_object_1",
                            "source_type": "object",
                            "system_name": "lhs_co_1_number",
                            "variable_id": "8113951d-6ab2-48ff-af64-ab96579abd0b",
                            "data_type_id": 1,
                            "datasheet_id": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                            "display_name": "Number",
                            "is_dependent": True,
                            "source_variable_id": "co_1_number",
                            "source_cf_meta_data": None,
                            "source_name_history": "Number << custom_object_1",
                        },
                        {
                            "warning": None,
                            "client_id": 3024,
                            "meta_data": None,
                            "source_id": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                            "is_primary": True,
                            "databook_id": "869b4b9b-5e9f-49e1-b24c-e4c5cee825b0",
                            "field_order": 0,
                            "is_adjusted": False,
                            "is_selected": True,
                            "source_name": "custom_object_1",
                            "source_type": "datasheet",
                            "system_name": "rhs_co_1_number",
                            "variable_id": "8113951d-6ab2-48ff-af64-ab96579abd0b__ss__1",
                            "data_type_id": 1,
                            "datasheet_id": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                            "display_name": "Number",
                            "is_dependent": True,
                            "source_variable_id": "co_1_number",
                            "source_cf_meta_data": None,
                            "source_name_history": "Number << custom_object_1",
                        },
                    ],
                    "rhs": [
                        {
                            "warning": None,
                            "client_id": 3024,
                            "meta_data": None,
                            "source_id": "1",
                            "is_primary": True,
                            "databook_id": "869b4b9b-5e9f-49e1-b24c-e4c5cee825b0",
                            "field_order": 0,
                            "is_adjusted": False,
                            "is_selected": True,
                            "source_name": "custom_object_1",
                            "source_type": "object",
                            "system_name": "co_1_number",
                            "variable_id": "8113951d-6ab2-48ff-af64-ab96579abd0b__ss__2",
                            "data_type_id": 1,
                            "datasheet_id": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                            "display_name": "Number",
                            "is_dependent": True,
                            "source_variable_id": "co_1_number",
                            "source_cf_meta_data": None,
                            "source_name_history": "Number << custom_object_1",
                        }
                    ],
                },
                "is_valid": True,
                "join_type": "LEFT",
                "source_ids": ["f962ea83-b58a-4ee4-983e-1dc07fe4d3fb"],
                "output_columns": [
                    {
                        "warning": None,
                        "client_id": 3024,
                        "meta_data": None,
                        "source_id": "1",
                        "is_primary": True,
                        "databook_id": "869b4b9b-5e9f-49e1-b24c-e4c5cee825b0",
                        "field_order": 0,
                        "is_adjusted": False,
                        "is_selected": True,
                        "source_name": "custom_object_1",
                        "source_type": "object",
                        "system_name": "lhs_lhs_co_1_number",
                        "variable_id": "8113951d-6ab2-48ff-af64-ab96579abd0b",
                        "data_type_id": 1,
                        "datasheet_id": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                        "display_name": "Number",
                        "is_dependent": True,
                        "source_variable_id": "co_1_number",
                        "source_cf_meta_data": None,
                        "source_name_history": "Number << custom_object_1",
                    },
                    {
                        "warning": None,
                        "client_id": 3024,
                        "meta_data": None,
                        "source_id": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                        "is_primary": True,
                        "databook_id": "869b4b9b-5e9f-49e1-b24c-e4c5cee825b0",
                        "field_order": 0,
                        "is_adjusted": False,
                        "is_selected": True,
                        "source_name": "custom_object_1",
                        "source_type": "datasheet",
                        "system_name": "lhs_rhs_co_1_number",
                        "variable_id": "8113951d-6ab2-48ff-af64-ab96579abd0b__ss__1",
                        "data_type_id": 1,
                        "datasheet_id": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                        "display_name": "Number",
                        "is_dependent": True,
                        "source_variable_id": "co_1_number",
                        "source_cf_meta_data": None,
                        "source_name_history": "Number << custom_object_1",
                    },
                    {
                        "warning": None,
                        "client_id": 3024,
                        "meta_data": None,
                        "source_id": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                        "is_primary": True,
                        "databook_id": "869b4b9b-5e9f-49e1-b24c-e4c5cee825b0",
                        "field_order": 0,
                        "is_adjusted": False,
                        "is_selected": True,
                        "source_name": "custom_object_1",
                        "source_type": "datasheet",
                        "system_name": "rhs_co_1_number",
                        "variable_id": "8113951d-6ab2-48ff-af64-ab96579abd0b__ss__2",
                        "data_type_id": 1,
                        "datasheet_id": "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                        "display_name": "Number",
                        "is_dependent": True,
                        "source_variable_id": "co_1_number",
                        "source_cf_meta_data": None,
                        "source_name_history": "Number << custom_object_1",
                    },
                ],
                "with_databook_id": "869b4b9b-5e9f-49e1-b24c-e4c5cee825b0",
                "transformation_id": "cf612b52-3957-4c96-91f8-34a397c83082",
                "used_variable_ids": [
                    "8113951d-6ab2-48ff-af64-ab96579abd0b__ss__2",
                    "8113951d-6ab2-48ff-af64-ab96579abd0b",
                    "a7f84b61-17d5-4d73-88e2-5ee9996649a6",
                    "8113951d-6ab2-48ff-af64-ab96579abd0b__ss__1",
                ],
                "transformation_source_map": {
                    "f962ea83-b58a-4ee4-983e-1dc07fe4d3fb_datasheet": 2
                },
            },
        ],
        source_databook_id=databook_id_1,
    )

    DatasheetVariable.objects.bulk_create(
        [
            DatasheetVariable(
                client_id=3024,
                datasheet_id="b2dd92d5-d46c-44b6-a23a-ae712407f38a",
                databook_id="869b4b9b-5e9f-49e1-b24c-e4c5cee825b0",
                meta_data=None,
                variable_id="5652ef0e-9321-4d52-95a8-905a7c353744",
                source_cf_meta_data=None,
                source_id="f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                warning=None,
                system_name="lhs_lhs_co_1_number",
                display_name="Number",
                data_type_id=1,
                source_variable_id="8113951d-6ab2-48ff-af64-ab96579abd0b",
                source_type="datasheet",
                is_selected=True,
                is_primary=True,
                field_order=0,
                knowledge_begin_date=timezone.now(),
            ),
            DatasheetVariable(
                client_id=3024,
                datasheet_id="b2dd92d5-d46c-44b6-a23a-ae712407f38a",
                databook_id="869b4b9b-5e9f-49e1-b24c-e4c5cee825b0",
                meta_data=None,
                variable_id="9956ec66-e98f-4955-a459-0d1c5eb9b0f6",
                source_cf_meta_data=None,
                source_id="f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                warning=None,
                system_name="lhs_rhs_co_1_number",
                display_name="test :: Number",
                data_type_id=1,
                source_variable_id="8113951d-6ab2-48ff-af64-ab96579abd0b__ss__1",
                source_type="datasheet",
                is_selected=True,
                is_primary=True,
                field_order=0,
                knowledge_begin_date=timezone.now(),
            ),
            DatasheetVariable(
                client_id=3024,
                datasheet_id="b2dd92d5-d46c-44b6-a23a-ae712407f38a",
                databook_id="869b4b9b-5e9f-49e1-b24c-e4c5cee825b0",
                meta_data=None,
                variable_id="67d0e6a6-ddda-4527-a725-0568146804f4",
                source_cf_meta_data=None,
                source_id="f962ea83-b58a-4ee4-983e-1dc07fe4d3fb",
                warning=None,
                system_name="rhs_co_1_number",
                display_name="test :: test :: Number",
                data_type_id=1,
                source_variable_id="8113951d-6ab2-48ff-af64-ab96579abd0b__ss__2",
                source_type="datasheet",
                is_selected=True,
                is_primary=True,
                field_order=0,
                knowledge_begin_date=timezone.now(),
            ),
        ]
    )

    Datasheet.objects.create(
        client_id=TEST_CLIENT_ID,
        datasheet_id=datasheet_id_7,
        databook_id=databook_id_1,
        name="calc-field",
        source_type="object",
        source_id=1,
        knowledge_begin_date=timezone.now(),
        order=2,
        primary_key=["co_1_number"],
        additional_details={
            "server": "0.0.0.0",
            "updated_by": "<EMAIL>",
            "prohibited_usage___login_user": "<EMAIL>",
        },
        transformation_spec=[],
        ordered_columns="{co_1_number}",
        data_origin="custom_object",
    )

    DatasheetVariable.objects.bulk_create(
        [
            DatasheetVariable(
                client_id=3024,
                datasheet_id=datasheet_id_7,
                databook_id=databook_id_1,
                meta_data={
                    "ast": {
                        "meta": {
                            "category": None,
                            "model_name": "calc-field",
                            "system_name": "co_1_number",
                            "data_type_id": 1,
                        },
                        "name": "Number",
                        "tags": None,
                        "type": "VARIABLE",
                        "data_type": "Integer",
                        "databook_id": "869b4b9b-5e9f-49e1-b24c-e4c5cee825b0",
                        "datasheet_id": "b12af5c1-35f0-4b8f-b22d-991ea4395ef3",
                    },
                    "criteria_type": "simple",
                    "evaluation_context": "delta",
                    "used_system_names": ["co_1_number"],
                    "case_insensitive": True,
                    "infix": [
                        {
                            "token": {
                                "key": "co_1_number",
                                "name": "Number",
                                "path": "Number << custom_object_1",
                                "system_name": "co_1_number",
                                "variable_id": "0e70b50b-c30d-4ff7-b8cf-752d456973b5",
                            },
                            "token_type": "DATASHEET_VARIABLES",
                        }
                    ],
                },
                variable_id="d01263a6-b93d-46b4-bb02-d6217bd27555",
                source_cf_meta_data=None,
                source_id=datasheet_id_7,
                warning=None,
                system_name="cf_num",
                display_name="num",
                data_type_id=1,
                source_variable_id=None,
                source_type="datasheet",
                is_selected=True,
                is_primary=False,
                field_order=1,
                knowledge_begin_date=timezone.now(),
            ),
            DatasheetVariable(
                client_id=3024,
                datasheet_id=datasheet_id_7,
                databook_id=databook_id_1,
                meta_data=None,
                variable_id="0e70b50b-c30d-4ff7-b8cf-752d456973b5",
                source_cf_meta_data=None,
                source_id="1",
                warning=None,
                system_name="co_1_number",
                display_name="Number",
                data_type_id=1,
                source_variable_id="co_1_number",
                source_type="object",
                is_selected=True,
                is_primary=True,
                field_order=0,
                knowledge_begin_date=timezone.now(),
            ),
        ]
    )


@pytest.mark.django_db
def datasheet_validator_delete_data():

    to_delete_datasheet_ids = [
        datasheet_id_1,
        datasheet_id_2,
        datasheet_id_3,
        datasheet_id_4,
        datasheet_id_5,
        datasheet_id_6,
        datasheet_id_7,
        datasheet_id_8,
        datasheet_id_9,
        datasheet_id_10,
    ]

    Datasheet.objects.filter(datasheet_id__in=to_delete_datasheet_ids).delete()
    DatasheetVariable.objects.filter(datasheet_id__in=to_delete_datasheet_ids).delete()


custom_object_1_details = {
    "co_1_percentage": {
        "source_variable_id": "co_1_percentage",
        "display_name": "Percentage",
        "data_type_id": 6,
    },
    "co_1_email": {
        "source_variable_id": "co_1_email",
        "display_name": "Email",
        "data_type_id": 12,
    },
    "co_1_date_2": {
        "source_variable_id": "co_1_date_2",
        "display_name": "Date-2",
        "data_type_id": 2,
    },
    "co_1_name": {
        "source_variable_id": "co_1_name",
        "display_name": "Name",
        "data_type_id": 4,
    },
    "co_1_number": {
        "source_variable_id": "co_1_number",
        "display_name": "Number",
        "data_type_id": 1,
    },
    "co_1_boolean": {
        "source_variable_id": "co_1_boolean",
        "display_name": "Boolean",
        "data_type_id": 3,
    },
    "co_1_date_1": {
        "source_variable_id": "co_1_date_1",
        "display_name": "Date-1",
        "data_type_id": 2,
    },
}


def get_system_name_to_variable_details(datasheet_id: UUID) -> dict:

    if datasheet_id == datasheet_id_1:
        sys_to_var_id_map = {
            "co_1_percentage": "c54ff1fc-43ae-4e06-935f-78527191f385",
            "co_1_email": "1b0b9972-764c-426a-891d-1d7338645519",
            "co_1_date_2": "16bd069f-eaaf-4d5b-9b9b-afd42c02f631",
            "co_1_name": "a7f84b61-17d5-4d73-88e2-5ee9996649a6",
            "co_1_number": "8113951d-6ab2-48ff-af64-ab96579abd0b",
            "co_1_boolean": "c66f062b-4bb1-46c8-9f4d-d3b3663ee1f2",
            "co_1_date_1": "a8f6f7d8-9731-4dc8-bc1a-328d093b389b",
        }
        return {
            k: {**v, "variable_id": sys_to_var_id_map[k]}
            for k, v in custom_object_1_details.items()
        }
    elif datasheet_id == datasheet_id_8:
        sys_to_var_id_map = {
            "co_1_percentage": "51611cc2-a1df-438d-891b-f9e5b520c8aa",
            "co_1_email": "93318c47-f5be-46dc-9bbe-309e31cc09c2",
            "co_1_date_2": "17b016db-c8e0-4c9f-9b08-9582aadb9292",
            "co_1_name": "bd577451-57e3-4a9c-b90e-4fc9b95dbf17",
            "co_1_number": "7f77338e-8d5d-4ac3-9c4f-aa0d25c1053d",
            "co_1_boolean": "3e41d505-51e8-4b1d-a55b-9748d520f713",
            "co_1_date_1": "1a4aaecf-8943-460b-bfb8-8cddf19cbd04",
        }
        return {
            k: {**v, "variable_id": sys_to_var_id_map[k]}
            for k, v in custom_object_1_details.items()
        }
    elif datasheet_id == datasheet_id_9:
        sys_to_var_id_map = {
            "co_1_name": "5c254d22-8ec4-435f-84c1-1cc49c597408",
            "co_1_number": "fef92446-3b70-4d01-8376-707eeda032f8",
            "co_1_boolean": "79cdd35b-93e0-4bb3-8dc4-e206eb876b23",
            "co_1_date_1": "e3db048b-98b4-4a81-998f-02cefa735fec",
            "co_1_email": "8c4c8500-267f-4cbe-8cb7-b008fd9a1807",
            "co_1_percentage": "5d6e89f1-6a62-4a62-87c5-5828c061023f",
            "co_1_date_2": "d3427b01-098f-4e2c-a5f4-2c321164912f",
        }
        return {
            k: {**v, "variable_id": sys_to_var_id_map[k]}
            for k, v in custom_object_1_details.items()
        }
    elif datasheet_id == datasheet_id_10:
        # datasheet_id_9 variable id is source_variable_id for datasheet_id_10 variables.
        sys_to_source_var_id_map = {
            "co_1_name": "5c254d22-8ec4-435f-84c1-1cc49c597408",
            "co_1_number": "fef92446-3b70-4d01-8376-707eeda032f8",
            "co_1_boolean": "79cdd35b-93e0-4bb3-8dc4-e206eb876b23",
            "co_1_date_1": "e3db048b-98b4-4a81-998f-02cefa735fec",
            "co_1_email": "8c4c8500-267f-4cbe-8cb7-b008fd9a1807",
            "co_1_percentage": "5d6e89f1-6a62-4a62-87c5-5828c061023f",
            "co_1_date_2": "d3427b01-098f-4e2c-a5f4-2c321164912f",
        }

        return {
            k: {
                **v,
                "source_variable_id": sys_to_source_var_id_map[k],
                "variable_id": str(uuid.uuid4()),
            }
            for k, v in custom_object_1_details.items()
        }

    return {}


def get_base_data():
    return {
        "source_variables": [
            {
                "source_id": 1,
                "source_name": "custom_object_1",
                "source_type": "object",
                "is_primary": False,
                "is_selected": True,
                "variable_id": "co_1_name",
                "source_variable_id": None,
                "source_name_history": "Name << custom_object_1",
                "system_name": "co_1_name",
                "display_name": "Name",
                "data_type_id": 4,
            },
            {
                "source_id": 1,
                "source_name": "custom_object_1",
                "source_type": "object",
                "is_primary": True,
                "is_selected": True,
                "variable_id": "co_1_number",
                "source_variable_id": None,
                "source_name_history": "Number << custom_object_1",
                "system_name": "co_1_number",
                "display_name": "Number",
                "data_type_id": 1,
            },
            {
                "source_id": 1,
                "source_name": "custom_object_1",
                "source_type": "object",
                "is_primary": False,
                "is_selected": True,
                "variable_id": "co_1_boolean",
                "source_variable_id": None,
                "source_name_history": "Boolean << custom_object_1",
                "system_name": "co_1_boolean",
                "display_name": "Boolean",
                "data_type_id": 3,
            },
            {
                "source_id": 1,
                "source_name": "custom_object_1",
                "source_type": "object",
                "is_primary": False,
                "is_selected": True,
                "variable_id": "co_1_date_1",
                "source_variable_id": None,
                "source_name_history": "Date-1 << custom_object_1",
                "system_name": "co_1_date_1",
                "display_name": "Date-1",
                "data_type_id": 2,
            },
            {
                "source_id": 1,
                "source_name": "custom_object_1",
                "source_type": "object",
                "is_primary": False,
                "is_selected": True,
                "variable_id": "co_1_email",
                "source_variable_id": None,
                "source_name_history": "Email << custom_object_1",
                "system_name": "co_1_email",
                "display_name": "Email",
                "data_type_id": 12,
            },
            {
                "source_id": 1,
                "source_name": "custom_object_1",
                "source_type": "object",
                "is_primary": False,
                "is_selected": True,
                "variable_id": "co_1_percentage",
                "source_variable_id": None,
                "source_name_history": "Percentage << custom_object_1",
                "system_name": "co_1_percentage",
                "display_name": "Percentage",
                "data_type_id": 6,
            },
            {
                "source_id": 1,
                "source_name": "custom_object_1",
                "source_type": "object",
                "is_primary": False,
                "is_selected": True,
                "variable_id": "co_1_date_2",
                "source_variable_id": None,
                "source_name_history": "Date-2 << custom_object_1",
                "system_name": "co_1_date_2",
                "display_name": "Date-2",
                "data_type_id": 2,
            },
        ],
        "initial_validation": True,
        "source_type": "object",
        "source_id": 1,
        "variables": [
            {
                "client_id": 3024,
                "datasheet_id": "382929d2-6294-4803-acc8-256ada17acec",
                "databook_id": "9605d772-f376-4cdc-b9a6-40ab8d2e2645",
                "is_selected": True,
                "meta_data": None,
                "variable_id": "bd577451-57e3-4a9c-b90e-4fc9b95dbf17",
                "source_cf_meta_data": None,
                "source_id": "1",
                "warning": None,
                "system_name": "co_1_name",
                "display_name": "Name",
                "data_type_id": 4,
                "source_variable_id": "co_1_name",
                "source_type": "object",
                "is_primary": False,
                "field_order": 0,
                "source_name": "custom_object_1",
                "is_adjusted": False,
                "source_name_history": "Name << custom_object_1",
                "is_dependent": False,
            },
            {
                "client_id": 3024,
                "datasheet_id": "382929d2-6294-4803-acc8-256ada17acec",
                "databook_id": "9605d772-f376-4cdc-b9a6-40ab8d2e2645",
                "is_selected": True,
                "meta_data": None,
                "variable_id": "7f77338e-8d5d-4ac3-9c4f-aa0d25c1053d",
                "source_cf_meta_data": None,
                "source_id": "1",
                "warning": None,
                "system_name": "co_1_number",
                "display_name": "Number",
                "data_type_id": 1,
                "source_variable_id": "co_1_number",
                "source_type": "object",
                "is_primary": True,
                "field_order": 0,
                "source_name": "custom_object_1",
                "is_adjusted": False,
                "source_name_history": "Number << custom_object_1",
                "is_dependent": False,
            },
            {
                "client_id": 3024,
                "datasheet_id": "382929d2-6294-4803-acc8-256ada17acec",
                "databook_id": "9605d772-f376-4cdc-b9a6-40ab8d2e2645",
                "is_selected": True,
                "meta_data": None,
                "variable_id": "3e41d505-51e8-4b1d-a55b-9748d520f713",
                "source_cf_meta_data": None,
                "source_id": "1",
                "warning": None,
                "system_name": "co_1_boolean",
                "display_name": "Boolean",
                "data_type_id": 3,
                "source_variable_id": "co_1_boolean",
                "source_type": "object",
                "is_primary": False,
                "field_order": 0,
                "source_name": "custom_object_1",
                "is_adjusted": False,
                "source_name_history": "Boolean << custom_object_1",
                "is_dependent": False,
            },
            {
                "client_id": 3024,
                "datasheet_id": "382929d2-6294-4803-acc8-256ada17acec",
                "databook_id": "9605d772-f376-4cdc-b9a6-40ab8d2e2645",
                "is_selected": True,
                "meta_data": None,
                "variable_id": "1a4aaecf-8943-460b-bfb8-8cddf19cbd04",
                "source_cf_meta_data": None,
                "source_id": "1",
                "warning": None,
                "system_name": "co_1_date_1",
                "display_name": "Date-1",
                "data_type_id": 2,
                "source_variable_id": "co_1_date_1",
                "source_type": "object",
                "is_primary": False,
                "field_order": 0,
                "source_name": "custom_object_1",
                "is_adjusted": False,
                "source_name_history": "Date-1 << custom_object_1",
                "is_dependent": False,
            },
            {
                "client_id": 3024,
                "datasheet_id": "382929d2-6294-4803-acc8-256ada17acec",
                "databook_id": "9605d772-f376-4cdc-b9a6-40ab8d2e2645",
                "is_selected": True,
                "meta_data": None,
                "variable_id": "93318c47-f5be-46dc-9bbe-309e31cc09c2",
                "source_cf_meta_data": None,
                "source_id": "1",
                "warning": None,
                "system_name": "co_1_email",
                "display_name": "Email",
                "data_type_id": 12,
                "source_variable_id": "co_1_email",
                "source_type": "object",
                "is_primary": False,
                "field_order": 0,
                "source_name": "custom_object_1",
                "is_adjusted": False,
                "source_name_history": "Email << custom_object_1",
                "is_dependent": False,
            },
            {
                "client_id": 3024,
                "datasheet_id": "382929d2-6294-4803-acc8-256ada17acec",
                "databook_id": "9605d772-f376-4cdc-b9a6-40ab8d2e2645",
                "is_selected": True,
                "meta_data": None,
                "variable_id": "51611cc2-a1df-438d-891b-f9e5b520c8aa",
                "source_cf_meta_data": None,
                "source_id": "1",
                "warning": None,
                "system_name": "co_1_percentage",
                "display_name": "Percentage",
                "data_type_id": 6,
                "source_variable_id": "co_1_percentage",
                "source_type": "object",
                "is_primary": False,
                "field_order": 0,
                "source_name": "custom_object_1",
                "is_adjusted": False,
                "source_name_history": "Percentage << custom_object_1",
                "is_dependent": False,
            },
            {
                "client_id": 3024,
                "datasheet_id": "382929d2-6294-4803-acc8-256ada17acec",
                "databook_id": "9605d772-f376-4cdc-b9a6-40ab8d2e2645",
                "is_selected": True,
                "meta_data": None,
                "variable_id": "17b016db-c8e0-4c9f-9b08-9582aadb9292",
                "source_cf_meta_data": None,
                "source_id": "1",
                "warning": None,
                "system_name": "co_1_date_2",
                "display_name": "Date-2",
                "data_type_id": 2,
                "source_variable_id": "co_1_date_2",
                "source_type": "object",
                "is_primary": False,
                "field_order": 0,
                "source_name": "custom_object_1",
                "is_adjusted": False,
                "source_name_history": "Date-2 << custom_object_1",
                "is_dependent": False,
            },
        ],
    }


def get_group_by_transformation_data():
    data = {
        "transformations": [
            {
                "key": "f4776a51-b9d5-481f-bc8e-f9fca54edb18",
                "transformation_id": "f4776a51-b9d5-481f-bc8e-f9fca54edb18",
                "type": "GROUP_BY",
                "by": [
                    {
                        "system_name": "co_1_email",
                        "display_name": "Email",
                        "data_type_id": 12,
                        "variable_id": "co_1_email",
                        "source_variable_id": None,
                        "source_id": 1,
                        "source_name": "custom_object_1",
                        "source_type": "object",
                        "is_primary": False,
                        "source_name_history": "Email << custom_object_1",
                    },
                    {
                        "system_name": "co_1_name",
                        "display_name": "Name",
                        "data_type_id": 4,
                        "variable_id": "co_1_name",
                        "source_variable_id": None,
                        "source_id": 1,
                        "source_name": "custom_object_1",
                        "source_type": "object",
                        "is_primary": False,
                        "source_name_history": "Name << custom_object_1",
                    },
                ],
                "aggregations": [
                    {
                        "col_name": "co_1_number",
                        "function": "SUM",
                        "of": "co_1_number",
                        "data_type_id": 1,
                        "variable_id": "co_1_number",
                    }
                ],
                "output_columns": [],
                "is_valid": False,
            }
        ],
    }

    base_data = get_base_data()
    group_by_transformation_added_data = base_data | data

    return group_by_transformation_added_data


def get_renaming_group_by_variable_data(
    previous_datasheet_validation_response,
):
    variables = previous_datasheet_validation_response["variables"]

    for variable in variables:
        if variable["system_name"] == "sum_co_1_number":
            variable["display_name"] = "Renamed sum of Number"
            break

    return previous_datasheet_validation_response


def get_adding_more_aggregations_to_existing_group_by_data(
    previous_datasheet_validation_response,
):

    data = {
        "transformations": [
            {
                "key": "f4776a51-b9d5-481f-bc8e-f9fca54edb18",
                "transformation_id": "f4776a51-b9d5-481f-bc8e-f9fca54edb18",
                "type": "GROUP_BY",
                "by": [
                    {
                        "system_name": "co_1_email",
                        "display_name": "Email",
                        "data_type_id": 12,
                        "variable_id": "co_1_email",
                        "source_variable_id": None,
                        "source_id": 1,
                        "source_name": "custom_object_1",
                        "source_type": "object",
                        "is_primary": False,
                        "source_name_history": "Email << custom_object_1",
                    },
                    {
                        "system_name": "co_1_name",
                        "display_name": "Name",
                        "data_type_id": 4,
                        "variable_id": "co_1_name",
                        "source_variable_id": None,
                        "source_id": 1,
                        "source_name": "custom_object_1",
                        "source_type": "object",
                        "is_primary": False,
                        "source_name_history": "Name << custom_object_1",
                    },
                ],
                "aggregations": [
                    {
                        "col_name": "co_1_number",
                        "function": "SUM",
                        "of": "co_1_number",
                        "data_type_id": 1,
                        "variable_id": "co_1_number",
                    },
                    {
                        "col_name": "co_1_percentage",
                        "function": "COUNT",
                        "of": "co_1_percentage",
                        "data_type_id": 6,
                        "variable_id": "co_1_percentage",
                    },
                ],
                "output_columns": [
                    {
                        "source_id": 1,
                        "is_primary": True,
                        "source_name": "custom_object_1",
                        "source_type": "object",
                        "system_name": "co_1_email",
                        "variable_id": "co_1_email",
                        "data_type_id": 12,
                        "display_name": "Email",
                        "system_generated": False,
                        "source_variable_id": None,
                        "source_name_history": "Email << custom_object_1",
                    },
                    {
                        "source_id": 1,
                        "is_primary": True,
                        "source_name": "custom_object_1",
                        "source_type": "object",
                        "system_name": "co_1_name",
                        "variable_id": "co_1_name",
                        "data_type_id": 4,
                        "display_name": "Name",
                        "system_generated": False,
                        "source_variable_id": None,
                        "source_name_history": "Name << custom_object_1",
                    },
                    {
                        "meta_data": None,
                        "source_id": "382929d2-6294-4803-acc8-256ada17acec",
                        "is_primary": False,
                        "column_name": "sum_co_1_number",
                        "is_selected": True,
                        "source_type": "datasheet",
                        "system_name": "sum_co_1_number",
                        "variable_id": "15f8a13e-4e30-43a4-92bc-c717e0970827",
                        "data_type_id": 1,
                        "display_name": "SUM::Number",
                        "system_generated": True,
                        "source_variable_id": None,
                        "source_cf_meta_data": None,
                        "source_name_history": "SUM::Number << Custom object sheet 1",
                    },
                ],
                "is_valid": False,
            }
        ],
        "variables": [
            {
                "client_id": 3024,
                "datasheet_id": "382929d2-6294-4803-acc8-256ada17acec",
                "databook_id": "9605d772-f376-4cdc-b9a6-40ab8d2e2645",
                "is_selected": True,
                "meta_data": None,
                "variable_id": "93318c47-f5be-46dc-9bbe-309e31cc09c2",
                "source_cf_meta_data": None,
                "source_id": "1",
                "warning": None,
                "system_name": "co_1_email",
                "display_name": "Email",
                "data_type_id": 12,
                "source_variable_id": "co_1_email",
                "source_type": "object",
                "is_primary": True,
                "field_order": 0,
                "source_name": "custom_object_1",
                "is_adjusted": False,
                "source_name_history": "Email << custom_object_1",
                "is_dependent": False,
            },
            {
                "client_id": 3024,
                "datasheet_id": "382929d2-6294-4803-acc8-256ada17acec",
                "databook_id": "9605d772-f376-4cdc-b9a6-40ab8d2e2645",
                "is_selected": True,
                "meta_data": None,
                "variable_id": "bd577451-57e3-4a9c-b90e-4fc9b95dbf17",
                "source_cf_meta_data": None,
                "source_id": "1",
                "warning": None,
                "system_name": "co_1_name",
                "display_name": "Name",
                "data_type_id": 4,
                "source_variable_id": "co_1_name",
                "source_type": "object",
                "is_primary": True,
                "field_order": 0,
                "source_name": "custom_object_1",
                "is_adjusted": False,
                "source_name_history": "Name << custom_object_1",
                "is_dependent": False,
            },
            {
                "client_id": 3024,
                "datasheet_id": "382929d2-6294-4803-acc8-256ada17acec",
                "databook_id": "9605d772-f376-4cdc-b9a6-40ab8d2e2645",
                "is_selected": True,
                "meta_data": None,
                "variable_id": "15f8a13e-4e30-43a4-92bc-c717e0970827",
                "source_cf_meta_data": None,
                "source_id": "382929d2-6294-4803-acc8-256ada17acec",
                "warning": None,
                "system_name": "sum_co_1_number",
                "display_name": "Renamed sum of Number",
                "data_type_id": 1,
                "source_variable_id": None,
                "source_type": "datasheet",
                "is_primary": False,
                "field_order": 0,
                "source_name": "Custom object sheet 1",
                "is_adjusted": False,
                "source_name_history": "Renamed sum of Number << Custom object sheet 1",
                "is_dependent": False,
            },
        ],
        "initial_validation": False,
    }

    previous_datasheet_validation_response.update(data)

    return previous_datasheet_validation_response


def get_adding_join_transformation_data(
    databook_id, previous_datasheet_validation_response
):
    data = {
        "transformations": [
            {
                "key": "d5d5b751-e966-4668-b7d4-f9a08d782c0d",
                "type": "GROUP_BY",
                "transformation_id": "d5d5b751-e966-4668-b7d4-f9a08d782c0d",
                "is_valid": True,
                "output_columns": [
                    {
                        "source_id": 1,
                        "is_primary": True,
                        "source_name": "custom_object_1",
                        "source_type": "object",
                        "system_name": "co_1_email",
                        "variable_id": "co_1_email",
                        "data_type_id": 12,
                        "display_name": "Email",
                        "system_generated": False,
                        "source_variable_id": None,
                        "source_name_history": "Email << custom_object_1",
                    },
                    {
                        "source_id": 1,
                        "is_primary": True,
                        "source_name": "custom_object_1",
                        "source_type": "object",
                        "system_name": "co_1_name",
                        "variable_id": "co_1_name",
                        "data_type_id": 4,
                        "display_name": "Name",
                        "system_generated": False,
                        "source_variable_id": None,
                        "source_name_history": "Name << custom_object_1",
                    },
                    {
                        "meta_data": None,
                        "source_id": "382929d2-6294-4803-acc8-256ada17acec",
                        "is_primary": False,
                        "column_name": "sum_co_1_number",
                        "is_selected": True,
                        "source_type": "datasheet",
                        "system_name": "sum_co_1_number",
                        "variable_id": "9610669f-6582-48cc-9c11-da10a2de5e11",
                        "data_type_id": 1,
                        "display_name": "Renamed sum of Number",
                        "system_generated": True,
                        "source_variable_id": None,
                        "source_cf_meta_data": None,
                        "source_name_history": "Renamed sum of Number << Custom object sheet 1",
                    },
                    {
                        "meta_data": None,
                        "source_id": "382929d2-6294-4803-acc8-256ada17acec",
                        "is_primary": False,
                        "column_name": "count_co_1_percentage",
                        "is_selected": True,
                        "source_type": "datasheet",
                        "system_name": "count_co_1_percentage",
                        "variable_id": "83c0feec-2306-48ba-87ec-471ce367e3d5",
                        "data_type_id": 1,
                        "display_name": "COUNT::Percentage",
                        "system_generated": True,
                        "source_variable_id": None,
                        "source_cf_meta_data": None,
                        "source_name_history": "COUNT::Percentage << Custom object sheet 1",
                    },
                ],
                "transformation_source_map": {"1_object": 0},
                "used_variable_ids": [
                    "co_1_percentage",
                    "co_1_email",
                    "co_1_name",
                    "co_1_number",
                ],
                "source_ids": [1],
                "by": [
                    {
                        "source_id": 1,
                        "is_primary": True,
                        "source_name": "custom_object_1",
                        "source_type": "object",
                        "system_name": "co_1_email",
                        "variable_id": "co_1_email",
                        "data_type_id": 12,
                        "display_name": "Email",
                        "system_generated": False,
                        "source_variable_id": None,
                        "source_name_history": "Email << custom_object_1",
                    },
                    {
                        "source_id": 1,
                        "is_primary": True,
                        "source_name": "custom_object_1",
                        "source_type": "object",
                        "system_name": "co_1_name",
                        "variable_id": "co_1_name",
                        "data_type_id": 4,
                        "display_name": "Name",
                        "system_generated": False,
                        "source_variable_id": None,
                        "source_name_history": "Name << custom_object_1",
                    },
                ],
                "aggregations": [
                    {
                        "of": "co_1_number",
                        "col_name": "sum_co_1_number",
                        "function": "SUM",
                        "variable_id": "co_1_number",
                        "data_type_id": 1,
                    },
                    {
                        "col_name": "co_1_percentage",
                        "function": "COUNT",
                        "of": "co_1_percentage",
                        "data_type_id": 6,
                        "variable_id": "co_1_percentage",
                    },
                ],
            },
            {
                "key": "2f891e63-09c9-41e2-b62b-5ae5e6e89302",
                "transformation_id": "2f891e63-09c9-41e2-b62b-5ae5e6e89302",
                "columns": {
                    "lhs": [
                        {
                            "system_name": "co_1_email",
                            "display_name": "Email",
                            "data_type_id": 12,
                            "source_cf_meta_data": None,
                            "variable_id": "co_1_email",
                            "source_variable_id": None,
                            "is_primary": True,
                            "source_name_history": "Email << custom_object_1",
                            "source_id": 1,
                            "source_type": "object",
                        },
                        {
                            "system_name": "co_1_name",
                            "display_name": "Name",
                            "data_type_id": 4,
                            "source_cf_meta_data": None,
                            "variable_id": "co_1_name",
                            "source_variable_id": None,
                            "is_primary": True,
                            "source_name_history": "Name << custom_object_1",
                            "source_id": 1,
                            "source_type": "object",
                        },
                        {
                            "system_name": "sum_co_1_number",
                            "display_name": "Renamed sum of Number",
                            "data_type_id": 1,
                            "source_cf_meta_data": None,
                            "variable_id": "9610669f-6582-48cc-9c11-da10a2de5e11",
                            "source_variable_id": None,
                            "is_primary": False,
                            "source_name_history": "Renamed sum of Number << Custom object sheet 1",
                            "source_id": "382929d2-6294-4803-acc8-256ada17acec",
                            "source_type": "datasheet",
                        },
                        {
                            "system_name": "count_co_1_percentage",
                            "display_name": "COUNT::Percentage",
                            "data_type_id": 1,
                            "source_cf_meta_data": None,
                            "variable_id": "83c0feec-2306-48ba-87ec-471ce367e3d5",
                            "source_variable_id": None,
                            "is_primary": False,
                            "source_name_history": "COUNT::Percentage << Custom object sheet 1",
                            "source_id": "382929d2-6294-4803-acc8-256ada17acec",
                            "source_type": "datasheet",
                        },
                    ],
                    "rhs": [
                        {
                            "system_name": "effective_start_date",
                            "display_name": "Effective Start Date",
                            "data_type_id": 2,
                            "source_cf_meta_data": None,
                            "variable_id": "3524e64b-9003-4a03-b7c3-178ed39ad678",
                            "source_variable_id": "effective_start_date_user",
                            "is_primary": True,
                            "source_name_history": "Effective Start Date << User",
                            "source_id": "user",
                            "source_type": "report",
                        },
                        {
                            "system_name": "effective_end_date",
                            "display_name": "Effective End Date",
                            "data_type_id": 2,
                            "source_cf_meta_data": None,
                            "variable_id": "02f933cd-ab07-4cb1-ac4b-0dcb005ec181",
                            "source_variable_id": "effective_end_date_user",
                            "is_primary": True,
                            "source_name_history": "Effective End Date << User",
                            "source_id": "user",
                            "source_type": "report",
                        },
                        {
                            "system_name": "employee_email_id",
                            "display_name": "Employee Email Id",
                            "data_type_id": 12,
                            "source_cf_meta_data": None,
                            "variable_id": "effa65a1-1343-49bc-81ab-59b7000fd4c0",
                            "source_variable_id": "employee_email_id_user",
                            "is_primary": True,
                            "source_name_history": "Employee Email Id << User",
                            "source_id": "user",
                            "source_type": "report",
                        },
                    ],
                },
                "type": "JOIN",
                "join_type": "LEFT",
                "with_databook_id": str(databook_id),
                "with": "1090d6c2-0ef8-4990-bf48-359d45cb1dec",
                "on": {
                    "lhs": [
                        {
                            "system_name": "co_1_email",
                            "display_name": "Email",
                            "data_type_id": 12,
                            "source_cf_meta_data": None,
                            "variable_id": "co_1_email",
                            "source_variable_id": None,
                            "is_primary": True,
                            "source_name_history": "Email << custom_object_1",
                            "source_id": 1,
                            "source_type": "object",
                        }
                    ],
                    "rhs": [
                        {
                            "system_name": "employee_email_id",
                            "display_name": "Employee Email Id",
                            "data_type_id": 12,
                            "source_cf_meta_data": None,
                            "variable_id": "effa65a1-1343-49bc-81ab-59b7000fd4c0",
                            "source_variable_id": "employee_email_id_user",
                            "is_primary": True,
                            "source_name_history": "Employee Email Id << User",
                            "source_id": "user",
                            "source_type": "report",
                        }
                    ],
                },
                "output_columns": [],
                "is_valid": False,
            },
        ],
        "variables": [
            {
                "client_id": 3024,
                "datasheet_id": "382929d2-6294-4803-acc8-256ada17acec",
                "databook_id": "9605d772-f376-4cdc-b9a6-40ab8d2e2645",
                "is_selected": True,
                "meta_data": None,
                "variable_id": "93318c47-f5be-46dc-9bbe-309e31cc09c2",
                "source_cf_meta_data": None,
                "source_id": "1",
                "warning": None,
                "system_name": "co_1_email",
                "display_name": "Email",
                "data_type_id": 12,
                "source_variable_id": "co_1_email",
                "source_type": "object",
                "is_primary": True,
                "field_order": 0,
                "source_name": "custom_object_1",
                "is_adjusted": False,
                "source_name_history": "Email << custom_object_1",
                "is_dependent": False,
            },
            {
                "client_id": 3024,
                "datasheet_id": "382929d2-6294-4803-acc8-256ada17acec",
                "databook_id": "9605d772-f376-4cdc-b9a6-40ab8d2e2645",
                "is_selected": True,
                "meta_data": None,
                "variable_id": "bd577451-57e3-4a9c-b90e-4fc9b95dbf17",
                "source_cf_meta_data": None,
                "source_id": "1",
                "warning": None,
                "system_name": "co_1_name",
                "display_name": "Name",
                "data_type_id": 4,
                "source_variable_id": "co_1_name",
                "source_type": "object",
                "is_primary": True,
                "field_order": 0,
                "source_name": "custom_object_1",
                "is_adjusted": False,
                "source_name_history": "Name << custom_object_1",
                "is_dependent": False,
            },
            {
                "client_id": 3024,
                "datasheet_id": "382929d2-6294-4803-acc8-256ada17acec",
                "databook_id": "9605d772-f376-4cdc-b9a6-40ab8d2e2645",
                "is_selected": True,
                "meta_data": None,
                "variable_id": "9610669f-6582-48cc-9c11-da10a2de5e11",
                "source_cf_meta_data": None,
                "source_id": "382929d2-6294-4803-acc8-256ada17acec",
                "warning": None,
                "system_name": "sum_co_1_number",
                "display_name": "Renamed sum of Number",
                "data_type_id": 1,
                "source_variable_id": None,
                "source_type": "datasheet",
                "is_primary": False,
                "field_order": 0,
                "source_name": "Custom object sheet 1",
                "is_adjusted": False,
                "source_name_history": "Renamed sum of Number << Custom object sheet 1",
                "is_dependent": False,
            },
            {
                "client_id": 3024,
                "datasheet_id": "382929d2-6294-4803-acc8-256ada17acec",
                "databook_id": "9605d772-f376-4cdc-b9a6-40ab8d2e2645",
                "is_selected": True,
                "meta_data": None,
                "variable_id": "83c0feec-2306-48ba-87ec-471ce367e3d5",
                "source_cf_meta_data": None,
                "source_id": "382929d2-6294-4803-acc8-256ada17acec",
                "warning": None,
                "system_name": "count_co_1_percentage",
                "display_name": "COUNT::Percentage",
                "data_type_id": 1,
                "source_variable_id": None,
                "source_type": "datasheet",
                "is_primary": False,
                "field_order": 0,
                "source_name": "Custom object sheet 1",
                "is_adjusted": False,
                "source_name_history": "COUNT::Percentage << Custom object sheet 1",
                "is_dependent": False,
            },
        ],
        "initial_validation": False,
    }

    previous_datasheet_validation_response.update(data)

    return previous_datasheet_validation_response


def get_flatten_transformation_data():
    flattened_data = {
        "transformations": [
            {
                "key": "61886e9b-9952-4f4c-bc28-ee5ac94e1c86",
                "transformation_id": "61886e9b-9952-4f4c-bc28-ee5ac94e1c86",
                "type": "FLATTEN",
                "col_name": "cf_hierarchy_field",
                "output_data_type": "Email",
                "variable_id": "95af3a89-a40e-44fc-bf4b-9d4692644251",
                "output_columns": [],
                "is_valid": False,
            }
        ],
        "variables": [
            {
                "client_id": 3024,
                "datasheet_id": "7c5b57c2-109e-4e73-bf94-b2ab2c4f1aa6",
                "databook_id": "9605d772-f376-4cdc-b9a6-40ab8d2e2645",
                "is_selected": True,
                "meta_data": None,
                "variable_id": "a8a377c7-8c11-47ac-9204-9031aaf0c3ae",
                "source_cf_meta_data": None,
                "source_id": "9cb6cc1f-52a0-44ac-97ee-35f7a7be47c3",
                "warning": None,
                "system_name": "co_1_name",
                "display_name": "Name",
                "data_type_id": 4,
                "source_variable_id": "5c254d22-8ec4-435f-84c1-1cc49c597408",
                "source_type": "datasheet",
                "is_primary": False,
                "field_order": 0,
                "source_name": "Main",
                "is_adjusted": False,
                "source_name_history": "Name << Main << custom_object_1",
                "is_dependent": False,
            },
            {
                "client_id": 3024,
                "datasheet_id": "7c5b57c2-109e-4e73-bf94-b2ab2c4f1aa6",
                "databook_id": "9605d772-f376-4cdc-b9a6-40ab8d2e2645",
                "is_selected": True,
                "meta_data": None,
                "variable_id": "317866db-35a0-4c37-9b07-4d282d4f4dce",
                "source_cf_meta_data": None,
                "source_id": "9cb6cc1f-52a0-44ac-97ee-35f7a7be47c3",
                "warning": None,
                "system_name": "co_1_number",
                "display_name": "Number",
                "data_type_id": 1,
                "source_variable_id": "fef92446-3b70-4d01-8376-707eeda032f8",
                "source_type": "datasheet",
                "is_primary": True,
                "field_order": 0,
                "source_name": "Main",
                "is_adjusted": False,
                "source_name_history": "Number << Main << custom_object_1",
                "is_dependent": False,
            },
            {
                "client_id": 3024,
                "datasheet_id": "7c5b57c2-109e-4e73-bf94-b2ab2c4f1aa6",
                "databook_id": "9605d772-f376-4cdc-b9a6-40ab8d2e2645",
                "is_selected": True,
                "meta_data": None,
                "variable_id": "25ec797c-1979-4381-8688-947b320535e3",
                "source_cf_meta_data": None,
                "source_id": "9cb6cc1f-52a0-44ac-97ee-35f7a7be47c3",
                "warning": None,
                "system_name": "co_1_boolean",
                "display_name": "Boolean",
                "data_type_id": 3,
                "source_variable_id": "79cdd35b-93e0-4bb3-8dc4-e206eb876b23",
                "source_type": "datasheet",
                "is_primary": False,
                "field_order": 0,
                "source_name": "Main",
                "is_adjusted": False,
                "source_name_history": "Boolean << Main << custom_object_1",
                "is_dependent": False,
            },
            {
                "client_id": 3024,
                "datasheet_id": "7c5b57c2-109e-4e73-bf94-b2ab2c4f1aa6",
                "databook_id": "9605d772-f376-4cdc-b9a6-40ab8d2e2645",
                "is_selected": True,
                "meta_data": None,
                "variable_id": "feeb75bc-df40-4f56-9c79-9a4823d4fd62",
                "source_cf_meta_data": None,
                "source_id": "9cb6cc1f-52a0-44ac-97ee-35f7a7be47c3",
                "warning": None,
                "system_name": "co_1_date_1",
                "display_name": "Date-1",
                "data_type_id": 2,
                "source_variable_id": "e3db048b-98b4-4a81-998f-02cefa735fec",
                "source_type": "datasheet",
                "is_primary": False,
                "field_order": 0,
                "source_name": "Main",
                "is_adjusted": False,
                "source_name_history": "Date-1 << Main << custom_object_1",
                "is_dependent": False,
            },
            {
                "client_id": 3024,
                "datasheet_id": "7c5b57c2-109e-4e73-bf94-b2ab2c4f1aa6",
                "databook_id": "9605d772-f376-4cdc-b9a6-40ab8d2e2645",
                "is_selected": True,
                "meta_data": None,
                "variable_id": "1ca01dcc-51a0-4fe3-b724-e9dbc640d546",
                "source_cf_meta_data": None,
                "source_id": "9cb6cc1f-52a0-44ac-97ee-35f7a7be47c3",
                "warning": None,
                "system_name": "co_1_email",
                "display_name": "Email",
                "data_type_id": 12,
                "source_variable_id": "8c4c8500-267f-4cbe-8cb7-b008fd9a1807",
                "source_type": "datasheet",
                "is_primary": False,
                "field_order": 0,
                "source_name": "Main",
                "is_adjusted": False,
                "source_name_history": "Email << Main << custom_object_1",
                "is_dependent": False,
            },
            {
                "client_id": 3024,
                "datasheet_id": "7c5b57c2-109e-4e73-bf94-b2ab2c4f1aa6",
                "databook_id": "9605d772-f376-4cdc-b9a6-40ab8d2e2645",
                "is_selected": True,
                "meta_data": None,
                "variable_id": "63d8f320-07f4-4eff-a522-ecc82796ddd7",
                "source_cf_meta_data": None,
                "source_id": "9cb6cc1f-52a0-44ac-97ee-35f7a7be47c3",
                "warning": None,
                "system_name": "co_1_percentage",
                "display_name": "Percentage",
                "data_type_id": 6,
                "source_variable_id": "5d6e89f1-6a62-4a62-87c5-5828c061023f",
                "source_type": "datasheet",
                "is_primary": False,
                "field_order": 0,
                "source_name": "Main",
                "is_adjusted": False,
                "source_name_history": "Percentage << Main << custom_object_1",
                "is_dependent": False,
            },
            {
                "client_id": 3024,
                "datasheet_id": "7c5b57c2-109e-4e73-bf94-b2ab2c4f1aa6",
                "databook_id": "9605d772-f376-4cdc-b9a6-40ab8d2e2645",
                "is_selected": True,
                "meta_data": None,
                "variable_id": "4719b920-9615-4ca6-af83-e6877a4fb5f3",
                "source_cf_meta_data": None,
                "source_id": "9cb6cc1f-52a0-44ac-97ee-35f7a7be47c3",
                "warning": None,
                "system_name": "co_1_date_2",
                "display_name": "Date-2",
                "data_type_id": 2,
                "source_variable_id": "d3427b01-098f-4e2c-a5f4-2c321164912f",
                "source_type": "datasheet",
                "is_primary": False,
                "field_order": 0,
                "source_name": "Main",
                "is_adjusted": False,
                "source_name_history": "Date-2 << Main << custom_object_1",
                "is_dependent": False,
            },
            {
                "client_id": 3024,
                "datasheet_id": "7c5b57c2-109e-4e73-bf94-b2ab2c4f1aa6",
                "databook_id": "9605d772-f376-4cdc-b9a6-40ab8d2e2645",
                "is_selected": True,
                "meta_data": None,
                "variable_id": "1df46bfd-126b-41c0-aa85-0cafb8f0b8e9",
                "source_cf_meta_data": {
                    "datasheet_id": "9cb6cc1f-52a0-44ac-97ee-35f7a7be47c3",
                    "hierarchy_for_data_type_id": 12,
                },
                "source_id": "9cb6cc1f-52a0-44ac-97ee-35f7a7be47c3",
                "warning": None,
                "system_name": "cf_hierarchy_field",
                "display_name": "Hierarchy Field",
                "data_type_id": 15,
                "source_variable_id": "95af3a89-a40e-44fc-bf4b-9d4692644251",
                "source_type": "datasheet",
                "is_primary": False,
                "field_order": 0,
                "source_name": "Main",
                "is_adjusted": False,
                "source_name_history": "Hierarchy Field << Main",
                "is_dependent": False,
            },
        ],
        "source_variables": [
            {
                "client_id": 3024,
                "datasheet_id": "9cb6cc1f-52a0-44ac-97ee-35f7a7be47c3",
                "databook_id": "9605d772-f376-4cdc-b9a6-40ab8d2e2645",
                "is_selected": True,
                "meta_data": None,
                "variable_id": "5c254d22-8ec4-435f-84c1-1cc49c597408",
                "source_cf_meta_data": None,
                "source_id": "1",
                "warning": None,
                "system_name": "co_1_name",
                "display_name": "Name",
                "data_type_id": 4,
                "source_variable_id": "co_1_name",
                "source_type": "object",
                "is_primary": False,
                "field_order": 0,
                "source_name": "custom_object_1",
                "is_adjusted": False,
                "source_name_history": "Name << custom_object_1",
                "is_dependent": True,
            },
            {
                "client_id": 3024,
                "datasheet_id": "9cb6cc1f-52a0-44ac-97ee-35f7a7be47c3",
                "databook_id": "9605d772-f376-4cdc-b9a6-40ab8d2e2645",
                "is_selected": True,
                "meta_data": None,
                "variable_id": "fef92446-3b70-4d01-8376-707eeda032f8",
                "source_cf_meta_data": None,
                "source_id": "1",
                "warning": None,
                "system_name": "co_1_number",
                "display_name": "Number",
                "data_type_id": 1,
                "source_variable_id": "co_1_number",
                "source_type": "object",
                "is_primary": True,
                "field_order": 0,
                "source_name": "custom_object_1",
                "is_adjusted": False,
                "source_name_history": "Number << custom_object_1",
                "is_dependent": True,
            },
            {
                "client_id": 3024,
                "datasheet_id": "9cb6cc1f-52a0-44ac-97ee-35f7a7be47c3",
                "databook_id": "9605d772-f376-4cdc-b9a6-40ab8d2e2645",
                "is_selected": True,
                "meta_data": None,
                "variable_id": "79cdd35b-93e0-4bb3-8dc4-e206eb876b23",
                "source_cf_meta_data": None,
                "source_id": "1",
                "warning": None,
                "system_name": "co_1_boolean",
                "display_name": "Boolean",
                "data_type_id": 3,
                "source_variable_id": "co_1_boolean",
                "source_type": "object",
                "is_primary": False,
                "field_order": 0,
                "source_name": "custom_object_1",
                "is_adjusted": False,
                "source_name_history": "Boolean << custom_object_1",
                "is_dependent": True,
            },
            {
                "client_id": 3024,
                "datasheet_id": "9cb6cc1f-52a0-44ac-97ee-35f7a7be47c3",
                "databook_id": "9605d772-f376-4cdc-b9a6-40ab8d2e2645",
                "is_selected": True,
                "meta_data": None,
                "variable_id": "e3db048b-98b4-4a81-998f-02cefa735fec",
                "source_cf_meta_data": None,
                "source_id": "1",
                "warning": None,
                "system_name": "co_1_date_1",
                "display_name": "Date-1",
                "data_type_id": 2,
                "source_variable_id": "co_1_date_1",
                "source_type": "object",
                "is_primary": False,
                "field_order": 0,
                "source_name": "custom_object_1",
                "is_adjusted": False,
                "source_name_history": "Date-1 << custom_object_1",
                "is_dependent": True,
            },
            {
                "client_id": 3024,
                "datasheet_id": "9cb6cc1f-52a0-44ac-97ee-35f7a7be47c3",
                "databook_id": "9605d772-f376-4cdc-b9a6-40ab8d2e2645",
                "is_selected": True,
                "meta_data": None,
                "variable_id": "8c4c8500-267f-4cbe-8cb7-b008fd9a1807",
                "source_cf_meta_data": None,
                "source_id": "1",
                "warning": None,
                "system_name": "co_1_email",
                "display_name": "Email",
                "data_type_id": 12,
                "source_variable_id": "co_1_email",
                "source_type": "object",
                "is_primary": False,
                "field_order": 0,
                "source_name": "custom_object_1",
                "is_adjusted": False,
                "source_name_history": "Email << custom_object_1",
                "is_dependent": True,
            },
            {
                "client_id": 3024,
                "datasheet_id": "9cb6cc1f-52a0-44ac-97ee-35f7a7be47c3",
                "databook_id": "9605d772-f376-4cdc-b9a6-40ab8d2e2645",
                "is_selected": True,
                "meta_data": None,
                "variable_id": "5d6e89f1-6a62-4a62-87c5-5828c061023f",
                "source_cf_meta_data": None,
                "source_id": "1",
                "warning": None,
                "system_name": "co_1_percentage",
                "display_name": "Percentage",
                "data_type_id": 6,
                "source_variable_id": "co_1_percentage",
                "source_type": "object",
                "is_primary": False,
                "field_order": 0,
                "source_name": "custom_object_1",
                "is_adjusted": False,
                "source_name_history": "Percentage << custom_object_1",
                "is_dependent": True,
            },
            {
                "client_id": 3024,
                "datasheet_id": "9cb6cc1f-52a0-44ac-97ee-35f7a7be47c3",
                "databook_id": "9605d772-f376-4cdc-b9a6-40ab8d2e2645",
                "is_selected": True,
                "meta_data": None,
                "variable_id": "d3427b01-098f-4e2c-a5f4-2c321164912f",
                "source_cf_meta_data": None,
                "source_id": "1",
                "warning": None,
                "system_name": "co_1_date_2",
                "display_name": "Date-2",
                "data_type_id": 2,
                "source_variable_id": "co_1_date_2",
                "source_type": "object",
                "is_primary": False,
                "field_order": 0,
                "source_name": "custom_object_1",
                "is_adjusted": False,
                "source_name_history": "Date-2 << custom_object_1",
                "is_dependent": True,
            },
            {
                "client_id": 3024,
                "datasheet_id": "9cb6cc1f-52a0-44ac-97ee-35f7a7be47c3",
                "databook_id": "9605d772-f376-4cdc-b9a6-40ab8d2e2645",
                "is_selected": True,
                "meta_data": {
                    "token": {
                        "name": "GenerateHierarchy(Email, CURRENT_DATE, adjustments, user, Employee Email Id, Reporting Manager EmailId, Effective Start Date, Effective End Date)",
                        "key": "GenerateHierarchy(Email, CURRENT_DATE, adjustments, user, Employee Email Id, Reporting Manager EmailId, Effective Start Date, Effective End Date)",
                        "function_name": "Hierarchy",
                        "data_type": "Hierarchy",
                        "type": "VARIABLE",
                        "token_category": "DYNAMIC",
                        "args": [
                            {
                                "token_type": "DATASHEET_VARIABLES",
                                "token": {
                                    "key": "co_1_email",
                                    "name": "Email",
                                    "variable_id": "8c4c8500-267f-4cbe-8cb7-b008fd9a1807",
                                },
                            },
                            "CURRENT_DATE",
                            {
                                "name": "adjustments",
                                "value": "9605d772-f376-4cdc-b9a6-40ab8d2e2645",
                            },
                            {
                                "name": "user",
                                "value": "f6b34a0a-f83d-43b5-b1cb-5ffbeae1417a",
                            },
                            {
                                "name": "Employee Email Id",
                                "value": "employee_email_id",
                                "variable_id": "cdab3669-3df3-4cc3-a939-9b36dc30c6df",
                            },
                            {
                                "name": "Reporting Manager EmailId",
                                "value": "reporting_manager_email_id",
                                "variable_id": "536f0903-c143-4810-8e4a-1c49efcf5c05",
                            },
                            {
                                "name": "Effective Start Date",
                                "value": "effective_start_date",
                                "variable_id": "668ac693-5de1-4ad2-bbec-9a7d42040a03",
                            },
                            {
                                "name": "Effective End Date",
                                "value": "effective_end_date",
                                "variable_id": "a964c4d1-c28b-42ce-bcff-e30917e1a65e",
                            },
                            {"name": "hierarchy_for_data_type_id", "value": 12},
                            {
                                "name": "reference_sheet_data_origin",
                                "value": "system_object",
                            },
                        ],
                        "is_system_generated": True,
                    },
                    "token_type": "FUNCTIONS",
                    "used_system_names": [],
                },
                "variable_id": "95af3a89-a40e-44fc-bf4b-9d4692644251",
                "source_cf_meta_data": {
                    "datasheet_id": "9cb6cc1f-52a0-44ac-97ee-35f7a7be47c3",
                    "hierarchy_for_data_type_id": 12,
                },
                "source_id": "9cb6cc1f-52a0-44ac-97ee-35f7a7be47c3",
                "warning": None,
                "system_name": "cf_hierarchy_field",
                "display_name": "Hierarchy Field",
                "data_type_id": 15,
                "source_variable_id": None,
                "source_type": "datasheet",
                "is_primary": False,
                "field_order": 1,
                "source_name": "Main",
                "is_adjusted": False,
                "source_name_history": "Hierarchy Field << Main",
                "is_dependent": True,
            },
        ],
        "initial_validation": True,
        "source_type": "datasheet",
        "source_id": "9cb6cc1f-52a0-44ac-97ee-35f7a7be47c3",
    }

    return flattened_data


def get_renamed_flatten_variable_data(previous_datasheet_validation_response):
    variables = previous_datasheet_validation_response["variables"]

    for variable in variables:
        if variable["system_name"] == "cf_hierarchy_field_email_47c3":
            variable["display_name"] = "HF Email flattened"

    return previous_datasheet_validation_response
