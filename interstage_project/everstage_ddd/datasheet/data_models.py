from datetime import datetime
from typing import Any, Dict, List, Optional, Union

from ninja import ModelSchema
from pydantic import UUID4, BaseModel, Field, RootModel, computed_field, field_validator
from rest_framework import serializers, status

from commission_engine.services.expression_designer import ui_filter_infix_converter
from everstage_ddd.datasheet.exceptions.exceptions import DatasheetException

from .enums import (
    AdjustmentTypes,
    DataOrigin,
    DatasheetGroupedBy,
    DatasheetSourceType,
    SortType,
    TransformationType,
    V1TokenTypes,
    V2TokenTypes,
)
from .models import (
    Databook,
    Datasheet,
    DatasheetAdjustments,
    DatasheetFilter,
    DatasheetPermissions,
    DatasheetTag,
    DatasheetTagMap,
    DatasheetVariable,
    DatasheetView,
    DSFilterOperators,
    DSPermissionsTarget,
    DSSnapshotInfoLocal,
)

MAX_CHAR_LENGTH = 254


class UpdateDatasheetRequest(BaseModel):
    """
    Request to update the datasheet
    """

    name: str | None = Field(
        None,
        max_length=MAX_CHAR_LENGTH,
        description=f"Name of the datasheet, maximum {MAX_CHAR_LENGTH} characters",
    )
    datasheet_id: str | UUID4
    ordered_columns: List[str] | None = None
    hidden_columns: List[str] | None = None

    @field_validator("name", mode="before")
    @classmethod
    def validate_name_length(cls, v: str | None) -> str | None:
        """
        Validates the name of the datasheet
        """
        if v is not None and len(v) > MAX_CHAR_LENGTH:
            raise DatasheetException(
                code="DATASHEET_NAME_TOO_LONG",
                message=f"Datasheet name cannot exceed {MAX_CHAR_LENGTH} characters.",
                status=status.HTTP_400_BAD_REQUEST,
            )
        return v


class UpdateDatasheetResponse(UpdateDatasheetRequest):
    status: str


class CreateDatasheetRequest(BaseModel):
    sourceDatabookId: Optional[  # noqa: N815 # camelcase since frontend expects it
        str
    ] = None
    databookId: Optional[str] = (  # noqa: N815 # camelcase since frontend expects it
        None  # noqa: N815 # camelcase since frontend expects it
    )
    name: str = Field(
        None,
        max_length=MAX_CHAR_LENGTH,
        description=f"Name of the datasheet, maximum {MAX_CHAR_LENGTH} characters",
    )
    sourceType: str  # noqa: N815 # camelcase since frontend expects it
    sourceId: str  # noqa: N815 # camelcase since frontend expects it
    dataOrigin: str  # noqa: N815 # camelcase since frontend expects it
    sourceName: str  # noqa: N815 # camelcase since frontend expects it

    @field_validator("name", mode="before")
    @classmethod
    def validate_name_length(cls, v: str | None) -> str | None:
        """
        Validates the name of the datasheet
        """
        if v is not None and len(v) > MAX_CHAR_LENGTH:
            raise DatasheetException(
                code="DATASHEET_NAME_TOO_LONG",
                message=f"Datasheet name cannot exceed {MAX_CHAR_LENGTH} characters.",
                status=status.HTTP_400_BAD_REQUEST,
            )
        return v


def get_databook_id(databook_id: UUID4 | None, info: Any) -> UUID4:
    if not databook_id:
        from .selectors import (
            DatasheetSelector,  # pylint disable=import-outside-toplevel
        )

        databook_id = (
            DatasheetSelector(
                client_id=info.data["client_id"],
                datasheet_id=info.data["datasheet_id"],
            )
            .get_datasheet()
            .databook_id
        )
    return databook_id


def get_databook_id_from_datasheet_id(client_id: int, datasheet_id: UUID4) -> UUID4:
    from .selectors import DatasheetSelector  # pylint disable=import-outside-toplevel

    datasheet_accessor = DatasheetSelector(
        client_id=client_id, datasheet_id=datasheet_id
    )
    databook_id = datasheet_accessor.get_datasheet().databook_id
    return databook_id


class CreateDatabookRequest(BaseModel):
    name: str


class UpdateDatabookRequest(BaseModel):
    """
    Request to update the databook
    """

    name: str | None = Field(
        None,
        max_length=MAX_CHAR_LENGTH,
        description=f"Name of the databook, maximum {MAX_CHAR_LENGTH} characters",
    )
    datasheet_order: List[UUID4] | None = None

    @field_validator("name", mode="before")
    @classmethod
    def validate_name_length(cls, v: str | None) -> str | None:
        """
        Validates the name of the databook
        """
        if v is not None and len(v) > MAX_CHAR_LENGTH:
            raise DatasheetException(
                code="DATABOOK_NAME_TOO_LONG",
                message=f"Databook name cannot exceed {MAX_CHAR_LENGTH} characters.",
                status=status.HTTP_400_BAD_REQUEST,
            )
        return v


class UpdateDatabookResponse(BaseModel):
    status: str
    name: str
    datasheet_order: List[UUID4]


class DatabookCloneResponse(BaseModel):
    status: str
    message: str


class DatabookSyncStatusResponse(BaseModel):
    client_id: int
    e2e_sync_run_id: str | None = None
    sync_status: str | None = None
    sync_end_time: datetime | str | None = None
    sync_start_time: datetime | None = None
    error_details: dict | None = None
    error_info: dict | None = None
    databook_id: UUID4


class CreateDatabookResponse(BaseModel):
    status: str
    name: str
    databookId: UUID4  # noqa: N815 # camelcase since frontend expects it


class CreateDatasheetResponse(BaseModel):
    status: str
    name: str
    datasheetId: UUID4  # noqa: N815 # camelcase since frontend expects it
    databookId: Optional[UUID4] = (  # noqa: N815 # camelcase since frontend expects it
        None
    )
    databookName: Optional[str] = (  # noqa: N815 # camelcase since frontend expects it
        None
    )


class DeleteDatasheetResponse(BaseModel):
    status: str
    datasheet_name: str
    datasheet_id: UUID4
    databook_id: Optional[UUID4] = None


class DeleteDatabookResponse(BaseModel):
    status: str
    databook_name: str
    databook_id: UUID4


class DatasheetInfo(ModelSchema):
    last_updated_at: datetime = Field(..., alias="knowledge_begin_date")
    last_generated_at: datetime | None = None
    does_user_has_co_permission: bool = True

    class Meta:
        model = Datasheet
        fields = ["datasheet_id", "name", "databook_id"]


class PinnedDatasheets(BaseModel):
    pinned_datasheets: List[DatasheetInfo]


class GroupedDatasheets(BaseModel):
    id: UUID4
    name: str
    datasheets: List[DatasheetInfo]


class DatasheetGroup(RootModel):
    class Config:
        use_enum_values = True

    root: Dict[DatasheetGroupedBy, List[GroupedDatasheets]]


class RankMetaData(BaseModel):
    """
    Rank_meta_data structure stored in datasheet_variable table
    """

    rank_strategy: str | None
    value_column: str
    partition: list
    ascending: bool


class SortMetaData(BaseModel):
    """
    Sort_meta_data structure used in RollingMetaData
    """

    column_name: str
    order: str


class RollingMetaData(BaseModel):
    """
    Rolling_meta_data structure stored in datasheet_variable table
    """

    rolling_strategy: str
    value_column: str
    partition: list
    sort_data: list[SortMetaData]


class HierarchyMetaData(BaseModel):
    """
    Hierarchy_meta_data structure stored in datasheet_variable table
    """

    hierarchy_for_column: str
    as_of_date_column: str
    reference_sheet: str
    child_column: str
    parent_column: str
    start_time_column: str | None
    end_time_column: str | None
    reference_book: str
    reference_sheet_data_origin: str
    hierarchy_for_data_type_id: int


class WindowFunctionBaseModel(BaseModel):
    """
    Common fields for WindowFunctionBaseModel
    """

    name: str
    type: str
    data_type: str
    across_rows: bool
    function_name: str
    token_category: str
    expression_string: str
    evaluation_context: Optional[str] = None
    is_system_generated: bool = False


class HierarchyModel(WindowFunctionBaseModel):
    args: Dict[str, HierarchyMetaData]
    hierarchy: HierarchyMetaData


class RankModel(WindowFunctionBaseModel):
    args: Dict[str, RankMetaData]
    rank: RankMetaData


class RollingModel(WindowFunctionBaseModel):
    args: Dict[str, RollingMetaData]
    rolling: RollingMetaData


class WindowFunctionModel(RootModel):
    root: Union[HierarchyModel, RankModel, RollingModel]


class InfixV1BaseModel(BaseModel):
    name: Any  # for constant it can be int, float, str
    type: V1TokenTypes

    class Config:
        use_enum_values = True


class BracketModel(InfixV1BaseModel):
    alt_name: str
    category: str


class OperatorModel(BracketModel):
    operand_type_ids: List[int]
    output_type_ids: List[int]
    needs_operand: bool
    multi_valued: bool
    output_types: List[str]
    operand_types: List[str]


class VariableMetaModel(BaseModel):
    category: Optional[str] = None
    model_name: str
    system_name: str
    data_type_id: int


class VariableModel(InfixV1BaseModel):
    meta: VariableMetaModel
    tags: Optional[List] = None
    data_type: str


class VariableModelWithDatasheet(VariableModel):
    datasheet_id: UUID4 | str
    databook_id: UUID4 | str


class VariableFunctionModel(InfixV1BaseModel):
    args: list
    data_type: str
    function_name: str
    token_category: str


class VariableConditionalModel(VariableFunctionModel):
    level: int
    else_is_nested: bool
    then_is_nested: bool
    else_do_nothing: bool


class InfixV1Model(RootModel):
    root: Union[
        VariableConditionalModel,
        VariableFunctionModel,
        VariableModelWithDatasheet,
        VariableModel,
        OperatorModel,
        BracketModel,
    ]


class InfixV2Model(BaseModel):
    token: dict
    token_type: V2TokenTypes

    class Config:
        use_enum_values = True


class InfixV1ListModel(RootModel):
    root: List[InfixV1Model]


class CalcFieldBaseModel(BaseModel):
    ast: dict = {}
    criteria_type: str
    evaluation_context: Optional[str] = None
    used_system_names: List[str] = []
    case_insensitive: bool = False


class CalcFieldV1Model(CalcFieldBaseModel):
    infix: Union[InfixV1Model, InfixV1ListModel]


class CalcFieldV2Model(CalcFieldBaseModel):
    infix: Union[InfixV2Model, List[InfixV2Model]]


class WindowFunctionV2Model(InfixV2Model):
    used_system_names: List[str] = []


class SourceCfMetaDataModel(BaseModel):
    datasheet_id: UUID4
    hierarchy_for_data_type_id: int


class MetaDataModel(RootModel):
    root: Union[
        CalcFieldV1Model, CalcFieldV2Model, WindowFunctionModel, WindowFunctionV2Model
    ]


class BaseDatasheetVariableModel(ModelSchema):
    meta_data: Optional[MetaDataModel] = None
    source_cf_meta_data: Optional[SourceCfMetaDataModel] = None
    ## Source id will be int in case of custom object and str in other cases
    source_id: int | str
    ## Variable id will be str in case of custom object/report object and UUID4 in datasheet
    variable_id: str | UUID4

    class Meta:
        model = DatasheetVariable
        fields = [
            "system_name",
            "display_name",
            "data_type",
            "meta_data",
            "source_cf_meta_data",
            "variable_id",
            "source_variable_id",
            "source_id",
            "source_type",
            "is_selected",
            "is_primary",
            "field_order",
            "warning",
        ]


class DatasheetVariablesList(RootModel):
    root: List[BaseDatasheetVariableModel]


class DatasheetVariableResponseBase(ModelSchema):
    client: int = Field(..., alias="client_id")
    datasheet_id: UUID4
    databook_id: UUID4
    is_selected: bool = True
    meta_data: Optional[MetaDataModel] = None
    variable_id: str | UUID4
    source_cf_meta_data: Optional[SourceCfMetaDataModel] = None
    source_id: int | str
    warning: Optional[str] = None

    class Meta:
        model = DatasheetVariable
        fields = [
            "system_name",
            "display_name",
            "data_type",
            "meta_data",
            "source_cf_meta_data",
            "variable_id",
            "source_variable_id",
            "source_id",
            "source_type",
            "is_selected",
            "is_primary",
            "field_order",
        ]

    @computed_field
    def source_name(self) -> str:
        return _get_source_name(
            client_id=self.client,
            source_id=self.source_id,
            source_type=self.source_type,
        )

    @computed_field
    def is_adjusted(self) -> bool:
        from .selectors.datasheet_adjustment_selector import DatasheetAdjustmentSelector

        return (
            DatasheetAdjustmentSelector(client_id=self.client)
            .client_kd_deleted_aware()
            .filter(datasheet_id=self.datasheet_id, data__has_key=self.system_name)
            .exists()
        )

    @field_validator("meta_data", mode="before")
    @classmethod
    def convert_meta_data(cls, meta_data, info) -> Optional[dict]:
        from commission_engine.services.expression_designer import (
            convert_calculated_field_meta_data,
        )

        if meta_data is not None and info.data.get("is_selected"):
            return convert_calculated_field_meta_data(
                client_id=info.data["client"],
                datasheet_var_obj=info.data | {"meta_data": meta_data},
                to_v2=True,
                requires_intermediate_variables=True,
            )

        return meta_data


class DatasheetVariableResponse(DatasheetVariableResponseBase):
    @computed_field
    def source_name_history(self) -> str:
        return _get_source_name_history(
            client_id=self.client,
            source_id=self.source_id,
            source_variable_id=self.source_variable_id,
            source_type=self.source_type,
            display_name=self.display_name,
        )

    @computed_field
    def is_dependent(self) -> bool:
        from .selectors import DatasheetVariableSelector

        return (
            DatasheetVariableSelector(client_id=self.client)
            .client_kd_deleted_aware()
            .filter(source_variable_id=self.variable_id, is_selected=True)
            .exists()
        )


class DatasheetVariablesListResponse(RootModel):
    root: List[DatasheetVariableResponse]


class DatasheetVariablesListResponseForCanvas(RootModel):
    root: List[DatasheetVariableResponseBase]


class DatasheetSourceVariable(BaseDatasheetVariableModel):
    source_variable_id: Optional[str] = None
    source_name_history: Optional[str] = None

    @field_validator("meta_data", mode="before")
    @classmethod
    def reset_meta_data(cls, _meta_data) -> None:
        return None


class DatasheetSourceVariablesList(RootModel):
    root: List[DatasheetSourceVariable]


class DatasheetSourceVariableResponse(DatasheetSourceVariable):
    client: int = Field(..., alias="client_id")

    @computed_field
    def source_name(self) -> str:
        return _get_source_name(
            client_id=self.client,
            source_id=self.source_id,
            source_type=self.source_type,
        )


class DatasheetSourceVariablesListResponse(RootModel):
    root: List[DatasheetSourceVariableResponse]


class DatasheetAdjustmentsModel(ModelSchema):
    class Meta:
        model = DatasheetAdjustments
        fields = "__all__"
        exclude = ["temporal_id", "client"]


class DatasheetModel(ModelSchema):
    source_type: str
    source_id: int | str

    class Meta:
        model = Datasheet
        fields = "__all__"
        exclude = ["temporal_id", "source_id", "source_type"]

    @field_validator("source_id", mode="before")
    @classmethod
    def convert_source_id_data_type(cls, source_id, info) -> int | str | UUID4:
        if info.data["source_type"] == DatasheetSourceType.CUSTOM_OBJECT.value:
            return int(source_id)
        return source_id

    @computed_field
    def source_name(self) -> str:
        return _get_source_name(
            client_id=self.client,
            source_id=self.source_id,
            source_type=self.source_type,
        )


class ViewMetaDetails(ModelSchema):
    filter_data: List[InfixV1Model] | List[InfixV2Model] = []
    pivot_data: dict = {}
    total_records: int
    hidden_columns: List[str] = []
    ordered_columns: List[str] = []

    class Meta:
        model = DatasheetView
        fields = [
            "view_id",
            "name",
        ]


class DatabookModel(ModelSchema):
    datasheets: list[DatasheetModel] = []

    class Meta:
        model = Databook
        fields = "__all__"
        exclude = ["temporal_id"]


class DataheetFilterModel(ModelSchema):
    class Meta:
        model = DatasheetFilter
        fields = "__all__"
        exclude = ["temporal_id"]


class DatashetPermissionsModel(ModelSchema):
    class Meta:
        model = DatasheetPermissions
        fields = "__all__"
        exclude = ["temporal_id"]


class DSFilterOperatorsModel(ModelSchema):
    class Meta:
        model = DSFilterOperators
        fields = "__all__"
        exclude = ["temporal_id"]


class DSPermissionsTargetModel(ModelSchema):
    class Meta:
        model = DSPermissionsTarget
        fields = "__all__"
        exclude = ["temporal_id"]


class DSSnapshotInfoLocalModel(ModelSchema):
    class Meta:
        model = DSSnapshotInfoLocal
        fields = "__all__"


class DatasheetTagModel(ModelSchema):
    class Meta:
        model = DatasheetTag
        fields = "__all__"
        fields_optional = ["created_at", "updated_at"]
        exclude = ["temporal_id", "version"]


class DatasheetTagMapModel(ModelSchema):
    class Meta:
        model = DatasheetTagMap
        fields = "__all__"
        exclude = ["temporal_id", "version"]


class PivotRequestModel(BaseModel):
    aggfunc: dict
    fill_value: str = ""
    index: List[str]
    columns: List[str]
    values: List[str]


class DatasheetDataRequest(BaseModel):
    client_id: int
    datasheet_id: UUID4
    # will be populated from datasheet_id if not provided
    databook_id: UUID4
    filters: List[InfixV1Model] | List[InfixV2Model] = []
    logged_in_user_email: str
    pivot_details: Optional[PivotRequestModel] = {}
    sort_columns_by: List[Dict[str, str]] = []
    page_size: int = Field(gt=0)
    page_number: int = Field(gt=0)

    @computed_field
    def page_offset(self) -> int:
        return (self.page_number - 1) * self.page_size

    @field_validator("databook_id", mode="before")
    @classmethod
    def populate_databook_id(cls, databook_id, info) -> UUID4:
        return get_databook_id(databook_id, info)

    @field_validator("filters", mode="before")
    @classmethod
    def transform_v2_infix_to_v1(cls, filters, info) -> list:
        return ui_filter_infix_converter(
            client_id=info.data["client_id"],
            ui_filter_infix=filters,
            databook_id=info.data["databook_id"],
            datasheet_id=info.data["datasheet_id"],
        )

    @field_validator("pivot_details", mode="before")
    @classmethod
    def remove_empty_dict(cls, pivot_details) -> dict | None:
        return pivot_details if pivot_details else None


class DatasheetDataResponse(BaseModel):
    data: list
    variables: DatasheetVariablesListResponse
    total_records: int
    hidden_columns: List[str] = []
    pivot_columns: dict = {}


class DatasheetViewCreateRequest(ModelSchema):
    class Meta:
        model = DatasheetView
        fields = [
            "datasheet_id",
            "name",
            "view_id",
            "last_updated_by",
            "created_at",
            "ordered_columns",
            "hidden_columns",
        ]


class DatasheetViewDetails(ModelSchema):
    filter_data: List[InfixV1Model] | List[InfixV2Model] = []
    pivot_data: ViewMetaDetails = {}
    hidden_columns: List[str] = []
    ordered_columns: List[str] = []

    class Meta:
        model = DatasheetView
        fields = [
            "view_id",
            "name",
            "datasheet_id",
            "filter_id",
            "pivot_id",
            "last_updated_by",
        ]


class CustomObjectSourceDetails(BaseModel):
    id: int = Field(..., alias="custom_object_id")
    name: str
    data_origin: str = DataOrigin.CUSTOM_OBJECT.value


class ReportObjectSourceDetails(BaseModel):
    id: str = Field(..., alias="ever_object_id")
    name: str
    data_origin: str


class DatasheetSourceDetails(BaseModel):
    id: UUID4 = Field(..., alias="datasheet_id")
    name: str
    databook_id: UUID4
    data_origin: str


class DatabookSourceDetails(BaseModel):
    id: UUID4 = Field(..., alias="databook_id")
    name: str


class DatasheetSourceOptions(RootModel):
    root: Dict[
        DatasheetSourceType,
        List[CustomObjectSourceDetails]
        | List[ReportObjectSourceDetails]
        | List[DatasheetSourceDetails]
        | List[DatabookSourceDetails],
    ]

    class Config:
        use_enum_values = True


class DatasheetSerializer(serializers.ModelSerializer):
    class Meta:
        model = Datasheet
        fields = "__all__"


class DatasheetVariableSerializer(serializers.ModelSerializer):
    class Meta:
        model = DatasheetVariable
        fields = "__all__"


class CreateAdjustmentRequest(BaseModel):
    operation: AdjustmentTypes
    data: dict | None | List[dict]
    comments: str
    row_key: str
    original_row_key: str
    is_global: bool = False
    client_id: int
    datasheet_id: UUID4
    databook_id: UUID4

    @field_validator("databook_id", mode="before")
    @classmethod
    def populate_databook_id(cls, databook_id, info) -> UUID4:
        return get_databook_id(databook_id, info)

    class Config:
        use_enum_values = True


class RevertAdjustmentRequest(BaseModel):
    row_key: str
    original_row_key: str
    adjustment_id: UUID4
    adjustment_number: int
    type: AdjustmentTypes
    sub_adjustment_number: str
    client_id: int
    datasheet_id: UUID4
    databook_id: UUID4

    @field_validator("databook_id", mode="before")
    @classmethod
    def populate_databook_id(cls, databook_id, info) -> UUID4:
        return get_databook_id(databook_id, info)

    class Config:
        use_enum_values = True


class UpdateAdjustmentRequest(BaseModel):
    operation: AdjustmentTypes
    data: dict | List[dict]
    comments: str
    row_key: str
    original_row_key: str
    adjustment_id: UUID4
    adjustment_number: int
    sub_adjustment_number: str
    adjustment_type: str
    is_global: bool = False
    client_id: int
    datasheet_id: UUID4
    databook_id: UUID4

    @field_validator("databook_id", mode="before")
    @classmethod
    def populate_databook_id(cls, databook_id, info) -> UUID4:
        return get_databook_id(databook_id, info)

    class Config:
        use_enum_values = True


class GetAdjustmentsRequest(BaseModel):
    client_id: int
    datasheet_id: UUID4
    databook_id: UUID4

    @field_validator("databook_id", mode="before")
    @classmethod
    def populate_databook_id(cls, databook_id, info) -> UUID4:
        return get_databook_id(databook_id, info)


class ExportDatasheetRequest(BaseModel):
    client_id: int
    datasheet_id: UUID4
    databook_id: UUID4
    apply_adjustments: bool = True
    need_hidden_columns: bool = False
    hidden_columns: List[str] = []
    filters: List[InfixV1Model] | List[InfixV2Model] = []
    pivot_details: Optional[PivotRequestModel] = {}

    @field_validator("databook_id", mode="before")
    @classmethod
    def populate_databook_id(cls, databook_id, info) -> UUID4:
        return get_databook_id(databook_id, info)

    @field_validator("filters", mode="before")
    @classmethod
    def transform_v2_infix_to_v1(cls, filters, info) -> list:
        return ui_filter_infix_converter(
            client_id=info.data["client_id"],
            ui_filter_infix=filters,
            databook_id=info.data["databook_id"],
            datasheet_id=info.data["datasheet_id"],
        )

    @field_validator("pivot_details", mode="before")
    @classmethod
    def remove_empty_dict(cls, pivot_details) -> dict | None:
        return pivot_details if pivot_details else None


class DatasheetSyncStatusRequest(BaseModel):
    client_id: int
    datasheet_id: UUID4
    databook_id: UUID4

    @field_validator("databook_id", mode="before")
    @classmethod
    def populate_databook_id(cls, databook_id, info) -> UUID4:
        return get_databook_id(databook_id, info)


class DatasheetSyncStatusResponse(BaseModel):
    client_id: int
    e2e_sync_run_id: str | None = None
    sync_status: str | None = None
    sync_end_time: datetime | str | None = None
    sync_start_time: datetime | None = None
    error_details: dict | None = None
    error_info: str | None = None
    datasheet_id: UUID4
    databook_id: UUID4

    @field_validator("databook_id", mode="before")
    @classmethod
    def populate_databook_id(cls, databook_id, info) -> UUID4:
        return get_databook_id(databook_id, info)


class RevertAdjustmentResponse(BaseModel):
    status: str


class AdjustmentErrorResponse(BaseModel):
    status: str
    message: str


class AdjustmentResponse(BaseModel):
    status: str
    adjustment_id: UUID4


class NotAppliedAdjustmentRowKeysModel(BaseModel):
    original_row_keys: List[str]
    adjusted_row_keys: List[str]


class GetAdjustmentsResponse(BaseModel):
    status: str
    adjustment_data: dict
    total_adjusted_records: int
    not_applied_row_keys: NotAppliedAdjustmentRowKeysModel
    dangling_rows: Optional[dict] = {}


class DatasheetGenerateTaskInfo(BaseModel):
    tasks: dict
    status: str
    e2e_sync_run_id: UUID4


class DatasheetGenerateResponse(BaseModel):
    status_code: int
    task_info: DatasheetGenerateTaskInfo


class DatasheetGenerateFailureResponse(BaseModel):
    status: int
    code: str
    message: str


class TransformationBase(BaseModel):
    """This is a base class for all transformation classes."""

    key: Union[UUID4, str]
    type: TransformationType
    transformation_id: UUID4
    is_valid: bool = False
    output_columns: List[dict] = []
    transformation_source_map: dict = {}
    used_variable_ids: list = []
    source_ids: list = []

    class Config:
        use_enum_values = True


class UnionOn(BaseModel):
    """This class represent the 'on' field of the UNION transformation_dict."""

    lhs: dict
    rhs: dict
    col_name: Optional[str] = None


class UnionTransformation(TransformationBase):
    """This class represent UNION transformation."""

    union_type: str
    on: List[UnionOn]
    with_: str = Field(
        alias="with",
        title="with_",
        description="Contains the datasheet_id of the sheet to be union with.",
    )
    with_databook_id: str


class Columns(BaseModel):
    source_id: str | int
    is_primary: bool
    source_type: str
    source_name: Optional[str] = ""
    system_name: str
    variable_id: str | UUID4
    data_type_id: int
    display_name: str
    is_selected: Optional[bool] = True
    source_variable_id: str | UUID4 | None = None
    source_cf_meta_data: Optional[dict] = None
    source_name_history: str | None = None
    system_generated: Optional[bool] = False


class JoinOn(BaseModel):
    """This class represent 'on' field of the JOIN transformation."""

    lhs: List[dict]
    rhs: List[dict]


class JoinTransformation(TransformationBase):
    """This class represent the JOIN transformation."""

    on: JoinOn
    columns: JoinOn
    join_type: str
    with_: str = Field(
        alias="with",
        title="with_",
        description="Contains the datasheet_id of the sheet to be joined with.",
    )
    with_databook_id: str


class AdvancedFilterTransformation(TransformationBase):
    """This class represent ADVANCED_FILTER transformation."""

    infix: InfixV1ListModel | List[InfixV2Model]
    used_system_names: List[str] = []


class GroupByTransformation(TransformationBase):
    """This class represent the GROUP_BY Transformation"""

    by: List[dict]
    aggregations: List[Dict]


class FlattenTransformation(TransformationBase):
    """This class represent the FLATTEN transformation."""

    col_name: str
    output_data_type: str
    flattened_col_name: str = ""
    variable_id: str


class TemporalSpliceColumn(BaseModel):
    """
    This class represent the input_columns and output_columns
    of the TEMPORAL_SPLICE transformation.
    """

    system_name: str
    data_type_id: int
    display_name: str


class TemporalSpliceTransformationMeta(BaseModel):
    """
    This class represent 'meta' field of the TEMPORAL_SPLICE Transformation.
    """

    source_id: str | int
    source_type: str
    input_columns: List[TemporalSpliceColumn] = []
    output_columns: List[TemporalSpliceColumn] = []
    email_id_column: dict
    with_databook_id: Optional[str]
    end_date_column: Optional[dict]
    start_date_column: Optional[dict]
    has_effective_dates: bool


class TemporalSpliceTransformation(TransformationBase):
    """This class represent TEMPORAL_SPLICE Transformation"""

    meta: List[TemporalSpliceTransformationMeta]


class GetUserPropertiesMeta(BaseModel):
    """
    This class represent 'user_properties' field of the GET_USER_PROPERTIES Transformation.
    """

    user_property_system_name: str
    data_type_id: int
    output_variable_system_name: str
    display_name: str
    variable_id: str


class GetUserPropertiesTransformation(TransformationBase):
    """This class represent GET_USER_PROPERTIES Transformation"""

    email_column: dict
    as_of_date_column: dict
    user_properties: List[GetUserPropertiesMeta]


class BasicFilterTransformation(TransformationBase):
    """This class represent the BASIC_FILTER transformation."""

    col_name: str
    operator: str
    value: Any
    variable_id: str
    display_name: str


class SortTransformation(TransformationBase):
    """This class represent the SORT transformation."""

    on: List[str]
    sort_type: SortType


class TransformationModel(RootModel):
    root: Union[
        UnionTransformation,
        SortTransformation,
        JoinTransformation,
        BasicFilterTransformation,
        AdvancedFilterTransformation,
        GroupByTransformation,
        FlattenTransformation,
        TemporalSpliceTransformation,
        GetUserPropertiesTransformation,
    ]


class EmptyAdvancedFilterTransformation(AdvancedFilterTransformation):
    infix: List


class EmptyBasicFilterTransformation(BasicFilterTransformation):
    col_name: None
    variable_id: None


class EmptyFlattenTransformation(FlattenTransformation):
    col_name: None
    variable_id: None


class EmptyGroupByTransformation(GroupByTransformation):
    by: List
    aggregations: List


class EmptyTemporalSpliceTransformationMeta(TemporalSpliceTransformationMeta):
    email_id_column: Optional[dict]
    source_id: Optional[str | int]
    source_type: Optional[str]


class EmptyTemporalSpliceTransformation(TemporalSpliceTransformation):
    meta: List[EmptyTemporalSpliceTransformationMeta]


class EmptyJoinOn(JoinOn):
    lhs: List
    rhs: List


class EmptyJoinTransformation(JoinTransformation):
    on: EmptyJoinOn
    columns: EmptyJoinOn


class EmptyTransformationModel(RootModel):
    root: Union[
        UnionTransformation,
        SortTransformation,
        EmptyJoinTransformation,
        EmptyBasicFilterTransformation,
        EmptyAdvancedFilterTransformation,
        EmptyGroupByTransformation,
        EmptyFlattenTransformation,
        EmptyTemporalSpliceTransformation,
        GetUserPropertiesTransformation,
    ]


class TransformationsListModel(RootModel):
    root: List[TransformationModel]


class DatasheetDetail(DatasheetModel):
    total_records: int
    total_dependent_sheets: int
    upstream_sheet_details: List[dict]
    downstream_sheet_details: List[dict]
    total_connected_commission_plans: int = 0
    commission_plan_details: list[dict] = []
    dashboard_details: list[dict] = []
    is_stale: bool
    is_pinned: bool
    total_adjustments: int = 0
    last_updated_at: datetime
    is_active_sync_present: bool = False
    is_active_databook_sync_present: bool = False
    is_archived: bool = False
    databook_name: str
    last_generated_at: Optional[datetime] = None
    views: List[ViewMetaDetails] = []
    filters: list = []
    pivot_details: dict = {}
    variables: DatasheetVariablesListResponse
    additional_source_variables: DatasheetVariablesListResponse = []
    updated_by: str
    created_on: datetime
    created_by: str


class DatasheetDetailsBaseModel(BaseModel):
    additional_source_variables: DatasheetVariablesListResponse = []
    databook_id: UUID4
    datasheet_id: UUID4
    downstream_sheet_details: List[dict]
    is_active_sync_present: bool = False
    source_name: str
    source_type: str
    name: str
    transformation_spec: TransformationsListModel | List[EmptyTransformationModel] = []


class DatasheetDetailEdit(DatasheetDetailsBaseModel):
    source_databook_id: Optional[UUID4] = None
    source_id: int | str
    variables: DatasheetVariablesListResponse

    @field_validator("source_id", mode="before")
    @classmethod
    def convert_source_id_data_type(cls, source_id, info) -> int | str | UUID4:
        if info.data["source_type"] == DatasheetSourceType.CUSTOM_OBJECT.value:
            return int(source_id)
        return source_id


class DatasheetDetailCanvas(DatasheetDetailsBaseModel):
    commission_plan_details: list[dict] = []
    created_by: str
    created_on: datetime
    dashboard_details: list[dict] = []
    data_last_updated_at: Optional[datetime] = None
    data_origin: str
    databook_name: str
    is_archived: bool = False
    is_calc_field_changed: bool = False
    is_config_changed: bool = False
    is_datasheet_generated: bool = False
    is_stale: bool
    last_generated_at: Optional[datetime] = None
    last_updated_at: datetime
    total_adjustments: int = 0
    total_connected_commission_plans: int = 0
    total_dependent_sheets: int
    total_records: int
    updated_by: str
    upstream_sheet_details: List[dict]
    views: List[ViewMetaDetails] = []
    dashboard_details: list[dict] = []
    variables: DatasheetVariablesListResponseForCanvas


class DatasheetValidateRequest(BaseModel):
    client_id: int
    datasheet_id: UUID4
    databook_id: UUID4
    variables: DatasheetVariablesList
    transformations: TransformationsListModel | List[EmptyTransformationModel] = []
    initial_validation: bool = False
    source_type: str
    source_id: Optional[int | str] = None
    source_variables: DatasheetSourceVariablesList
    has_source_changed: bool = False
    additional_source_variables: Optional[DatasheetVariablesList] = None

    @field_validator("databook_id", mode="before")
    @classmethod
    def populate_databook_id(cls, databook_id, info) -> UUID4:
        if not databook_id:
            from .selectors import (
                DatasheetSelector,  # pylint disable=import-outside-toplevel
            )

            databook_id = (
                DatasheetSelector(
                    client_id=info.data["client_id"],
                    datasheet_id=info.data["datasheet_id"],
                )
                .get_datasheet()
                .databook_id
            )
        return databook_id

    @field_validator("source_variables", mode="before")
    @classmethod
    def populate_source_variables(
        cls, source_variables, info
    ) -> DatasheetSourceVariablesList:
        if not source_variables:
            from everstage_ddd.datasheet.services.datasheet_service import (
                datasheet_fetch_source_variables,  # pylint disable=import-outside-toplevel
            )

            source_variables = datasheet_fetch_source_variables(
                client_id=info.data["client_id"],
                source_type=info.data["source_type"],
                source_id=info.data["source_id"],
            )
        return source_variables


class DatasheetCloneRequest(BaseModel):
    client_id: int
    datasheet_id: UUID4
    databook_id: Optional[UUID4]
    databook_name: str

    @field_validator("databook_id", mode="before")
    @classmethod
    def populate_databook_id(cls, databook_id, info) -> UUID4:
        return get_databook_id(databook_id, info)


class DatasheetVariableCloneRequest(BaseModel):
    client_id: int
    databook_id: UUID4
    clone_databook_id: UUID4
    datasheet_id: UUID4
    clone_datasheet_id: UUID4
    original_to_cloned_datasheet_id_map: dict | None = None


class DatasheetViewCloneRequest(BaseModel):
    client_id: int
    datasheet_id: UUID4
    clone_datasheet_id: UUID4


class DatasheetCloneResponse(BaseModel):
    status: str
    clone_datasheet_id: Union[UUID4, str]


class DatasheetValidateResponse(BaseModel):
    has_config_changed: bool
    datasheet_id: UUID4
    variables: DatasheetVariablesListResponse
    source_variables: DatasheetSourceVariablesList
    transformations: List[TransformationModel] | List[EmptyTransformationModel] = []
    source_type: str
    source_id: int | str


class DatasheetGenerateRequest(BaseModel):
    is_report_data_stale: bool
    is_force_invalidate: bool
    client_id: int
    datasheet_id: UUID4
    databook_id: UUID4
    additional_source_variables: DatasheetVariablesList = []
    transformation_spec: TransformationsListModel = []

    @field_validator("databook_id", mode="before")
    @classmethod
    def populate_databook_id(cls, databook_id, info) -> UUID4:
        return get_databook_id(databook_id, info)


class DatabookArchiveRestoreResponse(BaseModel):
    status: Optional[str] = None
    message: Optional[str] = None
    datasheet_details: Optional[List[Dict[str, str]]] = None


class DsFilterOperator(ModelSchema):
    class Meta:
        model = DSFilterOperators
        fields = [
            "name",
            "alt_name",
            "category",
            "operand_type_ids",
            "output_type_ids",
            "needs_operand",
            "multi_valued",
        ]


class DatasheetGraphRequest(BaseModel):
    client_id: int
    datasheet_id: Optional[UUID4] = None
    databook_id: UUID4 = None
    is_sheet_level_graph: bool = True


class DsFilterOperatorsList(RootModel):
    root: List[DsFilterOperator]


class SwitchVersionRequest(BaseModel):
    version: str


class SwitchVersionResponse(BaseModel):
    status: str
    msg: str


def _get_source_name(*, client_id, source_id, source_type) -> str:
    from everstage_ddd.datasheet.services.datasheet_service import (
        fetch_source_name_of_variable,
    )

    return fetch_source_name_of_variable(
        client_id=client_id,
        source_id=str(source_id),
        source_type=source_type,
    )


def _get_source_name_history(
    *, client_id, source_id, source_variable_id, source_type, display_name
) -> str:
    from everstage_ddd.datasheet.services.datasheet_service import (
        fetch_source_display_name_history,
    )

    return fetch_source_display_name_history(
        client_id=client_id,
        source_id=str(source_id),
        source_variable_id=source_variable_id,
        source_type=source_type,
        display_name=display_name,
    )


class DsCommEnrichVariable(BaseModel):
    system_name: str
    report_system_name: str
    data_type_id: int
    display_name: str
