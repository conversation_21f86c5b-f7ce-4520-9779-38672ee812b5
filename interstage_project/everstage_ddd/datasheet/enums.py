from enum import Enum

from commission_engine.services.expression_designer.data_conversion_models import (
    Datatypes,
)


class DatasheetGroupedBy(Enum):
    DATABOOK = "databooks"
    COMMISSION_PLAN = "commission_plans"
    ARCHIVED_DATABOOK = "archived_databooks"


class WindowCalculatedFields(Enum):
    RANK = "rank"
    ROLLING = "rolling"
    HIERARCHY = "hierarchy"


class V2TokenTypes(Enum):
    """
    Enum for the different types of tokens used in v2 expression designer.
    """

    DATASHEET_VARIABLES = "DATASHEET_VARIABLES"
    DATASHEET_INTER_VARIABLE = "DATASHEET_INTER_VARIABLE"
    CONSTANT_VARIABLES = "CONSTANT_VARIABLES"
    FUNCTIONS = "FUNCTIONS"
    OPERATORS = "OPERATORS"
    GROUPING_OPERATORS = "GROUPING_OPERATORS"
    AST_META = "AST_META"


class V1TokenTypes(Enum):
    """
    Enum for the different types of tokens used in v1 expression designer.
    """

    VARIABLE = "VARIABLE"
    OPERATOR = "OPERATOR"
    LBRACKET = "LBRACKET"
    RBRACKET = "RBRACKET"


class DatasheetSourceType(Enum):
    CUSTOM_OBJECT = "object"
    REPORT_OBJECT = "report"
    DATABOOK = "databook"
    DATASHEET = "datasheet"


class AdjustmentTypes(Enum):
    IGNORE = "IGNORE"
    UPDATE = "UPDATE"
    INSERT = "INSERT"
    SPLIT = "SPLIT"


class TransformationType(Enum):
    SORT = "SORT"  # deprecated
    GROUP_BY = "GROUP_BY"
    JOIN = "JOIN"
    UNION = "UNION"
    FILTER = "FILTER"
    ADVANCED_FILTER = "ADVANCED_FILTER"
    ADVANCED_FILTER_V2 = "ADVANCED_FILTER_V2"
    FLATTEN = "FLATTEN"
    TEMPORAL_SPLICE = "TEMPORAL_SPLICE"
    GET_USER_PROPERTIES = "GET_USER_PROPERTIES"


class DataOrigin(Enum):
    """
    Enum class for data origin
    """

    CUSTOM_OBJECT = "custom_object"
    COMMISSION_OBJECT = "commission_object"
    INTER_OBJECT = "inter_object"
    SYSTEM_OBJECT = "system_object"
    FORECAST_OBJECT = "forecast_object"
    INTER_FORECAST_OBJECT = "inter_forecast_object"


class JoinType(Enum):
    """
    Enum class for join transformation type
    """

    LEFT = "LEFT"
    RIGHT = "RIGHT"
    FULL = "FULL"
    INNER = "INNER"


class SortType(Enum):
    """
    Enum class for sort transformation type
    """

    ASC = "ASC"
    DESC = "DESC"


GroupByAggDatatypesMap = {
    "AVG": Datatypes.INTEGER.value,
    "COUNT": Datatypes.INTEGER.value,
    "COUNT_DISTINCT": Datatypes.INTEGER.value,
    "SUM": Datatypes.INTEGER.value,
    "MIN": None,
    "MAX": None,
}

multiple_source_transformation_registry = [
    TransformationType.JOIN.value,
    TransformationType.UNION.value,
    TransformationType.TEMPORAL_SPLICE.value,
]

SAME_SOURCE_VAR = "__ss__"
MAX_COLUMN_LIMIT = 500


class DanglingReason(Enum):
    PRIMARY_KEY_TRANSFORMED = "primary_key_transformed"
    ROW_INVALIDATED = "row_invalidated"
    UNKNOWN = "unknown"
