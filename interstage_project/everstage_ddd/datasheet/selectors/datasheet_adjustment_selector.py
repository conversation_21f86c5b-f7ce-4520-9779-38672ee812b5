import pandas as pd
from django.db.models import Max

from common.data_selectors.bi_temporal_selector import BiTemporalSelector
from everstage_ddd.datasheet.models.datasheet_models import DatasheetAdjustments


class DatasheetAdjustmentSelector(BiTemporalSelector):
    def __init__(self, client_id, datasheet_id=None):
        self.datasheet_id = datasheet_id
        self.model = DatasheetAdjustments
        super().__init__(
            client_id=client_id,
            model=self.model,
        )

    def create_datasheet_adjustments(self, data):
        return self.bitemporal_create(data)

    def get_max_adjustment_number(self):
        return self.client_aware().aggregate(Max("adjustment_number"))

    def is_adjustment_exist(self, databook_id, datasheet_id, original_row_key):
        return (
            self.client_kd_aware()
            .filter(
                databook_id=databook_id,
                datasheet_id=datasheet_id,
                original_row_key=original_row_key,
            )
            .exists()
        )

    def invalidate_adj_by_id_number(
        self, adjustment_id, adjustment_number, knowledge_date
    ):
        self.client_kd_aware().filter(
            adjustment_id=adjustment_id, adjustment_number=adjustment_number
        ).update(knowledge_end_date=knowledge_date)

    def invalidate(self, adjustment_id, adjustment_number, sub_number, knowledge_date):
        self.client_kd_aware().filter(
            adjustment_id=adjustment_id,
            adjustment_number=adjustment_number,
            sub_adjustment_number=sub_number,
        ).update(knowledge_end_date=knowledge_date)

    def invalidate_by_adjustment_id(
        self, adjustment_id, adjustment_number, sub_number, knowledge_date
    ):
        self.client_kd_aware().filter(
            adjustment_id=adjustment_id,
            adjustment_number=adjustment_number,
            sub_adjustment_number=sub_number,
        ).update(knowledge_end_date=knowledge_date)

    def get_adjustment_by_id_and_number(
        self, adjustment_id, adjustment_number, sub_number
    ):
        return (
            self.client_kd_aware()
            .filter(
                adjustment_id=adjustment_id,
                adjustment_number=adjustment_number,
                sub_adjustment_number=sub_number,
            )
            .first()
        )

    def _create_adjustment(self, record):
        record.pk = None
        record.save()

    def update_datasheet_adjustments(  # noqa: PLR0913
        self,
        adjustment_id,
        adjustment_number,
        sub_number,
        audit_details,
        data,
        knowledge_date,
        comments,
        is_global,
    ):
        prev_record = self.get_adjustment_by_id_and_number(
            adjustment_id, adjustment_number, sub_number
        )
        if prev_record:
            self.invalidate(
                adjustment_id, adjustment_number, sub_number, knowledge_date
            )
            prev_record.knowledge_begin_date = knowledge_date
            prev_record.data = data
            prev_record.additional_details = audit_details
            prev_record.comments = comments
            prev_record.is_global = is_global
            self._create_adjustment(prev_record)

    def update_dangling_adjustment_status(
        self,
        adjustment_id,
        adjustment_number,
        sub_number,
        invalidated_at,
        dangling_reason=None,
    ):
        self.client_kd_aware().filter(
            adjustment_id=adjustment_id,
            adjustment_number=adjustment_number,
            sub_adjustment_number=sub_number,
        ).update(dangling_reason=dangling_reason, invalidated_at=invalidated_at)

    def reset_dangling_status_of_all_adjustments(self, databook_id, datasheet_id):
        self.client_kd_aware().filter(
            databook_id=databook_id,
            datasheet_id=datasheet_id,
            dangling_reason__isnull=False,
        ).update(dangling_reason=None, invalidated_at=None)

    def get_row_keys_for_adjustments(
        self,
        databook_id,
        datasheet_id,
        adjustments_type_filter=None,
        knowledge_date=None,
    ):
        """Gets row_keys on which adjustments have to be applied

        Parameters
        ----------
        databook_id : str
            Databook ID of the datasheet whose row_keys for adjustment has to be fetched
        datasheet_id : str
            Datasheet ID of the datasheet whose row_keys for adjustment has to be fetched
        adjustments_type_filter : str, optional
            Type of adjustment to be filtered (GLOBAL or LOCAL)

        Returns
        -------
        set
            a set of strings representing the row_keys on which adjustments have to be applied
        """

        if knowledge_date:
            qs = self.client_kd_deleted_aware(knowledge_date=knowledge_date)
        else:
            qs = self.client_kd_aware()

        if adjustments_type_filter == "GLOBAL":
            return set(
                qs.filter(databook_id=databook_id, datasheet_id=datasheet_id)
                .filter(is_global=True)
                .values_list("row_key", flat=True)
            )
        if adjustments_type_filter == "LOCAL":
            return set(
                qs.filter(databook_id=databook_id, datasheet_id=datasheet_id)
                .filter(is_global=False)
                .values_list("row_key", flat=True)
            )
        if not adjustments_type_filter:
            return set(
                qs.filter(
                    databook_id=databook_id, datasheet_id=datasheet_id
                ).values_list("row_key", flat=True)
            )

        return set()

    def get_adjustments_for_datasheet_as_frame(
        self,
        databook_id,
        datasheet_id,
        adjustments_type_filter=None,
        knowledge_date=None,
    ):
        if knowledge_date:
            qs = self.client_kd_deleted_aware(knowledge_date)
        else:
            qs = self.client_kd_aware()
        if adjustments_type_filter == "GLOBAL":
            return pd.DataFrame(
                qs.filter(
                    databook_id=databook_id, datasheet_id=datasheet_id, is_global=True
                ).values()
            )
        if adjustments_type_filter == "LOCAL":
            return pd.DataFrame(
                qs.filter(
                    databook_id=databook_id, datasheet_id=datasheet_id, is_global=False
                ).values()
            )
        if not adjustments_type_filter:
            return pd.DataFrame(
                qs.filter(databook_id=databook_id, datasheet_id=datasheet_id).values()
            )

        return pd.DataFrame()

    def get_dangling_adjustments_as_frame(self, databook_id, datasheet_id):
        return pd.DataFrame(
            self.client_kd_aware()
            .filter(
                databook_id=databook_id,
                datasheet_id=datasheet_id,
                dangling_reason__isnull=False,
            )
            .values()
        )
