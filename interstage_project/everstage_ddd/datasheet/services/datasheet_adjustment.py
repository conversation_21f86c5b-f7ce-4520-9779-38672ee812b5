import logging
import time
import traceback
import uuid

import pandas as pd
from django.db import transaction
from django.utils import timezone
from sqlparse.exceptions import SQLParseError

from commission_engine.accessors.client_accessor import (
    is_show_dangling_adjustments_enabled,
    should_use_multi_engine_stormbreaker,
)
from commission_engine.accessors.databook_accessor import DatasheetVariableAccessor
from commission_engine.data_readers.databook_reader import DatasheetDataReader
from commission_engine.services.expression_designer.data_conversion_models import (
    Datatypes,
)
from commission_engine.utils.general_data import (
    DATASHEET_ADJUSTMENT_OPERATION,
    DATASHEET_SOURCE,
    SegmentEvents,
    SegmentProperties,
)
from commission_engine.utils.general_utils import log_time_taken, replace_nan_nat
from commission_engine.utils.report_utils import DataOrigin
from everstage_ddd.datasheet.data_models import (
    AdjustmentErrorResponse,
    AdjustmentResponse,
    CreateAdjustmentRequest,
    DatasheetAdjustmentsModel,
    GetAdjustmentsResponse,
    RevertAdjustmentRequest,
    RevertAdjustmentResponse,
    UpdateAdjustmentRequest,
)
from everstage_ddd.datasheet.selectors.datasheet_adjustment_selector import (
    DatasheetAdjustmentSelector,
)
from everstage_ddd.datasheet.selectors.datasheet_selector import DatasheetSelector
from everstage_ddd.datasheet.selectors.datasheet_variable_selector import (
    DatasheetVariableSelector,
)
from interstage_project.threadlocal_log_context import set_threadlocal_context
from spm.accessors.config_accessors.employee_accessor import EmployeeAccessor
from spm.accessors.variable_accessor import VariableDataTypeAccessor
from spm.constants.audit_trail import AUDIT_TRAIL_EVENT_TYPE as EVENT
from spm.constants.localization_constants import LocalizationEngMessages
from spm.services import audit_services
from spm.services.analytics_services.analytics_service import CoreAnalytics
from spm.services.databook_services import get_datasheet_last_generation_time
from spm.services.localization_services import get_localized_message_service

logger = logging.getLogger(__name__)


def adjustment_data_validate_checker(
    client_id, databook_id, datasheet_id, adjustment_type, adjustment_data=None
):
    """
    Validate if the adjustment data is in the correct format. Currently it checks,
    1. If date values in the adjusted data are correct
    Returns True if data is in correct format,
    """
    # no validation is needed for ignore adjustments
    if adjustment_type == DATASHEET_ADJUSTMENT_OPERATION.IGNORE.value:
        return True, ""

    if not isinstance(adjustment_data, list):
        adjustment_data = [adjustment_data]

    # get datasheet variables and their data types
    ds_variables_and_types = DatasheetVariableSelector(
        client_id=client_id
    ).get_ds_var_system_name_and_dtype_superset(
        databook_id=databook_id,
        datasheet_id=datasheet_id,
        knowledge_date=None,
    )

    # mapping of variable system name to its data_type id and display name
    ds_var_map = dict()
    for var in ds_variables_and_types:
        ds_var_map[var["system_name"]] = dict()
        ds_var_map[var["system_name"]]["display_name"] = var["display_name"]
        ds_var_map[var["system_name"]]["data_type_id"] = var["data_type_id"]

    data_type_id_name_map = VariableDataTypeAccessor().get_all_var_dtypes()
    for data in adjustment_data:
        for var, value in data.items():
            data_type_id = ds_var_map.get(var, {}).get("data_type_id")
            data_type = data_type_id_name_map.get(data_type_id, "String")
            if data_type == Datatypes.DATE.value and value == "":
                return (
                    False,
                    f"Invalid date value for {ds_var_map.get(var, {}).get('display_name')}",
                )

    return True, ""


@transaction.atomic
def datasheet_adjustment_create(  # noqa: PLR0915
    logged_in_user: str,
    adjustment_payload: CreateAdjustmentRequest,
    audit: dict,
) -> AdjustmentResponse | AdjustmentErrorResponse:
    """
    Create a new datasheet adjustment

    #TODO: Split the function into smaller functions
    """
    client_id = adjustment_payload.client_id
    datasheet_id = adjustment_payload.datasheet_id

    set_threadlocal_context(
        {
            "client_id": client_id,
            "datasheet_id": datasheet_id,
            "logged_in_user": logged_in_user,
        }
    )

    time = timezone.now()

    max_adj = DatasheetAdjustmentSelector(client_id).get_max_adjustment_number()
    max_ad_number = max_adj["adjustment_number__max"] if max_adj else 0

    new_adjustment = adjustment_payload.model_dump(by_alias=True)

    new_adjustment["client"] = client_id
    new_adjustment["knowledge_begin_date"] = time
    new_adjustment["adjustment_id"] = uuid.uuid4()
    new_adjustment["adjustment_number"] = int(max_ad_number) + 1 if max_ad_number else 1
    new_adjustment["additional_details"] = audit
    new_adjustment["created_at"] = time
    new_adjustment["created_by"] = logged_in_user
    new_adjustment["knowledge_end_date"] = None

    create_adjustment_response = {
        "status": "SUCCESS",
        "adjustment_id": new_adjustment["adjustment_id"],
    }

    failure_response = {
        "status": "ERROR",
    }

    # Validate the adjustment data
    adjustment_data_validity, data_validity_message = adjustment_data_validate_checker(
        client_id=client_id,
        databook_id=adjustment_payload.databook_id,
        datasheet_id=datasheet_id,
        adjustment_type=adjustment_payload.operation,
        adjustment_data=adjustment_payload.data,
    )

    if not adjustment_data_validity:
        return AdjustmentErrorResponse(
            **failure_response, message=data_validity_message
        )

    # ###################### audit log #####################
    datasheet = DatasheetSelector(client_id, datasheet_id).get_datasheet()
    ds_name = datasheet.name if datasheet else ""
    ds_origin = datasheet.data_origin if datasheet else ""

    set_threadlocal_context(
        {
            "operation": adjustment_payload.operation,
            "databook_id": adjustment_payload.databook_id,
            "datasheet_id": datasheet_id,
            "original_row_key": adjustment_payload.original_row_key,
        }
    )

    if ds_origin not in (
        DATASHEET_SOURCE.CUSTOM_OBJECT.value,
        DataOrigin.SYSTEM_OBJECT.value,
    ):
        logger.exception(
            "Excep in Adding adjustment to datasheet with adjustment_id {} Datasheet source cannot {}; ".format(
                new_adjustment["adjustment_id"], ds_origin
            )
        )
        raise ValueError(f"Datasheet source cannot {ds_origin}")  # noqa: TRY003

    event_type_code = EVENT["CREATE_DATASHEET-ADJUSTMENT"]["code"]
    event_key = new_adjustment["adjustment_number"]
    summary = "Created datasheet adjustment for " + ds_name
    audit_data = new_adjustment
    updated_by = audit["updated_by"]
    updated_at = time
    # ######################################################

    adj_exists = DatasheetAdjustmentSelector(client_id).is_adjustment_exist(
        new_adjustment["databook_id"],
        datasheet_id,
        new_adjustment["original_row_key"],
    )

    # Check if adjustment already exists
    if adj_exists:
        localized_message = get_localized_message_service(
            LocalizationEngMessages.ADJUSTMENT_EXIST.value, client_id
        )
        return AdjustmentErrorResponse(**failure_response, message=localized_message)

    # Handle IGNORE, UPDATE operations
    if new_adjustment["operation"] in ["IGNORE", "UPDATE"]:
        new_adjustment["sub_adjustment_number"] = (
            str(new_adjustment["adjustment_number"]) + "a"
        )
        new_adjustment["data_origin"] = ds_origin

        new_adjustment_serialized = DatasheetAdjustmentsModel(
            **new_adjustment
        ).model_dump(by_alias=True)

        try:
            # Persist the adjustment
            DatasheetAdjustmentSelector(client_id).create_datasheet_adjustments(
                new_adjustment_serialized
            )

            analytics_data = {
                "user_id": audit["updated_by"],
                "event_name": SegmentEvents.ADJUST_RECORD.value,
                "event_properties": {
                    SegmentProperties.DATABOOK_ID.value: new_adjustment["databook_id"],
                    SegmentProperties.DATASHEET_NAME.value: ds_name,
                    SegmentProperties.ADJUSTMENT_TYPE.value: new_adjustment[
                        "operation"
                    ],
                    SegmentProperties.IS_GLOBAL_ADJUSTMENT.value: new_adjustment.get(
                        "is_global", False
                    ),
                },
            }
            analytics = CoreAnalytics(analyser_type="segment")
            analytics.send_analytics(analytics_data)
            audit_services.log(
                client_id,
                event_type_code,
                event_key,
                summary,
                updated_by,
                updated_at,
                audit_data,
            )
            logger.info(
                "Datasheet Adjustment added successfully for adjustment_id - {}".format(
                    new_adjustment["adjustment_id"]
                )
            )

            return AdjustmentResponse(**create_adjustment_response)
        except Exception as exc:
            error_dict = {"trace_back": traceback.print_exc()}
            logger.exception(
                "Excep in Adding adjustment to datasheet with adjustment_id {}".format(
                    new_adjustment["adjustment_id"]
                ),
                error_dict,
            )
            raise SQLParseError() from exc

    # Handle SPLIT operation
    if new_adjustment["operation"] == "SPLIT":
        data = new_adjustment["data"]
        for ind, _ in enumerate(data):
            # Add the first record with IGNORE operation and rest as INSERT operations
            if ind == 0:
                new_adjustment["operation"] = "IGNORE"
                new_adjustment["row_key"] = new_adjustment["original_row_key"]
                new_adjustment["sub_adjustment_number"] = (
                    str(new_adjustment["adjustment_number"]) + "a"
                )
                new_adjustment["data"] = None

                new_adjustment_serialized = DatasheetAdjustmentsModel(
                    **new_adjustment
                ).model_dump(by_alias=True)

                DatasheetAdjustmentSelector(client_id).create_datasheet_adjustments(
                    new_adjustment_serialized
                )

            ch = "b"
            new_adjustment["operation"] = "INSERT"
            new_adjustment["row_key"] = (
                new_adjustment["original_row_key"] + "-" + str(ind + 1)
            )
            new_adjustment["sub_adjustment_number"] = str(
                new_adjustment["adjustment_number"]
            ) + chr(ord(ch) + ind)
            new_adjustment["data"] = data[ind]

            new_adjustment_serialized = DatasheetAdjustmentsModel(
                **new_adjustment
            ).model_dump(by_alias=True)
            try:
                DatasheetAdjustmentSelector(client_id).create_datasheet_adjustments(
                    new_adjustment_serialized
                )
            except Exception as exc:
                error_dict = {"trace_back": traceback.print_exc()}
                logger.exception(
                    "Excep in Adding adjustment to datasheet with adjustment_id {}".format(
                        new_adjustment["adjustment_id"]
                    ),
                    error_dict,
                )
                raise SQLParseError() from exc
        audit_services.log(
            client_id,
            event_type_code,
            event_key,
            summary,
            updated_by,
            updated_at,
            audit_data,
        )

        analytics_data = {
            "user_id": audit["updated_by"],
            "event_name": SegmentEvents.ADJUST_RECORD.value,
            "event_properties": {
                SegmentProperties.DATABOOK_ID.value: new_adjustment["databook_id"],
                SegmentProperties.DATASHEET_NAME.value: ds_name,
                SegmentProperties.ADJUSTMENT_TYPE.value: "SPLIT",
                SegmentProperties.IS_GLOBAL_ADJUSTMENT.value: new_adjustment.get(
                    "is_global", False
                ),
            },
        }
        analytics = CoreAnalytics(analyser_type="segment")
        analytics.send_analytics(analytics_data)
        logger.info(
            "Datasheet Adjustment added successfully for adjustment_id - {}".format(
                new_adjustment["adjustment_id"]
            )
        )
    return AdjustmentResponse(**create_adjustment_response)


@transaction.atomic
def revert_datasheet_adjustment(
    client_id: int,
    datasheet_id: uuid.UUID,
    logged_in_user: str,
    adjustment_payload: RevertAdjustmentRequest,
    audit: dict,
) -> RevertAdjustmentResponse:
    """
    This function reverts the adjustment made to a datasheet
    """
    set_threadlocal_context(
        {
            "client_id": client_id,
            "datasheet_id": datasheet_id,
            "logged_in_user": logged_in_user,
        }
    )

    knowledge_date = timezone.now()
    adjustment_id = adjustment_payload.adjustment_id
    adjustment_number = adjustment_payload.adjustment_number
    sub_number = adjustment_payload.sub_adjustment_number
    adjustment_type = adjustment_payload.type

    create_adjustment_response = {
        "status": "SUCCESS",
    }

    # ###################### audit log #####################
    datasheet = DatasheetSelector(client_id, datasheet_id).get_datasheet()
    ds_name = datasheet.name if datasheet else ""

    set_threadlocal_context(
        {
            "operation": adjustment_payload.type,
            "databook_id": adjustment_payload.databook_id,
            "datasheet_id": datasheet_id,
            "original_row_key": adjustment_payload.original_row_key,
            "adjustment_id": adjustment_payload.adjustment_id,
        }
    )

    event_type_code = EVENT["REVERT_DATASHEET-ADJUSTMENT"]["code"]
    event_key = adjustment_payload.adjustment_number
    summary = "Reverted datasheet adjustment"
    audit_data = adjustment_payload.model_dump(by_alias=True)
    updated_by = audit["updated_by"]
    updated_at = knowledge_date
    # ######################################################

    try:
        if adjustment_type.upper() == "SPLIT":
            DatasheetAdjustmentSelector(client_id).invalidate_adj_by_id_number(
                adjustment_id, adjustment_number, knowledge_date
            )
        else:
            DatasheetAdjustmentSelector(client_id).invalidate_by_adjustment_id(
                adjustment_id, adjustment_number, sub_number, knowledge_date
            )
        audit_services.log(
            client_id,
            event_type_code,
            event_key,
            summary,
            updated_by,
            updated_at,
            audit_data,
        )

        analytics_data = {
            "user_id": audit["updated_by"],
            "event_name": SegmentEvents.REVERT_ADJUSTMENT.value,
            "event_properties": {
                SegmentProperties.DATABOOK_ID.value: adjustment_payload.databook_id,
                SegmentProperties.DATASHEET_NAME.value: ds_name,
                SegmentProperties.ADJUSTMENT_TYPE.value: adjustment_type,
            },
        }
        analytics = CoreAnalytics(analyser_type="segment")
        analytics.send_analytics(analytics_data)
        logger.info(
            "Datasheet Adjustment reverted successfully for adjustment_id - {}".format(
                adjustment_payload.adjustment_id
            )
        )
        return RevertAdjustmentResponse(**create_adjustment_response)
    except Exception as exc:
        error_dict = {"trace_back": traceback.print_exc()}
        logger.exception(
            "Excep in Reverting adjustment to datasheet with adjustment_id {}".format(
                adjustment_payload.adjustment_id
            ),
            error_dict,
        )
        raise SQLParseError from exc


@transaction.atomic
def update_datasheet_adjustment(  # noqa: PLR0915
    client_id: int,
    datasheet_id: uuid.UUID,
    logged_in_user: str,
    adjustment_payload: UpdateAdjustmentRequest,
    audit: dict,
) -> AdjustmentResponse | AdjustmentErrorResponse:
    """
    Update a datasheet adjustment
    """
    set_threadlocal_context(
        {
            "client_id": client_id,
            "datasheet_id": datasheet_id,
            "logged_in_user": logged_in_user,
        }
    )
    time = timezone.now()
    client_id = adjustment_payload.client_id
    adjustment_id = adjustment_payload.adjustment_id
    adjustment_number = adjustment_payload.adjustment_number
    sub_number = adjustment_payload.sub_adjustment_number
    data = adjustment_payload.data
    adjustment_type = adjustment_payload.adjustment_type
    comments = adjustment_payload.comments
    is_global = adjustment_payload.is_global

    create_adjustment_response = {
        "status": "SUCCESS",
        "adjustment_id": adjustment_id,
    }

    failure_response = {
        "status": "ERROR",
    }

    # ###################### audit log #####################
    datasheet = DatasheetSelector(client_id, datasheet_id).get_datasheet()
    ds_name = datasheet.name if datasheet else ""

    set_threadlocal_context(
        {
            "operation": adjustment_payload.operation,
            "databook_id": adjustment_payload.databook_id,
            "datasheet_id": datasheet_id,
            "original_row_key": adjustment_payload.original_row_key,
            "adjustment_id": adjustment_payload.adjustment_id,
        }
    )
    event_type_code = EVENT["UPDATE_DATASHEET-ADJUSTMENT"]["code"]
    event_key = adjustment_payload.adjustment_number
    summary = "Updated datasheet adjustment for " + ds_name
    audit_data = adjustment_payload.model_dump(by_alias=True)
    updated_by = audit["updated_by"]
    updated_at = time
    # ######################################################

    # Validate the adjustment data
    adjustment_data_validity, data_validity_message = adjustment_data_validate_checker(
        client_id=client_id,
        databook_id=adjustment_payload.databook_id,
        datasheet_id=datasheet_id,
        adjustment_type=adjustment_type,
        adjustment_data=data,
    )
    if not adjustment_data_validity:
        return AdjustmentErrorResponse(
            **failure_response, message=data_validity_message
        )

    # Handle SPLIT operation
    if adjustment_type.upper() == "SPLIT":
        # Invalidate the existing record
        existing_record = DatasheetAdjustmentSelector(
            client_id
        ).get_adjustment_by_id_and_number(adjustment_id, adjustment_number, sub_number)
        DatasheetAdjustmentSelector(client_id).invalidate_adj_by_id_number(
            adjustment_id, adjustment_number, time
        )
        if existing_record:
            new_record = {
                "client": client_id,
                "knowledge_begin_date": time,
                "adjustment_id": adjustment_id,
                "databook_id": existing_record.databook_id,
                "datasheet_id": existing_record.datasheet_id,
                "adjustment_number": adjustment_number,
                "additional_details": audit,
                "original_row_key": existing_record.original_row_key,
                "operation": "IGNORE",
                "comments": comments,
                "is_global": is_global,
            }

            for ind, _ in enumerate(data):
                # Add the first record with IGNORE operation and rest as INSERT operations
                if ind == 0:
                    new_record["row_key"] = new_record["original_row_key"]
                    new_record["sub_adjustment_number"] = (
                        str(new_record["adjustment_number"]) + "a"
                    )
                    new_record["data"] = None

                    new_adjustment_serialized = DatasheetAdjustmentsModel(
                        **new_record
                    ).model_dump(by_alias=True)

                    DatasheetAdjustmentSelector(client_id).create_datasheet_adjustments(
                        new_adjustment_serialized
                    )
                    new_record["row_key"] = new_record["original_row_key"]
                    new_record["sub_adjustment_number"] = (
                        str(new_record["adjustment_number"]) + "a"
                    )

                ch = "b"
                new_record["operation"] = "INSERT"
                new_record["row_key"] = (
                    new_record["original_row_key"] + "-" + str(ind + 1)
                )
                new_record["sub_adjustment_number"] = str(
                    new_record["adjustment_number"]
                ) + chr(ord(ch) + ind)
                new_record["data"] = data[ind]

                new_adjustment_serialized = DatasheetAdjustmentsModel(
                    **new_record
                ).model_dump(by_alias=True)
                try:
                    DatasheetAdjustmentSelector(client_id).create_datasheet_adjustments(
                        new_adjustment_serialized
                    )
                except Exception as exc:
                    error_dict = {"trace_back": traceback.print_exc()}
                    logger.exception(
                        "Excep in Adding adjustment to datasheet with adjustment_id {}".format(
                            new_record["adjustment_id"]
                        ),
                        error_dict,
                    )
                    raise SQLParseError() from exc

            analytics_data = {
                "user_id": audit["updated_by"],
                "event_name": SegmentEvents.EDIT_ADJUSTED_RECORD.value,
                "event_properties": {
                    SegmentProperties.DATABOOK_ID.value: adjustment_payload.databook_id,
                    SegmentProperties.DATASHEET_NAME.value: ds_name,
                    SegmentProperties.ADJUSTMENT_TYPE.value: adjustment_payload.adjustment_type,
                },
            }
            analytics = CoreAnalytics(analyser_type="segment")
            analytics.send_analytics(analytics_data)
            logger.info(
                "Datasheet Adjustment updated successfully for adjustment_id - {}".format(
                    adjustment_payload.adjustment_id
                )
            )
            audit_services.log(
                client_id,
                event_type_code,
                event_key,
                summary,
                updated_by,
                updated_at,
                audit_data,
            )
            return AdjustmentResponse(**create_adjustment_response)

        localized_message = get_localized_message_service(
            LocalizationEngMessages.ADJUSTMENT_DOES_NOT_EXIST.value, client_id
        )
        return AdjustmentErrorResponse(**failure_response, message=localized_message)

    # Update adjustments that are not SPLIT (IGNORE, UPDATE)
    try:
        DatasheetAdjustmentSelector(client_id).update_datasheet_adjustments(
            adjustment_id,
            adjustment_number,
            sub_number,
            audit,
            data,
            time,
            comments,
            is_global,
        )
        audit_services.log(
            client_id,
            event_type_code,
            event_key,
            summary,
            updated_by,
            updated_at,
            audit_data,
        )

        analytics_data = {
            "user_id": audit["updated_by"],
            "event_name": SegmentEvents.EDIT_ADJUSTED_RECORD.value,
            "event_properties": {
                SegmentProperties.DATABOOK_ID.value: adjustment_payload.databook_id,
                SegmentProperties.DATASHEET_NAME.value: ds_name,
                SegmentProperties.ADJUSTMENT_TYPE.value: adjustment_payload.adjustment_type,
            },
        }
        analytics = CoreAnalytics(analyser_type="segment")
        analytics.send_analytics(analytics_data)
        logger.info(
            "Datasheet Adjustment updated successfully for adjustment_id - {}".format(
                adjustment_payload.adjustment_id
            )
        )
        return AdjustmentResponse(**create_adjustment_response)

    except Exception as exc:
        error_dict = {"trace_back": traceback.print_exc()}
        logger.exception(
            "Excep in Adding adjustment to datasheet with adjustment_id {}".format(
                adjustment_payload.databook_id
            ),
            error_dict,
        )
        raise SQLParseError() from exc


@transaction.atomic
def fetch_datasheet_adjustment_data(
    client_id: int,
    datasheet_id: uuid.UUID,
    databook_id: uuid.UUID,
    logged_in_user: str,
    apply_datasheet_permissions: bool = True,  # noqa: FBT001, FBT002
) -> GetAdjustmentsResponse | AdjustmentErrorResponse:
    """
    Fetch the adjustment data for a given row key
    """
    set_threadlocal_context(
        {
            "client_id": client_id,
            "datasheet_id": datasheet_id,
        }
    )

    logger.info("BEGIN -> Fetch Datasheet Adjustment data {}".format(datasheet_id))

    try:
        adjustment_data, not_applied_row_keys = get_adjustment_data(
            client_id=client_id,
            databook_id=databook_id,
            datasheet_id=datasheet_id,
            logged_in_user=logged_in_user,
            apply_datasheet_permissions=apply_datasheet_permissions,
        )
        if is_show_dangling_adjustments_enabled(client_id):
            dangling_rows = get_dangling_adjustments(
                client_id, databook_id, datasheet_id
            )
        else:
            dangling_rows = {}
        logger.info("END: Fetch adjustment data")
        return GetAdjustmentsResponse(
            status="SUCCESS",
            adjustment_data=adjustment_data,
            total_adjusted_records=len(adjustment_data),
            not_applied_row_keys=not_applied_row_keys,
            dangling_rows=dangling_rows,
        )
    except PermissionError as ex:
        logger.exception(
            "Permission error while fetching datasheet adjustment data",
        )
        return AdjustmentErrorResponse(
            status="ERROR",
            message="Permission error while fetching datasheet adjustment data: %s"
            % ex,
        )


@log_time_taken()
def get_adjustment_data(  # noqa: PLR0912, PLR0913, PLR0915
    client_id, databook_id, datasheet_id, logged_in_user, apply_datasheet_permissions
):
    """
    This function fetches all the adjustments for a datasheet
    """
    logger.info("Fetching adjustment row keys")
    adjustment_row_keys = DatasheetAdjustmentSelector(
        client_id
    ).get_row_keys_for_adjustments(databook_id, datasheet_id)
    datasheet_last_generation_time = get_datasheet_last_generation_time(
        client_id, databook_id, datasheet_id
    )

    not_applied_row_keys = {
        "original_row_keys": [],
        "adjusted_row_keys": [],
    }

    if None in adjustment_row_keys:
        adjustment_row_keys.remove(None)
    if adjustment_row_keys:
        logger.info(
            "Adjustments present for this datasheet: {}".format(
                len(adjustment_row_keys)
            )
        )
        start_time = time.monotonic()

        if should_use_multi_engine_stormbreaker(client_id):
            ds_reader = DatasheetDataReader(
                client_id,
                databook_id,
                datasheet_id,
                logged_in_user,
                apply_datasheet_permissions,
                compute_strategy="duckdb_fallback_variant_snowflake",
            )
        else:
            ds_reader = DatasheetDataReader(
                client_id,
                databook_id,
                datasheet_id,
                logged_in_user,
                apply_datasheet_permissions,
            )

        try:
            logger.info("BEGIN: Fetch original data")
            data = ds_reader.fetch_data(as_data_frame=True, only_adjustments=True)
            logger.info("END: Fetch original data: {} rows".format(len(data)))
            logger.info(
                "Time taken to fetch original data: %s seconds",
                time.monotonic() - start_time,
            )
        except PermissionError:
            logger.exception("Permission error while fetching datasheet data")
            return {}
        logger.info("BEGIN: Fetch Adjustment data as frame")
        df_adjustment_data_for_datasheet = DatasheetAdjustmentSelector(
            client_id
        ).get_adjustments_for_datasheet_as_frame(
            databook_id, datasheet_id, adjustments_type_filter=None
        )

        # an adjustment is not applied if the knowledge_begin_date is greater than the datasheet_last_generation_time
        not_applied_adjustments = df_adjustment_data_for_datasheet.filter(
            items=["original_row_key", "row_key", "knowledge_begin_date"]
        ).loc[
            df_adjustment_data_for_datasheet["knowledge_begin_date"]
            > datasheet_last_generation_time
        ]

        original_row_keys = list(not_applied_adjustments["original_row_key"].unique())
        adjusted_row_keys = list(not_applied_adjustments["row_key"].unique())

        # used to hightlight new adjustments that are not applied
        not_applied_row_keys["original_row_keys"] = original_row_keys
        # used to hightlight edited adjustments that are not applied
        not_applied_row_keys["adjusted_row_keys"] = adjusted_row_keys

        logger.info("END: Fetch Adjustment data as frame")
        if len(df_adjustment_data_for_datasheet.index) != 0 and len(data.index) != 0:
            # making a copy of the dataFrame for future use of original dataFrame
            original_data = data.copy()
            adjustment_data = {}
            cols_to_be_added = [
                "comments",
                "adjustment_number",
                "adjustment_id",
                "sub_adjustment_number",
                "is_global",
                "row_key",
            ]
            logger.info("BEGIN: Update records operation")

            employee_email_ids = set(
                df_adjustment_data_for_datasheet.additional_details.apply(
                    lambda additional_details: additional_details.get("updated_by")
                ).to_list()
            )
            records = list(
                EmployeeAccessor(
                    client_id=client_id
                ).get_employees_name_as_dict_with_full_name(
                    employee_email_ids=employee_email_ids
                )
            )
            email_id_map = {
                record["employee_email_id"]: record["full_name"] for record in records
            }

            # ------------ UPDATE records Operation ------------

            # Filtering out only the UPDATE operation from all the adjustments
            update_operations_df = df_adjustment_data_for_datasheet[
                df_adjustment_data_for_datasheet.operation == "UPDATE"
            ]

            # converting UPDATE adjustments dataFrame into list of dicts
            #  and Iterating over each adjustment
            update_operations = update_operations_df.to_dict("records")
            for update_operation in update_operations:
                row_key = update_operation["original_row_key"]
                # constructing the adjustment details dictionary for each entry.
                updated_by = update_operation["additional_details"].get(
                    "updated_by", ""
                )
                adjustment_details = {
                    "type": "UPDATE",
                    "adjustment_date": update_operation["knowledge_begin_date"],
                    "applied_by": (
                        f"{email_id_map[updated_by]} ({updated_by})"
                        if email_id_map.get(updated_by)
                        else None
                    ),
                }
                for column in cols_to_be_added:
                    adjustment_details[column] = update_operation[column]
                original_values = {}
                for key in update_operation["data"]:
                    record = data.loc[data["row_key"] == row_key, key]
                    if not record.empty:
                        original_values[key] = data.loc[
                            data["row_key"] == row_key, key
                        ].iloc[0]
                # updating all the cells which matches the row key condition
                adjustment_id = str(update_operation["adjustment_id"])
                keys_list = ["is_adjustment", "adjustment_details", "original_values"]
                values_list = [True, adjustment_details, original_values]
                for key, value in update_operation["data"].items():
                    keys_list.append(key)
                    values_list.append(value)
                data.loc[data.row_key == row_key, keys_list] = values_list
                data_to_append = data[data.row_key == row_key].to_dict("records")

                if adjustment_id not in adjustment_data:
                    adjustment_data[adjustment_id] = []
                if data_to_append:
                    adjustment_data[adjustment_id].append(data_to_append[0])

            logger.info("END: Update records operation")

            logger.info("BEGIN: Insert records operation")
            # INSERT records Operation
            # Filtering out only the INSERT operation from all the adjustments
            insert_operations_df = df_adjustment_data_for_datasheet[
                df_adjustment_data_for_datasheet.operation == "INSERT"
            ]
            # converting INSERT adjustments dataFrame into list of dicts
            #  and Iterating over each adjustment
            insert_operations = insert_operations_df.to_dict("records")

            for insert_operation in insert_operations:
                row_key = insert_operation["original_row_key"]
                if row_key not in original_data.row_key.unique():
                    continue
                all_split_values_for_row_key = {}
                records_with_row_key = insert_operations_df[
                    insert_operations_df["original_row_key"] == row_key
                ]
                records_with_row_key = records_with_row_key.to_dict("records")

                for idx, record in enumerate(records_with_row_key):
                    if record["data"]:
                        all_split_values_for_row_key[idx] = record["data"]
                    else:
                        all_split_values_for_row_key[idx] = {}

                updated_by = insert_operation["additional_details"].get(
                    "updated_by", ""
                )

                adjustment_details = {
                    "type": "SPLIT",
                    "split_values": all_split_values_for_row_key,
                    "adjustment_date": insert_operation["knowledge_begin_date"],
                    "applied_by": (
                        f"{email_id_map[updated_by]} ({updated_by})"
                        if email_id_map.get(updated_by)
                        else None
                    ),
                }
                for column in cols_to_be_added:
                    adjustment_details[column] = insert_operation[column]
                original_record = original_data[original_data["row_key"] == row_key]
                original_values = {}
                if not original_record.empty:
                    for key in insert_operation["data"]:
                        original_values[key] = original_record[key].iloc[0]
                original_record["is_adjustment"] = True
                original_record["adjustment_details"] = [adjustment_details]
                original_record["original_values"] = [original_values]
                original_record["row_key"] = insert_operation["row_key"]
                adjustment_id = str(insert_operation["adjustment_id"])

                for key, value in insert_operation["data"].items():
                    original_record[key] = value
                data = pd.concat(
                    [data, pd.DataFrame(original_record)], ignore_index=True
                )
                if adjustment_id not in adjustment_data:
                    adjustment_data[adjustment_id] = []

                adjustment_data[adjustment_id].append(
                    original_record.to_dict("records")[0]
                )
            logger.info("END: Insert records operation")
            logger.info("BEGIN: Ignore records operation")
            # HANDLING IGNORE ADJUSTMENTS
            # Filtering out only the IGNORE operation from all the adjustments
            ignore_operations_df = df_adjustment_data_for_datasheet[
                df_adjustment_data_for_datasheet.operation == "IGNORE"
            ]
            # converting Ignore adjustments dataFrame into list of dicts
            #  and Iterating over each adjustment
            ignore_operations = ignore_operations_df.to_dict("records")
            for ignore_operation in ignore_operations:
                original_row_key = ignore_operation["original_row_key"]
                if original_row_key not in original_data.row_key.unique():
                    continue

                updated_by = ignore_operation["additional_details"].get(
                    "updated_by", ""
                )

                adjustment_details = {
                    "type": "IGNORE",
                    "adjustment_date": ignore_operation["knowledge_begin_date"],
                    "applied_by": (
                        f"{email_id_map[updated_by]} ({updated_by})"
                        if email_id_map.get(updated_by)
                        else None
                    ),
                }
                for column in cols_to_be_added:
                    adjustment_details[column] = ignore_operation[column]
                data_to_append = data[data.row_key == original_row_key].to_dict(
                    "records"
                )
                if data_to_append:
                    data_to_append[0]["is_adjustment"] = True
                    data_to_append[0]["adjustment_details"] = adjustment_details
                    data_to_append[0]["original_values"] = None

                    adjustment_id = str(ignore_operation["adjustment_id"])
                    if adjustment_id not in adjustment_data:
                        adjustment_data[adjustment_id] = []
                    adjustment_data[adjustment_id].append(data_to_append[0])

            logger.info("END: Ignore records operation")
            replace_nan_nat(
                adjustment_data,
                skip_keys=[
                    "adjustment_details",
                    "original_values",
                    "row_key",
                    "is_adjustment",
                ],
            )
            return adjustment_data, not_applied_row_keys

        return {}, not_applied_row_keys

    logger.info("No adjustments for this datasheet")
    return {}, not_applied_row_keys


@log_time_taken()
def get_dangling_adjustments(client_id, databook_id, datasheet_id):
    dangling_rows = {}
    cols_to_be_added = [
        "comments",
        "adjustment_number",
        "adjustment_id",
        "sub_adjustment_number",
        "is_global",
        "row_key",
    ]
    dangling_adjustments_df = DatasheetAdjustmentSelector(
        client_id
    ).get_dangling_adjustments_as_frame(databook_id, datasheet_id)
    if len(dangling_adjustments_df.index) != 0:
        employee_email_ids = set(
            dangling_adjustments_df.additional_details.apply(
                lambda additional_details: additional_details.get("updated_by")
            ).to_list()
        )
        records = list(
            EmployeeAccessor(
                client_id=client_id
            ).get_employees_name_as_dict_with_full_name(
                employee_email_ids=employee_email_ids
            )
        )
        email_id_map = {
            record["employee_email_id"]: record["full_name"] for record in records
        }
        adjustments_other_then_ignore = dangling_adjustments_df[
            dangling_adjustments_df.operation != "IGNORE"
        ]
        dangling_adjustments_other_then_ignore = adjustments_other_then_ignore.to_dict(
            "records"
        )
        # HANDELING DANGLING ADJUSTMENTS
        datasheet_variables_accessor = DatasheetVariableAccessor(client_id)
        for dangling_adjustment in dangling_adjustments_other_then_ignore:
            adjustment_type = dangling_adjustment["operation"]
            original_row_key = dangling_adjustment["original_row_key"]
            updated_by = dangling_adjustment["additional_details"].get("updated_by", "")
            # Convert data to key, display_name, value structure
            adjusted_data = []
            if adjustment_type == "INSERT":
                split_records = dangling_adjustments_df[
                    dangling_adjustments_df["original_row_key"] == original_row_key
                ].to_dict("records")
                for split_record in split_records:
                    if split_record.get("data"):
                        for key, value in split_record["data"].items():
                            display_name_and_data_type_id = datasheet_variables_accessor.get_display_name_from_system_name_latest(
                                key, databook_id, datasheet_id
                            )
                            adjusted_data.append(
                                {
                                    "row_key": split_record["row_key"],
                                    "key": key,
                                    "display_name": (
                                        display_name_and_data_type_id.get(
                                            "display_name", key
                                        )
                                        if display_name_and_data_type_id
                                        else key
                                    ),
                                    "data_type_id": (
                                        display_name_and_data_type_id.get(
                                            "data_type_id", None
                                        )
                                        if display_name_and_data_type_id
                                        else None
                                    ),
                                    "value": value,
                                }
                            )
            else:
                if dangling_adjustment.get("data"):
                    for key, value in dangling_adjustment["data"].items():
                        display_name_and_data_type_id = datasheet_variables_accessor.get_display_name_from_system_name_latest(
                            key, databook_id, datasheet_id
                        )
                        adjusted_data.append(
                            {
                                "key": key,
                                "display_name": (
                                    display_name_and_data_type_id.get(
                                        "display_name", key
                                    )
                                    if display_name_and_data_type_id
                                    else key
                                ),
                                "data_type_id": (
                                    display_name_and_data_type_id.get(
                                        "data_type_id", None
                                    )
                                    if display_name_and_data_type_id
                                    else None
                                ),
                                "value": value,
                            }
                        )

            # Create adjustment details following the same pattern as regular adjustments
            adjustment_details = {
                "type": ("SPLIT" if adjustment_type == "INSERT" else adjustment_type),
                "adjustment_date": dangling_adjustment["knowledge_begin_date"],
                "applied_by": (
                    f"{email_id_map[updated_by]} ({updated_by})"
                    if email_id_map.get(updated_by)
                    else None
                ),
                "adjusted_data": adjusted_data,  # Add the structured data here
                "invalidated_at": dangling_adjustment.get("invalidated_at"),
            }
            # Add other fields from cols_to_be_added like regular adjustments
            for column in cols_to_be_added:
                adjustment_details[column] = dangling_adjustment[column]

            # Create empty record structure similar to regular adjustments
            data_to_append = {
                "row_key": dangling_adjustment["row_key"],
                "original_row_key": original_row_key,
                "dangling_reason": dangling_adjustment.get("dangling_reason"),
            }

            # Set adjustment flags and details like regular adjustments
            data_to_append["is_adjustment"] = True
            data_to_append["adjustment_details"] = adjustment_details

            adjustment_id = str(dangling_adjustment["adjustment_id"])
            if adjustment_id not in dangling_rows:
                dangling_rows[adjustment_id] = []
            dangling_rows[adjustment_id].append(data_to_append)

        dangling_adjustments_ignore = dangling_adjustments_df[
            dangling_adjustments_df.operation == "IGNORE"
        ]
        dangling_adjustments_ignore = dangling_adjustments_ignore.to_dict("records")
        for dangling_adjustment in dangling_adjustments_ignore:
            adjustment_type = dangling_adjustment["operation"]
            original_row_key = dangling_adjustment["original_row_key"]
            updated_by = dangling_adjustment["additional_details"].get("updated_by", "")
            # Convert data to key, display_name, value structure
            adjusted_data = []
            # Create adjustment details following the same pattern as regular adjustments
            adjustment_details = {
                "type": adjustment_type,
                "adjustment_date": dangling_adjustment["knowledge_begin_date"],
                "applied_by": (
                    f"{email_id_map[updated_by]} ({updated_by})"
                    if email_id_map.get(updated_by)
                    else None
                ),
                "adjusted_data": adjusted_data,  # Add the structured data here
                "invalidated_at": dangling_adjustment.get("invalidated_at"),
            }
            # Add other fields from cols_to_be_added like regular adjustments
            for column in cols_to_be_added:
                adjustment_details[column] = dangling_adjustment[column]

            # Create empty record structure similar to regular adjustments
            data_to_append = {
                "row_key": dangling_adjustment["row_key"],
                "original_row_key": original_row_key,
                "dangling_reason": dangling_adjustment.get("dangling_reason"),
            }

            # Set adjustment flags and details like regular adjustments
            data_to_append["is_adjustment"] = True
            data_to_append["adjustment_details"] = adjustment_details

            adjustment_id = str(dangling_adjustment["adjustment_id"])
            if adjustment_id not in dangling_rows:
                dangling_rows[adjustment_id] = []
            dangling_rows[adjustment_id].append(data_to_append)
        return dangling_rows
    return {}
