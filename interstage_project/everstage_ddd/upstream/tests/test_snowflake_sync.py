from datetime import datetime
from unittest.mock import MagicMock, patch
from uuid import uuid4

import pytest

from everstage_ddd.upstream.snowflake_sync import UpstreamSnowflakeSync
from everstage_ddd.upstream.upstream_models import UpstreamSyncParams


@pytest.fixture
def upstream_sync_params():
    return UpstreamSyncParams(
        client_id=1,
        e2e_sync_run_id=uuid4(),
        sync_run_id=uuid4(),
        source_data_table="public.source_table",
        custom_object_id=123,
        integration_id=uuid4(),
        sync_mode="all",
        changes_sync_time=datetime(2024, 1, 1),  # noqa: DTZ001
        is_source_data_as_variant=False,
        transformation_logic=None,
        primary_kd=datetime(2024, 1, 2),  # noqa: DTZ001
    )


@patch(
    "commission_engine.accessors.custom_object_accessor.CustomObjectVariableAccessor.get_custom_variables_by_id",
    return_value=[{"system_name": "field1", "data_type__data_type": "STRING"}],
)
@patch(
    "commission_engine.accessors.custom_object_accessor.CustomObjectAccessor.get_object_fields_by_id",
    return_value=[{"primary_key": ["field1"], "snapshot_key": ["field1"]}],
)
@patch("interstage_project.threadlocal_log_context.set_threadlocal_context")
@patch.object(UpstreamSnowflakeSync, "validate_source_table", return_value=None)
@patch(
    "commission_engine.utils.databook_utils.resolve_snowflake_data_type",
    return_value="DATE",
)
@patch(
    "commission_engine.utils.general_utils.get_custom_object_data_table_name",
    return_value="custom_object_data_1_123",
)
@patch(
    "interstage_project.global_utils.general_utils.uuid_in_snakecase",
    side_effect=lambda x: str(x).replace("-", "_"),
)
def test_invalidate_all_valid_custom_object_manual_upload_data_rows_success(  # noqa: PLR0913
    mock_uuid_in_snakecase,
    mock_get_custom_object_data_table_name,
    mock_resolve_snowflake_data_type,
    mock_validate_source_table,
    mock_set_threadlocal_context,
    mock_get_object_fields_by_id,
    mock_get_custom_variables_by_id,
    upstream_sync_params,
):
    sync = UpstreamSnowflakeSync(upstream_sync_params)
    mock_session = MagicMock()
    mock_sql = mock_session.sql
    mock_collect = mock_sql.return_value.collect
    # Should not raise
    sync.invalidate_all_valid_custom_object_manual_upload_data_rows(
        mock_session, datetime(2024, 1, 3)  # noqa: DTZ001
    )
    # Check that sql was called with expected params
    assert mock_sql.call_count == 1
    _, kwargs = mock_sql.call_args
    assert kwargs["params"] == [datetime(2024, 1, 3), 1, 123]  # noqa: DTZ001
    assert mock_collect.called


@patch(
    "commission_engine.accessors.custom_object_accessor.CustomObjectVariableAccessor.get_custom_variables_by_id",
    return_value=[{"system_name": "field1", "data_type__data_type": "STRING"}],
)
@patch(
    "commission_engine.accessors.custom_object_accessor.CustomObjectAccessor.get_object_fields_by_id",
    return_value=[{"primary_key": ["field1"], "snapshot_key": ["field1"]}],
)
@patch("interstage_project.threadlocal_log_context.set_threadlocal_context")
@patch.object(UpstreamSnowflakeSync, "validate_source_table", return_value=None)
@patch(
    "commission_engine.utils.databook_utils.resolve_snowflake_data_type",
    return_value="DATE",
)
@patch(
    "commission_engine.utils.general_utils.get_custom_object_data_table_name",
    return_value="custom_object_data_1_123",
)
@patch(
    "interstage_project.global_utils.general_utils.uuid_in_snakecase",
    side_effect=lambda x: str(x).replace("-", "_"),
)
def test_invalidate_all_valid_custom_object_manual_upload_data_rows_exception(  # noqa: PLR0913
    mock_uuid_in_snakecase,
    mock_get_custom_object_data_table_name,
    mock_resolve_snowflake_data_type,
    mock_validate_source_table,
    mock_set_threadlocal_context,
    mock_get_object_fields_by_id,
    mock_get_custom_variables_by_id,
    upstream_sync_params,
):
    sync = UpstreamSnowflakeSync(upstream_sync_params)
    mock_session = MagicMock()
    mock_sql = mock_session.sql
    # Simulate exception on collect
    mock_sql.return_value.collect.side_effect = Exception("Snowflake error")
    with pytest.raises(Exception, match="Snowflake error"):
        sync.invalidate_all_valid_custom_object_manual_upload_data_rows(
            mock_session, datetime(2024, 1, 3)  # noqa: DTZ001
        )
    # Should log exception and re-raise
    assert mock_sql.call_count == 1
