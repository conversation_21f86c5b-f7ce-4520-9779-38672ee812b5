from django.core.exceptions import EmptyResultSet
from django.db.models import Q
from spm.models.docusign_models import Docusign


# This accessor is used to access the Docusign model for the ICM module.
class DocusignAccessor:
    def __init__(self, client_id):
        self.client_id = client_id

    def client_aware(self):
        # If the additional_details is null or does not have the module key, it will be considered as ICM module.
        return Docusign.objects.filter(client=self.client_id).filter(
            Q(additional_details__isnull=True)
            | ~Q(additional_details__has_key="module")
        )

    def invalidate_all_client_records(self, time):
        self.client_aware().filter(knowledge_end_date__isnull=True).update(
            knowledge_end_date=time, is_deleted=True
        )

    def delete_all_client_records(self):
        self.client_aware().delete()

    def client_kd_aware(self):
        return self.client_aware().filter(
            is_deleted=False, knowledge_end_date__isnull=True
        )

    def get_oauth_details(self, email_id):
        return self.client_kd_aware().filter(login_email_id=email_id).first()

    def get_docusign_details(self, email_id):
        return self.client_kd_aware().filter(login_email_id=email_id).first()

    def get_account_id(self, email_id):
        data = (
            self.client_kd_aware()
            .filter(email_id=email_id)
            .values("account_id")
            .first()
        )
        if data:
            return data.get("account_id", None)
        else:
            raise EmptyResultSet("Error fetching account_id of the user")

    def invalidate(
        self,
        account_id,
        knowledge_date,
        docusign_email_id=None,
        login_email_id=None,
    ):
        query_set = self.client_kd_aware().filter(account_id=account_id)
        if query_set.exists() and login_email_id:
            query_set = query_set.filter(login_email_id=login_email_id)
        if query_set.exists() and docusign_email_id:
            query_set = query_set.filter(email_id=docusign_email_id)
        if query_set.exists():
            query_set.update(knowledge_end_date=knowledge_date)

    def invalidate_user(self, email_id, knowledge_date):
        self.client_kd_aware().filter(login_email_id=email_id).update(
            knowledge_end_date=knowledge_date
        )

    def delete_user_account(self, prev_record, login_email_id, knowledge_date):
        if prev_record:
            self.invalidate_user(login_email_id, knowledge_date)
            prev_record.knowledge_begin_date = knowledge_date
            prev_record.is_deleted = True
            self.create(prev_record)

    def create(self, record):
        record.pk = None
        record.save()

    def update_user_id(self, user_id, email_id, knowledge_date):
        previous_record = self.get_docusign_details(email_id)
        if previous_record:
            self.invalidate_user(email_id, knowledge_date)
            previous_record.knowledge_begin_date = knowledge_date
            previous_record.user_id = user_id
            self.create(previous_record)

    def get_docusign_data_by_account(self, login_email_id, account_id):
        query_set = self.client_kd_aware().filter(
            login_email_id=login_email_id, account_id=account_id
        )
        return query_set.first() if query_set else None

    def docusign_connected_account_count(self):
        return self.client_kd_aware().count()

    def get_docusign_distinct_active_account(self, current_time):
        return list(
            self.client_kd_aware()
            .filter(refresh_token_expires_at__gt=current_time)
            .values("account_id", "login_email_id", "client_id")
            .order_by("account_id", "-refresh_token_expires_at")
            .distinct("account_id")
        )
