import json
import time
from io import Bytes<PERSON>
from unittest.mock import MagicMock, patch

import pandas as pd
import pytest
from data_import_validator_mock_data import *

from spm.services.data_import_services.data_import_validator import (
    CustomObjectDataUploader,
)


def get_co_data(extracted_keys):
    """
    Mock function to get_custom_object_data
    We fetch the custom_object_data from the csv_file "custom_object_data_6.csv"
    """
    df = pd.read_csv(
        "spm/tests/data_import_services/mock_co_data.csv",
        quotechar='"',
    )
    df = df.query(f"custom_object_id == 1 and row_key in {extracted_keys}")
    df_mock_data = df.filter(["row_key", "data"]).to_dict("records")
    for data_record in df_mock_data:
        data_record["data"] = json.loads(data_record["data"])

    return df_mock_data


@pytest.fixture
def get_file_obj():
    """
    Fixture to read the data from the file and convert into bytes oject
    """
    file_name = "spm/tests/data_import_services/import_co_test_data.csv"
    file_obj = BytesIO()

    with open(file_name, mode="rb") as file:
        file_obj = BytesIO(file.read())
    file.close()
    return file_obj


@pytest.fixture
def get_keys_config_1(get_file_obj):
    """
    Fixture to extract the primary keys from the csv_file inorder to fetch data of those keys from custom_object_data
    """
    client_id = 1
    co_obj_id = 1
    config = config1

    co_data_uploader = CustomObjectDataUploader(
        client_id,
        co_obj_id,
        config,
        get_file_obj,
    )
    co_data_uploader.extract_required_data()
    extracted_primary_keys = co_data_uploader.primary_and_snapshot_key_validator()

    return extracted_primary_keys


@pytest.mark.django_db
@patch("spm.services.data_import_services.data_import_validator.get_custom_object_data")
def test_create_update(mock_get_custom_object_data, get_keys_config_1, get_file_obj):
    """
    Test function to test upsert custom_object_data
    """
    client_id = 1
    co_obj_id = 1
    config = config1
    mock_get_custom_object_data.return_value = get_co_data(get_keys_config_1)

    custom_obj_uploader = CustomObjectDataUploader(
        client_id,
        co_obj_id,
        config,
        get_file_obj,
    )

    result = custom_obj_uploader.get_records_for_create_update()

    success_record_1 = {
        "co_2_invoice_name": "01H50HRB533SRYD9NY7E78ZCGB",
        "co_2_invoice_start_date": "05-Jul-2023",
        "co_2_invoice_end_date": "19-Aug-2023",
        "co_2_deal_name": "0",
        "co_2_payee_email": "<EMAIL>",
        "co_2_invoice_amount": "75306.62",
        "co_2_comm_percent": "71",
        "co_2_comm_bool": "true",
        "row_key": "01h50hrb533sryd9ny7e78zcgb#:::#0",
        "snapshot_row_key": "01h50hrb533sryd9ny7e78zcgb",
    }

    success_record_2 = {
        "co_2_invoice_name": "01H50HRB5RGBYMZ4QPTAAJ7SG2",
        "co_2_invoice_start_date": "23-Jul-2023",
        "co_2_invoice_end_date": "22-Aug-2023",
        "co_2_deal_name": "울란바토르",
        "co_2_payee_email": "<EMAIL>",
        "co_2_invoice_amount": "73123.39",
        "co_2_comm_percent": "191",
        "co_2_comm_bool": "FALSE",
        "row_key": "01h50hrb5rgbymz4qptaaj7sg2#:::#울란바토르",
        "snapshot_row_key": "01h50hrb5rgbymz4qptaaj7sg2",
    }

    # We use set of keys to verify the successful records because we identify the sucessful recods
    # by eliminating the error_records and ignored_records by a set operation
    # Since set is not a ordered data structure, the order of the successful records will vary at each rum.
    # While the order of error records and ignored records are preserved as list
    create_keys = {
        '01h50hrb5qx32xzef9ean0p4qb#:::#<>?:"{}|_+',
        "01h50hrb4wncqv5jrptg0d63xz#:::#ω≈ç√∫˜µ≤≥÷",
        "01h50hrb5bp92afsw7q3mpht5t#:::#<svg><script>0<1>alert('xss')</script>",
        "01h50hrb57qc789hfvhhas6gcj#:::#'",
        "01h50hrb533sryd9ny7e78zcgb#:::#0",
        "01h50hrb4ha525q7d0233v8vvh#:::#社會科學院語學研究所",
        "01h50hrb5m0gc1zj9d25c8qwdc#:::#/dev/null; touch /tmp/blns.fail ; echo",
        "01h50hrb45s0dakv0xyz0yajrm#:::#123",
        "01h50hrb4nd8hp16g969ftg5wz#:::#✋🏿 💪🏿 👐🏿 🙌🏿 👏🏿 🙏🏿",
        "01h50hrb5rgbymz4qptaaj7sg2#:::#울란바토르",
        "01h50hrb69b6f2ce0a87tatn9t#:::#../../../../../../../../../../../etc/hosts",
        "01h50hrb4ptzsbfwjace4xvtbs#:::#null",
        "01h50hrb41abwxy63hb0crnekt#:::#nil",
        "01h50hrb504yh5k1n2kmzzqwpj#:::#() { 0; }; touch /tmp/blns.shellshock1.fail;",
        "01h50hrb6d4y9djzgqrfwxfjw5#:::#ثم نفس سقطت وبالتحديد،, جزيرتي باستخدام أن دنو. إذ هنا؟ الستار وتنصيب كان. أهّل ايطاليا، بريطانيا-فرنسا قد أخذ. سليمان، إتفاقية بين ما, يذكر الحدود أي بعد, معاملة بولندا، الإطلاق عل إيو.",
        "01h50hrb4mt7mepcs3zp7j0jyh#:::#-1",
        "01h50hrb66hp6j9sszmk8p33e8#:::#1.00e+02",
        "01h50hrb5px711q8y4tz11cpdm#:::#\u180e",
        "01h50hrb4s5xa6ncejmg4tdmge#:::#1'; drop table users--",
        "01h50hrb5vr41f1nmdav1cxfpd#:::#❤️ 💔 💌 💕 💞 💓 💗 💖 💘 💝 💟 💜 💛 💚 💙",
        "01h50hrb61f0m0jkk03dt40e4j#:::#1;drop table users",
        "01h50hrb3xcvy57441adzcs6yf#:::#() { 0; }; touch /tmp/blns.shellshock1.fail;",
        "01h50hrb6es097r6mtgsfq19st#:::#⁰⁴⁵₀₁₂",
        "01h50hrb58hm3h0a6007324zs1#:::#ﾟ･✿ヾ╲(｡◕‿◕｡)╱✿･ﾟ",
        "01h50hrb4wf8q3va1nt8b77kkr#:::#$\xa01.00",
        "01h50hrb402arqkhz95r19bnfb#:::#/dev/null; touch /tmp/blns.fail ; echo",
        "01h50hrb6xptpwmqavqjpag0w5#:::#田中さんにあげて下さい",
        "01h50hrb4ymz6ef5d62ycf06xa#:::#👩🏽",
        "01h50hrb5jxxyrd94c2v9xkd9c#:::#-1.00e+02",
        "01h50hrb51yaf4e7cxjjyzpbee#:::#0/0",
        "01h50hrb5d9gqgbtd5gvyc37nd#:::#123",
        "01h50hrb4ry4sta29etvbvz0ce#:::#-1",
        "01h50hrb4z9wje9ttyk47f5c79#:::#z̮̞̠͙͔ͅḁ̗̞͈̻̗ḷ͙͎̯̹̞͓g̻o̭̗̮",
        "01h50hrb3vn43j1895mj5zsbne#:::#(╯°□°）╯︵ ┻━┻)",
        "01h50hrb532j2qa5tgstbd4506#:::#1/0",
        "01h50hrb5grjsbwcbfaezhdb3c#:::#,./;'[]\\-=",
        "01h50hrb6essrjc5wqcdskcaj9#:::#-1",
        "01h50hrb689nzpdy416hn86mx7#:::#<svg><script>0<1>alert('xss')</script>",
        "01h50hrb60bb6m737cs2tg4hce#:::#null",
        "01h50hrb431b5ay9pqemfayx7a#:::#¸˛ç◊ı˜â¯˘¿",
        "01h50hrb3yx4rcfj0zdhyced1p#:::#👩🏽",
        "01h50hrb5dsjddc8rdkwyrytaz#:::#-1.00e+02",
        "01h50hrb636nmjv1y7exxrzgej#:::#1",
        "01h50hrb42jtz6nqs5z76ykw3s#:::#<img src=x onerror=alert('hi') />",
        "01h50hrb5wx7k1vd8ysnzg1vye#:::#123",
        "01h50hrb59fq0mgm6pqvhsh4qb#:::#田中さんにあげて下さい",
        "01h50hrb422s81cdw5mabfq2dz#:::#!@#$%^&*()",
    }
    update_record = {
        "01h50hrb4jn8sd9se0wpce47fvv#:::#1.00e+02": {
            "co_2_invoice_name": "01H50HRB4JN8SD9SE0WPCE47FVV",
            "co_2_invoice_start_date": "13-Jul-2023",
            "co_2_invoice_end_date": "10-Aug-2023",
            "co_2_deal_name": "1.00E+02",
            "co_2_payee_email": "<EMAIL>",
            "co_2_invoice_amount": "41971.01",
            "co_2_comm_percent": "301",
            "co_2_comm_bool": "",
            "row_key": "01h50hrb4jn8sd9se0wpce47fvv#:::#1.00e+02",
            "snapshot_row_key": "01h50hrb4jn8sd9se0wpce47fvv",
        },
        "01h50hrb6rdsanvkatbembtghk#:::#null": {
            "co_2_invoice_name": "01H50HRB6RDSANVKATBEMBTGHK",
            "co_2_invoice_start_date": "02-Jul-2023",
            "co_2_invoice_end_date": "05-Aug-2023",
            "co_2_deal_name": "null",
            "co_2_payee_email": "<EMAIL>",
            "co_2_invoice_amount": "39141.82",
            "co_2_comm_percent": "",
            "co_2_comm_bool": "",
            "row_key": "01h50hrb6rdsanvkatbembtghk#:::#null",
            "snapshot_row_key": "01h50hrb6rdsanvkatbembtghk",
        },
        "01h50hrb46dvwc7wypf1025wpn#:::#-1/2": {
            "co_2_invoice_name": "01H50HRB46DVWC7WYPF1025WPN",
            "co_2_invoice_start_date": "13-Jul-2023",
            "co_2_invoice_end_date": "08-Aug-2023",
            "co_2_deal_name": "-1/2",
            "co_2_payee_email": "<EMAIL>",
            "co_2_invoice_amount": "81126.72",
            "co_2_comm_percent": "336",
            "co_2_comm_bool": "TrUe",
            "row_key": "01h50hrb46dvwc7wypf1025wpn#:::#-1/2",
            "snapshot_row_key": "01h50hrb46dvwc7wypf1025wpn",
        },
    }
    result_success = result["successful_records"]["csv_records_to_create"]
    result_update = result["successful_records"]["csv_records_to_update"]
    result_db_records = result["successful_records"]["db_records"]
    expected_set = set()
    for record in result_success:
        assert record["row_key"] in create_keys
        expected_set.add(record["row_key"])
        if record["row_key"] == "01h50hrb533sryd9ny7e78zcgb#:::#0":
            assert record == success_record_1
        if record["row_key"] == "01h50hrb5rgbymz4qptaaj7sg2#:::#울란바토르":
            assert record == success_record_2
    assert expected_set == create_keys
    for record in result_update:
        assert record["row_key"] in update_record
        assert record == update_record[record["row_key"]]
    assert "01h50hrb4jn8sd9se0wpce47fvv#:::#1.00e+02" in result_db_records
    print("result_error_records", result["error_records"])
    assert result["error_records"] == error_records[0]

    assert result["total_records_count"] == 95
    assert 95 == (
        len(result_success)
        + len(result["ignored_records"])
        + len(result["error_records"])
        + len(result_update)
    )


@pytest.mark.django_db
@patch("spm.services.data_import_services.data_import_validator.get_row_keys")
def test_create(mock_get_custom_object_data, get_keys_config_1, get_file_obj):
    """
    Test dunction to test create custom_object_data
    """
    client_id = 1
    co_obj_id = 1
    config = config1
    mock_get_custom_object_data.return_value = [
        data["row_key"] for data in get_co_data(get_keys_config_1)
    ]
    custom_obj_uploader = CustomObjectDataUploader(
        client_id,
        co_obj_id,
        config,
        get_file_obj,
    )

    result = custom_obj_uploader.get_records_for_create()

    ignored_records = [
        {
            "co_2_invoice_name": "01H50HRB46DVWC7WYPF1025WPN",
            "co_2_invoice_start_date": "13-Jul-2023",
            "co_2_invoice_end_date": "08-Aug-2023",
            "co_2_deal_name": "-1/2",
            "co_2_payee_email": "<EMAIL>",
            "co_2_invoice_amount": "81126.72",
            "co_2_comm_percent": "336",
            "co_2_comm_bool": "TrUe",
            "reason": "Ignored because this is existing data, and the chosen import type is Create new data.",
        },
        {
            "co_2_invoice_name": "01H50HRB4JN8SD9SE0WPCE47FVV",
            "co_2_invoice_start_date": "13-Jul-2023",
            "co_2_invoice_end_date": "10-Aug-2023",
            "co_2_deal_name": "1.00E+02",
            "co_2_payee_email": "<EMAIL>",
            "co_2_invoice_amount": "41971.01",
            "co_2_comm_percent": "301",
            "co_2_comm_bool": "",
            "reason": "Ignored because this is existing data, and the chosen import type is Create new data.",
        },
        {
            "co_2_invoice_name": "01H50HRB6RDSANVKATBEMBTGHK",
            "co_2_invoice_start_date": "02-Jul-2023",
            "co_2_invoice_end_date": "05-Aug-2023",
            "co_2_deal_name": "null",
            "co_2_payee_email": "<EMAIL>",
            "co_2_invoice_amount": "39141.82",
            "co_2_comm_percent": "",
            "co_2_comm_bool": "",
            "reason": "Ignored because this is existing data, and the chosen import type is Create new data.",
        },
    ]

    # We use set of keys to verify the successful records because we identify the sucessful recods
    # by eliminating the error_records and ignored_records by a set operation
    # Since set is not a ordered data structure, the order of the successful records will vary at each rum.
    # While the order of error records and ignored records are preserved as list
    sucess_records_set = {
        "01h50hrb5px711q8y4tz11cpdm#:::#\u180e",
        "01h50hrb5d9gqgbtd5gvyc37nd#:::#123",
        "01h50hrb689nzpdy416hn86mx7#:::#<svg><script>0<1>alert('xss')</script>",
        "01h50hrb41abwxy63hb0crnekt#:::#nil",
        "01h50hrb402arqkhz95r19bnfb#:::#/dev/null; touch /tmp/blns.fail ; echo",
        "01h50hrb5m0gc1zj9d25c8qwdc#:::#/dev/null; touch /tmp/blns.fail ; echo",
        "01h50hrb4ymz6ef5d62ycf06xa#:::#👩🏽",
        "01h50hrb57qc789hfvhhas6gcj#:::#'",
        "01h50hrb66hp6j9sszmk8p33e8#:::#1.00e+02",
        '01h50hrb5qx32xzef9ean0p4qb#:::#<>?:"{}|_+',
        "01h50hrb3vn43j1895mj5zsbne#:::#(╯°□°）╯︵ ┻━┻)",
        "01h50hrb431b5ay9pqemfayx7a#:::#¸˛ç◊ı˜â¯˘¿",
        "01h50hrb4wncqv5jrptg0d63xz#:::#ω≈ç√∫˜µ≤≥÷",
        "01h50hrb6es097r6mtgsfq19st#:::#⁰⁴⁵₀₁₂",
        "01h50hrb59fq0mgm6pqvhsh4qb#:::#田中さんにあげて下さい",
        "01h50hrb4z9wje9ttyk47f5c79#:::#z̮̞̠͙͔ͅḁ̗̞͈̻̗ḷ͙͎̯̹̞͓g̻o̭̗̮",
        "01h50hrb5grjsbwcbfaezhdb3c#:::#,./;'[]\\-=",
        "01h50hrb6xptpwmqavqjpag0w5#:::#田中さんにあげて下さい",
        "01h50hrb5bp92afsw7q3mpht5t#:::#<svg><script>0<1>alert('xss')</script>",
        "01h50hrb4mt7mepcs3zp7j0jyh#:::#-1",
        "01h50hrb5wx7k1vd8ysnzg1vye#:::#123",
        "01h50hrb4nd8hp16g969ftg5wz#:::#✋🏿 💪🏿 👐🏿 🙌🏿 👏🏿 🙏🏿",
        "01h50hrb422s81cdw5mabfq2dz#:::#!@#$%^&*()",
        "01h50hrb5vr41f1nmdav1cxfpd#:::#❤️ 💔 💌 💕 💞 💓 💗 💖 💘 💝 💟 💜 💛 💚 💙",
        "01h50hrb60bb6m737cs2tg4hce#:::#null",
        "01h50hrb45s0dakv0xyz0yajrm#:::#123",
        "01h50hrb5dsjddc8rdkwyrytaz#:::#-1.00e+02",
        "01h50hrb4ry4sta29etvbvz0ce#:::#-1",
        "01h50hrb4ha525q7d0233v8vvh#:::#社會科學院語學研究所",
        "01h50hrb4ptzsbfwjace4xvtbs#:::#null",
        "01h50hrb51yaf4e7cxjjyzpbee#:::#0/0",
        "01h50hrb636nmjv1y7exxrzgej#:::#1",
        "01h50hrb4wf8q3va1nt8b77kkr#:::#$\xa01.00",
        "01h50hrb532j2qa5tgstbd4506#:::#1/0",
        "01h50hrb5jxxyrd94c2v9xkd9c#:::#-1.00e+02",
        "01h50hrb5rgbymz4qptaaj7sg2#:::#울란바토르",
        "01h50hrb3yx4rcfj0zdhyced1p#:::#👩🏽",
        "01h50hrb58hm3h0a6007324zs1#:::#ﾟ･✿ヾ╲(｡◕‿◕｡)╱✿･ﾟ",
        "01h50hrb533sryd9ny7e78zcgb#:::#0",
        "01h50hrb504yh5k1n2kmzzqwpj#:::#() { 0; }; touch /tmp/blns.shellshock1.fail;",
        "01h50hrb3xcvy57441adzcs6yf#:::#() { 0; }; touch /tmp/blns.shellshock1.fail;",
        "01h50hrb6d4y9djzgqrfwxfjw5#:::#ثم نفس سقطت وبالتحديد،, جزيرتي باستخدام أن دنو. إذ هنا؟ الستار وتنصيب كان. أهّل ايطاليا، بريطانيا-فرنسا قد أخذ. سليمان، إتفاقية بين ما, يذكر الحدود أي بعد, معاملة بولندا، الإطلاق عل إيو.",
        "01h50hrb4s5xa6ncejmg4tdmge#:::#1'; drop table users--",
        "01h50hrb42jtz6nqs5z76ykw3s#:::#<img src=x onerror=alert('hi') />",
        "01h50hrb6essrjc5wqcdskcaj9#:::#-1",
        "01h50hrb61f0m0jkk03dt40e4j#:::#1;drop table users",
        "01h50hrb69b6f2ce0a87tatn9t#:::#../../../../../../../../../../../etc/hosts",
    }

    success_record_1 = {
        "co_2_invoice_name": "01H50HRB636NMJV1Y7EXXRZGEJ",
        "co_2_invoice_start_date": "05-Jul-2023",
        "co_2_invoice_end_date": "24-Aug-2023",
        "co_2_deal_name": "1",
        "co_2_payee_email": "<EMAIL>",
        "co_2_invoice_amount": "15222.16",
        "co_2_comm_percent": "26",
        "co_2_comm_bool": "FALSE",
        "row_key": "01h50hrb636nmjv1y7exxrzgej#:::#1",
        "snapshot_row_key": "01h50hrb636nmjv1y7exxrzgej",
    }
    success_record_2 = {
        "co_2_invoice_name": "01H50HRB4PTZSBFWJACE4XVTBS",
        "co_2_invoice_start_date": "",
        "co_2_invoice_end_date": "16-Aug-2023",
        "co_2_deal_name": "null",
        "co_2_payee_email": "<EMAIL>",
        "co_2_invoice_amount": "68218.98",
        "co_2_comm_percent": "40",
        "co_2_comm_bool": "TRUE",
        "row_key": "01h50hrb4ptzsbfwjace4xvtbs#:::#null",
        "snapshot_row_key": "01h50hrb4ptzsbfwjace4xvtbs",
    }
    expected_set = set()
    for record in result["successful_records"]["csv_records_to_create"]:
        assert record["row_key"] in sucess_records_set
        expected_set.add(record["row_key"])
        if record["row_key"] == "01h50hrb636nmjv1y7exxrzgej#:::#1":
            assert record == success_record_1
        if record["row_key"] == "01h50hrb4ptzsbfwjace4xvtbs#:::#null":
            assert record == success_record_2
    assert expected_set == sucess_records_set
    assert result["ignored_records"] == ignored_records
    assert result["error_records"] == error_records[0]

    assert result["total_records_count"] == 95
    assert 95 == (
        len(result["successful_records"]["csv_records_to_create"])
        + len(result["ignored_records"])
        + len(result["error_records"])
    )


@pytest.mark.django_db
@patch("spm.services.data_import_services.data_import_validator.get_custom_object_data")
def test_update(mock_get_custom_object_data, get_keys_config_1, get_file_obj):
    """
    Test function update custom_object_data
    """
    client_id = 1
    co_obj_id = 1
    config = config1
    mock_get_custom_object_data.return_value = get_co_data(get_keys_config_1)

    custom_obj_uploader = CustomObjectDataUploader(
        client_id,
        co_obj_id,
        config,
        get_file_obj,
    )

    result = custom_obj_uploader.get_records_for_update()

    # We use set of keys to verify the successful records because we identify the successful recods
    # by eliminating the error_records and ignored_records by a set operation
    # Since set is not a ordered data structure, the order of the successful records will vary at each rum.
    # While the order of error records and ignored records are preserved as list

    update_record = {
        "01h50hrb4jn8sd9se0wpce47fvv#:::#1.00e+02": {
            "co_2_invoice_name": "01H50HRB4JN8SD9SE0WPCE47FVV",
            "co_2_invoice_start_date": "13-Jul-2023",
            "co_2_invoice_end_date": "10-Aug-2023",
            "co_2_deal_name": "1.00E+02",
            "co_2_payee_email": "<EMAIL>",
            "co_2_invoice_amount": "41971.01",
            "co_2_comm_percent": "301",
            "co_2_comm_bool": "",
            "row_key": "01h50hrb4jn8sd9se0wpce47fvv#:::#1.00e+02",
            "snapshot_row_key": "01h50hrb4jn8sd9se0wpce47fvv",
        },
        "01h50hrb6rdsanvkatbembtghk#:::#null": {
            "co_2_invoice_name": "01H50HRB6RDSANVKATBEMBTGHK",
            "co_2_invoice_start_date": "02-Jul-2023",
            "co_2_invoice_end_date": "05-Aug-2023",
            "co_2_deal_name": "null",
            "co_2_payee_email": "<EMAIL>",
            "co_2_invoice_amount": "39141.82",
            "co_2_comm_percent": "",
            "co_2_comm_bool": "",
            "row_key": "01h50hrb6rdsanvkatbembtghk#:::#null",
            "snapshot_row_key": "01h50hrb6rdsanvkatbembtghk",
        },
        "01h50hrb46dvwc7wypf1025wpn#:::#-1/2": {
            "co_2_invoice_name": "01H50HRB46DVWC7WYPF1025WPN",
            "co_2_invoice_start_date": "13-Jul-2023",
            "co_2_invoice_end_date": "08-Aug-2023",
            "co_2_deal_name": "-1/2",
            "co_2_payee_email": "<EMAIL>",
            "co_2_invoice_amount": "81126.72",
            "co_2_comm_percent": "336",
            "co_2_comm_bool": "TrUe",
            "row_key": "01h50hrb46dvwc7wypf1025wpn#:::#-1/2",
            "snapshot_row_key": "01h50hrb46dvwc7wypf1025wpn",
        },
    }

    ignored_record_1 = {
        "co_2_invoice_name": "01H50HRB4TYWNFRC17S472XWWM",
        "co_2_invoice_start_date": "24-Jul-2023",
        "co_2_invoice_end_date": "29-Aug-2023",
        "co_2_deal_name": "\u202btest\u202b",
        "co_2_payee_email": "<EMAIL>",
        "co_2_invoice_amount": "32884.22",
        "co_2_comm_percent": "-45.75",
        "co_2_comm_bool": "false\u202c\u202c",
        "reason": "Ignored because this is new data, but the chosen import type is Update existing data.",
    }
    ignored_record_2 = {
        "co_2_invoice_name": "01H50HRB4WF8Q3VA1NT8B77KKR",
        "co_2_invoice_start_date": "28-Jul-2023",
        "co_2_invoice_end_date": "08-Aug-2023",
        "co_2_deal_name": "$\xa01.00",
        "co_2_payee_email": "<EMAIL>",
        "co_2_invoice_amount": "98,599.87",
        "co_2_comm_percent": "324",
        "co_2_comm_bool": "",
        "reason": "Ignored because this is new data, but the chosen import type is Update existing data.",
    }
    result_update = result["successful_records"]

    for record in result_update["csv_records_to_update"]:
        assert record["row_key"] in update_record
        assert record == update_record[record["row_key"]]

    assert ignored_record_1 in result["ignored_records"]
    assert ignored_record_2 in result["ignored_records"]
    assert "01h50hrb4jn8sd9se0wpce47fvv#:::#1.00e+02" in result_update["db_records"]

    assert result["total_records_count"] == 95
    assert 95 == (
        len(result_update["csv_records_to_update"])
        + len(result["ignored_records"])
        + len(result["error_records"])
    )


@pytest.mark.django_db
@patch("spm.services.data_import_services.data_import_validator.get_custom_object_data")
def test_delete(mock_get_custom_object_data, get_keys_config_1, get_file_obj):
    """
    Test to delete custom_object_data
    """
    client_id = 1
    co_obj_id = 1
    config = config1

    mock_get_custom_object_data.return_value = get_co_data(get_keys_config_1)

    custom_obj_uploader = CustomObjectDataUploader(
        client_id,
        co_obj_id,
        config,
        get_file_obj,
    )

    result = custom_obj_uploader.get_records_for_delete()

    delete_keys = [
        "01h50hrb4jn8sd9se0wpce47fvv#:::#1.00e+02",
        "01h50hrb46dvwc7wypf1025wpn#:::#-1/2",
        "01h50hrb6rdsanvkatbembtghk#:::#null",
    ]
    delete_records = [
        {
            "co_2_invoice_name": "01H50HRB4JN8SD9SE0WPCE47FVV",
            "co_2_invoice_start_date": "13-Jul-2023",
            "co_2_invoice_end_date": "10-Aug-2023",
            "co_2_deal_name": "1.00E+02",
            "co_2_payee_email": "<EMAIL>",
            "co_2_invoice_amount": "41971.01",
            "co_2_comm_percent": "301",
            "co_2_comm_bool": "",
            "row_key": "01h50hrb4jn8sd9se0wpce47fvv#:::#1.00e+02",
            "snapshot_row_key": None,
        },
        {
            "co_2_invoice_name": "01H50HRB46DVWC7WYPF1025WPN",
            "co_2_invoice_start_date": "13-Jul-2023",
            "co_2_invoice_end_date": "08-Aug-2023",
            "co_2_deal_name": "-1/2",
            "co_2_payee_email": "<EMAIL>",
            "co_2_invoice_amount": "81126.72",
            "co_2_comm_percent": "336",
            "co_2_comm_bool": "TrUe",
            "row_key": "01h50hrb46dvwc7wypf1025wpn#:::#-1/2",
            "snapshot_row_key": None,
        },
        {
            "co_2_invoice_name": "01H50HRB6RDSANVKATBEMBTGHK",
            "co_2_invoice_start_date": "02-Jul-2023",
            "co_2_invoice_end_date": "05-Aug-2023",
            "co_2_deal_name": "null",
            "co_2_payee_email": "<EMAIL>",
            "co_2_invoice_amount": "39141.82",
            "co_2_comm_percent": "",
            "co_2_comm_bool": "",
            "row_key": "01h50hrb6rdsanvkatbembtghk#:::#null",
            "snapshot_row_key": None,
        },
    ]

    ignored_record_1 = {
        "co_2_invoice_name": "01H50HRB4TYWNFRC17S472XWWM",
        "co_2_invoice_start_date": "24-Jul-2023",
        "co_2_invoice_end_date": "29-Aug-2023",
        "co_2_deal_name": "\u202btest\u202b",
        "co_2_payee_email": "<EMAIL>",
        "co_2_invoice_amount": "32884.22",
        "co_2_comm_percent": "-45.75",
        "co_2_comm_bool": "false\u202c\u202c",
        "reason": "Ignored because this is new data, but the chosen import type is Delete data.",
    }
    ignored_record_2 = {
        "co_2_invoice_name": "01H50HRB4WF8Q3VA1NT8B77KKR",
        "co_2_invoice_start_date": "28-Jul-2023",
        "co_2_invoice_end_date": "08-Aug-2023",
        "co_2_deal_name": "$\xa01.00",
        "co_2_payee_email": "<EMAIL>",
        "co_2_invoice_amount": "98,599.87",
        "co_2_comm_percent": "324",
        "co_2_comm_bool": "",
        "reason": "Ignored because this is new data, but the chosen import type is Delete data.",
    }

    error_record_1 = {
        "co_2_invoice_name": "01H50HRB4RACZDTTMEVMZ7WPQ0",
        "co_2_invoice_start_date": "20-Jul-2023",
        "co_2_invoice_end_date": "03-Aug-2023",
        "co_2_deal_name": "",
        "co_2_payee_email": "<EMAIL>",
        "co_2_invoice_amount": "10731.72",
        "co_2_comm_percent": "283",
        "co_2_comm_bool": "FALSE",
        "error": "Deal Name: Missing primary key",
    }

    assert set(result["successful_records"]["db_records"].keys()) == set(delete_keys)

    # We use in to verify the successful records because we identify the successful recods
    # by eliminating the error_records and ignored_records by a set operation
    # Since set is not a ordered data structure, the order of the successful records will vary at each rum.
    # While the order of error records and ignored records are preserved as list

    for record in result["successful_records"]["csv_records_to_delete"]:
        assert record in delete_records

    assert ignored_record_1 in result["ignored_records"]
    assert ignored_record_2 in result["ignored_records"]
    assert error_record_1 in result["error_records"]

    assert result["total_records_count"] == 95
    assert 95 == (
        len(result["successful_records"]["csv_records_to_delete"])
        + len(result["ignored_records"])
        + len(result["error_records"])
    )


@pytest.mark.performance
@pytest.mark.django_db
@patch("spm.services.data_import_services.data_import_validator.get_custom_object_data")
@pytest.mark.parametrize("performance_check", [35], indirect=["performance_check"])
@pytest.mark.usefixtures("performance_check")
def test_create_update_performance(mock_get_custom_object_data):
    """
    Performance test that runs only when --performance-check is specified while running pytest
    """
    client_id = 1
    co_obj_id = 1
    config = config2

    file_name = "spm/tests/data_import_services/performance_test_data.csv"
    file_obj = BytesIO()
    with open(file_name, mode="rb") as file:
        file_obj = BytesIO(file.read())
    file.close()

    custom_obj_data_extractor = CustomObjectDataUploader(
        client_id,
        co_obj_id,
        config,
        file_obj,
    )
    custom_obj_data_extractor.extract_required_data()
    extracted_primary_keys = (
        custom_obj_data_extractor.primary_and_snapshot_key_validator()
    )
    mock_get_custom_object_data.return_value = get_co_data(extracted_primary_keys)
    custom_obj_uploader = CustomObjectDataUploader(
        client_id,
        co_obj_id,
        config,
        file_obj,
    )
    result = custom_obj_uploader.get_records_for_create_update()
    assert result is not None


@pytest.mark.django_db
@patch("spm.services.data_import_services.data_import_validator.get_custom_object_data")
def test_windows_encoding(mock_get_custom_object_data):
    """
    Performance test that runs only when --performance-check is specified while running pytest
    """
    client_id = 1
    co_obj_id = 1
    config = config3

    file_name = (
        "spm/tests/data_import_services/091323 Metro Nuv Fid Everstage Test2.csv"
    )
    file_obj = BytesIO()
    with open(file_name, mode="rb") as file:
        file_obj = BytesIO(file.read())
    file.close()

    custom_obj_data_extractor = CustomObjectDataUploader(
        client_id,
        co_obj_id,
        config,
        file_obj,
    )
    custom_obj_data_extractor.extract_required_data()
    extracted_primary_keys = (
        custom_obj_data_extractor.primary_and_snapshot_key_validator()
    )
    mock_get_custom_object_data.return_value = get_co_data(extracted_primary_keys)
    custom_obj_uploader = CustomObjectDataUploader(
        client_id,
        co_obj_id,
        config,
        file_obj,
    )
    result = custom_obj_uploader.get_records_for_create_update()
    assert result is not None


@pytest.mark.django_db
@patch("spm.services.data_import_services.data_import_validator.get_custom_object_data")
def test_utf_8_bom_encoding(mock_get_custom_object_data):
    """
    Performance test that runs only when --performance-check is specified while running pytest
    """
    client_id = 1
    co_obj_id = 1
    config = config3

    file_name = "spm/tests/data_import_services/Data import.csv"
    file_obj = BytesIO()
    with open(file_name, mode="rb") as file:
        file_obj = BytesIO(file.read())
    file.close()

    custom_obj_data_extractor = CustomObjectDataUploader(
        client_id,
        co_obj_id,
        config,
        file_obj,
    )
    custom_obj_data_extractor.extract_required_data()
    extracted_primary_keys = (
        custom_obj_data_extractor.primary_and_snapshot_key_validator()
    )
    mock_get_custom_object_data.return_value = get_co_data(extracted_primary_keys)
    custom_obj_uploader = CustomObjectDataUploader(
        client_id,
        co_obj_id,
        config,
        file_obj,
    )
    result = custom_obj_uploader.get_records_for_create_update()
    assert result is not None


@pytest.mark.django_db
@patch("spm.services.data_import_services.data_import_validator.get_custom_object_data")
def test_date_ambuguity(mock_get_custom_object_data):
    """
    Test case to check for ambigous date formats in data
    """
    client_id = 1
    co_obj_id = 1
    config = config4

    file_name = "spm/tests/data_import_services/date_ambiquity_check.csv"
    file_obj = BytesIO()
    with open(file_name, mode="rb") as file:
        file_obj = BytesIO(file.read())
    file.close()

    custom_obj_data_extractor = CustomObjectDataUploader(
        client_id,
        co_obj_id,
        config,
        file_obj,
    )
    custom_obj_data_extractor.extract_required_data()
    extracted_primary_keys = (
        custom_obj_data_extractor.primary_and_snapshot_key_validator()
    )
    mock_get_custom_object_data.return_value = get_co_data(extracted_primary_keys)
    custom_obj_uploader = CustomObjectDataUploader(
        client_id,
        co_obj_id,
        config,
        file_obj,
    )
    result = custom_obj_uploader.get_records_for_create_update()
    assert result is not None
    assert result["total_records_count"] == len(result["error_records"])
    assert result["total_records_count"] == 4
    for record in result["error_records"]:
        assert record["error"] == "Duplicate record found."


@pytest.fixture
def get_keys_config_date(get_file_obj):
    """
    Fixture to extract the primary keys from the csv_file inorder to fetch data of those keys from custom_object_data
    """
    client_id = 1
    co_obj_id = 1
    config = config_date

    co_data_uploader = CustomObjectDataUploader(
        client_id,
        co_obj_id,
        config,
        get_file_obj,
    )
    co_data_uploader.extract_required_data()
    extracted_primary_keys = co_data_uploader.primary_and_snapshot_key_validator()

    return extracted_primary_keys


@pytest.mark.django_db
@patch("spm.services.data_import_services.data_import_validator.get_custom_object_data")
def test_invalid_data_delete(mock_get_custom_object_data, get_keys_config_date):
    """
    Test to delete custom_object_data when all the data is invalid
    """
    client_id = 1
    co_obj_id = 1
    config = config_date

    file_name = "spm/tests/data_import_services/import_co_date_data_test.csv"
    file_obj = BytesIO()

    with open(file_name, mode="rb") as file:
        file_obj = BytesIO(file.read())
    file.close()

    mock_get_custom_object_data.return_value = get_co_data(get_keys_config_date)

    custom_obj_uploader = CustomObjectDataUploader(
        client_id,
        co_obj_id,
        config,
        file_obj,
    )

    result = custom_obj_uploader.get_records_for_delete()
    print("Result", result)

    assert result["successful_records"]["db_records"] == []
    assert result["successful_records"]["csv_records_to_delete"] == []

    for record in result["error_records"]:
        assert (
            record["error"]
            == "Invoice Start Date: Invalid date format in primary key. Use (YYYY-MM-DD HH:MM:SS) format"
        )

    assert result["total_records_count"] == 2
    assert 2 == (
        len(result["successful_records"]["csv_records_to_delete"])
        + len(result["ignored_records"])
        + len(result["error_records"])
    )


@pytest.mark.django_db
@patch("spm.services.data_import_services.data_import_validator.get_custom_object_data")
def test_small_file_special_char(mock_get_custom_object_data):
    """
    Test for small files where there is a presence of special characters
    """
    client_id = 1
    co_obj_id = 2
    config = config5

    file_name = "spm/tests/data_import_services/small_file_special_char.csv"
    file_obj = BytesIO()
    with open(file_name, mode="rb") as file:
        file_obj = BytesIO(file.read())
    file.close()

    custom_obj_data_extractor = CustomObjectDataUploader(
        client_id,
        co_obj_id,
        config,
        file_obj,
    )
    custom_obj_data_extractor.extract_required_data()
    extracted_primary_keys = (
        custom_obj_data_extractor.primary_and_snapshot_key_validator()
    )
    mock_get_custom_object_data.return_value = get_co_data(extracted_primary_keys)
    custom_obj_uploader = CustomObjectDataUploader(
        client_id,
        co_obj_id,
        config,
        file_obj,
    )
    result = custom_obj_uploader.get_records_for_create_update()
    assert result is not None


def get_file_object(file_path):
    """
    Function to read the data from the file and convert into bytes oject
    """
    file_obj = BytesIO()

    with open(file_path, mode="rb") as file:
        file_obj = BytesIO(file.read())
    file.close()
    return file_obj


@pytest.mark.django_db
@pytest.mark.parametrize(
    "file_info",
    [
        ("import_test_data_extract.csv", "csv"),
        ("import_test_data_extract.xlsx", "xlsx"),
        ("import_test_data_extract.xls", "xls"),
    ],
)
def test_extract_required_data_different_formats(file_info):
    """Test extract_required_data with different file formats using actual test files"""
    file_name, file_format = file_info
    file_path = f"spm/tests/data_import_services/{file_name}"

    # Get file object
    file_obj = get_file_object(file_path)

    # Initialize CustomObjectDataUploader
    uploader = CustomObjectDataUploader(
        client_id=1,
        custom_object_id=1,
        config_map=config1,
        csv_object=file_obj,
        uploaded_file_name=file_name,
    )

    # Execute the method
    result = uploader.extract_required_data()

    # Basic assertions
    assert result is True
    assert len(uploader.extracted_data) > 0

    # Store the first format's data to compare with other formats
    if not hasattr(test_extract_required_data_different_formats, "first_format_data"):
        test_extract_required_data_different_formats.first_format_data = (
            uploader.extracted_data
        )
    else:
        # Compare current format's data with the first format's data
        assert (
            uploader.extracted_data
            == test_extract_required_data_different_formats.first_format_data
        )


@pytest.mark.django_db
def test_encoding_detection_fallback_with_real_charset():
    """
    Test the encoding detection fallback mechanism when chardet fails
    but charset_normalizer succeeds
    """
    client_id = 1
    co_obj_id = 1
    config = {"system_name_header_name_map": {"test_field": "test_field"}}

    # Create content in windows-1250 encoding
    test_content = "test_field\nvalué with áccents".encode("windows-1250")
    file_obj = BytesIO(test_content)

    uploader = CustomObjectDataUploader(
        client_id,
        co_obj_id,
        config,
        file_obj,
    )

    # Mock chardet to fail with low confidence
    with patch.object(
        uploader,
        "detect_encoding_using_chardet",
        return_value={"encoding": "ascii", "confidence": 0.5},
    ):
        result = uploader.extract_required_data()
        assert result is True
        assert len(uploader.extracted_data) == 1
        assert "valué with áccents" in uploader.extracted_data[0].values()


@pytest.mark.django_db
def test_encoding_detection_with_decode_error_fallback():
    """
    Test that when decoding with chardet's encoding fails,
    we fall back to charset_normalizer's actual detection
    """
    client_id = 1
    co_obj_id = 1
    config = {"system_name_header_name_map": {"test_field": "test_field"}}

    # Create content with special characters in windows-1250 encoding
    test_content = "test_field\nvalué with áccents".encode("windows-1250")
    file_obj = BytesIO(test_content)

    uploader = CustomObjectDataUploader(
        client_id,
        co_obj_id,
        config,
        file_obj,
    )
    # Mock chardet to return UTF-8 with high confidence (which will fail when decoding)
    with patch.object(
        uploader,
        "detect_encoding_using_chardet",
        return_value={"encoding": "utf-8", "confidence": 0.9},
    ):
        result = uploader.extract_required_data()
        assert result is True
        assert len(uploader.extracted_data) == 1
        assert "valué with áccents" in uploader.extracted_data[0].values()


@pytest.mark.django_db
def test_encoding_detection_both_fail():
    """
    Test that when both chardet and charset_normalizer fail,
    an exception is raised
    """
    client_id = 1
    co_obj_id = 1
    config = {"system_name_header_name_map": {"test_field": "test_field"}}

    # Create content with special characters in windows-1250 encoding
    test_content = "test_field\nvalué with áccents".encode("windows-1250")
    file_obj = BytesIO(test_content)

    uploader = CustomObjectDataUploader(
        client_id,
        co_obj_id,
        config,
        file_obj,
    )

    # Mock chardet to fail with low confidence
    with patch.object(
        uploader,
        "detect_encoding_using_chardet",
        return_value={"encoding": "utf-8", "confidence": 0.9},
    ):
        # Mock charset_normalizer to also fail with low confidence
        with patch.object(
            uploader,
            "detect_encoding_using_charset_normalizer",
            return_value={"encoding": "utf-8", "confidence": 0.9},
        ):
            # Should raise an exception
            with pytest.raises(Exception) as excinfo:
                uploader.extract_required_data()

            assert "Unable to detect encoding" in str(excinfo.value)


@pytest.mark.django_db
def test_charset_normalizer_detection_with_different_encodings():
    """
    Test charset_normalizer's ability to detect different encodings
    """
    client_id = 1
    co_obj_id = 1
    config = config1
    file_path = "spm/tests/data_import_services/encoding_test_file.csv"
    original_file_obj = get_file_object(file_path)
    original_file_obj.seek(0)
    file_content = original_file_obj.read().decode("utf-8")

    # List of encodings to test
    encodings_to_test = [
        "utf-8",
        "iso-8859-1",  # Latin-1
        "iso-8859-15",  # Latin-9
        "windows-1252",  # Windows Western European
        "utf-16",
        "utf-16-le",
        "utf-16-be",
        "mac_iceland",
    ]

    similar_encodings = {
        "iso-8859-1": ["windows-1250", "windows-1252", "iso-8859-15"],
        "iso-8859-15": ["windows-1250", "windows-1252", "iso-8859-1"],
        "windows-1252": ["windows-1250", "iso-8859-1", "iso-8859-15"],
    }

    for encoding in encodings_to_test:
        # Encode the test content with the current encoding
        encoded_content = file_content.encode(encoding)
        file_obj = BytesIO(encoded_content)

        uploader = CustomObjectDataUploader(
            client_id,
            co_obj_id,
            config,
            file_obj,
        )

        # Call the charset_normalizer detection method directly
        uploader.csv_object.seek(0)
        # binary_data = uploader.csv_object.read()
        detected_format = uploader.detect_encoding_using_charset_normalizer()

        # Log the results for debugging
        print(f"\nEncoding: {encoding}")
        print(f"Detected: {detected_format}")

        # Verify detection results
        assert detected_format.get("encoding") is not None
        assert detected_format.get("confidence") > 0.5
        if encoding in similar_encodings:
            assert (
                detected_format["encoding"] == encoding
                or detected_format["encoding"] in similar_encodings[encoding]
            )


@pytest.mark.django_db
def test_validate_employee_email():
    """
    Test the validate_employee_email function with various email formats.
    Tests both valid and invalid email patterns to ensure proper validation.
    """
    from spm.services.data_import_services.data_import_validator import (
        CustomObjectDataValidator,
    )

    # Create a validator instance with minimal required parameters
    validator = CustomObjectDataValidator(
        client_id=1,
        raw_records=[],
        co_data_header_map={},
        co_data_type_map={},
        date_format="%d-%b-%Y",
    )

    # Test valid email formats
    valid_emails = [
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "kevino'<EMAIL>",
        "<EMAIL>",
    ]

    for email in valid_emails:
        assert validator.validate_employee_email(email) is True

    # Test invalid email formats
    invalid_emails = [
        "invalid-email",
        "@example.com",
        "user@",
        "user..|<EMAIL>",
        "user <EMAIL>",
        "user@example",
    ]

    for email in invalid_emails:
        with pytest.raises(Exception):
            validator.validate_employee_email(email)
