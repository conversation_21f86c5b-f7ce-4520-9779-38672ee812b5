import logging
import re
import traceback
import uuid

import pydash

from commission_engine.accessors.databook_accessor import (
    DatabookAccessor,
    DatasheetAccessor,
    DatasheetVariableAccessor,
)
from commission_engine.models.databook_models import Datasheet
from everstage_ddd.llm_agent.mannai_interface.execute_entity import execute_skill
from spm.utils import is_valid_uuid

logger = logging.getLogger(__name__)


def bulk_datasheet_order_update(
    client_id: int, datasheet_order_records: dict[str, int]
) -> None:
    """
    Update the datasheet order for the given client_id
    """
    datasheet_accessor = DatasheetAccessor(client_id=client_id)
    datasheet_accessor.bulk_datasheet_order_update(records=datasheet_order_records)


def remove_prefixes_lhs_rhs(word: str) -> str:
    # Define a regular expression pattern to match 'lhs_' or 'rhs_' at the beginning of the word
    pattern = r"^(lhs_|rhs_)+"

    # Use re.sub to remove the matched prefixes
    result = re.sub(pattern, "", word)

    return result


def datasheets_ids_for_databooks(client_id: int, databook_ids: list | None) -> list:
    """
    This function will return the list of datasheet ids for the given databook id
    """
    return DatasheetAccessor(client_id=client_id).get_datasheets_ids_for_databooks(
        databook_ids=databook_ids
    )


def is_possible_to_archive_given_databook(
    client_id: int, databook_id: str
) -> tuple[bool, set]:
    """
    A databook can be archived if none of its constituent datasheets are used in any other active databook
    """
    from spm.services.datasheet_graph.datasheet_graph import (
        DataSheetGraph,
        DatasheetNode,
    )

    datasheet_graph = DataSheetGraph(
        client_id=client_id, include_stale_information_query=False
    )
    datasheet_nodes: set[DatasheetNode] = datasheet_graph.datasheets(
        databook_id=databook_id
    )
    all_descendant_datasheet_node: set[DatasheetNode] = {
        descendant_node
        for datasheet_node in datasheet_nodes
        for descendant_node in datasheet_graph.descendants_by_type(
            node_id=datasheet_node.node_id
        )["datasheet"]
        if descendant_node.databook_id != databook_id
    }

    from spm.services.databook_services import remove_archived_databooks

    affected_databook_ids: list = remove_archived_databooks(
        client_id=datasheet_graph.client_id,
        databook_ids=[
            datasheet_node.databook_id
            for datasheet_node in all_descendant_datasheet_node
        ],
    )

    reason_datasheet_nodes = set()
    for datasheet_node in all_descendant_datasheet_node:
        if uuid.UUID(datasheet_node.databook_id) in affected_databook_ids:
            reason_datasheet_nodes.add(datasheet_node)

    if len(reason_datasheet_nodes) > 0:
        return False, reason_datasheet_nodes
    return True, set()


def is_possible_to_active_given_databook(
    client_id: int, databook_id: str
) -> tuple[bool, set]:
    """
    A databook can be unarchived (moved to Active) if none of its constituent datasheets are linked to another archived databook's datasheets.
    """
    from spm.services.datasheet_graph.datasheet_graph import (
        DataSheetGraph,
        DatasheetNode,
    )

    datasheet_graph = DataSheetGraph(
        client_id=client_id, include_stale_information_query=False
    )
    datasheet_nodes: set[DatasheetNode] = datasheet_graph.datasheets(
        databook_id=databook_id
    )
    all_ancestor_datasheet_node: set[DatasheetNode] = {
        ancestor_node
        for datasheet_node in datasheet_nodes
        for ancestor_node in datasheet_graph.ancestors(
            datasheet_id=datasheet_node.node_id
        )
        if isinstance(ancestor_node, DatasheetNode)
        and ancestor_node.databook_id != databook_id
    }

    from spm.services.databook_services import get_archived_books_from_list

    affected_databook_ids: list = get_archived_books_from_list(
        client_id=datasheet_graph.client_id,
        databook_ids=[
            datasheet_node.databook_id for datasheet_node in all_ancestor_datasheet_node
        ],
    )

    reason_datasheet_nodes = set()
    for datasheet_node in all_ancestor_datasheet_node:
        if uuid.UUID(datasheet_node.databook_id) in affected_databook_ids:
            reason_datasheet_nodes.add(datasheet_node)

    if len(reason_datasheet_nodes) > 0:
        return False, reason_datasheet_nodes
    return True, set()


def can_delete_databook(client_id: int, databook_id: str) -> tuple[bool, set]:
    """
    A check to see if the databook is possible to delete.
    """
    from spm.services.datasheet_graph.datasheet_graph import (
        DataSheetGraph,
        DatasheetNode,
    )

    datasheet_graph = DataSheetGraph(
        client_id=client_id, include_stale_information_query=False
    )
    datasheet_nodes: set[DatasheetNode] = datasheet_graph.datasheets(
        databook_id=databook_id
    )
    dependent_datasheet_nodes: set = {
        descendant_node
        for datasheet_node in datasheet_nodes
        for descendant_node in datasheet_graph.descendants_by_type(
            node_id=datasheet_node.node_id
        )["datasheet"]
        if descendant_node.databook_id != databook_id
    }
    if len(dependent_datasheet_nodes) > 0:
        return False, dependent_datasheet_nodes
    return True, set()


def custom_object_ancestor(client_id: int, datasheet_ids: list[str]) -> set[int]:
    """
    Returns a set of custom object ancestors for the given datasheet_ids.
    """
    from spm.services.datasheet_graph.datasheet_graph import DataSheetGraph

    datasheet_graph = DataSheetGraph(
        client_id=client_id, include_stale_information_query=False
    )
    custom_object_ids = set()

    for datasheet_id in datasheet_ids:
        custom_object_nodes = datasheet_graph.ancestors_by_type(
            datasheet_id=datasheet_id
        )["custom_object"]
        custom_object_ids.update(
            int(custom_object_node.node_id)
            for custom_object_node in custom_object_nodes
        )

    return custom_object_ids


def get_ordered_columns_for_given_datasheet_id(
    client_id, databook_id, datasheet_id
) -> list:
    """
    This method is used to get the ordered columns for a given datasheet
    """
    ordered_column_dict = DatasheetAccessor(
        client_id=client_id
    ).get_ordered_columns_for_given_datasheet_id(
        databook_id=databook_id, datasheet_id=datasheet_id
    )
    ordered_columns = ordered_columns = pydash.get(
        ordered_column_dict[0], "ordered_columns", []
    )
    return ordered_columns


def datasheet_names_by_ids(client_id, datasheet_ids):
    """
    A function to get datasheet names by ids
    """
    datasheet_names = DatasheetAccessor(client_id=client_id).get_datasheet_name_by_id(
        datasheet_id=datasheet_ids
    )
    return datasheet_names


def is_datasheet_exist_for_given_client(
    client_id, datasheet_id
) -> tuple[bool, list[Datasheet] | None]:
    """
    Check if a datasheet exists for the given client.

    Returns a tuple (bool, list) where the boolean is True if the datasheet exists, and the list contains Datasheet object or is None.
    """
    return DatasheetAccessor(client_id=client_id).is_datasheet_exist_for_given_client(
        datasheet_id=datasheet_id
    )


def is_datasheet_exist_in_a_databook(
    client_id: int, databook_id: str, datasheet_id: str
) -> bool:
    """
    Check if a datasheet exists in a given databook.
    """
    return (
        False
        if (not is_valid_uuid(datasheet_id) or not is_valid_uuid(databook_id))
        else DatasheetAccessor(client_id=client_id).is_datasheet_exist(
            databook_id=databook_id, datasheet_id=datasheet_id
        )
    )


def add_generative_description(
    client_id: int, email_id: str, databook_ids: list[str]
) -> tuple[str, bool]:
    """
    Add generative description for all the datasheets in the given databooks
    """

    try:
        datasheet_ids = datasheets_ids_for_databooks(
            client_id=client_id, databook_ids=databook_ids
        )
        datasheet_details = DatasheetAccessor(client_id=client_id).get_prompt_details(
            datasheet_ids=datasheet_ids
        )
        datasheet_variables_details = DatasheetVariableAccessor(
            client_id=client_id
        ).get_prompt_details(datasheet_ids=datasheet_ids)

        final_prompt_details = {}
        for detail in datasheet_details:
            datasheet_id = detail["datasheet_id"]
            datasheet_name = detail["name"]
            transformations = detail["transformation_spec"]
            final_prompt_details[datasheet_id] = {
                "name": datasheet_name,
                "transformations": transformations,
                "variables": [],
            }

        for detail in datasheet_variables_details:
            datasheet_id = detail["datasheet_id"]
            variables = {
                "display_name": detail["display_name"],
                "system_name": detail["system_name"],
            }
            final_prompt_details[datasheet_id]["variables"].append(variables)

        failed_datasheet_ids = []

        for datasheet_id in datasheet_ids:
            user_prompt = f"datasheet_details: {final_prompt_details[datasheet_id]}"
            agent_tag = "Autogen Description"
            ai_content, status = execute_skill(
                client_id=client_id,
                skill_tag=agent_tag,
                employee_email_id=email_id,
                user_prompt=user_prompt,
            )
            if status == "Success":
                description = ai_content.get("ai_generated_content", None)
                DatasheetAccessor(client_id=client_id).fill_description_for_datasheet(
                    datasheet_id=datasheet_id, description=description
                )
            else:
                failed_datasheet_ids.append(datasheet_id)

        if failed_datasheet_ids:
            logger.error(
                f"Failed to generate description for datasheets: {failed_datasheet_ids}"
            )
            return "Few datasheets failed to generate description", False
        else:  # noqa: RET505
            return "All datasheets description generated successfully", True

    except Exception:  # noqa: BLE001
        logger.info(traceback.format_exc())
        return "Few datasheets failed to generate description", False


def remove_archived_datasheets(client_id: int, datasheet_ids: list[str]) -> list[str]:
    """
    Removes the datasheets which are from archived databooks from the given datasheet_ids.
    """
    logger.info(
        f"BEGIN: Removing datasheets which are from archived databooks for client_id: {client_id}"
    )
    datasheet_ids = DatabookAccessor(
        client_id=client_id
    ).remove_datasheets_which_are_from_archived_databooks(datasheet_ids=datasheet_ids)
    logger.info(
        f"END: Removing datasheets which are from archived databooks for client_id: {client_id} - datasheet_ids: {datasheet_ids}"
    )
    return datasheet_ids


def get_force_skip_enabled_datasheet_ids(client_id: int) -> list[str]:
    """
    Get the list of datasheet ids that are force datasheet skip enabled
    """
    logger.info(
        f"BEGIN: Getting force skip enabled datasheet ids for client_id: {client_id}"
    )
    datasheet_accessor = DatasheetAccessor(client_id=client_id)
    records = datasheet_accessor.get_force_skip_enabled_datasheet_ids()
    logger.info(
        f"END: Getting force skip enabled datasheet ids for client_id: {client_id} - records: {records}"
    )
    return records


def is_datasheet_force_skipped(client_id: int, datasheet_id: str) -> bool:
    """
    Check if a datasheet is force skipped
    """
    logger.info(
        f"BEGIN: Checking if datasheet is force skipped for client_id: {client_id} - datasheet_id: {datasheet_id}"
    )
    is_force_skipped = DatasheetAccessor(
        client_id=client_id
    ).is_datasheet_force_skipped(datasheet_id=datasheet_id)
    logger.info(
        f"END: Checking if datasheet is force skipped for client_id: {client_id} - datasheet_id: {datasheet_id} - is_force_skipped: {is_force_skipped}"
    )
    return is_force_skipped


def get_force_skip_statuses(client_id: int, databook_ids: list[str]) -> dict[str, bool]:
    """
    Get the force skip statuses for the given databook_ids
    """
    logger.info(
        f"BEGIN: Getting force skip statuses for client_id: {client_id} - databook_ids: {databook_ids}"
    )
    force_skip_statuses = DatasheetAccessor(
        client_id=client_id
    ).get_force_skip_statuses(databook_ids=databook_ids)
    logger.info(
        f"END: Getting force skip statuses for client_id: {client_id} - databook_ids: {databook_ids} - force_skip_statuses: {force_skip_statuses}"
    )
    return force_skip_statuses
