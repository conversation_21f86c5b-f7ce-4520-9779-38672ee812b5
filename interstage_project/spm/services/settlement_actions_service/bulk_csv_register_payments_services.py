import logging
from datetime import datetime

import pandas as pd
from django.utils import timezone
from django.utils.timezone import make_aware
from rest_framework import status
from rest_framework.response import Response

from commission_engine.accessors.client_accessor import (
    avoid_concurrent_register_payment,
    can_avoid_locking_with_pending_changes,
)
from commission_engine.accessors.payout_status_accessor import PayoutStatusAccessor
from commission_engine.utils import end_of_day
from commission_engine.utils.general_data import RBACComponent, RbacPermissions
from commission_engine.utils.general_utils import replace_nan_nat_for_df
from spm.accessors.config_accessors.employee_accessor import (
    EmployeeAccessor,
    EmployeePayrollAccessor,
)
from spm.accessors.payout_process_lock_accessors import PayoutProcessLockAccessor
from spm.bulk_uploader.base_bulk_uploader import BaseBulkUploader
from spm.constants import AUDIT_TRAIL_EVENT_TYPE as EVENT
from spm.models.payout_process_lock_models import (
    PayeePeriodActionEvents,
    PayoutProcessMessageCodes,
)
from spm.schema_validators.base_schema_validator import BaseSchemaValidator, SchemaError
from spm.services import audit_services
from spm.services.commission_actions_service.payout_action_service import (
    check_sync_running_status,
    get_payees_with_pending_changes,
)
from spm.services.rbac_services import (
    get_data_permission,
    get_ui_permissions,
    get_valid_payee_emails,
    is_payout_value_permission,
)
from spm.services.settlement_actions_service.settlement_actions_service import (
    acquire_lock_payout_process,
    acquire_payout_lock,
    generate_conflicting_payees_csv,
    release_payout_lock,
    update_paid_status,
)

logger = logging.getLogger(__name__)


class PaymentSchemaValidator(BaseSchemaValidator):
    """Schema Validator to validate records in Payment Bulk upload CSV file"""

    def __init__(self, client_id, raw_records):
        self.client_id = client_id
        self.raw_records = raw_records

        # Define the schema for validation
        all_fields = {
            "payee_email_id": self.check_mandatory(
                self.validate_email, is_mandatory=True
            ),
            "paid_amount": self.check_mandatory(
                self.validate_number, is_mandatory=True
            ),
        }

        super().__init__(all_fields=all_fields)


class UploadBasedPaymentStatusUpdate(BaseBulkUploader):
    def __init__(
        self,
        client_id: int,
        period_end_date: str,
        audit: dict,
        raw_records: list = None,
        logged_in_user: str = None,
        payout_kd: str = None,
    ):
        """
        client_id: ID of the client
        raw_records: List of records to be processed
        period_end_date: End date for the payment period
        audit: Audit info for logging
        logged_in_user: User who is logged in
        payout_kd: Payout knowledge date for the payout entries to be registered for that period
        """
        super().__init__(client_id, raw_records)

        # Use the raw_records from the base class to set uploaded_data
        self.uploaded_data = pd.DataFrame(self.raw_records)

        self.client_id = client_id
        self.logged_in_user = logged_in_user
        self.period_end_date = period_end_date
        self.ped = make_aware(
            end_of_day(datetime.strptime(self.period_end_date, "%d-%m-%Y"))
        )
        self.event_type_code = EVENT["INITIATE_PAYOUT"]["code"]
        self.current_datetime = timezone.now()
        self.payout_date = (
            datetime.strptime(payout_kd, "%d-%m-%Y").strftime("%Y-%m-%d")
            if payout_kd
            else self.current_datetime.strftime("%Y-%m-%d")
        )
        self.payout_status_data = {}
        self.payee_frozen_map = {}
        self.valid_uploaded_records_count = 0
        self.valid_uploaded_records = []
        self.invalid_uploaded_records = []
        self.missing_columns = []
        self.empty_columns = []
        self.valid_emails_from_ps = [
            emp["payee_email_id"]
            for emp in PayoutStatusAccessor(client_id).get_payees_with_ped(self.ped)
        ]

        # These 2 specified columns are mandatory in the CSV file
        self.required_columns = ["payee_email_id", "paid_amount"]
        self.non_empty_columns = [
            "payee_email_id",
            "paid_amount",
        ]
        self.logger = logger
        self.audit = audit
        self.schema_validator_ = PaymentSchemaValidator(client_id, raw_records)

    def _log_and_return_error(self, message):
        self.logger.info(message)
        return False, message, [], []

    def _validate_columns(self):
        self.logger.info("Validating required and non-empty columns in uploaded data")
        missing_columns = [
            column
            for column in self.required_columns
            if column not in self.uploaded_data.columns
        ]

        if missing_columns:
            self.logger.info(f"Missing required fields: {missing_columns}")
            self.missing_columns = missing_columns
            return False

        empty_columns = [
            column
            for column in self.non_empty_columns
            if self.uploaded_data[column].isnull().any()
        ]

        if empty_columns:
            self.logger.info(f"Empty values in: {empty_columns}")
            self.empty_columns = empty_columns
            return False

        self.logger.info("All required and non-empty columns are present")
        return True

    def _map_payout_data(self):
        self.logger.info(
            "Mapping payout data to uploaded data for payees %s for period %s",
            self.uploaded_data["payee_email_id"].unique(),
            self.ped,
        )
        payee_ids = self.uploaded_data["payee_email_id"].unique()
        ps_records_for_payees = PayoutStatusAccessor(
            self.client_id
        ).get_payee_period_records(payee_ids, self.ped)

        self.payout_status_data = {
            payout["payee_email_id"]: payout for payout in ps_records_for_payees
        }

        self.uploaded_data["pending_amount"] = self.uploaded_data["payee_email_id"].map(
            {
                payout["payee_email_id"]: payout["pending_amount"]
                for payout in ps_records_for_payees
            }
        )
        self.uploaded_data["total_payout"] = self.uploaded_data["payee_email_id"].map(
            {
                payout["payee_email_id"]: payout["total_payout"]
                for payout in ps_records_for_payees
            }
        )
        self.logger.info("Payout data mapping complete")

    def mask_rows_without_permission(
        self,
        not_permitted_emails,
        email_col="payee_email_id",
        error_message="User does not have permission for this payee.",
    ):
        """
        For each row in df, if the email_col value is in not_permitted_emails,
        blank all columns except email_col and raw record id and add a validation error
        """
        for idx, row in self.uploaded_data.iterrows():
            payee_email = row.get(email_col)
            if payee_email in not_permitted_emails:
                self.uploaded_data.at[idx, "paid_amount"] = ""
                self.uploaded_data.at[idx, "comment"] = ""
                self.uploaded_data.at[idx, "total_payout"] = ""
                self.uploaded_data.at[idx, "pending_amount"] = ""
                self.uploaded_data.at[idx, "currency_code"] = ""
                self.uploaded_data.at[idx, "designation"] = ""
                self.uploaded_data.at[idx, "payee_name"] = ""
                self.uploaded_data.at[idx, "errors"] = [
                    {
                        "error_message": error_message,
                        "error_col_name": email_col,
                        "error_col_value": payee_email,
                    }
                ]
        return self.uploaded_data

    def _handle_permission_check(self):
        valid_email_rows = self.uploaded_data["payee_email_id"].notnull()
        to_check_emails = (
            self.uploaded_data.loc[valid_email_rows, "payee_email_id"].unique().tolist()
        )

        # Data permission check for payout statements
        # If not ALL_DATA, then filter payees based on data permission
        _, not_permitted = filter_payees_by_data_permission(
            self.client_id,
            to_check_emails,
            self.logged_in_user,
            RBACComponent.PAYOUTS_STATEMENTS.value,
            self.ped,
        )
        self.uploaded_data = self.mask_rows_without_permission(
            not_permitted,
            email_col="payee_email_id",
            error_message="You don't have access to view the data for this payee",
        )

        # Checking for "view:payoutvalueothers" permission. If not payout value others permission
        # filter out other payee ids except that of logged in user.
        _, not_permitted = filter_payees_by_view_payout_others_permission(
            self.client_id,
            to_check_emails,
            self.logged_in_user,
        )
        self.uploaded_data = self.mask_rows_without_permission(
            not_permitted,
            email_col="payee_email_id",
            error_message="You do not have permission to view payout value for this payee",
        )

        # Handle manage:payouts permission
        # if manage:payouts not present, then filter out payees that are not frozen

        # Get payee details and frozen map
        if not self.payee_frozen_map:
            payee_frozen_map = {
                email: (
                    self.payout_status_data.get(email, {}).get("comm_calc_status")
                    == "Frozen"
                    and self.payout_status_data.get(email, {}).get(
                        "settlement_calc_status"
                    )
                    == "Frozen"
                )
                for email in to_check_emails
            }
            self.payee_frozen_map = payee_frozen_map

        user_permissions = set(get_ui_permissions(self.client_id, self.logged_in_user))
        has_manage_payouts = RbacPermissions.MANAGE_PAYOUTS.value in user_permissions

        for idx, row in self.uploaded_data.iterrows():
            payee_email = row.get("payee_email_id")
            is_frozen = self.payee_frozen_map.get(payee_email, False)

            if not is_frozen and not has_manage_payouts:
                errors = row["errors"] if isinstance(row["errors"], list) else []
                errors.append(
                    {
                        "error_message": "You don't have permission to lock this payee's statement, so payment can't be registered",
                        "error_col_name": "payee_email_id",
                        "error_col_value": payee_email,
                    }
                )
                self.uploaded_data.at[idx, "errors"] = errors

    def _initialize_errors_column(self):
        self.logger.info("Initializing errors column in uploaded data")
        self.uploaded_data["errors"] = [[] for _ in range(len(self.uploaded_data))]

    def _preprocess_columns_in_uploaded_data(self):
        self.logger.info("Preprocessing columns in uploaded data")
        # Filter rows that passed validation and errors are empty
        schema_valid_rows = self.uploaded_data["errors"].apply(
            lambda x: isinstance(x, list) and len(x) == 0
        )

        if "comment" in self.uploaded_data.columns:
            self.uploaded_data["comment"] = (
                self.uploaded_data["comment"].fillna("").astype(str)
            )
        self.uploaded_data["total_payout"] = (
            self.uploaded_data["total_payout"].fillna(0).astype(str)
        )
        self.uploaded_data["pending_amount"] = (
            self.uploaded_data["pending_amount"].fillna(0).astype(str)
        )
        self.uploaded_data["paid_amount"] = (
            self.uploaded_data["paid_amount"].fillna(0).astype(str)
        )

        columns_to_convert = ["total_payout", "pending_amount", "paid_amount"]
        for column in columns_to_convert:
            self.uploaded_data.loc[schema_valid_rows, column] = pd.to_numeric(
                self.uploaded_data.loc[schema_valid_rows, column], errors="coerce"
            ).fillna(0)
        self.logger.info("Preprocessing of columns complete")

    def _round_numeric_columns(self):
        """
        Round off all valid numeric columns (paid_amount, total_payout, pending_amount) to 2 decimal places,
        """
        numeric_columns = ["paid_amount", "total_payout", "pending_amount"]

        def has_invalid_amount_error(errors):
            if not isinstance(errors, list):
                return False
            for err in errors:
                if (
                    err.get("error_message") == "Invalid amount entered"
                    and err.get("error_col_name") == "Payment Amount"
                ):
                    return True
            return False

        skip_mask = self.uploaded_data["errors"].apply(has_invalid_amount_error)

        for col in numeric_columns:
            col_values = self.uploaded_data[col]
            new_col = col_values.copy()
            # Build a mask for values to round: not skipped by error, and not empty string
            round_mask = (~skip_mask) & (col_values != "")
            # Only round where round_mask is True and value is numeric
            to_round = pd.to_numeric(col_values[round_mask], errors="coerce").round(2)
            # Fill NaNs (from non-numeric) with the original values
            new_col[round_mask] = to_round.combine_first(col_values[round_mask])
            self.uploaded_data[col] = new_col

    def _validate_rows(self):
        self.logger.info("Performing row-level validations on uploaded data")
        # Only validate rows that have no existing errors
        schema_valid_rows = self.uploaded_data["errors"].apply(
            lambda x: isinstance(x, list) and not x
        )

        def append_error(row, message, column, value):
            if isinstance(row["errors"], list):
                row["errors"].append(
                    {
                        "error_message": message,
                        "error_col_name": column,
                        "error_col_value": value,
                    }
                )
            return row["errors"]

        # Invalid email addresses
        invalid_email_rows = schema_valid_rows & ~self.uploaded_data[
            "payee_email_id"
        ].isin(self.valid_emails_from_ps)
        self.uploaded_data.loc[invalid_email_rows, "errors"] = self.uploaded_data.loc[
            invalid_email_rows
        ].apply(
            lambda row: append_error(
                row,
                "Email address not recognized. Please verify or add as new payee",
                "Email Address",
                row["payee_email_id"],
            ),
            axis=1,
        )

        # Duplicate payee email IDs
        duplicate_email_rows = schema_valid_rows & self.uploaded_data.duplicated(
            subset=["payee_email_id"], keep="first"
        )
        self.uploaded_data.loc[duplicate_email_rows, "errors"] = self.uploaded_data.loc[
            duplicate_email_rows
        ].apply(
            lambda row: append_error(
                row,
                f"Duplicate payee email {row['payee_email_id']}",
                "Email Address",
                row["payee_email_id"],
            ),
            axis=1,
        )

        # Missing pending amounts
        missing_pending_amount_rows = (
            schema_valid_rows & self.uploaded_data["pending_amount"].isnull()
        )
        self.uploaded_data.loc[
            missing_pending_amount_rows, "errors"
        ] = self.uploaded_data.loc[missing_pending_amount_rows].apply(
            lambda row: append_error(
                row,
                "Pending amount missing for payee",
                "Pending Amount",
                row["pending_amount"],
            ),
            axis=1,
        )

        # Total payout is zero
        zero_total_payout_rows = schema_valid_rows & (
            self.uploaded_data["total_payout"] == 0
        )
        self.uploaded_data.loc[
            zero_total_payout_rows, "errors"
        ] = self.uploaded_data.loc[zero_total_payout_rows].apply(
            lambda row: append_error(
                row,
                f"Total payout is 0 for payee {row['payee_email_id']}",
                "Total Payout",
                row["total_payout"],
            ),
            axis=1,
        )

        # Paid amount is zero
        zero_paid_amount_rows = schema_valid_rows & (
            self.uploaded_data["paid_amount"] == 0
        )
        self.uploaded_data.loc[
            zero_paid_amount_rows, "errors"
        ] = self.uploaded_data.loc[zero_paid_amount_rows].apply(
            lambda row: append_error(
                row,
                "Amount must be greater than 0 to register payment",
                "Paid Amount",
                row["paid_amount"],
            ),
            axis=1,
        )

        # Convert to numeric for comparison to handle invalid strings
        self.uploaded_data["paid_amount_num"] = pd.to_numeric(
            self.uploaded_data["paid_amount"], errors="coerce"
        )
        self.uploaded_data["pending_amount_num"] = pd.to_numeric(
            self.uploaded_data["pending_amount"], errors="coerce"
        )
        self.uploaded_data["total_payout_num"] = pd.to_numeric(
            self.uploaded_data["total_payout"], errors="coerce"
        )

        # Payment already made
        payment_already_made_rows = schema_valid_rows & (
            (self.uploaded_data["total_payout_num"] > 0)
            & (self.uploaded_data["pending_amount_num"] == 0)
        )
        self.uploaded_data.loc[
            payment_already_made_rows, "errors"
        ] = self.uploaded_data.loc[payment_already_made_rows].apply(
            lambda row: append_error(
                row,
                f"Payment already made for {row['payee_email_id']}",
                "Email Address",
                row["payee_email_id"],
            ),
            axis=1,
        )

        # Paid amount is negative and the pending amount is positive
        negative_paid_amount_rows = schema_valid_rows & (
            (self.uploaded_data["paid_amount_num"] < 0)
            & (self.uploaded_data["pending_amount_num"] > 0)
        )
        self.uploaded_data.loc[
            negative_paid_amount_rows, "errors"
        ] = self.uploaded_data.loc[negative_paid_amount_rows].apply(
            lambda row: append_error(
                row,
                f"Payment being registered {row['paid_amount']} is less than the eligible payable amount of {row['pending_amount']}",
                "Paid Amount",
                row["paid_amount"],
            ),
            axis=1,
        )

        # Payment exceeds available balance
        # Exclude negative payment rows from the next check
        not_negative_paid_amount_rows = ~negative_paid_amount_rows
        invalid_payment_rows = (
            schema_valid_rows
            & not_negative_paid_amount_rows
            & (
                (
                    self.uploaded_data["paid_amount_num"].abs()
                    > self.uploaded_data["pending_amount_num"].abs()
                )
                | (
                    (self.uploaded_data["paid_amount_num"] > 0)
                    & (self.uploaded_data["pending_amount_num"] < 0)
                )
            )
        )
        self.uploaded_data.loc[invalid_payment_rows, "errors"] = self.uploaded_data.loc[
            invalid_payment_rows
        ].apply(
            lambda row: append_error(
                row,
                f"Payment exceeds available balance of {row['pending_amount']}",
                "Paid Amount",
                row["paid_amount"],
            ),
            axis=1,
        )

        self.uploaded_data.drop(
            columns=["paid_amount_num", "pending_amount_num", "total_payout_num"],
            inplace=True,
        )
        self.logger.info("Row-level validations complete")

    def _validate_schema(self):
        self.logger.info("Validating schema for each row in uploaded data")
        error_message_map = {
            "payee_email_id": {
                "error_message": "Invalid email provided",
                "error_col_name": "Email Address",
            },
            "paid_amount": {
                "error_message": "Invalid amount entered",
                "error_col_name": "Payment Amount",
            },
        }

        def format_errors(errors, row):
            """
            Format validation errors into structured format using field-level mapping.
            """
            formatted = []
            for field, messages in errors.items():
                # Get the custom error message and column name
                field_mapping = error_message_map.get(field, {})
                custom_msg = field_mapping.get("error_message", messages[0])
                custom_col_name = field_mapping.get("error_col_name", field)

                for _ in messages:
                    formatted.append(
                        {
                            "error_message": custom_msg,
                            "error_col_name": custom_col_name,
                            "error_col_value": row.get(field, ""),
                        }
                    )
            return formatted

        def validate_row(row):
            # Only validate if errors is empty - which implies records that have permission
            if isinstance(row["errors"], list) and len(row["errors"]) > 0:
                return row["errors"]
            try:
                validated_row = self.schema_validator_.validate(row.to_dict())
                if validated_row.get("errors"):
                    return format_errors(validated_row["errors"], row)
                return []
            except SchemaError as e:
                return [
                    {
                        "error_message": str(e),
                        "error_col_name": None,
                        "error_col_value": None,
                    }
                ]

        self.uploaded_data["errors"] = self.uploaded_data.apply(validate_row, axis=1)
        self.logger.info("Schema validation complete")

    def _separate_valid_and_invalid_records(self):
        self.logger.info("Separating valid and invalid records after validation")
        valid_condition = self.uploaded_data["errors"].isnull() | self.uploaded_data[
            "errors"
        ].apply(lambda x: isinstance(x, list) and len(x) == 0)
        invalid_condition = ~valid_condition

        columns_to_convert = ["total_payout", "pending_amount", "paid_amount"]
        for column in columns_to_convert:
            self.uploaded_data[column] = self.uploaded_data[column].astype(str)

        valid_records = self.uploaded_data[valid_condition].to_dict("records")
        invalid_records = self.uploaded_data[invalid_condition].to_dict("records")

        self.logger.info(
            f"Found {len(valid_records)} valid records and {len(invalid_records)} invalid records."
        )
        return valid_records, invalid_records

    def _finalize_validation(self, valid_records, invalid_records):
        self.logger.info("Finalizing validation results")
        if invalid_records:
            self.logger.error(f"Validation failed for these records: {invalid_records}")

        self.valid_uploaded_records_count = len(valid_records)
        self.valid_uploaded_records = valid_records
        self.invalid_uploaded_records = invalid_records

        if not self.valid_uploaded_records_count:
            return (
                False,
                "No valid records found",
                self.invalid_uploaded_records,
                self.valid_uploaded_records,
            )

        self.logger.info("Validation finalized")
        return (
            True,
            "Validation successful",
            self.invalid_uploaded_records,
            self.valid_uploaded_records,
        )

    def _populate_missing_columns(self):
        """
        Populate payee details columns in the uploaded data if any of the required fields are missing
        """
        self.logger.info("Populating missing columns in uploaded data if required")
        additional_required_columns = [
            "payee_name",
            "designation",
            "currency_code",
            "total_payout",
            "pending_amount",
        ]

        # Check if any required column is missing in uploaded_data
        missing_columns = [
            col
            for col in additional_required_columns
            if col not in self.uploaded_data.columns
        ]

        # All required columns are present, no need to fetch details
        if not missing_columns:
            return

        valid_rows = self.uploaded_data["payee_email_id"].notnull()
        payee_email_ids = self.uploaded_data.loc[valid_rows, "payee_email_id"].unique()

        payee_details, payee_frozen_map = get_payee_details(
            self.client_id, self.period_end_date, payee_email_ids
        )
        self.payee_frozen_map = payee_frozen_map

        payee_details_df = pd.DataFrame(payee_details)

        # Ensure all required columns are present in payee_details_df, even if empty
        for col in additional_required_columns:
            if col not in payee_details_df.columns:
                payee_details_df[col] = None

        self.uploaded_data = self.uploaded_data.merge(
            payee_details_df,
            how="left",
            on="payee_email_id",
            suffixes=("", "_computed"),
        )

        for column in additional_required_columns:
            computed_col = f"{column}_computed"
            if computed_col in self.uploaded_data.columns:
                if column not in self.uploaded_data.columns:
                    self.uploaded_data[column] = self.uploaded_data[computed_col]
                else:
                    self.uploaded_data[column] = self.uploaded_data[column].fillna(
                        self.uploaded_data[computed_col]
                    )

        self.uploaded_data.drop(
            columns=[
                col for col in self.uploaded_data.columns if col.endswith("_computed")
            ],
            inplace=True,
        )
        self.logger.info("Missing columns population complete")

    def validate_file(self):
        """Validate the file content using PaymentSchemaValidator for schema validations and does custom validations"""
        self.logger.info("Starting validation process")
        if self.uploaded_data is None:
            return self._log_and_return_error("No data to validate")

        # Validate required and non-empty columns
        if not self._validate_columns():
            error_message = ""
            if self.missing_columns:
                error_message += (
                    f"Missing required columns: {', '.join(self.missing_columns)}. "
                )
            if self.empty_columns:
                error_message += (
                    f"Columns {', '.join(self.empty_columns)} have empty values."
                )
            return self._log_and_return_error(error_message)

        # Sort by rawRecordId if column exists
        if "raw_record_id" in self.uploaded_data.columns:
            self.uploaded_data = self.uploaded_data.sort_values(by="raw_record_id")

        # Populate payee details columns if not already present
        self._populate_missing_columns()

        # Map payout data to the uploaded data
        self._map_payout_data()

        # Initialize errors column
        self._initialize_errors_column()

        # Handle permission checks
        self._handle_permission_check()

        # Validate schema for each row
        self._validate_schema()

        # Preprocess columns
        self._preprocess_columns_in_uploaded_data()

        # Round numeric columns
        self._round_numeric_columns()

        # Perform row-level validations
        self._validate_rows()

        # Separate valid and invalid records
        valid_records, invalid_records = self._separate_valid_and_invalid_records()

        # Log and return results
        self.logger.info("File validation process complete")
        return self._finalize_validation(valid_records, invalid_records)

    def get_valid_record_payee_emails(self):
        """Get a list of valid payee email ids from the valid records"""
        if not self.valid_uploaded_records_count:
            self.logger.error("No valid records found")
            return []
        return [record["payee_email_id"] for record in self.valid_uploaded_records]

    def _prepare_settlement_details(self):
        self.logger.info("Preparing settlement details for valid records")
        settlement_details = {}
        payee_ids = []
        for record in self.valid_uploaded_records:
            payee_email_id = record["payee_email_id"]
            payee_ids.append(payee_email_id)
            settlement_details[payee_email_id] = {
                "calculated_adjustments": self.payout_status_data[payee_email_id][
                    "payout_split_up"
                ],
                "paid_amount": record["paid_amount"],
                "payee_email_id": payee_email_id,
                "period_start_date": self.payout_status_data[payee_email_id][
                    "period_start_date"
                ].strftime("%Y-%m-%d"),
                "period_end_date": self.payout_status_data[payee_email_id][
                    "period_end_date"
                ].strftime("%Y-%m-%d"),
                "comment": record["comment"],
                "payout_knowledge_date": record["payout_knowledge_date"],
            }
        self.logger.info("Settlement details preparation complete")
        return settlement_details, payee_ids

    def _check_ongoing_sync_and_avoid_locking(self):
        self.logger.info("Checking for ongoing sync and locking avoidance")
        result = can_avoid_locking_with_pending_changes(
            self.client_id
        ) and check_sync_running_status(self.client_id)
        self.logger.info(f"Ongoing sync and avoid locking check result: {result}")
        return result

    def _generate_response(self, info, final_status):
        self.logger.info(
            f"Generating response with status {final_status} and info: {info}"
        )
        return Response(
            info,
            status=final_status,
        )

    def _handle_pending_changes(
        self,
        payee_ids,
        is_pending_changes,
        pending_changes_payees_set,
        pending_changes_file_content,
    ):
        self.logger.info(
            "Handling pending changes for payees %s",
            payee_ids,
        )
        if (
            can_avoid_locking_with_pending_changes(self.client_id)
            and is_pending_changes
        ):
            if len(payee_ids) == 1:
                return self._generate_response(
                    {
                        "status": "SUCCESS",
                        "message_code": "PAYMENT_RESTRICTED_SINGLE_PAYEE_MESSAGE",
                        "pending_changes_file_content": pending_changes_file_content,
                        "display_message": "Payment(s) registered successfully for 0 payee(s). However, payments for 1 payee(s) with unsynced commission-impacting changes could not be registered (see downloaded CSV). Run commission sync for these payees or all payees in this payout period and retry payment registration ",
                    },
                    status.HTTP_200_OK,
                )
            elif len(payee_ids) == len(pending_changes_payees_set):
                return self._generate_response(
                    {
                        "status": "SUCCESS",
                        "message_code": "PAYMENT_PARTIALLY_RESTRICTED_MESSAGE",
                        "pending_changes_file_content": pending_changes_file_content,
                        "display_message": f"Payment(s) registered successfully for 0 payee(s). However, payments for {len(pending_changes_payees_set)} payee(s) with unsynced commission-impacting changes could not be registered (see downloaded CSV). Run commission sync for these payees or all payees in this payout period and retry payment registration ",
                    },
                    status.HTTP_200_OK,
                )
            else:
                payee_ids = [
                    email
                    for email in payee_ids
                    if email not in pending_changes_payees_set
                ]
                self.audit["payee_ids"] = payee_ids
        self.logger.info("Pending changes handling complete")
        return payee_ids

    def _log_audit_event(self, payee_ids):
        event_key = f"payout_{self.period_end_date}"
        summary = "Bulk Upload Register Payment"
        updated_by = self.audit["updated_by"]
        self.audit["payee_ids"] = payee_ids
        updated_at = timezone.now()
        audit_services.log(
            self.client_id,
            self.event_type_code,
            event_key,
            summary,
            updated_by,
            updated_at,
            self.audit,
        )

    def _process_payouts_with_locking(
        self,
        settlement_details,
        payee_ids,
        total_payees,
        is_pending_changes,
        pending_changes_payees_set,
        pending_changes_file_content,
    ):
        self.logger.info("Processing payouts with locking for payees")
        if avoid_concurrent_register_payment(self.client_id):
            return self._process_with_locking(
                settlement_details,
                payee_ids,
                total_payees,
                is_pending_changes,
                pending_changes_payees_set,
                pending_changes_file_content,
            )
        return self._process_without_locking(
            settlement_details,
            payee_ids,
            total_payees,
            is_pending_changes,
            pending_changes_payees_set,
            pending_changes_file_content,
        )

    def _process_with_locking(
        self,
        settlement_details,
        payee_ids,
        total_payees,
        is_pending_changes,
        pending_changes_payees_set,
        pending_changes_file_content,
    ):
        self.logger.info("Acquiring lock and processing payouts for payees")
        payout_process_lock_accessor = None
        try:
            payout_process_lock_accessor = PayoutProcessLockAccessor(
                client_id=self.client_id
            )
            processed_payee_ids = acquire_lock_payout_process(
                self.client_id,
                payee_ids,
                self.ped,
                PayeePeriodActionEvents.INITIATE_PAYOUT.value,
            )
            payee_ids_to_process = processed_payee_ids["payee_ids_to_process"]
            conflicting_payees = processed_payee_ids["conflicting_payees"]

            response_data = {
                "status": "SUCCESS",
                "message_code": PayoutProcessMessageCodes.PROCESSED_COMPLETELY.value,
                "conflictingPayeesCsv": None,
                "message": "Successfully processed payouts for selected payees.",
            }
            result = {}
            # Process payees without conflicts
            if payee_ids_to_process:
                result = update_paid_status(
                    client_id=self.client_id,
                    payee_ids=payee_ids_to_process,
                    date=self.period_end_date,
                    settlement_details=settlement_details,
                    audit=self.audit,
                    logged_in_user=self.logged_in_user,
                )
                response_data["message_code"] = (
                    PayoutProcessMessageCodes.PROCESSED_COMPLETELY.value
                )
                response_data["message"] = "Payment(s) registered successfully"

            if len(conflicting_payees) > 0:
                response_data["conflictingPayeesCsv"] = generate_conflicting_payees_csv(
                    conflicting_payees
                )
                response_data["message"] = (
                    f"Payement(s) successfully registered for {len(result)} payees(s). However, registration(s) for {len(conflicting_payees)} payee(s) could not be completed as a previous process is still in progress. Please check the downloaded CSV for details and try again later."
                    if conflicting_payees and result
                    else (
                        "Payment(s) could not be registered for the selected payee(s) as a previous process is still in progress. Please check the downloaded CSV for details and try again later."
                        if conflicting_payees and not result
                        else "Payment(s) registered successfully"
                    )
                )
                response_data["message_code"] = (
                    PayoutProcessMessageCodes.PROCESSED_PARTIALLY.value
                    if conflicting_payees and result
                    else PayoutProcessMessageCodes.PROCESS_FAILED.value
                )

            if (
                can_avoid_locking_with_pending_changes(self.client_id)
                and is_pending_changes
            ):
                return self._generate_response(
                    {
                        "status": "SUCCESS",
                        "message_code": "PAYMENT_PARTIALLY_RESTRICTED_MESSAGE",
                        "pending_changes_file_content": pending_changes_file_content,
                        "display_message": f"Payment(s) registered successfully for {total_payees - len(pending_changes_payees_set)} payee(s). However, payments for {len(pending_changes_payees_set)} payee(s) with unsynced commission-impacting changes could not be registered (see downloaded CSV). Run commission sync for these payees or all payees in this payout period and retry payment registration ",
                    },
                    status.HTTP_200_OK,
                )

            self.logger.info("Payouts with locking completed")
            return Response(response_data, status=status.HTTP_200_OK)
        except Exception as e:
            self.logger.error(f"Error while processing payouts: {str(e)}")
            return Response(
                {"error": "Error while processing payouts"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        finally:
            if payout_process_lock_accessor and payee_ids_to_process and self.ped:
                payout_process_lock_accessor.release_lock(
                    payee_ids_to_process,
                    self.ped,
                    PayeePeriodActionEvents.INITIATE_PAYOUT.value,
                )

    def _process_without_locking(
        self,
        settlement_details,
        payee_ids,
        total_payees,
        is_pending_changes,
        pending_changes_payees_set,
        pending_changes_file_content,
    ):
        self.logger.info("Processing payouts without locking for payees")
        cache_key = f"client_{self.client_id}_{self.event_type_code}"
        if not acquire_payout_lock(cache_key):
            self.logger.info(
                "Could not acquire payout lock. Previous process may still be running."
            )
            return self._generate_response(
                {
                    "status": "FAILED",
                    "message": "The previous payment registration is still being processed. Please wait a few minutes before trying again.",
                },
                status.HTTP_400_BAD_REQUEST,
            )
        try:
            update_paid_status(
                client_id=self.client_id,
                payee_ids=payee_ids,
                date=self.period_end_date,
                settlement_details=settlement_details,
                audit=self.audit,
                logged_in_user=self.logged_in_user,
            )
            if (
                can_avoid_locking_with_pending_changes(self.client_id)
                and is_pending_changes
            ):
                return self._generate_response(
                    {
                        "status": "SUCCESS",
                        "message_code": "PAYMENT_PARTIALLY_RESTRICTED_MESSAGE",
                        "pending_changes_file_content": pending_changes_file_content,
                        "display_message": f"Payment(s) registered successfully for {total_payees - len(pending_changes_payees_set)} payee(s). However, payments for {len(pending_changes_payees_set)} payee(s) with unsynced commission-impacting changes could not be registered (see downloaded CSV). Run commission sync for these payees or all payees in this payout period and retry payment registration ",
                    },
                    status.HTTP_200_OK,
                )
            self.logger.info("Payouts without locking completed")
            return Response(
                {
                    "status": "SUCCESS",
                    "message": "File processed successfully",
                    "message_code": PayoutProcessMessageCodes.PROCESSED_COMPLETELY.value,
                },
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            self.logger.error(f"Error while processing payouts: {str(e)}")
            return Response(
                {"error": "Error while processing payouts"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        finally:
            release_payout_lock(cache_key)

    def process_valid_records(self):
        """Process valid records using the update_paid_status function."""
        self.logger.info("Starting processing of valid records")
        try:
            # Prepare settlement details and get list of payee ids
            settlement_details, payee_ids = self._prepare_settlement_details()

            # Handle ongoing sync or locking issues
            if self._check_ongoing_sync_and_avoid_locking():
                return self._generate_response(
                    {
                        "status": "FAILED",
                        "message_code": "CANNOT_LOCK_ON_ONGOING_SYNC",
                    },
                    status.HTTP_200_OK,
                )

            total_payees = len(payee_ids)

            # Handle pending changes
            (
                is_pending_changes,
                pending_changes_payees_set,
                pending_changes_file_content,
            ) = get_payees_with_pending_changes(
                self.client_id, payee_ids, self.period_end_date
            )
            pending_changes_result = self._handle_pending_changes(
                payee_ids,
                is_pending_changes,
                pending_changes_payees_set,
                pending_changes_file_content,
            )

            if isinstance(pending_changes_result, Response):
                return pending_changes_result
            payee_ids = pending_changes_result

            # Log audit event
            self._log_audit_event(payee_ids)

            # Process payouts with locking mechanism
            return self._process_payouts_with_locking(
                settlement_details=settlement_details,
                payee_ids=payee_ids,
                total_payees=total_payees,
                is_pending_changes=is_pending_changes,
                pending_changes_payees_set=pending_changes_payees_set,
                pending_changes_file_content=pending_changes_file_content,
            )

        except Exception as e:
            self.logger.exception(f"Error processing valid records: {str(e)}")
            return Response(
                {"error": "Error while processing payouts"},
                status=status.HTTP_400_BAD_REQUEST,
            )

    def persist_records(self):
        pass

    def preprocess_records(self):
        """Preprocess uploaded data by setting default values for missing or null fields."""
        # Define default values for columns
        default_values = {
            "comment": "",
            "payout_knowledge_date": self.payout_date,
        }

        # Iterate over default values and set them in the DataFrame
        for column, default_value in default_values.items():
            if column in self.uploaded_data.columns:
                self.uploaded_data[column] = (
                    self.uploaded_data[column].fillna(default_value).astype(str)
                )
            else:
                self.uploaded_data[column] = default_value


def get_payee_details(client_id, period_end_date, payee_email_ids):
    """
    Compute payee details for the given email IDs and period end date.
    """
    logger.info(
        "Fetching payee details for client_id %s, period_end_date %s, payee_email_ids %s",
        client_id,
        period_end_date,
        payee_email_ids,
    )
    payee_email_ids = tuple(payee_email_ids)
    period_end_date = make_aware(
        end_of_day(datetime.strptime(period_end_date, "%d-%m-%Y"))
    )

    ea = EmployeeAccessor(client_id)
    epa = EmployeePayrollAccessor(client_id)
    ps = PayoutStatusAccessor(client_id)

    # Fetch employee details
    # Even for exited users have to fetch details
    employee_details = ea.get_all_employees_with_limit(
        email_list=payee_email_ids, is_to_fetch_exited_users=True
    )
    employee_details_map = {
        employee.employee_email_id: employee for employee in employee_details
    }

    # Fetch employee payroll details
    employee_payroll_details = epa.get_all_employees_payroll_for_date_qs(
        date=period_end_date, employee_email_ids=payee_email_ids
    )
    employee_payroll_details_map = {
        employee.employee_email_id: employee for employee in employee_payroll_details
    }

    # Fetch payout status details
    payout_status_details = ps.get_payee_period_records(
        payee_ids=payee_email_ids, ped=period_end_date
    )
    payout_status_details_map = {
        payout["payee_email_id"]: payout for payout in payout_status_details
    }

    payee_frozen_map = {
        email: (
            payout_status_details_map.get(email, {}).get("comm_calc_status") == "Frozen"
            and payout_status_details_map.get(email, {}).get("settlement_calc_status")
            == "Frozen"
        )
        for email in payee_email_ids
    }

    # Combine data for each payee
    result = []
    for payee_email_id in payee_email_ids:
        employee = employee_details_map.get(payee_email_id, {})
        employee_payroll = employee_payroll_details_map.get(payee_email_id, {})
        payout_status = payout_status_details_map.get(payee_email_id, {})

        combined_data = {
            "payee_name": employee.full_name if employee else "",
            "designation": employee_payroll.designation if employee_payroll else "",
            "payee_email_id": payee_email_id,
            "currency_code": employee_payroll.pay_currency if employee_payroll else "",
            "total_payout": round(payout_status.get("total_payout", 0), 2),
            "pending_amount": round(payout_status.get("pending_amount", 0), 2),
            "paid_amount": 0,  # Default value for template
            "comment": None,  # Default value for template
        }
        result.append(combined_data)

    logger.info("Payee details fetching and combination complete")
    return result, payee_frozen_map


def filter_payees_by_view_payout_others_permission(
    client_id, payee_email_ids, logged_in_user
):
    """
    Checks for "view:payoutvalueothers" permission. If not payout value others permission
    then filter out other payee ids except that of logged in user.
    Args:
        client_id: int
        payee_email_ids: list of str
        logged_in_user: str
    """
    permitted = []
    not_permitted = []
    for payee_email in payee_email_ids:
        has_permission = is_payout_value_permission(client_id, logged_in_user)
        if has_permission or logged_in_user == payee_email:
            permitted.append(payee_email)
        else:
            not_permitted.append(payee_email)
    return permitted, not_permitted


def filter_payees_by_data_permission(
    client_id, payee_email_ids, logged_in_user, component, effective_date=None
):
    """
    Returns (permitted, not_permitted) payees based on data permission for the component.
    Args:
        client_id: int
        payee_email_ids: list of str
        logged_in_user: str
        component: str
        effective_date: str
    """
    data_permission = get_data_permission(client_id, logged_in_user, component)
    if data_permission and data_permission["type"] != "ALL_DATA":
        valid_emails = set(
            get_valid_payee_emails(
                client_id, logged_in_user, data_permission, effective_date
            )
        )
        permitted = [email for email in payee_email_ids if email in valid_emails]
        not_permitted = [
            email for email in payee_email_ids if email not in valid_emails
        ]
    else:
        permitted = list(payee_email_ids)
        not_permitted = []
    return permitted, not_permitted


def get_prefilled_template(
    client_id, period_end_date, payee_email_ids, logged_in_user=None
):
    """
    Get prefilled template for payees for period.
    Exclude payees for which the user does not have payout permission or data permission.
    """
    logger.info(
        "Generating prefilled template for client_id %s, period_end_date %s, payee_email_ids %s",
        client_id,
        period_end_date,
        payee_email_ids,
    )

    # Data permission filter
    permitted_email_ids, _ = filter_payees_by_data_permission(
        client_id,
        payee_email_ids,
        logged_in_user,
        RBACComponent.PAYOUTS_STATEMENTS.value,
    )

    # Checking for "view:payoutvalueothers" permission. If not payout value others permission
    # filter out other payee ids except that of logged in user.
    permitted_email_ids, _ = filter_payees_by_view_payout_others_permission(
        client_id, permitted_email_ids, logged_in_user
    )

    # If not manage:payouts permission, then filter out payees that are not frozen
    # Get payee details and frozen map
    payee_details, payee_frozen_map = get_payee_details(
        client_id, period_end_date, permitted_email_ids
    )

    user_permissions = set(get_ui_permissions(client_id, logged_in_user))
    has_manage_payouts = RbacPermissions.MANAGE_PAYOUTS.value in user_permissions
    filtered_payee_details = []
    for detail in payee_details:
        payee_email = detail["payee_email_id"]
        is_frozen = payee_frozen_map.get(payee_email, False)
        if is_frozen:
            filtered_payee_details.append(detail)
        elif has_manage_payouts:
            filtered_payee_details.append(detail)
        else:
            continue

    df = pd.DataFrame(filtered_payee_details)
    df.rename(
        columns={
            "payee_name": "Payee Name",
            "designation": "Designation",
            "payee_email_id": "Email Address",
            "currency_code": "Currency Code",
            "total_payout": "Total Payout",
            "pending_amount": "Pending Amount",
            "paid_amount": "Payment Amount",
            "comment": "Comment",
        },
        inplace=True,
    )
    clean_df = replace_nan_nat_for_df(df)
    csv_file = clean_df.to_csv(index=False)
    logger.info("Prefilled template generation complete")
    return True, csv_file
