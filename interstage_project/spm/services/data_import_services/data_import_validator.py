import csv
import re
import time
from collections import OrderedDict, defaultdict
from datetime import datetime
from typing import Callable

import charset_normalizer
import pandas as pd
from chardet.universaldetector import UniversalDetector
from schema import SchemaError

from commission_engine.snowflake_accessors.custom_object_data_accessor import (
    get_custom_object_data,
    get_row_keys,
)
from commission_engine.utils import generate_hash
from interstage_project.utils import LogWithContext
from spm.bulk_uploader.base_bulk_uploader import BaseBulkUploader
from spm.constants.data_import_constants import DATE_FORMAT_MAP
from spm.schema_validators.base_schema_validator import BaseSchemaValidator
from spm.types import DataValidationType


class CustomObjectDataValidator(BaseSchemaValidator):
    """
    This class is used to validate the data for custom object bulk upload.
    This class validates email, date, boll, number, percent and string fields.

    We are not converting the values therefore the validation functions return True if valid and SchemaError if in_valid
    """

    def validate_employee_email(self, email_id: str) -> bool:
        email_id = email_id.lower().strip()

        pat = r"\b[A-Za-z0-9._%'+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z|0-9]{1,}\b"
        if not re.fullmatch(pat, email_id):
            raise SchemaError("Invalid email format.")
        else:
            return True

    def validate_percent(self, percent: str) -> bool:
        try:
            if percent.endswith("%"):
                percent = percent[:-1]
            if ",," in percent:
                raise SchemaError("Invalid percentage format.")
            if "." in percent and "," in percent:
                if percent.rindex(",") > percent.index("."):
                    raise SchemaError("Invalid percentage format.")
            percent = percent.replace(",", "")
            float(percent)
            return True

        except Exception as exc:
            raise SchemaError("Invalid percentage format.") from exc

    def check_is_mandatory(self, validate_func: Callable, is_mandatory=False):
        def func(val):
            if not val:
                if is_mandatory:
                    raise SchemaError("This field is required.")
                else:
                    return True
            return validate_func(val)

        return func

    def validate_co_string(self, val: str) -> bool:
        if not isinstance(val, str):
            raise SchemaError("Invalid string format.")
        return True

    def validate_co_bool_str(self, val: str) -> bool:
        if type(val) is bool:
            return True
        if val.lower() == "true":
            return True
        if val.lower() == "false":
            return True

        raise SchemaError("Invalid boolean format.")

    def validate_co_number(self, val: str) -> bool:
        try:
            if ",," in val:
                raise SchemaError("Invalid number format.")
            if "." in val and "," in val:
                if val.rindex(",") > val.index("."):
                    raise SchemaError("Invalid number format.")
            val = val.replace(",", "")
            float(val)
            return True
        except ValueError as e:
            raise SchemaError("Invalid number format.") from e

    def date_type_validator(self, value):
        req_date_format = "%d-%b-%Y"
        try:
            if value:
                value = value.strip()
                date = datetime.strptime(value, self.date_format)
                return date.strftime(req_date_format)
            else:
                raise SchemaError("Invalid date value.")
        except ValueError:
            try:
                if self.is_excel:
                    value = value.strip()
                    date = datetime.strptime(value, "%Y-%m-%d %H:%M:%S")
                    return date.strftime(req_date_format)
            except ValueError:
                pass

        raise SchemaError(
            f"Date should be in the format {DATE_FORMAT_MAP[self.date_format]}."
        )

    def __init__(
        self,
        client_id,
        raw_records,
        co_data_header_map,
        co_data_type_map,
        date_format,
        is_excel: bool = False,
    ):
        self.client_id: int = client_id
        self.co_data_header_map = co_data_header_map
        self.raw_records = raw_records
        self.co_data_type_map = co_data_type_map
        self.validator_functions_map = {
            "string": self.validate_co_string,
            "integer": self.validate_co_number,
            "date": self.date_type_validator,
            "email": self.validate_employee_email,
            "boolean": self.validate_co_bool_str,
            "percentage": self.validate_percent,
        }
        self.date_format: str = date_format
        self.is_excel: bool = is_excel
        self.user_format_bool: bool = False
        self.pandas_format_bool: bool = False
        """
        We build the all_fields dict from the co_data_header_map, co_data_type_map and validator_functions_map.
        
        co_data_map = {"co3_email":"Email Header", "co3_date":"Start Date Header", "co3_number":"Number Header"}
        co_data_type_map = {"co3_email":{"type":"email"}, "co3_date":{"type":"date"}, "co3_number":{"type":"number"}}
        
        all_fields = {"co3_email": self.validate_employee_email, "Start Date Header": self.validate_date, "Number Header": self.validate_number}
        """
        self.all_fields = {
            key: self.check_is_mandatory(
                self.validator_functions_map[self.co_data_type_map[key]["type"]]
            )
            for key in self.co_data_header_map.keys()
        }
        super().__init__(self.all_fields)


class CustomObjectDataUploader(BaseBulkUploader):
    """
    This class contains all the methods to upload the data for custom object bulk upload.

    CustomObjectData Validation constsis of 3 parts
    1. Primary Key Validation - if error then it is moved to error list
    2. Check for duplicate - if duplicate then move to error list along with the original record
    3. Extract data based on the strategy:
        "create": Consider only new records other records are moved to ignored list
        "update": Consider only existing records other records are moved to ignored list
        "create_update": Consider both new and existing records
        "delete": Consider only existing records other records are moved to ignored list
    4. Data Validation - if error then it is moved to error list
    """

    def __init__(
        self,
        client_id,
        custom_object_id,
        config_map,
        csv_object,
        logger=None,
        sub_set_validation=False,
        uploaded_file_name="uploaded_file_name",
    ):
        self.client_id = client_id
        self.custom_object_id = custom_object_id
        self.csv_object = csv_object
        self.co_data_type_map = None
        self.extracted_data = []
        self.error_records = []
        self.duplicate_records = defaultdict(list)
        self.without_duplicate_records = OrderedDict()
        self.db_records = []
        self.ignored_records = []
        self.ignored_record_keys = []
        self.logger = logger if logger else LogWithContext({"client_id": client_id})
        self.config_map = config_map
        self.co_data_header_map = {}
        self.schema_validators = []
        self.uploaded_file_name = uploaded_file_name
        self.is_excel: bool = (
            True if uploaded_file_name.split(".")[-1] in ["xls", "xlsx"] else False
        )
        self.sub_set_validation = sub_set_validation  # when this is set to be true along with error field and error_dict field will be created which stores errors as dict
        super(CustomObjectDataUploader, self).__init__(client_id, [])

    def validate_schema(self):
        """
        This function iterates through all the rows and columns to validate each data with Schema
        We initialize each validator for each column and finally consolidate all errors.
        """
        # all_records_invalidate = False
        start_time = start_time = time.time()
        self.logger.info("BEGIN: VALIDATE SCHEMA")
        for idx, record in enumerate(self.raw_records):
            row_key = record["row_key"]
            errors = {}
            for validator in self.schema_validators:
                self.raw_records[idx] = validator.validate(
                    record, error_record_ids=self.error_record_ids
                )
                self.raw_records[idx]["row_key"] = row_key
                errors = self.raw_records[idx].get("errors", {}) | errors
            self.raw_records[idx]["errors"] = errors
            if self.raw_records[idx].get("errors"):
                self.error_record_ids.append(row_key)
        self.logger.info(f"END: VALIDATE SCHEMA {time.time() - start_time}")

    def detect_encoding_using_chardet(self):
        """Tries to detect the encoding of csv file using chardet"""
        self.logger.info("Trying to detect encoding using chardet")
        self.csv_object.seek(0)
        detector = UniversalDetector()
        for line in self.csv_object.readlines():
            detector.feed(line)
            if detector.done:
                break
        # To increase the accuracy in detecting the encoding
        self.csv_object.seek(0)
        lines_in_file = self.csv_object.readlines()
        if len(lines_in_file) < 20:
            self.logger.info("The file has less than 20 lines")
            for _ in range(10):
                for line in lines_in_file:
                    detector.feed(line)

        detector.close()
        detected_format = detector.result
        return detected_format

    def detect_encoding_using_charset_normalizer(self):
        """Tries to detect the encoding of the csv file usign charset normalizer"""
        self.logger.info("Trying to detect encoding using charset Normalizer")
        self.csv_object.seek(0)
        binary_data_1 = self.csv_object.read()
        # Use charset_normalizer to detect encoding
        detected_format = charset_normalizer.detect(binary_data_1)
        return detected_format

    def detect_and_decode_csv(self, binary_data):
        """
        Attempts to detect encoding and decode CSV data using multiple detectors in sequence.
        Returns the decoded CSV data if successful, otherwise raises an exception.
        """
        # List of detector functions to try in order
        detectors = {
            "chardet": self.detect_encoding_using_chardet,
            "charset_normalizer": self.detect_encoding_using_charset_normalizer,
            # Add more detectors here if needed in the future
        }

        # detector_names = ["chardet", "charset_normalizer"]
        failed_attempts = []

        for detector_name, detector in detectors.items():
            try:
                detected_format = detector()

                if (
                    detected_format.get("encoding") is None
                    or detected_format.get("confidence") < 0.7
                ):
                    self.logger.info(
                        f"Low confidence (< 0.7) detection using {detector_name}: {detected_format}"
                    )
                    failed_attempts.append(f"{detector_name}: {detected_format}")
                    continue

                self.logger.info(
                    f"Successfully detected encoding using {detector_name}: {detected_format}"
                )
                encoded_format = detected_format["encoding"]
                return binary_data.decode(encoded_format)

            except Exception as e:
                self.logger.info(f"Error using {detector_name}: {str(e)}")
                failed_attempts.append(f"{detector_name}: {str(e)}")

        # If we get here, all detectors failed
        error_message = f"Unable to detect encoding of the file after trying: {', '.join(failed_attempts)}"
        self.logger.info(error_message)
        raise Exception(error_message)

    def extract_required_data(self):
        """
        This function extracts the data from the CSV/Excel file and stores it in extracted_data.
        While extracting, only the mapped columns are considered, and other columns are pruned.
        """
        self.logger.info("BEGIN: extract_required_data")
        co_obj_map = self.config_map["system_name_header_name_map"]

        self.csv_object.seek(0)  # Reset the BytesIO file object to the beginning
        binary_data = self.csv_object.getvalue()

        try:
            # First try to process as Excel
            file_extension = self.uploaded_file_name.split(".")[-1]
            # Determine the Excel engine based on file extension
            engine = None
            if file_extension.endswith(".xls"):
                engine = "xlrd"

            # Read Excel file info without loading the data
            excel_file = pd.ExcelFile(binary_data, engine=engine)
            sheet_names = excel_file.sheet_names
            # Use first sheet by default
            if len(sheet_names) > 0:
                self.logger.info(f"Processing Excel sheet: {sheet_names[0]}")
                df = pd.read_excel(
                    binary_data,
                    sheet_name=sheet_names[0],
                    dtype=str,
                    na_filter=False,
                    nrows=(
                        10 if self.sub_set_validation else None
                    ),  # for the 10 rows subset validation taking only the first 10 rows
                )
                csv_data = df.to_csv(index=False)
            else:
                raise Exception("No sheets found in the Excel file")

        except Exception as e:
            if "No sheets found" in str(e) or "No valid data sheet" in str(e):
                self.logger.info("No valid data sheet found in the Excel file")
                raise
            # If Excel processing fails, treat as CSV
            csv_data = self.detect_and_decode_csv(binary_data)

        csv_reader = csv.DictReader(csv_data.splitlines())
        mapped_headers = co_obj_map.keys()

        # We prune data so that we reduce the volume of data that needs to be evaluated
        # Pruning is done by omitting the columns that are not mapped
        for row in csv_reader:
            pruned_data = {}
            for key in mapped_headers:
                if co_obj_map[key] in row:
                    value = str(row[co_obj_map[key]]).strip()
                    pruned_data[key] = value
            self.extracted_data.append(pruned_data)

        if len(self.extracted_data) == 0:
            self.logger.info("No data found in the CSV/Excel file")

        # initialize co_header_map for further processing
        self.co_data_header_map = co_obj_map
        self.logger.info("END: extract_required_data")
        return True

    def key_date_validator(self, date_format: str, date: str) -> bool:
        """
        This function validates the date format of the primary key.
        """
        try:
            datetime.strptime(date, date_format)
            return True
        except ValueError:
            return False

    def get_date_columns(self, keys: list[str]):
        date_columns = []
        for key in keys:
            if (
                key in self.config_map["custom_object_meta_data"]
                and self.config_map["custom_object_meta_data"][key]["type"] == "date"
            ):
                date_columns.append(key)
        return date_columns

    def validate_row(
        self,
        row: dict,
        keys: list[str],
        date_keys: list[str],
        validated_data: dict | None = None,
    ) -> dict:
        """
        This function validates the row based on the keys (primary or snapshot).

        1. Check if the cell value is empty.
        2. Check if the key is a date then validate the date format.
        3. If the key is already validated, then use the validated_data from the previous key validation.

        Returns (as dict):
            - has_error: bool -> If the row has any error.
            - has_date_error: bool -> If the row has any date error.
            - missing_keys: list[str] -> List of keys which are missing.
            - date_error_keys: list[str] -> List of keys which are date error.
            - key_data_list: list[str] -> List of values.
            - validated_data: dict -> Has the validated / transformed data.
        """
        is_error = False
        is_date_error = False
        missing_keys = []
        date_error_keys = []
        key_data_list = []

        if validated_data is None:
            validated_data = {}

        for key in keys:
            data = str(row.get(key)).strip()

            if key in validated_data:
                key_data_list.append(validated_data[key])
                continue

            # Validation 1: Check if the cell value is empty.
            if data == "":
                is_error = True
                missing_keys.append(key)

            # Validation 2: Check if the key is a date then validate the date format.
            if key in date_keys:
                date_format_to_convert = self.config_map["date_format_map"].get(
                    key, "%d-%b-%Y"
                )
                # If the date format is invalid then it is a error record
                if (
                    not self.key_date_validator(date_format_to_convert, data)
                    and not self.is_excel
                ):
                    is_date_error = True
                    date_error_keys.append(key)
                elif (
                    self.is_excel
                    and not self.key_date_validator("%Y-%m-%d %H:%M:%S", data)
                    and not self.key_date_validator(date_format_to_convert, data)
                ):
                    is_date_error = True
                    date_error_keys.append(key)
                # If the date format is valid then convert the date to the required format(DD-MMM-YYYY) for generating row_key
                else:
                    date_format_to_convert = (
                        date_format_to_convert
                        if self.key_date_validator(date_format_to_convert, data)
                        else "%Y-%m-%d %H:%M:%S"
                    )

                    data = datetime.strptime(data, date_format_to_convert).strftime(
                        "%d-%b-%Y"
                    )

            validated_data[key] = data
            key_data_list.append(data)
        # if self.multi_date_format and self.is_excel:
        #     self.first_multi_format_row_key = key_data_list
        return {
            "has_error": is_error,
            "has_date_error": is_date_error,
            "missing_keys": missing_keys,
            "date_error_keys": date_error_keys,
            "key_data_list": key_data_list,
            "validated_data": validated_data,
        }

    def primary_and_snapshot_key_validator(self, upload_type: str = "") -> list[str]:
        """
        This function validates the primary and snapshot keys of the extracted data.

        Returns (as list):
            - valid_row_keys: list[str] -> List of row keys which are valid.
        """
        self.logger.info("BEGIN: primary_and_snapshot_key_validator")

        primary_keys = self.config_map["primary_keys"]
        snapshot_keys = self.config_map["snapshot_keys"]

        valid_row_keys = []

        # If primary keys and snapshot keys are same, then we don't need to validate the snapshot keys again
        skip_snapshot_validation = set(primary_keys) == set(snapshot_keys)

        for row in self.extracted_data:
            # Validate the primary keys
            primary_validation = self._validate_key_set(row, primary_keys)
            snapshot_validation = {"is_valid": False}

            if upload_type == "delete":
                # If type is delete, then we don't need to validate the snapshot keys
                snapshot_validation = {"is_valid": True}
            elif skip_snapshot_validation:
                self.logger.info(
                    "Skipping snapshot validation as primary keys and snapshot keys are same."
                )
                # If primary keys and snapshot keys are same, then row_key is same for primary and snapshot
                if primary_validation["is_valid"]:
                    snapshot_validation = {
                        "is_valid": True,
                        "row_key": primary_validation["row_key"],
                    }
            else:
                # Validate the snapshot keys, use the validated_data from primary key validation to avoid duplicate column validation.
                snapshot_validation = self._validate_key_set(
                    row, snapshot_keys, primary_validation["validated_data"]
                )

            if primary_validation["is_valid"] and snapshot_validation["is_valid"]:
                self.process_valid_row(row, primary_validation, snapshot_validation)
                valid_row_keys.append(primary_validation["row_key"])
            else:
                self.add_error_record(row, primary_validation, snapshot_validation)

        self.logger.info("END: primary_and_snapshot_key_validator")
        return valid_row_keys

    def _validate_key_set(
        self, row: dict, keys: list[str], validated_data: dict | None = None
    ) -> dict:
        """
        This function validates the keys. (primary or snapshot)

        Arguments:
            - row: dict -> Row to be validated against the keys.
            - keys: list[str] -> List of primary or snapshot keys to be validated.
            - validated_data: dict | None -> If one of the keys is already validated, then validated_data will have the transformed / validated data for that key.

        Returns (as dict):
            - is_valid: bool -> If the row is valid.
            - row_key: str -> Row key. (only if is_valid is True)
            - validated_data: dict -> Validated data.
        """
        date_keys = self.get_date_columns(keys)

        validation_result = self.validate_row(row, keys, date_keys, validated_data)

        if validation_result["has_error"] or validation_result["has_date_error"]:
            return {"is_valid": False, **validation_result}

        row_key = "#:::#".join(validation_result["key_data_list"]).lower()
        return {"is_valid": True, "row_key": row_key, **validation_result}

    def add_error_record(
        self, item: dict, primary_validation: dict, snapshot_validation: dict
    ):
        """
        This function adds the error record to the error_records list.

        1. If the row has both primary and snapshot errors in different columns, then the error message will be comma separated for both the keys.
        2. For row with both primary and snapshot errors in same column, we will only show the error message for the primary key.
        3. For empty column data, the error message will be "Missing primary key" or "Missing snapshot key".
        4. For invalid date format, the error message will be "Invalid date format in primary key" or "Invalid date format in snapshot key".
        """
        error_item = item.copy()
        error_messages = []
        error_messages_dict = {}

        for validation in [primary_validation, snapshot_validation]:
            key_type = "primary" if validation == primary_validation else "snapshot"

            if validation["is_valid"]:
                # For delete upload type or when primary and snapshot keys are same, the validation["is_valid"] will be True
                # So we don't need to add the error message
                continue

            if validation.get("has_error"):
                error_messages.extend(
                    f"{self.co_data_header_map[key]}: Missing {key_type} key"
                    for key in validation["missing_keys"]
                )
                if self.sub_set_validation:
                    for key in validation["missing_keys"]:
                        error_messages_dict[key] = f"Missing {key_type} key"

            if validation.get("has_date_error"):
                error_messages.extend(
                    f"{self.co_data_header_map[key]}: Invalid date format in {key_type} key. Use {DATE_FORMAT_MAP[self.config_map['date_format_map'].get(key,'DD-MM-YYYY')]} format"
                    for key in validation["date_error_keys"]
                    if key not in validation["missing_keys"]
                )
                if self.sub_set_validation:
                    for key in validation["date_error_keys"]:
                        if key not in validation["missing_keys"]:
                            error_messages_dict[key] = (
                                f"Invalid date format in {key_type} key. Use {DATE_FORMAT_MAP[self.config_map['date_format_map'].get(key,'DD-MM-YYYY')]} format"
                            )

        error_item["error"] = ", ".join(error_messages)
        if self.sub_set_validation:
            error_item["error_dict"] = error_messages_dict
        self.error_records.append(error_item)

    def process_valid_row(
        self, item: dict, primary_validation: dict, snapshot_validation: dict
    ):
        """
        This function processes the valid row.

        1. A record is duplicate if the row_key already exists in duplicate_records
        2. A record is a duplicate for the 1st time if it's row_key is already exist in without_duplicate_rexords
            Then delete the record which is already present in without_duplicate records which  is also a duplicate.
        3. A record which is not a duplicate record is a unique record
        """
        row_key = primary_validation["row_key"]

        # If upload_type is delete, we skip the snapshot validation thus snapshot_data_row_key will be None
        snapshot_data_row_key = snapshot_validation.get("row_key", None)

        # Check if we identified this row_key as duplicate previously
        if row_key in self.duplicate_records:
            item["error"] = "Duplicate record found."
            if self.sub_set_validation:
                item["error_dict"] = {"_": "Duplicate record found."}
            self.duplicate_records[row_key].append(item)
            return

        # Check if the record is already present in without_duplicate_records
        if row_key in self.without_duplicate_records:
            item["error"] = "Duplicate record found."
            if self.sub_set_validation:
                item["error_dict"] = {"_": "Duplicate record found."}
            self.duplicate_records[row_key].append(item.copy())
            self.without_duplicate_records[row_key]["error"] = "Duplicate record found."
            if self.sub_set_validation:
                self.without_duplicate_records[row_key]["error_dict"] = {
                    "_": "Duplicate record found."
                }
            del self.without_duplicate_records[row_key]["row_key"]
            del self.without_duplicate_records[row_key]["snapshot_row_key"]
            self.duplicate_records[row_key].append(
                self.without_duplicate_records[row_key]
            )
            del self.without_duplicate_records[row_key]
        else:
            self.without_duplicate_records[row_key] = item.copy()
            self.without_duplicate_records[row_key]["row_key"] = row_key
            self.without_duplicate_records[row_key][
                "snapshot_row_key"
            ] = snapshot_data_row_key

    def data_type_validator(self, records_to_validate: list):
        """
        This function uses Schema validator to validate the data type.
        This function initializes the CustomObjectDataValidator class and calls the validate_schema function.
        """
        self.logger.info("BEGIN: data_type_validator")

        for sys_name, header_name in self.co_data_header_map.items():
            schema_validator = CustomObjectDataValidator(
                self.client_id,
                records_to_validate,
                {sys_name: header_name},
                self.config_map["custom_object_meta_data"],
                self.config_map["date_format_map"].get(sys_name, "%d-%b-%Y"),
                self.is_excel,
            )
            self.schema_validators.append(schema_validator)

        self.raw_records = list(records_to_validate)
        self.validate_schema()
        self.logger.info("END: data_type_validator")

    def build_error_and_ignored_records(self, ignored_reason=None):
        """
        This function builds the error and ignored records.
        The error records are stored in error_records with the error message.
        The ignored records are stored in ignored_records.
        """
        self.logger.info("BEGIN: build_error_and_ignored_records")

        # Build Ignored records
        # Create and Update doesn't have any ignored records so it doesn't have a reason to ignore
        if ignored_reason:
            for record_id in self.ignored_record_keys:
                temp_record = self.without_duplicate_records[record_id].copy()
                del temp_record["row_key"]
                del temp_record["snapshot_row_key"]
                temp_record["reason"] = ignored_reason
                self.ignored_records.append(temp_record)
        error_record_id_set = set(self.error_record_ids)

        # Build Error records
        for record in self.raw_records:
            if record["row_key"] in error_record_id_set:
                temp_record = record.copy()
                error_messages = []
                error_messages_dict = {}
                for key, value in record["errors"].items():
                    for error_message in value:
                        # WHen we encounter error in decoding the there will be a column_name mismatch
                        # We cannot validate the data for that column
                        if key == "_":
                            error_messages.append(
                                "Encoding format of file is unsupported."
                            )
                            if self.sub_set_validation:
                                error_messages_dict[key] = (
                                    "Encoding format of file is unsupported."
                                )
                            self.logger.info(f"Error in decoding characters on {value}")
                        else:
                            error_messages.append(
                                f"{self.co_data_header_map[key]} : {error_message}"
                            )
                            if self.sub_set_validation:
                                error_messages_dict[key] = error_message
                temp_record["error"] = ", ".join(error_messages)
                if self.sub_set_validation:
                    temp_record["error_dict"] = error_messages_dict
                del temp_record["errors"]
                del temp_record["row_key"]
                del temp_record["snapshot_row_key"]
                self.error_records.append(temp_record)

        # Append Duplicate records to error records
        for row_key in self.duplicate_records.keys():
            self.error_records.extend(self.duplicate_records[row_key])
        self.logger.info("END: build_error_and_ignored_records")

    def get_records_for_create(self) -> DataValidationType:
        """
        This function
        1. extracts the data from the csv file
        2. validates the primary key
        3. separate repeated records into ignored_records and records_to_validate from without_duplicate_records
        4. validate the data type of the records_to_validate
        5. return the records_to_validate

        For create we need only the records which are not present in the database.

        return structure is `DataValidationType`. Refer to the type definition for more details.
        """
        self.logger.info("BEGIN: get_records_for_create")
        start_time = time.time()

        is_date_valid = self.extract_required_data()
        if not is_date_valid:
            return {
                "successful_records": {
                    "csv_records_to_create": [],
                    "csv_records_to_update": [],
                    "csv_records_to_delete": [],
                    "db_records": [],
                },
                "total_records_count": len(self.error_records),
                "error_records": self.error_records,
                "ignored_records": self.ignored_records,
            }

        self.logger.info(f"Time taken to extract data: {time.time() - start_time}")

        start_time = time.time()
        extracted_primary_keys = self.primary_and_snapshot_key_validator()
        self.logger.info(
            f"Time taken to validate primary key: {time.time() - start_time}"
        )

        self.db_records = set(
            get_row_keys(
                self.client_id,
                self.custom_object_id,
                options={"row_key": extracted_primary_keys},
            )
        )
        records_to_validate = []

        # 3. separate repeated records into ignored_records and records_to_validate from without_duplicate_records
        for data_key in self.without_duplicate_records.keys():
            if data_key in self.db_records:
                self.ignored_record_keys.append(data_key)
            else:
                records_to_validate.append(self.without_duplicate_records[data_key])

        # 4. validate the data type of the records_to_validate
        start_time = time.time()
        self.data_type_validator(records_to_validate)
        self.logger.info(
            f"Time taken to validate data type: {time.time() - start_time}"
        )

        # Build error records
        ignored_reason = "Ignored because this is existing data, and the chosen import type is Create new data."
        start_time = time.time()
        self.build_error_and_ignored_records(ignored_reason)

        successful_keys = (
            set(self.without_duplicate_records.keys())
            - set(self.ignored_record_keys)
            - set(self.error_record_ids)
        )
        successful_records = [
            self.without_duplicate_records[key] for key in successful_keys
        ]

        self.logger.info(
            f"Time taken to build error records: {time.time() - start_time}"
        )
        self.logger.info(
            f"END: get_records_for_create, successful_records - {len(successful_keys)}, error_records - {len(self.error_records)}, ignored_records - {len(self.ignored_records)}"
        )
        return {
            "successful_records": {"csv_records_to_create": successful_records},
            "total_records_count": len(self.extracted_data),
            "error_records": self.error_records,
            "ignored_records": self.ignored_records,
        }

    def get_records_for_update(self) -> DataValidationType:
        """
        This function
        1. extracts the data from the csv file
        2. validates the primary key
        3. separate repeated records into ignored_records and records_to_validate from without_duplicate_records
        4. validate the data type of the records_to_validate
        5. return the records_to_validate

        For update we need only the records which are already present in the database.

        return structure is `DataValidationType`. Refer to the type definition for more details.
        """
        self.logger.info("BEGIN: get_records_for_update")
        start_time = time.time()
        is_date_valid = self.extract_required_data()
        if not is_date_valid:
            return {
                "successful_records": {
                    "csv_records_to_create": [],
                    "csv_records_to_update": [],
                    "csv_records_to_delete": [],
                    "db_records": [],
                },
                "total_records_count": len(self.error_records),
                "error_records": self.error_records,
                "ignored_records": self.ignored_records,
            }

        self.logger.info(f"Time taken to extract data: {time.time() - start_time}")

        start_time = time.time()
        extracted_primary_keys = self.primary_and_snapshot_key_validator()

        self.logger.info(
            f"Time taken to validate primary key: {time.time() - start_time}"
        )

        if len(extracted_primary_keys) > 0:
            records = get_custom_object_data(
                self.client_id,
                self.custom_object_id,
                options={"row_key": extracted_primary_keys},
            )
            self.db_records = generate_hash(records, "row_key")
        else:
            self.logger.info("No Valide data found in the CSV file")
            self.db_records = []

        records_to_validate = []

        # 3. separate repeated records into ignored_records and records_to_validate from without_duplicate_records
        for data_key in self.without_duplicate_records.keys():
            if not (data_key in self.db_records):
                self.ignored_record_keys.append(data_key)
            else:
                records_to_validate.append(self.without_duplicate_records[data_key])

        # 4. validate the data type of the records_to_validate
        start_time = time.time()
        self.data_type_validator(records_to_validate)
        self.logger.info(
            f"Time taken to validate data type: {time.time() - start_time}"
        )

        # Build error records
        start_time = time.time()
        ignored_reason = "Ignored because this is new data, but the chosen import type is Update existing data."
        self.build_error_and_ignored_records(ignored_reason)
        self.logger.info(
            f"Time taken to build error records: {time.time() - start_time}"
        )

        successful_keys = (
            set(self.without_duplicate_records.keys())
            - set(self.ignored_record_keys)
            - set(self.error_record_ids)
        )
        successful_records = [
            self.without_duplicate_records[key] for key in successful_keys
        ]
        self.logger.info(
            f"END: get_records_for_update, successful_records - {len(successful_records)}, error_records - {len(self.error_records)}, ignored_records - {len(self.ignored_records)}"
        )
        return {
            "successful_records": {
                "csv_records_to_update": successful_records,
                "db_records": self.db_records,
            },
            "total_records_count": len(self.extracted_data),
            "error_records": self.error_records,
            "ignored_records": self.ignored_records,
        }

    def get_records_for_create_update(self) -> DataValidationType:
        """
        This function
        1. extracts the data from the csv file
        2. validates the primary key
        3. separate repeated records into records_to_update from without_duplicate_records
        4. validate the data type of the records_to_validate
        5. return the records_to_validate

        For create_update we need all the records (the records that are present in database and absent in database).

        return structure is `DataValidationType`. Refer to the type definition for more details.
        """
        self.logger.info("BEGIN: get_records_for_create_update")
        start_time = time.time()
        is_date_valid = self.extract_required_data()
        if not is_date_valid:
            return {
                "successful_records": {
                    "csv_records_to_create": [],
                    "csv_records_to_update": [],
                    "csv_records_to_delete": [],
                    "db_records": [],
                },
                "total_records_count": len(self.error_records),
                "error_records": self.error_records,
                "ignored_records": self.ignored_records,
            }

        self.logger.info(f"Time taken to extract data: {time.time() - start_time}")
        start_time = time.time()
        extracted_primary_keys = self.primary_and_snapshot_key_validator()

        self.logger.info(
            f"Time taken to validate primary key: {time.time() - start_time}"
        )

        if len(extracted_primary_keys) > 0:
            records = get_custom_object_data(
                self.client_id,
                self.custom_object_id,
                options={"row_key": extracted_primary_keys},
            )
            self.db_records = generate_hash(records, "row_key")
        else:
            self.logger.info("No Valide data found in the CSV file")
            self.db_records = []

        records_to_validate = []
        keys_to_update = []

        # 3. separate repeated records into records to update from without_duplicate_records
        for data_key in self.without_duplicate_records.keys():
            if data_key in self.db_records:
                keys_to_update.append(data_key)
            records_to_validate.append(self.without_duplicate_records[data_key])

        # 4. Data type validation
        start_time = time.time()
        self.data_type_validator(records_to_validate)
        self.logger.info(
            f"Time taken to validate data type: {time.time() - start_time}"
        )

        # Build error and ignored records. This strategy does't have igored records
        start_time = time.time()
        self.build_error_and_ignored_records()
        self.logger.info(
            f"Time taken to build error records: {time.time() - start_time}"
        )

        create_keys = (
            set(self.without_duplicate_records.keys())
            - set(keys_to_update)
            - set(self.error_record_ids)
        )
        keys_to_update = set(keys_to_update) - set(self.error_record_ids)
        db_records_to_update = {key: self.db_records[key] for key in keys_to_update}
        records_to_create = [self.without_duplicate_records[key] for key in create_keys]
        records_to_update = [
            self.without_duplicate_records[key] for key in keys_to_update
        ]
        self.logger.info(f"Time taken to create_update: {time.time() - start_time}")
        self.logger.info(
            f"END: get_records_for_create_update, records_to_create - {len(records_to_create)}, records_to_update - {len(records_to_update)}, error_records - {len(self.error_records)}, ignored_records - {len(self.ignored_records)}"
        )
        return {
            "successful_records": {
                "csv_records_to_create": records_to_create,
                "csv_records_to_update": records_to_update,
                "db_records": db_records_to_update,
            },
            "total_records_count": len(self.extracted_data),
            "error_records": self.error_records,
            "ignored_records": self.ignored_records,
        }

    def get_records_for_delete(self) -> DataValidationType:
        """
        This function
        1. extracts the data from the csv file
        2. validates the primary key
        3. segregate records to delete
        4. return the records_to_delete

        For delete we need only the records which are present in the database.

        return structure is `DataValidationType`. Refer to the type definition for more details.
        """
        self.logger.info("BEGIN: get_records_for_delete")
        start_time = time.time()
        is_date_valid = self.extract_required_data()
        if not is_date_valid:
            return {
                "successful_records": {
                    "csv_records_to_create": [],
                    "csv_records_to_update": [],
                    "csv_records_to_delete": [],
                    "db_records": [],
                },
                "total_records_count": len(self.error_records),
                "error_records": self.error_records,
                "ignored_records": self.ignored_records,
            }

        self.logger.info(f"Time taken to extract data: {time.time() - start_time}")

        start_time = time.time()
        extracted_primary_keys = self.primary_and_snapshot_key_validator(
            upload_type="delete"
        )
        self.logger.info(
            f"Time taken to validate primary key: {time.time() - start_time}"
        )

        if len(extracted_primary_keys) > 0:
            records = get_custom_object_data(
                self.client_id,
                self.custom_object_id,
                options={"row_key": extracted_primary_keys},
            )
            self.db_records = generate_hash(records, "row_key")
        else:
            self.logger.info("No Valide data found in the CSV file")
            self.db_records = []

        records_to_delete = []
        # 3. segregate records to delete
        for data_key in self.without_duplicate_records.keys():
            if not (data_key in self.db_records):
                self.ignored_record_keys.append(data_key)
            else:
                records_to_delete.append(self.without_duplicate_records[data_key])

        # Build error and ignored records
        start_time = time.time()
        ignored_reason = "Ignored because this is new data, but the chosen import type is Delete data."
        self.build_error_and_ignored_records(ignored_reason)
        self.logger.info(
            f"Time taken to build error records: {time.time() - start_time}"
        )

        self.logger.info(
            f"END: get_records_for_delete, records_to_delete - {len(records_to_delete)}, error_records - {len(self.error_records)}, ignored_records - {len(self.ignored_records)}"
        )
        return {
            "successful_records": {
                "csv_records_to_delete": records_to_delete,
                "db_records": self.db_records,
            },
            "total_records_count": len(self.extracted_data),
            "error_records": self.error_records,
            "ignored_records": self.ignored_records,
        }

    def preprocess_records(self):
        # pylint: disable=unnecessary-pass
        """Method to process the raw_records and save it to processed_records."""
        pass

    def persist_records(self) -> int:
        # pylint: disable=unnecessary-pass
        """Method to save the records to database and return the success count."""
        return 1
