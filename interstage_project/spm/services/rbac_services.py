import traceback
import uuid
from ast import literal_eval
from typing import Dict, List, Optional, Union

from django.core.cache import cache
from django.db import transaction
from django.utils import timezone
from rest_framework import status
from rest_framework.response import Response
from sqlparse.exceptions import SQLParseError

from commission_engine.accessors.client_accessor import (
    get_client_features,
    get_client_subscription_plan,
    should_insert_meta_data_to_vec_db,
)
from commission_engine.utils.general_data import (
    COMMISSION_TYPE,
    RBAC,
    RBACComponent,
    RbacPermissions,
    RBACPlan,
    SegmentEvents,
    SegmentProperties,
    commission_plan_scope_map,
)
from interstage_project.celery import TaskGroupEnum
from interstage_project.utils import get_queue_name_respect_to_task_group
from spm.accessors.accessor_factories.commission_plan_accessor_factory import (
    PlanSharedDetailsAccessorFactory,
)
from spm.accessors.config_accessors.employee_accessor import EmployeeAccessor
from spm.accessors.employee_accessor_v2 import EmployeeWriteAccessor
from spm.accessors.rbac_accessors import PermissionsAccessor, RolePermissionsAccessor
from spm.constants import AUDIT_TRAIL_EVENT_TYPE as EVENT
from spm.constants.localization_constants import (
    rbac_component_localization_map,
    rbac_permission_localization_map,
)
from spm.services import audit_services
from spm.services.analytics_services.analytics_service import CoreAnalytics
from spm.services.config_services.hierarchy_reportee_services import (
    check_if_reportee_in_hierarchy,
    get_reportees_in_hierarchy,
)
from spm.services.localization_services import get_localized_message_service
from spm.services.team_services.membership_services import (
    get_own_dynamic_flat_team_email_ids,
)
from spm.services.user_group_service import (
    UserGroupMemberService,
    get_paginated_user_group_members,
)
from spm.utils import serialize_name


def merge_permission_objects(role_permission_list):
    # Parse the first JSON object to get the set of keys to union
    union_keys = set()
    for obj in role_permission_list:
        union_keys.update(obj.keys())

    # Initialize empty dictionaries for the unique union of each key
    union_dict = {}
    for key in union_keys:
        union_dict[key] = {}

    # Loop over the JSON objects
    for role_permission in role_permission_list:
        for key in union_keys:
            if key in role_permission:
                for permission_type, permission_type_value in role_permission[
                    key
                ].items():
                    if permission_type == "permissions":
                        union_dict[key].setdefault(permission_type, []).extend(
                            set(role_permission[key][permission_type])
                            - set(union_dict[key].get(permission_type, []))
                        )
                    elif permission_type == "data_permission":
                        union_dict[key].setdefault(
                            permission_type, {} if permission_type_value else None
                        )
                        union_dict[key][permission_type] = merge_data_permissions(
                            [union_dict[key][permission_type], permission_type_value]
                        )
                    elif permission_type == "impersonated_user_roles":
                        union_dict[key].setdefault(permission_type, [])
                        if "ALL" in union_dict[key][permission_type]:
                            continue
                        elif "ALL" in permission_type_value:
                            union_dict[key][permission_type] = ["ALL"]
                        else:
                            union_dict[key][permission_type].extend(
                                set(role_permission[key][permission_type])
                                - set(union_dict[key].get(permission_type, []))
                            )
                    elif permission_type == "plans_scope":
                        union_dict[key].setdefault(
                            permission_type,
                            {
                                "can_view": None,
                                "can_edit": None,
                                "can_delete": None,
                            },
                        )
                        precedence = {None: 1, "SHARED_PLANS": 2, "ALL_PLANS": 3}
                        for sub_key, sub_value in permission_type_value.items():
                            existing_value = union_dict[key][permission_type].get(
                                sub_key
                            )
                            if precedence[sub_value] > precedence[existing_value]:
                                union_dict[key][permission_type][sub_key] = sub_value
    return union_dict


def merge_data_permissions(data_permission_list):
    default_team_data_permissions = {
        "type": "INDIVIDUAL_AND_TEAM_DATA",
        "selected_user_groups": [],
        "is_user_groups_selected": False,
        "is_reporting_team_selected": False,
    }
    # Define a dictionary to specify special merging logic for certain keys
    merge_logic = {"ALL_DATA": 2, "INDIVIDUAL_AND_TEAM_DATA": 1, "INDIVIDUAL_DATA": 0}
    result = {}
    for dp in data_permission_list:
        if dp:
            if dp.get("type") == "ALL_DATA":
                return dp
            elif dp.get("type") == "INDIVIDUAL_AND_TEAM_DATA":
                rs = result.setdefault(
                    "INDIVIDUAL_AND_TEAM_DATA",
                    default_team_data_permissions,
                )
                if dp.get("is_user_groups_selected", False):
                    rs["selected_user_groups"].extend(
                        dp.get("selected_user_groups", [])
                    )
                rs["is_user_groups_selected"] = (
                    dp.get("is_user_groups_selected", [])
                    or rs["is_user_groups_selected"]
                )
                rs["is_reporting_team_selected"] = (
                    dp.get("is_reporting_team_selected", [])
                    or rs["is_reporting_team_selected"]
                )
            elif dp.get("type") == "INDIVIDUAL_DATA":
                result["INDIVIDUAL_DATA"] = dp

    return (
        max(
            result.values(),
            key=lambda x: merge_logic.get(x.get("type", "") if x else "", -1),
        )
        if result.values()
        else result
    )


def create_role(client_id, display_name, description, logger, audit):
    time = timezone.now()
    role_permission_id = uuid.uuid4()
    role_permissions_accessor = RolePermissionsAccessor(client_id)
    is_name_exists = role_permissions_accessor.is_name_exist(display_name)
    if is_name_exists:
        return Response(
            {"error_message": "Name already taken, please use a different name"},
            status=status.HTTP_400_BAD_REQUEST,
        )

    data = {
        "client_id": client_id,
        "knowledge_begin_date": time,
        "role_permission_id": role_permission_id,
        "display_name": display_name,
        "description": description,
        "additional_details": audit,
        "created_at": time,
        "permissions": {
            "everstage": {
                "permissions": ["view:everstage", "view:approvals"],
                "data_permission": None,
            }
        },
    }

    ###################### audit log #####################
    event_type_code = EVENT["CREATE_ROLE-RBAC"]["code"]
    event_key = role_permission_id
    summary = "Created Role"
    updated_by = audit["updated_by"]
    updated_at = time
    audit_data = []
    ######################################################

    try:
        role_permissions_accessor.create_objects([data])
        role_latest = role_permissions_accessor.get_role_by_role_permission_id(
            role_permission_id,
            projection=[
                "role_permission_id",
                "display_name",
                "description",
                "updated_at",
                "is_editable",
                "created_at",
            ],
        )
        role_latest.update({"user_assigned_count": 0})
        audit_services.log(
            client_id,
            event_type_code,
            event_key,
            summary,
            updated_by,
            updated_at,
            audit_data,
        )
        from everstage_ddd.global_search.meta_data_extractor.ever_objects_meta_data import (
            upsert_ever_obj_meta_data_in_vector_db,
        )

        if should_insert_meta_data_to_vec_db(client_id):
            logger.info("BEGIN: Updated Ever Object Meta Data in Vector DB for user")
            upsert_ever_obj_meta_data_in_vector_db(client_id, "user")
        logger.info(
            "Role Created successfully with role_permission_id {}".format(
                role_permission_id
            )
        )
        analytics_data = {
            "user_id": audit["updated_by"],
            "event_name": SegmentEvents.NEW_ROLES.value,
            "event_properties": {SegmentProperties.NEW_ROLE.value: display_name},
        }
        analytics = CoreAnalytics(analyser_type="segment")
        analytics.send_analytics(analytics_data)
        return Response(role_latest, status=status.HTTP_201_CREATED)
    except Exception as exc:
        error_dict = {"trace_back": traceback.format_exc()}
        logger.error(
            "Exception in Creating role with role_permission_id {}".format(
                role_permission_id
            ),
            error_dict,
        )
        raise SQLParseError() from exc


def get_all_role_details(client_id, logger):
    try:
        all_roles = RolePermissionsAccessor(client_id).show_user_roles()
        count_all_employees_by_role = EmployeeAccessor(
            client_id
        ).get_count_all_employee_ids_by_role()

        role_count_map = {
            row.get("role_id", "").replace('"', ""): row.get("role_count", 0)
            for row in count_all_employees_by_role
        }

        for role in all_roles:
            role_id = str(role["role_permission_id"])
            role["user_assigned_count"] = role_count_map.get(role_id, 0)
            role["description"] = get_localized_message_service(
                role["description"], client_id
            )
        return all_roles

    except Exception as exc:
        error_dict = {"trace_back": traceback.print_exc()}
        logger.error("Exception", error_dict)
        raise SQLParseError() from exc


def get_role_details(client_id, role_permission_id, logger):
    try:
        is_role_exists = RolePermissionsAccessor(
            client_id
        ).is_role_permission_id_exists(role_permission_id)
        if not is_role_exists:
            logger.info(
                "Role Doesnt Exist in the system with role_permission_id {}".format(
                    role_permission_id
                )
            )
            return Response(
                {"error_message": "Role Doesnt Exist!"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        role = RolePermissionsAccessor(client_id).get_role_by_role_permission_id(
            role_permission_id,
            projection=[
                "role_permission_id",
                "display_name",
                "description",
                "updated_at",
                "is_editable",
                "created_at",
            ],
        )
        count = EmployeeAccessor(client_id).get_user_role_count(role_permission_id)
        role.update({"user_assigned_count": count})
        return Response(role, status=status.HTTP_201_CREATED)
    except Exception as exc:
        error_dict = {"trace_back": traceback.print_exc()}
        logger.error("Exception", error_dict)
        raise SQLParseError() from exc


def filter_permissions(permission_list):
    return {a: b for a, b in permission_list.items() if a != "parent_id"}


def group_permissions(permission_list, start=None):
    return [
        filter_permissions(
            {
                **permission,
                "permissions": group_permissions(
                    permission_list, permission["permission_id"]
                ),
            }
        )
        for permission in permission_list
        if permission["parent_id"] == start
    ]


def should_skip_component(component, client_id):
    """
    Show RBAC component if the feature is enabled
    """

    skip_components_map = {
        "everai": ["enable_everai"],
        "territory_plans": ["show_territory_plan"],
    }  # Feature flag : Component
    component_name = component["component_system_name"]

    if component_name in skip_components_map:
        client_features = skip_components_map[component_name]

        for feature in client_features:
            if get_client_features(client_id).get(feature, False):
                return False  # Do not skip if any feature is enabled for this component

        return True  # Skip if none of the features are enabled

    return False  # Do not skip if the component is not in the map


def role_permission_details(client_id, role_permission_id, logger):
    logger.update_context({"role_permission_id": role_permission_id})
    try:
        all_permissions = PermissionsAccessor(client_id).get_all_permissions()
        commission_plan_permissions = [
            "view:commissionplan",
            "edit:commissionplan",
            "delete:commissionplan",
        ]
        if not all_permissions:
            logger.info(
                "Permissions Doesnt Exist for this Client Id - {}".format(client_id)
            )
            return Response(
                {"error_message": "Permissions Doesnt Exist!"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        all_components = PermissionsAccessor(
            client_id
        ).get_all_distinct_component_details()
        role_permission = RolePermissionsAccessor(
            client_id
        ).get_role_by_role_permission_id(role_permission_id=role_permission_id)
        is_role_exists = RolePermissionsAccessor(
            client_id
        ).is_role_permission_id_exists(role_permission_id)
        if not is_role_exists:
            logger.info(
                "Role Doesnt Exist in the system with role_permission_id {}".format(
                    role_permission_id
                )
            )
            return Response(
                {"error_message": "Role Doesnt Exist!"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        # role permission value { datasheet:{}, dashboard:{permissions:[], data_permissions:{}}}
        component_to_permissions_map = {}
        admin_ui_permissions = admin_ui_permission_checks(client_id)
        for permission in all_permissions:
            value = False
            impersonated_user_roles = None
            plans_scope_obj = {}
            if (
                role_permission
                and role_permission["permissions"]
                and permission["component_system_name"]
                in role_permission["permissions"]
            ):
                if (
                    permission["permission_id"]
                    in role_permission["permissions"][
                        permission["component_system_name"]
                    ]["permissions"]
                ):
                    value = True
                if (
                    permission["permission_id"] == "allow:impersonation"
                    and "impersonated_user_roles"
                    in role_permission["permissions"][
                        permission["component_system_name"]
                    ]
                ):
                    impersonated_user_roles = role_permission["permissions"][
                        permission["component_system_name"]
                    ]["impersonated_user_roles"]
                if (
                    permission["permission_id"] in commission_plan_permissions
                    and "plans_scope"
                    in role_permission["permissions"][
                        permission["component_system_name"]
                    ]
                ):
                    plans_scope_obj = role_permission["permissions"][
                        permission["component_system_name"]
                    ]["plans_scope"]
            if permission["permission_id"] not in admin_ui_permissions:
                localized_permission_name = get_localized_message_service(
                    rbac_permission_localization_map[permission["permission_id"]][
                        "permission_name"
                    ],
                    client_id,
                )
                localized_permission_description = get_localized_message_service(
                    rbac_permission_localization_map[permission["permission_id"]][
                        "permission_description"
                    ],
                    client_id,
                )
                permission_data = {
                    "permission_id": permission["permission_id"],
                    "permission_name": localized_permission_name,
                    "parent_id": permission["parent_id"],
                    "value": value,
                    "permission_description": localized_permission_description,
                }
                if plans_scope_obj:
                    permission_data.update(
                        {
                            "plans_scope": plans_scope_obj[
                                commission_plan_scope_map[permission["permission_id"]]
                            ]
                        }
                    )
                if impersonated_user_roles:
                    permission_data.update(
                        {"impersonated_user_roles": impersonated_user_roles}
                    )
                if (
                    permission["component_system_name"]
                    not in component_to_permissions_map
                ):
                    component_to_permissions_map[
                        permission["component_system_name"]
                    ] = [permission_data]
                else:
                    component_to_permissions_map[
                        permission["component_system_name"]
                    ].append(permission_data)

        result_data = []
        for component in all_components:
            if should_skip_component(component, client_id):
                continue

            permissions = group_permissions(
                component_to_permissions_map[component["component_system_name"]]
            )

            localized_component_display_name = get_localized_message_service(
                rbac_component_localization_map[component["component_system_name"]][
                    "component_display_name"
                ],
                client_id,
            )
            component_details = {
                "component_order": component["component_order"],
                "component_system_name": component["component_system_name"],
                "component_display_name": localized_component_display_name,
                "permission_title": RBAC.UI_PERMISSION.value
                + localized_component_display_name,
                "data_permission_title": RBAC.DATA_PERMISSION.value
                + localized_component_display_name,
                "show_data_permissions": component["show_data_permissions"],
                "permissions": permissions,
                "data_permission": None,
            }
            if (
                role_permission
                and role_permission["permissions"]
                and component["component_system_name"] in role_permission["permissions"]
                and "data_permission"
                in role_permission["permissions"][component["component_system_name"]]
            ):
                component_details["data_permission"] = role_permission["permissions"][
                    component["component_system_name"]
                ]["data_permission"]
            result_data.append(component_details)
        # sorting result based on the component order
        sorted_result = sorted(result_data, key=lambda d: d["component_order"])
        return Response(
            sorted_result,
            status=status.HTTP_201_CREATED,
        )
    except Exception as exc:
        error_dict = {"trace_back": traceback.print_exc()}
        logger.error(
            "Exception in Fetching role with role_permission_id {}".format(
                role_permission_id
            ),
            error_dict,
        )
        raise SQLParseError() from exc


def delete_role_from_impersonate_list(client_id, role_permission_id, ked, audit):
    accessor = RolePermissionsAccessor(client_id)
    all_impersonate_users = accessor.get_impersonate_users(role_permission_id)

    if all_impersonate_users is None:
        return Response({"error_message": "Role Doesnt Exist!"}, status=400)

    for role_permission in all_impersonate_users:
        if role_permission and role_permission.permissions:
            role_permission.permissions["manage_users"][
                "impersonated_user_roles"
            ].remove(role_permission_id)
            if not role_permission.permissions["manage_users"][
                "impersonated_user_roles"
            ]:
                role_permission.permissions["manage_users"]["permissions"].remove(
                    "allow:impersonation"
                )

    accessor.invalidate_impersonate_users(role_permission_id, ked)
    accessor.invalidate_and_update_role(all_impersonate_users, ked, audit)


def invalidate_role(client_id, role_permission_id, logger, audit):
    ked = timezone.now()
    role_response = {}
    logger.update_context({"role_permission_id": role_permission_id})
    try:
        is_role_exists = RolePermissionsAccessor(
            client_id
        ).is_role_permission_id_exists(role_permission_id)
        if not is_role_exists:
            logger.info(
                "Role Doesnt Exist in the system with role_permission_id {}".format(
                    role_permission_id
                )
            )
            return Response(
                {"error_message": "Role Doesnt Exist!"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        ###################### audit log #####################
        event_type_code = EVENT["DELETE_ROLE-RBAC"]["code"]
        event_key = role_permission_id
        summary = "Deleted Role"
        updated_by = audit["updated_by"]
        updated_at = ked
        audit_data = []
        ######################################################
        delete_role_from_impersonate_list(client_id, role_permission_id, ked, audit)
        delete_permissions_in_cache(client_id, role_permission_id)  # delete cache
        RolePermissionsAccessor(client_id).invalidate_role(role_permission_id, ked)
        audit_services.log(
            client_id,
            event_type_code,
            event_key,
            summary,
            updated_by,
            updated_at,
            audit_data,
        )
        from everstage_ddd.global_search.meta_data_extractor.ever_objects_meta_data import (
            upsert_ever_obj_meta_data_in_vector_db,
        )

        if should_insert_meta_data_to_vec_db(client_id):
            logger.info("BEGIN: Updated Ever Object Meta Data in Vector DB for user")
            upsert_ever_obj_meta_data_in_vector_db(client_id, "user")
        logger.info(
            "Deleted Role with role_permission_id {} successfully".format(
                role_permission_id
            )
        )
        return Response(role_response, status=status.HTTP_201_CREATED)
    except Exception as exc:
        error_dict = {"trace_back": traceback.print_exc()}
        logger.error(
            "Exception in Deleting role with role_permission_id {}".format(
                role_permission_id
            ),
            error_dict,
        )
        raise SQLParseError() from exc


def move_and_invalidate_role(
    client_id, role_permission_id, logger, audit, new_role_permission_id
):
    ked = timezone.now()
    logger.update_context({"role_permission_id": role_permission_id})
    role_permission_accessor = RolePermissionsAccessor(client_id)
    employee_accessor = EmployeeAccessor(client_id)

    if not role_permission_accessor.is_role_permission_id_exists(
        role_permission_id
    ) or not role_permission_accessor.is_role_permission_id_exists(
        new_role_permission_id
    ):
        logger.info(
            f"Role Doesnt Exist in the system with role_permission_id {role_permission_id}"
        )
        return Response(
            {"error_message": "Role Doesnt Exist!"}, status=status.HTTP_400_BAD_REQUEST
        )

    # Fetch old and new role permissions to invalidate shared plan entries on role change
    roles = role_permission_accessor.get_role_by_role_permission_id(
        [role_permission_id, new_role_permission_id],
        projection=["role_permission_id", "permissions"],
    )

    if roles:
        prev_role_plan_scope = {}
        curr_role_plan_scope = {}
        for role in roles:
            if str(role["role_permission_id"]) == role_permission_id:
                prev_role_plan_scope = (
                    role.get("permissions", {})
                    .get("commission_plans", {})
                    .get("plans_scope", {})
                )
            elif str(role["role_permission_id"]) == new_role_permission_id:
                curr_role_plan_scope = (
                    role.get("permissions", {})
                    .get("commission_plans", {})
                    .get("plans_scope", {})
                )

        # Handle removal of shared plan entries
        remove_shared_plan_entries_on_role_change(
            client_id,
            prev_role_plan_scope,
            curr_role_plan_scope,
            role_ids=[str(role_permission_id)],
        )
        logger.info(
            f"Invalidted shared plan entries for role_permission_id {role_permission_id} successfully"
        )

    EmployeeWriteAccessor(client_id).invalidate_and_update_role(
        role_permission_id, new_role_permission_id, ked, audit
    )
    count = employee_accessor.get_user_role_count(new_role_permission_id)

    role_latest = role_permission_accessor.get_role_by_role_permission_id(
        new_role_permission_id,
        projection=[
            "role_permission_id",
            "display_name",
            "description",
            "updated_at",
            "is_editable",
            "created_at",
        ],
    )
    role_latest.update({"user_assigned_count": count})
    role_response = {"moved_user_role": role_latest}

    delete_role_from_impersonate_list(client_id, role_permission_id, ked, audit)
    delete_permissions_in_cache(client_id, role_permission_id)
    role_permission_accessor.invalidate_role(role_permission_id, ked)

    event_type_code = EVENT["DELETE_ROLE-RBAC"]["code"]
    event_key = role_permission_id
    summary = "Deleted Role"
    updated_by = audit["updated_by"]
    updated_at = ked
    audit_services.log(
        client_id, event_type_code, event_key, summary, updated_by, updated_at, []
    )
    from spm.tasks import update_learnupon_groups_for_role_task

    # Schedule async task to update LearnUpon groups for the new role
    subscription_plan = get_client_subscription_plan(client_id)
    misc_queue_name = get_queue_name_respect_to_task_group(
        client_id, subscription_plan, TaskGroupEnum.MISC.value
    )
    update_learnupon_groups_for_role_task.si(client_id, new_role_permission_id).set(
        queue=misc_queue_name
    ).apply_async()

    logger.info(
        f"Deleted Role with role_permission_id {role_permission_id} successfully"
    )
    return Response(role_response, status=status.HTTP_201_CREATED)


def edit_role(
    client_id,
    role_permission_id,
    permissions,
    display_name,
    description,
    logger,
    audit,
):
    # Import here to avoid circular dependency
    from spm.tasks import update_learnupon_groups_for_role_task

    # get role details
    time = timezone.now()
    role = RolePermissionsAccessor(client_id).get_role_by_role_permission_id(
        role_permission_id
    )
    if not permissions:
        return Response(
            {"error_message": "Please select atleast one permission"},
            status=status.HTTP_400_BAD_REQUEST,
        )

    if role is None:
        logger.info(
            "Role Doesnt Exist in the system with role_permission_id {}".format(
                role_permission_id
            )
        )
        return Response(
            {"error_message": "Role Doesnt Exist!"}, status=status.HTTP_400_BAD_REQUEST
        )
    is_name_exists = RolePermissionsAccessor(client_id).is_name_exist(display_name)
    is_role_name_changed = role["display_name"] != display_name
    if is_role_name_changed and is_name_exists:
        return Response(
            {"error_message": "Name already taken, please use a different name"},
            status=status.HTTP_400_BAD_REQUEST,
        )
    prev_role_plan_scope = (
        role.get("permissions", {}).get("commission_plans", {}).get("plans_scope", {})
    )
    curr_role_plan_scope = permissions.get("commission_plans", {}).get(
        "plans_scope", {}
    )
    prev_global_search_permission = role.get("permissions", {}).get(
        RBACComponent.GLOBAL_SEARCH.value, {}
    )

    # manage:multiperiodsync is a backend permission added to settings permissions via script
    # If manage:multiperiodsync permission is present for the role already, add it again to that role permission
    if "manage:multiperiodsync" in role["permissions"].get("settings", {}).get(
        "permissions", []
    ):
        settings_permissions = permissions.setdefault("settings", {}).setdefault(
            "permissions", []
        )
        if "manage:multiperiodsync" not in settings_permissions:
            settings_permissions.append("manage:multiperiodsync")

        permissions["settings"].setdefault("data_permission", None)

    is_admin_ui_action = (audit or {}).pop("is_admin_ui_action", False)
    role["display_name"] = display_name
    role["description"] = description
    RolePermissionsAccessor(client_id).invalidate_role(role_permission_id, time)
    # edit role details
    role["knowledge_begin_date"] = time
    role["knowledge_end_date"] = None
    role["additional_details"] = audit
    role["permissions"] = permissions
    role["permissions"].update(
        {
            "everstage": {
                "permissions": ["view:everstage", "view:approvals"],
                "data_permission": None,
            }
        }
    )
    # Retain global search permission if role is edited from application UI - since show_to_user is False
    if prev_global_search_permission and not is_admin_ui_action:
        role["permissions"].update(
            {RBACComponent.GLOBAL_SEARCH.value: prev_global_search_permission}
        )

    try:
        ###################### audit log #####################
        event_type_code = EVENT["EDIT_ROLE-RBAC"]["code"]
        event_key = role_permission_id
        summary = "Edited Role"
        updated_by = audit["updated_by"]
        updated_at = time
        audit_data = []
        ######################################################
        RolePermissionsAccessor(client_id).create_objects([role])
        # update cache
        update_cache_permissions_by_role(client_id, role_permission_id)
        remove_shared_plan_entries_on_role_change(
            client_id,
            prev_role_plan_scope,
            curr_role_plan_scope,
            role_ids=[str(role_permission_id)],
        )

        # Schedule async task to update LearnUpon groups
        subscription_plan = get_client_subscription_plan(client_id)
        misc_queue_name = get_queue_name_respect_to_task_group(
            client_id, subscription_plan, TaskGroupEnum.MISC.value
        )
        update_learnupon_groups_for_role_task.si(client_id, role_permission_id).set(
            queue=misc_queue_name
        ).apply_async()

        count = EmployeeAccessor(client_id).get_user_role_count(role_permission_id)
        role_latest = RolePermissionsAccessor(client_id).get_role_by_role_permission_id(
            role_permission_id,
            projection=[
                "role_permission_id",
                "display_name",
                "description",
                "updated_at",
                "is_editable",
                "created_at",
            ],
        )
        role_latest.update({"user_assigned_count": count})

        if not is_admin_ui_action:
            audit_services.log(
                client_id,
                event_type_code,
                event_key,
                summary,
                updated_by,
                updated_at,
                audit_data,
            )
            analytics_data = {
                "user_id": audit["updated_by"],
                "event_name": SegmentEvents.EDIT_ROLES.value,
                "event_properties": {SegmentProperties.EDIT_ROLE.value: display_name},
            }
            analytics = CoreAnalytics(analyser_type="segment")
            analytics.send_analytics(analytics_data)

        from everstage_ddd.global_search.meta_data_extractor.ever_objects_meta_data import (
            upsert_ever_obj_meta_data_in_vector_db,
        )

        if should_insert_meta_data_to_vec_db(client_id) and is_role_name_changed:
            logger.info("BEGIN: Updated Ever Object Meta Data in Vector DB for user")
            upsert_ever_obj_meta_data_in_vector_db(client_id, "user")
        logger.info(
            "Role Edited successfully with role_permission_id {}".format(
                role_permission_id
            )
        )
        return Response(
            role_latest,
            status=status.HTTP_201_CREATED,
        )
    except Exception as exc:
        error_dict = {"trace_back": traceback.print_exc()}
        logger.error(
            "Exception in Editing role with role_permission_id {}".format(
                role_permission_id
            ),
            error_dict,
        )
        raise SQLParseError() from exc


def clone_role(client_id, role_permission_id, logger, audit):
    time = timezone.now()
    clone_role_permission_id = uuid.uuid4()
    role_accessor = RolePermissionsAccessor(client_id)
    clone_role_obj = role_accessor.get_role_by_role_permission_id(role_permission_id)
    try:
        if not clone_role_obj:
            logger.info(
                "Role with role_permission_id {} doesn't exist".format(
                    role_permission_id
                )
            )
            return Response({"error_message": "Role doesn't exist!"}, status=400)
        clone_display_name = "Copy of {}".format(clone_role_obj["display_name"])
        existing_display_names = role_accessor.get_role_display_names()
        clone_display_name = serialize_name(clone_display_name, existing_display_names)
        clone_role_obj.update(
            {
                "knowledge_begin_date": time,
                "display_name": clone_display_name,
                "role_permission_id": clone_role_permission_id,
                "additional_details": audit,
                "created_at": time,
                "is_editable": True,
            }
        )
        role_accessor.create_objects([clone_role_obj])
        update_cache_permissions_by_role(client_id, clone_role_permission_id)
        count = EmployeeAccessor(client_id).get_user_role_count(
            clone_role_permission_id
        )
        role_latest = role_accessor.get_role_by_role_permission_id(
            clone_role_permission_id,
            projection=[
                "role_permission_id",
                "display_name",
                "description",
                "updated_at",
                "is_editable",
                "created_at",
            ],
        )
        role_latest.update({"user_assigned_count": count})
        localized_description = get_localized_message_service(
            role_latest.get("description", ""), client_id
        )
        role_latest.update({"description": localized_description})
        audit_services.log(
            client_id,
            EVENT["CLONE_ROLE-RBAC"]["code"],
            clone_role_permission_id,
            "Cloned Role",
            audit["updated_by"],
            time,
            [],
        )
        from everstage_ddd.global_search.meta_data_extractor.ever_objects_meta_data import (
            upsert_ever_obj_meta_data_in_vector_db,
        )

        if should_insert_meta_data_to_vec_db(client_id):
            logger.info("BEGIN: Updated Ever Object Meta Data in Vector DB for user")
            upsert_ever_obj_meta_data_in_vector_db(client_id, "user")
        logger.info(
            "Role cloned successfully with role_permission_id {}".format(
                clone_role_permission_id
            )
        )
        analytics_data = {
            "user_id": audit["updated_by"],
            "event_name": SegmentEvents.CLONE_ROLES.value,
            "event_properties": {
                SegmentProperties.CLONE_ROLE.value: clone_display_name
            },
        }
        analytics = CoreAnalytics(analyser_type="segment")
        analytics.send_analytics(analytics_data)
        return Response(role_latest, status=201)
    except Exception as exc:
        error_dict = {"trace_back": traceback.print_exc()}
        logger.error(
            "Exception in cloning role with role_permission_id {}".format(
                clone_role_permission_id
            ),
            error_dict,
        )
        raise SQLParseError() from exc


def get_payee_role_id(client_id, email_id):
    return EmployeeAccessor(client_id).get_employee_role(email_id)


def get_ui_permission_cache_key(client_id, role_id):
    return f"{client_id}-UI_PERM_ROLE-{role_id}"


def get_data_permission_cache_key(client_id, role_id, component):
    return f"{client_id}-DATA_PERM_ROLE_COMPONENT_LEVEL-{role_id}-{component}"


def get_user_permissions(client_id, email_id):
    role_id = get_payee_role_id(client_id, email_id)

    role_permissions = RolePermissionsAccessor(
        client_id
    ).get_role_by_role_permission_id(role_id)

    permissions = []
    impersonated_user_roles = []
    valid_role_permissions = []
    plans_scope = {}
    if role_permissions:
        for role_permission in role_permissions:
            if (
                role_permission
                and "permissions" in role_permission
                and role_permission["permissions"]
            ):
                valid_role_permissions.append(role_permission["permissions"])

        merged_role_permissions = merge_permission_objects(valid_role_permissions)
        for component in merged_role_permissions:
            if "permissions" in merged_role_permissions[component]:
                permissions.extend(merged_role_permissions[component]["permissions"])
        if (
            "manage_users" in merged_role_permissions
            and "impersonated_user_roles" in merged_role_permissions["manage_users"]
        ):
            impersonated_user_roles = merged_role_permissions["manage_users"][
                "impersonated_user_roles"
            ]
        if (
            "commission_plans" in merged_role_permissions
            and "plans_scope" in merged_role_permissions["commission_plans"]
        ):
            plans_scope = merged_role_permissions["commission_plans"]["plans_scope"]
    return {
        "permissions": permissions,
        "impersonated_user_roles": impersonated_user_roles,
        "plans_scope": plans_scope,
    }


def get_permissions_by_role_and_component(client_id, role_id, component):
    """
    Returns the permissions for a given role_id and component

    Args:
        client_id (str): client id of the user
        role_id (str): role id of the user
        component (str): component for which permissions are to be fetched

    Returns:
        list: list of permissions for the given role_id and component
    """
    role_permissions = RolePermissionsAccessor(
        client_id
    ).get_role_by_role_permission_id(role_id)
    if role_permissions and role_permissions.get("permissions"):
        return (
            role_permissions.get("permissions", {})
            .get(component, {})
            .get("permissions", [])
        )
    return []


def get_ui_permissions(client_id, email_id):
    role_ids = get_payee_role_id(client_id, email_id)
    return get_ui_permission_by_role(client_id, role_ids)


def get_single_ui_permission_by_role(client_id, role_id):
    cache_key = get_ui_permission_cache_key(client_id, role_id)
    permissions = []
    if cache.get(cache_key) is None:
        role_permissions = RolePermissionsAccessor(
            client_id
        ).get_role_by_role_permission_id(role_id)
        if role_permissions is not None and "permissions" in role_permissions:
            for component in role_permissions["permissions"]:
                if "permissions" in role_permissions["permissions"][component]:
                    permissions.extend(
                        role_permissions["permissions"][component]["permissions"]
                    )
        cache.set(cache_key, permissions, None)
    else:
        permissions = cache.get(cache_key)
    return permissions


def get_ui_permission_by_role(client_id, role_ids):
    if isinstance(role_ids, str):
        role_ids = [role_ids]

    valid_role_permissions = []
    merged_permissions = []
    for role in role_ids:
        role_permission = get_single_ui_permission_by_role(client_id, role)
        valid_role_permissions.append(role_permission)

    merged_permissions = list(
        set(merged_permissions).union(*[set(perm) for perm in valid_role_permissions])
    )
    return merged_permissions


def get_single_id_data_permission(client_id, role_id, component):
    cache_key = get_data_permission_cache_key(client_id, role_id, component)
    data_permission = {}
    if cache.get(cache_key) is None:
        role_permissions = RolePermissionsAccessor(
            client_id
        ).get_role_by_role_permission_id(role_id)
        if (
            role_permissions
            and "permissions" in role_permissions
            and component in role_permissions["permissions"]
            and "data_permission" in role_permissions["permissions"][component]
        ):
            data_permission = role_permissions["permissions"][component][
                "data_permission"
            ]
        cache.set(cache_key, data_permission, None)
    else:
        data_permission = cache.get(cache_key)
    return data_permission


def get_data_permission(client_id, email_id, component):
    role_ids = get_payee_role_id(client_id, email_id)
    valid_data_permissions = []

    for role in role_ids:
        data_perm = get_single_id_data_permission(client_id, role, component)
        valid_data_permissions.append(data_perm)

    return merge_data_permissions(valid_data_permissions)


def update_ui_permission_cache(client_id, role_id, cache_key):
    role_permissions = RolePermissionsAccessor(
        client_id
    ).get_role_by_role_permission_id(role_id)
    permissions = []
    if role_permissions is not None:
        for component in role_permissions["permissions"]:
            if "permissions" in role_permissions["permissions"][component]:
                permissions.extend(
                    role_permissions["permissions"][component]["permissions"]
                )
    cache.set(cache_key, permissions, None)
    return permissions


def get_ui_permission_cache(client_id, role_id, cache_key):
    if cache.get(cache_key) is None:
        # logger.info(f"Cache miss. Cache key -  {role_permissions_cache_key}")
        permissions = update_ui_permission_cache(client_id, role_id, cache_key)
    else:
        # logger.info(f"Cache hit. cache key -  {role_permissions_cache_key}")
        permissions = cache.get(cache_key)
    return permissions


def update_data_permission_cache(client_id, role_id, component, cache_key):
    role_permissions = RolePermissionsAccessor(
        client_id
    ).get_role_by_role_permission_id(role_id)
    data_permission = {}
    if (
        role_permissions
        and role_permissions["permissions"]
        and component in role_permissions["permissions"]
        and "data_permission" in role_permissions["permissions"][component]
    ):
        data_permission = role_permissions["permissions"][component]["data_permission"]
    cache.set(cache_key, data_permission, None)
    return data_permission


def get_data_permission_cache(client_id, role_id, component, cache_key):
    if cache.get(cache_key) is None:
        data_permissions = update_data_permission_cache(
            client_id, role_id, component, cache_key
        )
    else:
        data_permissions = cache.get(cache_key)
    return data_permissions


def update_cache_permissions_by_role(client_id, role_id):
    role_permission = RolePermissionsAccessor(client_id).get_role_by_role_permission_id(
        role_id
    )
    update_permissions_in_cache(client_id, role_id, role_permission)


def update_permissions_in_cache(client_id, role_id, role_permissions):
    if not role_permissions or not isinstance(
        role_permissions.get("permissions"), dict
    ):
        return

    ui_perm_cache_key = get_ui_permission_cache_key(client_id, role_id)
    cache.set(
        ui_perm_cache_key,
        [
            p
            for component in role_permissions["permissions"].values()
            for p in component.get("permissions", [])
        ],
        None,
    )
    pattern = f"{client_id}-DATA_PERM_ROLE_COMPONENT_LEVEL-{role_id}*"
    existing_data_perm_keys = set(cache.keys(pattern))  # type: ignore
    new_data_perm_keys = set()

    for component in role_permissions.get("permissions", {}):
        data_permission = (
            role_permissions.get("permissions", {})
            .get(component, {})
            .get("data_permission", {})
        )
        if data_permission:
            data_perm_cache_key = get_data_permission_cache_key(
                client_id, role_id, component
            )
            cache.set(data_perm_cache_key, data_permission, None)
            new_data_perm_keys.add(data_perm_cache_key)
    stale_data_perm_keys = existing_data_perm_keys - new_data_perm_keys
    cache.delete_many(stale_data_perm_keys)


def delete_permissions_in_cache(client_id, role_id):
    role_permissions = RolePermissionsAccessor(
        client_id
    ).get_role_by_role_permission_id(role_id)
    if role_permissions and role_permissions["permissions"]:
        ui_perm_cache_key = get_ui_permission_cache_key(client_id, role_id)
        cache_keys = [ui_perm_cache_key]
        for component in role_permissions["permissions"]:
            data_perm_cache_key = get_data_permission_cache_key(
                client_id, role_id, component
            )
            cache_keys.append(data_perm_cache_key)

        cache.delete_many(cache_keys)


def get_valid_payee_emails(client_id, email, data_permission, effective_date=None):
    payee_emails = {email}
    if (
        data_permission
        and data_permission["type"] == RBAC.INDIVIDUAL_AND_TEAM_DATA.value
    ):
        if data_permission["is_reporting_team_selected"]:
            reportees = get_own_dynamic_flat_team_email_ids(
                client_id, email, effective_date=effective_date
            )
            payee_emails |= set(reportees)
        if data_permission["is_user_groups_selected"]:
            for user_group in data_permission["selected_user_groups"]:
                payee_emails |= set(
                    UserGroupMemberService(client_id).get_user_group_members_emails(
                        user_group
                    )
                )
    return list(payee_emails)


def get_valid_payee_emails_paginated(
    client_id,
    email,
    data_permission,
    limit,
    search_term=None,
    full_name_offset=None,
    email_offset=None,
):
    if data_permission["type"] == RBAC.ALL_DATA.value:
        power_admin_role_ids = set(
            RolePermissionsAccessor(client_id).get_all_power_admins_role_id()
        )
        return EmployeeAccessor(client_id).get_payees_paginated_with_search(
            limit=limit,
            full_name=full_name_offset,
            email=email_offset,
            search_term=search_term,
            exclude_roles=power_admin_role_ids,
        )

    users_list, reportee_keys = [], set()
    if data_permission["type"] == RBAC.INDIVIDUAL_AND_TEAM_DATA.value:
        if data_permission["is_reporting_team_selected"]:
            reportees_in_hierarchy = get_reportees_in_hierarchy(
                client_id, email, search_term, limit, full_name_offset, email_offset
            )
            users_list.extend(reportees_in_hierarchy)
            for reportee in reportees_in_hierarchy:
                reportee_keys.add(
                    (reportee["full_name"], reportee["employee_email_id"])
                )
        if data_permission["is_user_groups_selected"]:
            user_group_members = get_paginated_user_group_members(
                client_id,
                email,
                data_permission["selected_user_groups"],
                limit,
                search_term,
                full_name_offset,
                email_offset,
            )
            if reportee_keys:
                for member in user_group_members:
                    if (
                        member["full_name"],
                        member["employee_email_id"],
                    ) not in reportee_keys:
                        users_list.append(member)
            else:
                users_list.extend(user_group_members)

        users_list = sorted(
            users_list,
            key=lambda d: (d["full_name"].lower(), d["employee_email_id"]),
        )

        users_list = users_list[:limit]
    return users_list


def is_email_in_individual_and_team_data(
    client_id, login_user_id, email_id, data_permission
):
    if data_permission["type"] == RBAC.INDIVIDUAL_AND_TEAM_DATA.value:
        if login_user_id == email_id:
            return True
        else:
            if data_permission["is_user_groups_selected"]:
                for user_group in data_permission["selected_user_groups"]:
                    if email_id in set(
                        UserGroupMemberService(client_id).get_user_group_members_emails(
                            user_group
                        )
                    ):
                        return True
            if data_permission["is_reporting_team_selected"]:
                return check_if_reportee_in_hierarchy(
                    client_id, login_user_id, email_id
                )
            return False
    else:
        return False


def is_view_payroll_permission(client_id, email):
    role_permission_ids = get_payee_role_id(client_id, email)
    return RolePermissionsAccessor(client_id).is_view_payroll_permission(
        role_permission_ids
    )


def is_restricted_own_data_permission(
    client_id: str, logged_email: str, provided_email: str
) -> bool:
    """
    Checks if access to own data is restricted.
    """
    if logged_email != provided_email:
        return False

    return not is_own_data_permission(client_id, logged_email)


def is_own_data_permission(client_id, email):
    ui_permissions = get_ui_permissions(client_id, email)
    return RbacPermissions.MANAGE_OWNDATA.value in ui_permissions


def is_payout_value_permission(client_id, email):
    role_permission_ids = get_payee_role_id(client_id, email)
    return RolePermissionsAccessor(client_id).is_payout_value_others_permission(
        role_permission_ids
    )


def is_user_power_admin(client_id, email_id):
    ui_permissions = get_ui_permissions(client_id, email_id)
    return RbacPermissions.MANAGE_ALLADMINS.value in ui_permissions


def has_settings_permissions(client_id, email_id):
    """Check for any settings related permissions for the user"""
    ui_permissions = get_ui_permissions(client_id, email_id)
    settings_perm = [
        RbacPermissions.MANAGE_CONFIG.value,
        RbacPermissions.MANAGE_CONTRACTS.value,
        RbacPermissions.MANAGE_DATASETTINGS.value,
    ]
    return len(set(settings_perm).intersection(ui_permissions)) > 0


def does_user_have_databook_manage_permission(client_id, email):
    role_permission_ids = get_payee_role_id(client_id, email)
    databook_permissions = RolePermissionsAccessor(client_id).get_databook_permissions(
        role_permission_ids
    )
    manage_databook_roles = [
        "manage:databook",
        "manage:datasheetpermissions",
        "manage:datasheetadjustments",
    ]
    if databook_permissions and any(
        permission in manage_databook_roles for permission in databook_permissions
    ):
        return True
    return False


def admin_ui_permission_checks(client_id):
    client_features = get_client_features(client_id=client_id)
    admin_ui_permissions = []
    feature_permission = {
        "show_approval_feature": ["view:requestapprovals"],
        "show_superset_dashboard": ["manage:analytics"],
        "enable_everai": [
            "manage:agentworkbench",
            "manage:autogendescription",
        ],
        "show_territory_plan": [
            "view:territoryplans",
            "explore:territoryplans",
            "edit:territoryplans",
        ],
        # "show_quota": ["view:quotas", "manage:quotas", "view:hiddenquotas"],
    }
    for feature, permissions in feature_permission.items():
        if not client_features.get(feature):
            admin_ui_permissions.extend(permissions)
    return admin_ui_permissions


@transaction.atomic
def handle_permissions_admin_ui(features):
    """
    Updates the permissions of all roles that have access to the admin UI features based on the enabled/disabled features.

    Args:
        features (dict): A dictionary containing the features and their enabled/disabled status.

    Returns:
        None
    """
    try:
        # Define the admin UI features and their corresponding permissions and components
        feature_permission = {
            "show_approval_feature": (
                "payouts_statements",
                ["view:requestapprovals"],
                ["Power Admin", "Super Admin", "Admin"],
                {"type": "ALL_DATA"},
            ),
            "show_superset_dashboard": (
                "dashboard",
                ["manage:analytics"],
                ["Power Admin", "Super Admin", "Admin"],
                None,
            ),
            "enable_everai": (
                "everai",
                [
                    "manage:agentworkbench",
                    "manage:autogendescription",
                ],
                ["Power Admin"],
                None,
            ),
            "show_territory_plan": (
                "territory_plans",
                [
                    "view:territoryplans",
                    "explore:territoryplans",
                    "edit:territoryplans",
                ],
                ["Power Admin"],
                None,
            ),
            # "show_quota": (
            #     "quotas_draws",
            #     ["view:quotas", "manage:quotas", "view:hiddenquotas"],
            #     ["view:quotas"],
            # ),
        }

        feature_enabled = {
            feature: (
                literal_eval(features[feature].capitalize())
                if feature in features
                else False
            )
            for feature in feature_permission.keys()
        }
        client_id = features.get("client_id")
        time = timezone.now()

        client_features = get_client_features(client_id=client_id)
        # Get all the roles and initialize the list of updated roles and role permission ids
        roles = RolePermissionsAccessor(client_id).get_all_roles()
        updated_roles = []
        role_permission_ids = []

        # Loop through all the roles and update their permissions based on the enabled/disabled features
        for role in roles:
            role_permission = role["permissions"]
            is_permission_updated = False

            for feature, permission in feature_permission.items():
                component = permission[0]
                is_enabled = feature_enabled[feature]

                if client_features.get(feature) == is_enabled:
                    continue

                if not is_enabled:
                    # If the feature is disabled, remove its permissions from the role
                    if component in role_permission:
                        role_permission[component]["permissions"] = [
                            perm
                            for perm in role_permission[component]["permissions"]
                            if perm not in permission[1]
                        ]
                        if role_permission[component]["permissions"] == []:
                            role_permission[component]["data_permission"] = None
                        is_permission_updated = True
                else:
                    # If the feature is enabled and the role is an admin or payee add its permissions to the role
                    if role["display_name"] in permission[2]:
                        if component in role_permission:
                            for perm in permission[1]:
                                if (
                                    perm
                                    not in role_permission[component]["permissions"]
                                ):
                                    role_permission[component]["permissions"].append(
                                        perm
                                    )
                                    is_permission_updated = True
                        else:
                            role_permission[component] = {
                                "permissions": permission[1],
                                "data_permission": permission[3],
                            }
                            is_permission_updated = True

            # If the role's permissions were updated, add it to the list of updated roles and role permission IDs
            if is_permission_updated:
                role["permissions"] = role_permission
                role["knowledge_begin_date"] = time
                role_permission_ids.append(role["role_permission_id"])
                updated_roles.append(role)

        # Use bulk_update to update all the objects at once
        RolePermissionsAccessor(client_id).update_objects(
            updated_roles, role_permission_ids, time
        )

        # Update Cache
        role_objs = RolePermissionsAccessor(client_id).get_role_by_role_permission_ids(
            role_permission_ids, True
        )
        for role in role_objs:
            update_permissions_in_cache(client_id, role["role_permission_id"], role)

    except Exception as exc:
        raise exc


def get_role_display_name(client_id, role_id):
    if not isinstance(role_id, list):
        role_id = [role_id]
    user_role_details = RolePermissionsAccessor(
        client_id
    ).get_role_by_role_permission_id(
        role_id,
        projection=[
            "role_permission_id",
            "display_name",
        ],
    )
    if user_role_details:
        if isinstance(role_id, list):
            display_names = [role.get("display_name", "") for role in user_role_details]
            all_display_names = ",".join(display_names)
            return all_display_names
        return user_role_details.get("display_name")


def does_user_have_permission(
    client_id: str,
    logged_email_id: str,
    ui_permission: set,
    component: Optional[str] = None,
    user_email_ids: Optional[set] = None,
) -> bool:
    """
    Check if a user has the specified permissions.

    Args:
        client_id (str): The client ID.
        logged_email_id (str): The email ID of the logged-in user.
        ui_permission (set): The set of UI permissions to check.
        component (str, optional): The component for which to check data permissions ("quota_draws", "payouts_statements"..).
        user_email_id (str, optional): The email ID of the user for whom to check data permissions.

    Returns:
        bool: True if the user has the specified permissions, False otherwise.
    """
    user_ui_permission = set(get_ui_permissions(client_id, logged_email_id))

    if component:
        user_data_permission = get_data_permission(
            client_id, logged_email_id, component
        )

        if user_data_permission and user_data_permission["type"] != RBAC.ALL_DATA.value:
            valid_emails = set(
                get_valid_payee_emails(client_id, logged_email_id, user_data_permission)
                if user_data_permission
                else set()
            )

            return user_email_ids is not None and user_email_ids.issubset(valid_emails)

    return ui_permission.issubset(user_ui_permission)


def merge_commission_plan_scope(
    role_plan_scope_map: Dict[str, Dict[str, str]], role_ids: List[str]
) -> Dict[str, Union[None, str]]:
    """
    Merge plan scope of all roles.

    Args:
        role_plan_scope_map (Dict[str, Dict[str, str]]): Role plan scope map.
        role_ids (List[str]): List of role IDs.

    Returns:
        Dict[str, str]: Merged plan scope.
    """
    precedence = {None: 1, "SHARED_PLANS": 2, "ALL_PLANS": 3}

    merged_plan_scope: Dict[str, Union[None, str]] = {
        "can_view": None,
        "can_edit": None,
        "can_delete": None,
    }

    for role in role_ids:
        plan_scope = role_plan_scope_map.get(role, {})
        for sub_permission_type, permission_value in plan_scope.items():
            if (
                precedence[permission_value]
                > precedence[merged_plan_scope[sub_permission_type]]
            ):
                merged_plan_scope[sub_permission_type] = permission_value

    return merged_plan_scope


def invalidate_all_shared_plans_for_emp_with_role(
    client_id: int,
    prev_role_ids: List[str],
    curr_role_ids: List[str],
    employee_email_id: str,
):
    # Fetching only roles with view commission plan permission
    roles_with_commission_plan = RolePermissionsAccessor(
        client_id
    ).get_commission_plan_permission_roles()

    # Map roles to their respective plan scopes
    role_plan_scope_map = {
        str(role["role_permission_id"]): role["permissions__commission_plans"][
            "plans_scope"
        ]
        for role in roles_with_commission_plan
    }

    # Merge commission plan scopes for previous and current roles
    prev_role_plan_scope = merge_commission_plan_scope(
        role_plan_scope_map, prev_role_ids
    )
    curr_role_plan_scope = merge_commission_plan_scope(
        role_plan_scope_map, curr_role_ids
    )
    remove_shared_plan_entries_on_role_change(
        client_id, prev_role_plan_scope, curr_role_plan_scope, [employee_email_id]
    )


def remove_shared_plan_entries_on_role_change(
    client_id: int,
    prev_role_plan_scope: Dict[str, Union[None, str]],
    curr_role_plan_scope: Dict[str, Union[None, str]],
    employee_email_ids: Optional[List[str]] = None,
    role_ids: Optional[List[str]] = None,
) -> None:
    """
    Remove shared plan(commission and forecast) entries when roles change.
    """
    try:
        time = timezone.now()

        # Determine the target types if there is a change in the plan scope to invalidate
        target_types = [
            permission_type
            for permission_type, permission_value in prev_role_plan_scope.items()
            if permission_type != RBACPlan.DELETE.value
            and curr_role_plan_scope.get(permission_type) != permission_value
        ]

        if target_types:
            if employee_email_ids is None and role_ids is not None:
                employee_email_ids = EmployeeAccessor(
                    client_id
                ).get_employee_email_ids_by_role(role_ids)

            for commission_type in [
                COMMISSION_TYPE.COMMISSION,
                COMMISSION_TYPE.FORECAST,
            ]:
                accessor = PlanSharedDetailsAccessorFactory(
                    client_id, commission_type
                ).get_accessor()
                accessor.invalidate_shared_plan_records_of_target(
                    target_list=employee_email_ids,
                    permission_list=target_types,
                    time=time,
                )

    except Exception as exc:
        raise exc


def get_invalid_emails_for_given_perms_and_emails(client_id, email_ids, permissions):
    invalid_emails = [
        email
        for email in email_ids
        if not does_user_have_permission(client_id, email, set(permissions))
    ]
    return invalid_emails
