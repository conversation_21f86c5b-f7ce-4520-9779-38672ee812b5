import json
import logging
import math
import os
import sys
from datetime import datetime, time
from decimal import Decimal
from io import BytesIO
from time import time as T
from typing import Any, Dict, List

import xlsxwriter
from dateutil.parser import parse
from django.template.loader import render_to_string
from django.utils import timezone
from django.utils.timezone import make_aware
from pydantic import BaseModel
from xlsxwriter.utility import xl_rowcol_to_cell

from commission_engine.accessors.client_accessor import get_client
from commission_engine.accessors.databook_accessor import DatasheetVariableAccessor
from commission_engine.services.client_feature_service import has_feature
from commission_engine.utils import (
    end_of_day,
    first_day_of_month,
    make_aware_wrapper,
    start_of_day,
)
from commission_engine.utils.date_utils import get_period_label_email
from commission_engine.utils.general_data import (
    PROFILE_CARD_DEFAULT_FIELDS_MAP,
    RBAC,
    CommissionViewType,
    RBACComponent,
    RbacPermissions,
    SegmentEvents,
    SegmentProperties,
    StatementsPdf,
)
from common.html_to_pdf import convert_html_to_pdf
from everstage_ddd.custom_metrics import get_bcr_metrics_for_payee_period
from interstage_project.auth_utils import authorize_for_profile_lookup_v2
from interstage_project.global_utils.localization_utils import (
    get_localized_message_utils,
)
from spm.accessors.config_accessors.client_config_accessor import ClientConfigAccessor
from spm.accessors.config_accessors.employee_accessor import (
    EmployeeAccessor,
    EmployeePayrollAccessor,
)
from spm.accessors.custom_field_accessor import (
    CustomFieldDataAccessor,
    CustomFieldsAccessor,
)
from spm.accessors.quota_acessors import QuotaAccessor
from spm.accessors.team_accessors.membership_accessor import MembershipAccessor
from spm.accessors.team_accessors.team_accessor import TeamAccessor
from spm.axogen_report_utils import write_axogen_custom_report_to_excel
from spm.constants.localization_constants import (
    StatementTerms,
    StatementTranslationTerms,
)
from spm.services.analytics_services.analytics_service import CoreAnalytics
from spm.services.commission_data_services import (  # get_criteria_columns_without_hyperlink_columns,
    get_payout_details_for_criteria,
)
from spm.services.commission_statement_service import CommissionsOverview
from spm.services.config_services.client_config_services import (
    get_default_options_and_fields,
)
from spm.services.datasheet_permission_services import (
    get_visible_columns_for_logged_in_user,
)
from spm.services.localization_services import get_statements_translated_message_service
from spm.services.period_label_services import get_custom_period_label
from spm.services.rbac_services import (
    get_data_permission,
    get_ui_permissions,
    get_valid_payee_emails,
    is_view_payroll_permission,
)
from spm.services.settlement_actions_service.settlement_total_service import (
    SettlementTotalService,
)
from spm.utils import convert_img_to_base64, get_valid_worksheet

logger = logging.getLogger(__name__)

CRITERIA_NAME_FONT_COLOR = "#980000"
AMOUNT_GREEN_COLOR = "green"
AMOUNT_RED_COLOR = "#f40e01"
DEERRED_AMOUNT_COLOR = "#DC2626"
DEFAULT_AMOUNT_COLOR = "black"
CELL_WIDTH = 18
MAX_COL_WIDTH = 200


class BasicTable(BaseModel):
    """
    Example:
    {
        "table": "Payout Summary",
        "headers": ["Criteria", "Amount"],
        "rows": [
            {
                "Criteria": "Current Period Payout",
                "Amount": "1000"
            },
        ]
    }
    """

    table: str
    headers: List[str]
    rows: List[dict[str, str]]


class CriteriaTable(BaseModel):
    """
    Example:
    {
        "id": "6371889-3278h-1832bf-3729b",
        "criteria": "Criteria 1",
        "amount": "1000"
    }
    """

    id: str
    criteria: str
    amount: str


class PlanTable(BaseModel):
    """
    Example:
    {
        "plan_name": "Plan 1",
        "payout_details": "1000",
        "criteria": [
            {
                "id": "6371889-3278h-1832bf-3729b",
                "criteria": "Criteria 1",
                "amount": "1000"
            },
        ]
    }
    """

    plan_name: str
    payout_details: str
    criteria: List[CriteriaTable]


class PreviousDeferredPlanTable(BaseModel):
    """
    Example:
    {
        "commisson_period": "Q2 2021",
        "periodkey": "q22021",
        "deferred_plan": [
                {
                "plan_name": "Plan 1",
                "payout_details": "1000",
                "criteria": [
                    {
                        "id": "6371889-3278h-1832bf-3729b",
                        "criteria": "Criteria 1",
                        "amount": "1000"
                    },
                ]
            },
        ]
    """

    deferred_plan: List[PlanTable]
    comission_period: str
    period_key: str


class AdjustmentRow(BaseModel):
    """
    Example:
    {
        "total": "1000",
        "headers": [
        {
            "field":1,
            "headerName":"Adjustment Type",
            "maxWidth": 200,
        },
        ],
        "rows": [
            {
               "1": "Adjustment 1",
            },
        ]
    }
    """

    total: str
    headers: List[dict[str, str | int]]
    rows: List[dict[str, str]]


class AdjustmentsTable(BaseModel):
    """
    Example
    {
        "commission_adjustments": {
            "adjustments":AdjustmentRow
        },
        "draw_adjustments": {
            "adjustments":AdjustmentRow
        }
    """

    commission_adjustments: dict[str, AdjustmentRow]
    draw_adjustments: dict[str, AdjustmentRow]


class StatementPDFModel(BaseModel):
    client_id: int
    use_aggrid: bool
    payout_summary: BasicTable | dict = {}
    commission_summary: BasicTable | dict = {}
    quota_attainment: BasicTable | dict = {}
    current_pd_payout_details: List[PlanTable] | List = []
    previous_deferred_comm_pd_payout_details: List[PreviousDeferredPlanTable] | List = (
        []
    )
    deferred_commission_details: List[PlanTable] | List = []
    earned_commission_details: List[PlanTable] | List = []
    payee_details: dict[str, str | bool | float | int | None]
    payroll_details: dict[str, str | bool | float | int | None]
    custom_fields: dict[str, str | bool | float | int | None]
    table_headers: dict[str, str]
    localized_terms: dict[str, str]
    image_url: str
    banner: str
    json_data: str
    adjustments: AdjustmentsTable | dict = {}
    payout_status_details: dict[str, str | bool | float | int | None]
    breakdown_summary: BasicTable | dict = {}
    show_payout_table_breakdown: bool


def has_view_payroll_data_permission(client_id, login_user_id, payee_email_id):
    data_permission = get_data_permission(
        client_id, login_user_id, RBACComponent.MANAGE_USERS.value
    )
    if data_permission and data_permission["type"] == RBAC.ALL_DATA.value:
        return True
    else:
        payee_emails = get_valid_payee_emails(client_id, login_user_id, data_permission)
        if payee_email_id in payee_emails:
            return True
    return False


def find_statement_period(client_id, payee_email, psd, ped, fiscal_start_month):
    """
    This functions the returns the desired period name for all types of payouts
    (monthly, quarterly, annualy, custom)
    """
    # Getting payroll details here to get payout frequency as the payout freq
    # from settlement total service is the label of the custom payout frequency.
    employee_payroll = EmployeePayrollAccessor(client_id).get_employee_payroll_for_date(
        ped, payee_email
    )

    has_period_label_feature = has_feature(client_id, "payout_period_label")
    period = ""
    if has_period_label_feature:
        # Get custom period label, if present
        period_tuple = [(psd, ped)]
        period = get_custom_period_label(client_id, period_tuple)

    if not period:
        # Get period label for all type of payout cycle
        period = get_period_label_email(
            psd=psd,
            ped=ped,
            fiscal_start_month=fiscal_start_month,
            payout_freq=employee_payroll.payout_frequency,
            client_id=client_id,
        )
    return period


class ExportStatements:
    """
    Class to Export Statements data as CSV.
    """

    def __init__(
        self, client_id, payee_email, psd, ped, login_user_id, is_settlement_view=False
    ):
        self.client_id = client_id
        self.payee_email = payee_email
        self.psd = psd
        self.ped = ped
        self.is_settlement_view = is_settlement_view
        self.payee_details = {}
        self.output = BytesIO()
        self.payroll_details = {}
        self.selected_user_fields = {}
        self.workbook = xlsxwriter.Workbook(self.output, {"remove_timezone": True})
        self.bold = self.workbook.add_format({"bold": True, "font_size": 9})
        self.italic = self.workbook.add_format({"italic": True, "font_size": 9})
        self.bold_italic = self.workbook.add_format(
            {"bold": True, "italic": True, "font_size": 9}
        )
        self.number_format = self.workbook.add_format(
            {"num_format": "#,##0.00", "font_size": 9}
        )
        self.number_format_with_strike = self.workbook.add_format(
            {
                "num_format": "#,##0.00",
                "font_size": 9,
                "font_strikeout": True,  # Enables strikethrough
            }
        )
        self.bold_number_format = self.workbook.add_format(
            {"bold": True, "num_format": "#,##0.00", "font_size": 9}
        )
        self.percentage_format = self.workbook.add_format(
            {"num_format": "0.00%", "font_size": 9}
        )
        self.bold_percentage_format = self.workbook.add_format(
            {"bold": True, "num_format": "0.00%", "font_size": 9}
        )
        self.client = get_client(client_id)
        self.show_payout_table_breakdown = False
        if (
            "show_payout_table_breakdown" in self.client.client_features
            and self.client.client_features["show_payout_table_breakdown"]
        ):
            self.show_payout_table_breakdown = True

        self.show_commission_percent = False
        if (
            "show_commission_percent" in self.client.client_features
            and self.client.client_features["show_commission_percent"]
        ):
            self.show_commission_percent = True
        self.plan_criteria_map = {}
        self.datasheet_ids = set()
        self.payout_view_data = {}
        self.datasheet_variable_map = {}
        self.adjustment_total = Decimal(0)
        self.statements_data = {}
        self.criteria_qe_map = {}
        self.login_user_id = login_user_id
        self.localized_terms = {}

    def all_text_format(
        self,
        font_color="black",
        bold=False,
        italic=False,
        num_format=None,
        percentage_format=None,
        bg_color=None,
        font_size=None,
        strikethrough=False,
    ):
        format_dict = {
            "font_color": font_color,
            "bold": bold,
            "italic": italic,
            "font_size": 9,
        }
        if percentage_format:
            format_dict["num_format"] = "0.00%"
        if num_format:
            format_dict["num_format"] = "#,##0.00"
            format_dict["align"] = "right"
        if bg_color:
            format_dict["bg_color"] = bg_color
        if font_size:
            format_dict["font_size"] = font_size
        if strikethrough:
            format_dict["font_strikeout"] = True  # Apply strikethrough
        return self.workbook.add_format(format_dict)

    def font_color_for_amount(self, amount):
        amount = Decimal(amount)
        amount_font_color = DEFAULT_AMOUNT_COLOR
        if amount < 0:
            amount_font_color = AMOUNT_RED_COLOR
        if amount > 0:
            amount_font_color = AMOUNT_GREEN_COLOR
        return amount_font_color

    def add_selected_user_fields(self):
        custom_fields_data = CustomFieldDataAccessor(
            self.client_id
        ).get_custom_field_data_based_on_sec_kd(self.ped, self.payee_email)
        custom_fields_data = custom_fields_data.data if custom_fields_data else {}
        custom_fields = CustomFieldsAccessor(self.client_id).get_all_custom_fields()
        custom_fields_type_map = {}
        custom_fields_name_map = {}
        custom_fields_options_map = {}
        for custom_field in custom_fields:
            custom_fields_type_map[custom_field["system_name"]] = custom_field[
                "field_type"
            ]
            custom_fields_name_map[custom_field["system_name"]] = custom_field[
                "display_name"
            ]
            custom_fields_options_map[custom_field["system_name"]] = {
                item["system_name"]: item["display_name"]
                for item in custom_field["options"]
            }
        for selected_field in self.selected_user_fields:
            # if the field is a custom field
            if selected_field.startswith("cf_"):
                # if the type of custom_field is checkbox then set the value accordingly
                if custom_fields_type_map[selected_field] == "Checkbox":
                    self.payee_details[custom_fields_name_map[selected_field]] = (
                        "True"
                        if custom_fields_data.get(selected_field, None)
                        else "False"
                    )
                elif custom_fields_type_map[selected_field] == "Dropdown":
                    self.payee_details[custom_fields_name_map[selected_field]] = (
                        custom_fields_options_map[selected_field].get(
                            custom_fields_data.get(selected_field, None), None
                        )
                    )
                else:
                    self.payee_details[custom_fields_name_map[selected_field]] = (
                        custom_fields_data.get(selected_field, None)
                    )
            else:
                if selected_field == "bcr":
                    self.add_bcr()
                else:
                    localized_terminology = get_localized_message_utils(
                        PROFILE_CARD_DEFAULT_FIELDS_MAP[selected_field], self.client_id
                    )
                    self.payee_details[localized_terminology] = self.payroll_details[
                        selected_field
                    ]

    def add_teams_details(self):
        ids = MembershipAccessor(self.client_id).get_all_teams_of_whole_team(
            self.ped, [self.payee_email]
        )

        team_ids = []
        for team_id in ids:
            team_ids.append(team_id["team_id"])
        teams = TeamAccessor(self.client_id).get_all_teams_of_type_team_id(
            team_ids, "team", as_dicts=False
        )
        team_name_list = []
        for team in teams:
            team_name_list.append(team.team_name)
        return team_name_list

    def add_pods_details(self):
        team = TeamAccessor(self.client_id).get_team_id_of_pod_owner(self.payee_email)
        if team:
            member_list = MembershipAccessor(
                self.client_id
            ).get_team_members_ed_in_team(team.team_id, self.ped, False)
        else:
            member_list = []
        pod_member_ids_list = []
        for pod_data in member_list:
            pod_member_ids_list.append(pod_data.group_member_email_id)
        member_name_list = EmployeeAccessor(self.client_id).get_employees_name(
            pod_member_ids_list
        )
        pod_member_names = []
        for member in member_name_list:
            pod_member_names.append(f"{member['first_name']} {member['last_name']}")

        return pod_member_names

    def sort_plans_by_type_and_order(self, commission_details):
        """
        Sorts a list of plan IDs by type (MAIN/SPIFF) and display order.
        """
        plan_ids = list(commission_details.keys())
        return sorted(
            plan_ids,
            key=lambda plan_id: (
                (
                    1
                    if commission_details.get(plan_id, {}).get("plan_type") == "MAIN"
                    else 2
                ),
                (
                    commission_details.get(plan_id, {}).get("plan_display_order")
                    if commission_details.get(plan_id, {}).get("plan_display_order")
                    is not None
                    else sys.maxsize
                ),
            ),
        )

    def add_customised_statement_fields(self):
        config_record = ClientConfigAccessor(self.client_id).get_profile_fields()
        configs = {}
        localized_team_name = get_localized_message_utils("TEAM_NAME", self.client_id)
        localized_pod_name = get_localized_message_utils("POD_NAME", self.client_id)
        if config_record:
            configs = config_record.value
        else:
            (
                configs["options"],
                configs["selected_fields"],
            ) = get_default_options_and_fields()
        self.selected_user_fields = configs["selected_fields"]
        if configs["options"].get("user_fields", False):
            self.add_selected_user_fields()
        if configs["options"].get("teams", False):
            self.payee_details["empty_line3"] = ""
            self.payee_details[localized_team_name] = self.add_teams_details()
        if configs["options"].get("pod_details", False):
            self.payee_details[localized_pod_name] = self.add_pods_details()

    def add_localized_terms(self):
        preferred_language = self.client.client_features.get("preferred_language", "")
        if preferred_language:
            localized_last_calculated = StatementTerms.LAST_CALCULATED.value
            localized_downloaded = StatementTerms.DOWNLOADED.value
            localized_period = StatementTerms.PERIOD.value
            localized_from_current_period = StatementTerms.FROM_CURRENT_PERIOD.value
        else:
            localized_last_calculated = get_localized_message_utils(
                StatementTerms.LAST_CALCULATED.name, self.client_id
            )
            localized_downloaded = get_localized_message_utils(
                StatementTerms.DOWNLOADED.name, self.client_id
            )
            localized_period = get_localized_message_utils(
                StatementTerms.PERIOD.name, self.client_id
            )
            localized_from_current_period = get_localized_message_utils(
                StatementTerms.FROM_CURRENT_PERIOD.name, self.client_id
            )

        self.localized_terms = {
            "last_calculated": localized_last_calculated,
            "downloaded": localized_downloaded,
            "period": localized_period,
            "from_current_period": localized_from_current_period,
        }

    def add_bcr(self):
        bcr_list = get_bcr_metrics_for_payee_period(
            self.client_id, self.payee_email, self.psd, self.ped
        )

        for bcr in bcr_list:
            for key, val in bcr.items():
                self.payee_details[key] = val

    def create_payout_summary_sheet(self):
        summary_label = get_localized_message_utils("SUMMARY", self.client_id)
        worksheet = self.workbook.add_worksheet(summary_label)
        localized_payout_summary = get_localized_message_utils(
            StatementTerms.PAYOUT_SUMMARY.name, self.client_id
        )
        localized_payout_percent = get_localized_message_utils(
            StatementTerms.PAYOUT_PERCENT.name, self.client_id
        )
        localized_current_period_payout = get_localized_message_utils(
            StatementTerms.CURRENT_PERIOD_PAYOUT.name, self.client_id
        )
        localized_past_def_payout = get_localized_message_utils(
            StatementTerms.PAST_DEF_COMM_PAYOUT.name, self.client_id
        )
        localized_payout_arrears = get_localized_message_utils(
            StatementTerms.PAYOUT_ARREARS.name, self.client_id
        )
        localized_total_payout = get_localized_message_utils(
            StatementTerms.TOTAL_PAYOUT.name, self.client_id
        )
        localized_commission = get_localized_message_utils(
            StatementTerms.COMMISSION.name, self.client_id
        )
        localized_comm_summary = get_localized_message_utils(
            StatementTerms.COMMISSION_SUMMARY.name, self.client_id
        )
        localized_earned_comm = get_localized_message_utils(
            StatementTerms.EARNED_COMMISSION.name, self.client_id
        )
        localized_deferred_comm = get_localized_message_utils(
            StatementTerms.DEFERRED_COMMISSION.name, self.client_id
        )
        localized_quota_attainment = get_localized_message_utils(
            StatementTerms.QUOTA_ATTAINMENT.name, self.client_id
        )
        localized_quota_period = get_localized_message_utils(
            StatementTerms.QUOTA_PERIOD.name, self.client_id
        )
        localized_quota_att_percent = get_localized_message_utils(
            StatementTerms.QUOTA_ATTAINMENT_PERCENT.name, self.client_id
        )
        localized_quota = get_localized_message_utils(
            StatementTerms.QUOTA.name, self.client_id
        )
        localized_adjustments = get_localized_message_utils(
            StatementTerms.ADJUSTMENTS.name, self.client_id
        )
        localized_attained = get_localized_message_utils(
            StatementTerms.ATTAINED.name, self.client_id
        )
        localized_manager_individual = get_localized_message_utils(
            StatementTerms.MANAGER_OR_INDIVIDUAL.name, self.client_id
        )
        localized_name = get_localized_message_utils("NAME", self.client_id)

        overview_obj = CommissionsOverview(
            client_id=self.client_id,
            psd=self.psd,
            ped=self.ped,
            payee_email=self.payee_email,
        )

        self.statements_data = SettlementTotalService(
            client_id=self.client_id, ped=self.ped, payee_emails=[self.payee_email]
        ).get_statements_data(export_details=True)

        period = find_statement_period(
            self.client_id,
            self.payee_email,
            self.psd,
            self.ped,
            self.client.fiscal_start_month,
        )
        localized_statements_header = get_localized_message_utils(
            StatementTranslationTerms.STATEMENT_FOR.name, self.client_id
        )
        header_text = f"{localized_statements_header} {period}"
        worksheet.write("A1", header_text, self.bold)

        current_time = timezone.now().strftime("%d %b %Y, %I:%M%p")
        is_payroll = is_view_payroll_permission(
            self.client_id, self.login_user_id
        ) and has_view_payroll_data_permission(
            self.client_id, self.login_user_id, self.payee_email
        )

        localized_name = get_localized_message_utils("NAME", self.client_id)
        localized_email = get_localized_message_utils("EMAIL", self.client_id)
        localized_designation = get_localized_message_utils(
            "DESIGNATION", self.client_id
        )
        localized_period_start_date = get_localized_message_utils(
            "PERIOD_START_DATE", self.client_id
        )
        localized_period_end_date = get_localized_message_utils(
            "PERIOD_END_DATE", self.client_id
        )
        localized_last_calculated = get_localized_message_utils(
            "LAST_CALCULATED", self.client_id
        )
        localized_downloaded = get_localized_message_utils("DOWNLOADED", self.client_id)
        localized_status = get_localized_message_utils("STATUS", self.client_id)

        self.payee_details = {
            localized_name: self.statements_data["payee_name"],
            localized_email: self.payee_email,
            localized_designation: self.statements_data["designation"],
            "empty_line1": "",
            localized_period_start_date: self.psd.strftime("%d-%b-%Y"),
            localized_period_end_date: self.ped.strftime("%d-%b-%Y"),
            localized_last_calculated: "",
            localized_downloaded: current_time,
            localized_status: "",
            "empty_line2": "",
        }
        self.payroll_details = {
            "pay_currency": (
                self.statements_data["payee_currency"]
                if self.statements_data.get("payee_currency")
                else None
            ),
            "payout_frequency": (
                self.statements_data["payout_frequency"]
                if self.statements_data.get("payout_frequency")
                else None
            ),
            "variable_pay": (
                float(self.statements_data["variable_pay_as_per_period"])
                if is_payroll
                else 0
            ),
            "reporting_manager": (
                self.statements_data["reporting_manager"]
                if self.statements_data.get("reporting_manager")
                else None
            ),
            "employment_country": (
                self.statements_data["employment_country"]
                if self.statements_data.get("employment_country")
                else None
            ),
            "fixed_pay": self.statements_data.get("fixed_pay", 0) if is_payroll else 0,
            "employee_id": (
                self.statements_data["employee_id"]
                if self.statements_data.get("employee_id")
                else None
            ),
        }

        self.add_customised_statement_fields()

        self.add_localized_terms()

        localized_last_calculated = get_localized_message_utils(
            "LAST_CALCULATED", self.client_id
        )
        localized_status = get_localized_message_utils("STATUS", self.client_id)

        self.payee_details[localized_last_calculated] = (
            self.statements_data["lastUpdatedTime"].strftime("%d %b %Y, %I:%M%p")
            if self.statements_data["lastUpdatedTime"]
            else None
        )
        localized_statement_locked = get_localized_message_utils(
            "STATEMENT_LOCKED", self.client_id
        )
        localized_statement_unlocked = get_localized_message_utils(
            "STATEMENT_UNLOCKED", self.client_id
        )
        self.payee_details[localized_status] = (
            localized_statement_locked
            if self.statements_data["comm_calc_status"] == "Frozen"
            else localized_statement_unlocked
        )

        # add payee details
        index = 3
        localized_variable_pay = get_localized_message_utils(
            PROFILE_CARD_DEFAULT_FIELDS_MAP["variable_pay"], self.client_id
        )
        localized_team_name = get_localized_message_utils("TEAM_NAME", self.client_id)
        localized_pod_name = get_localized_message_utils("POD_NAME", self.client_id)
        for key, value in self.payee_details.items():
            if "empty_line" in key:
                key = None
                value = None
            worksheet.write("A" + str(index), key, self.italic)
            if key == localized_name:
                worksheet.write("B" + str(index), value, self.bold)
            elif key == localized_variable_pay:
                worksheet.write_number("B" + str(index), value, self.number_format)
            elif key == localized_team_name or key == localized_pod_name:
                if len(value) > 0:
                    for val in value:
                        worksheet.write("B" + str(index), val, self.all_text_format())
                        index += 1
                else:
                    worksheet.write("B" + str(index), None)
            else:
                worksheet.write("B" + str(index), value, self.all_text_format())
            index += 1
        index += 1
        worksheet.write("A" + str(index), localized_payout_summary, self.bold)
        worksheet.write(
            "B" + str(index),
            localized_commission
            + " ( "
            + str(self.payroll_details["pay_currency"])
            + " )",
            self.bold,
        )
        worksheet.write("C" + str(index), localized_payout_percent, self.bold)

        # add payout details
        index += 1
        commissions_list = [
            "earned_commission_details",
            "deferred_commission_details",
            "previous_commission_deferred_details",
        ]
        for key in commissions_list:
            if key != "previous_commission_deferred_details":
                for item in list(self.statements_data[key].values()):
                    if (
                        key == "deferred_commission_details"
                        and not self.statements_data["deferred_commission"]
                    ):
                        continue
                    if key == "earned_commission_details":
                        self.plan_criteria_map[item["plan_id"]] = {
                            "plan_name": item["plan_name"],
                            "criteria": item["criteria_details"],
                        }
                    self.datasheet_ids = self.datasheet_ids.union(
                        {
                            str(item["datasheet_id"])
                            for item in list(item["criteria_details"].values())
                        }
                    )
            else:
                for plan in list(self.statements_data[key].values()):
                    for item in list(plan["plan_details"].values()):
                        self.datasheet_ids = self.datasheet_ids.union(
                            {
                                str(item["datasheet_id"])
                                for item in list(item["criteria_details"].values())
                            }
                        )

        if self.statements_data.get("current_payout_details"):
            worksheet.write(
                "A" + str(index),
                localized_current_period_payout,
                self.all_text_format(),
            )
            worksheet.write_number(
                "B" + str(index),
                self.statements_data["current_payout"],
                self.all_text_format(
                    num_format=True,
                    font_color=self.font_color_for_amount(
                        self.statements_data["current_payout"]
                    ),
                ),
            )
            if self.show_commission_percent:
                if self.payroll_details["variable_pay"] != 0:
                    percentage = (
                        "="
                        + (xl_rowcol_to_cell(index - 1, 1) or "")
                        + "/"
                        + str(self.payroll_details["variable_pay"])
                    )
                else:
                    percentage = 0
                worksheet.write("C" + str(index), percentage, self.percentage_format)
            index += 1

        if self.statements_data.get("previous_commission_deferred"):
            worksheet.write(
                "A" + str(index),
                localized_past_def_payout,
                self.all_text_format(),
            )
            worksheet.write_number(
                "B" + str(index),
                self.statements_data["previous_commission_deferred"],
                self.all_text_format(
                    num_format=True,
                    font_color=self.font_color_for_amount(
                        self.statements_data["previous_commission_deferred"]
                    ),
                ),
            )
            if self.show_commission_percent:
                if self.payroll_details["variable_pay"] != 0:
                    percentage = (
                        "="
                        + (xl_rowcol_to_cell(index - 1, 1) or "")
                        + "/"
                        + str(self.payroll_details["variable_pay"])
                    )
                else:
                    percentage = 0
                worksheet.write("C" + str(index), percentage, self.percentage_format)
            index += 1

        if self.statements_data.get("payout_arrears"):
            worksheet.write(
                "A" + str(index), localized_payout_arrears, self.all_text_format()
            )
            worksheet.write_number(
                "B" + str(index),
                self.statements_data["payout_arrears"],
                self.all_text_format(
                    num_format=True,
                    font_color=self.font_color_for_amount(
                        self.statements_data["payout_arrears"]
                    ),
                ),
            )
            if self.show_commission_percent:
                if self.payroll_details["variable_pay"] != 0:
                    percentage = (
                        "="
                        + (xl_rowcol_to_cell(index - 1, 1) or "")
                        + "/"
                        + str(self.payroll_details["variable_pay"])
                    )
                else:
                    percentage = 0
                worksheet.write("C" + str(index), percentage, self.percentage_format)
            index += 1

        for item in self.statements_data["adjustments_details"]:
            self.adjustment_total += Decimal(item["amount"])

        worksheet.write("A" + str(index), localized_adjustments, self.all_text_format())
        worksheet.write_number(
            "B" + str(index),
            self.adjustment_total,
            self.all_text_format(
                num_format=True,
                font_color=self.font_color_for_amount(self.adjustment_total),
            ),
        )
        if self.show_commission_percent:
            if self.payroll_details["variable_pay"] != 0:
                percentage = (
                    "="
                    + (xl_rowcol_to_cell(index - 1, 1) or "")
                    + "/"
                    + str(self.payroll_details["variable_pay"])
                )
            else:
                percentage = 0
            worksheet.write("C" + str(index), percentage, self.percentage_format)

        localized_recoverable_draw = get_localized_message_utils(
            "RECOVERABLE_DRAW", self.client_id
        )

        index += 1
        worksheet.write(
            "A" + str(index), localized_recoverable_draw, self.all_text_format()
        )
        worksheet.write_number(
            "B" + str(index),
            self.statements_data["draws"],
            self.all_text_format(num_format=True),
        )
        if self.show_commission_percent:
            if self.payroll_details["variable_pay"] != 0:
                percentage = (
                    "="
                    + (xl_rowcol_to_cell(index - 1, 1) or "")
                    + "/"
                    + str(self.payroll_details["variable_pay"])
                )
            else:
                percentage = 0
            worksheet.write("C" + str(index), percentage, self.percentage_format)

        index += 1
        worksheet.write("A" + str(index), localized_total_payout, self.bold_italic)
        worksheet.write_number(
            "B" + str(index),
            self.statements_data["total_payout"],
            self.all_text_format(
                num_format=True,
                bold=True,
                font_color=self.font_color_for_amount(
                    self.statements_data["total_payout"]
                ),
            ),
        )
        if self.show_commission_percent:
            if self.payroll_details["variable_pay"] != 0:
                percentage = (
                    "="
                    + (xl_rowcol_to_cell(index - 1, 1) or "")
                    + "/"
                    + str(self.payroll_details["variable_pay"])
                )
            else:
                percentage = 0
            worksheet.write("C" + str(index), percentage, self.bold_percentage_format)

        if self.statements_data["has_settlement_rules_history_for_active_plans"]:
            index += 2
            worksheet.write("A" + str(index), localized_comm_summary, self.bold)
            index += 1
            worksheet.write("A" + str(index), localized_earned_comm, self.italic)
            worksheet.write(
                "B" + str(index),
                self.statements_data["earned_commission"],
                self.all_text_format(
                    num_format=True,
                ),
            )
            index += 1
            worksheet.write("A" + str(index), localized_deferred_comm, self.italic)
            worksheet.write(
                "B" + str(index),
                self.statements_data["deferred_commission"],
                self.all_text_format(font_color=AMOUNT_RED_COLOR, num_format=True),
            )

        # add quota details
        quota_details = overview_obj.get_quota_attainment_details()
        criteria_qe_data = quota_details.get("criteria_quota_erosion_map", {})
        quota_data = quota_details.get("quota_data", [])
        has_reportees = quota_details.get("has_reportees", False)
        self.criteria_qe_map = {
            item["criteria_id"]: (
                item["currency_symbol"] + str(round(item["quota_erosion"], 2))
                if item["quota_erosion"]
                else 0
            )
            for item in criteria_qe_data
        }

        user_permissions = get_ui_permissions(self.client_id, self.login_user_id)
        is_authorized = authorize_for_profile_lookup_v2(
            self.client_id,
            self.login_user_id,
            self.payee_email,
            RBACComponent.QUOTAS_DRAWS.value,
        )
        show_quota_details = (
            is_authorized and RbacPermissions.VIEW_QUOTAS.value in user_permissions
        )

        if quota_data and show_quota_details:
            index += 2
            worksheet.write("A" + str(index), localized_quota_attainment, self.bold)
            quota_columns = [
                localized_name,
                localized_manager_individual,
                localized_quota_period,
                localized_quota,
                localized_attained,
                localized_quota_att_percent,
            ]
            for item in range(len(quota_columns)):
                worksheet.write(index, item, quota_columns[item], self.bold_italic)

            index += 1
            col_index = 0

            quota_category_names = [item["quota_category_name"] for item in quota_data]
            qs = QuotaAccessor(self.client_id).get_quota_category_display_name_map(
                quota_category_names
            )
            quota_display_names = {
                record["quota_category_name"]: record["display_name"] for record in qs
            }
            if "Primary" in quota_category_names:
                quota_display_names["Primary"] = get_localized_message_utils(
                    "PRIMARY_QUOTA", self.client_id
                )

            for item in quota_data:
                quota_type_localized = item["quota_schedule_localized"] or "NA"
                quota_category_display_name = quota_display_names.get(
                    item["quota_category_name"]
                )
                worksheet.write(
                    index,
                    col_index,
                    quota_category_display_name,
                    self.all_text_format(),
                )
                worksheet.write(
                    index,
                    col_index + 1,
                    (
                        get_localized_message_utils("AS_INDIVIDUAL", self.client_id)
                        if not has_reportees
                        else (
                            get_localized_message_utils("AS_MANAGER", self.client_id)
                            if item["is_team"]
                            else get_localized_message_utils(
                                "AS_INDIVIDUAL", self.client_id
                            )
                        )
                    ),
                    self.all_text_format(),
                )
                worksheet.write(
                    index, col_index + 2, quota_type_localized, self.all_text_format()
                )
                worksheet.write_number(
                    index, col_index + 3, item["quota_value"], self.number_format
                )
                worksheet.write_number(
                    index, col_index + 4, item["attained_quota"], self.number_format
                )
                if item["quota_value"]:
                    percentage = (
                        "="
                        + (xl_rowcol_to_cell(index, col_index + 4) or "")
                        + "/"
                        + (xl_rowcol_to_cell(index, col_index + 3) or "")
                    )
                else:
                    percentage = 0
                worksheet.write(
                    index,
                    col_index + 5,
                    percentage,
                    self.percentage_format,
                )
                index += 1
        index += 2
        self.payout_view_data = SettlementTotalService(
            client_id=self.client_id, ped=self.ped, payee_emails=[self.payee_email]
        ).get_payouts_view_data()
        breakdown_data = (
            self.payout_view_data[self.payee_email]
            if self.payee_email in self.payout_view_data
            else []
        )
        if self.show_payout_table_breakdown and breakdown_data:
            localized_breakdown = get_localized_message_utils(
                StatementTranslationTerms.BREAKDOWN.name, self.client_id
            )
            worksheet.write("A" + str(index), localized_breakdown, self.bold)
            localized_date = get_localized_message_utils("DATE", self.client_id)
            localized_amount = get_localized_message_utils("AMOUNT", self.client_id)
            localized_comments = get_localized_message_utils("COMMENTS", self.client_id)
            breakdown_columns = [
                localized_date,
                f'{localized_amount} ( {self.statements_data["payee_currency"]} )',
                localized_comments,
            ]
            for item in range(len(breakdown_columns)):
                worksheet.write(index, item, breakdown_columns[item], self.bold_italic)
            index += 1
            col_index = 0
            for item in breakdown_data:
                date = item["date"]
                amount = item["amount"]
                comment = item["comment"]
                is_paid = item["is_paid"]
                formatted_date = datetime.strptime(date, "%Y-%m-%d").strftime(
                    "%d %b %Y"
                )
                if is_paid:
                    worksheet.write(
                        index,
                        col_index,
                        formatted_date,
                        self.all_text_format(),
                    )
                    worksheet.write_number(
                        index, col_index + 1, float(amount), self.number_format
                    )
                    worksheet.write(
                        index, col_index + 2, comment, self.all_text_format()
                    )
                else:
                    worksheet.write(
                        index,
                        col_index,
                        formatted_date,
                        self.all_text_format(strikethrough=True),
                    )
                    worksheet.write_number(
                        index,
                        col_index + 1,
                        float(amount),
                        self.number_format_with_strike,
                    )
                    worksheet.write(
                        index,
                        col_index + 2,
                        comment,
                        self.all_text_format(strikethrough=True),
                    )
                index += 1

        worksheet.set_column(0, worksheet.dim_colmax, CELL_WIDTH)

    def create_criteria_data_table(
        self, worksheet, index, commission_view_type, criteria_details, criteria_data
    ):
        is_line_item_level = criteria_data["is_line_item_level"]
        localized_payout_percent = get_localized_message_utils(
            StatementTerms.PAYOUT_PERCENT.name, self.client_id
        )
        localized_comm_summation = get_localized_message_utils(
            StatementTerms.COMM_SUMMATION.name, self.client_id
        )
        localized_comm = get_localized_message_utils(
            StatementTerms.COMMISSION.name, self.client_id
        )
        localized_quota_erosion = get_localized_message_utils(
            StatementTerms.QUOTA_EROSION.name, self.client_id
        )
        localized_amount = get_localized_message_utils("AMOUNT", self.client_id)
        localized_total = get_localized_message_utils("TOTAL", self.client_id)

        criteria_id = criteria_details["criteria_id"]
        criteria_type = criteria_details["criteria_type"]

        worksheet.write(
            "C" + str(index),
            f'{localized_amount} ( {self.payroll_details["pay_currency"]} )',
            self.all_text_format(italic=True, bold=True),
        )
        worksheet.write(
            "D" + str(index),
            localized_payout_percent,
            self.all_text_format(italic=True, bold=True),
        )
        if (
            commission_view_type == CommissionViewType.EARNED_COMMISSIONS.value
            and criteria_type in ("Quota", "CustomQuota")
        ):
            worksheet.write(
                "E" + str(index),
                localized_quota_erosion
                + f' {localized_total} ( {self.payroll_details["pay_currency"]} )',
                self.all_text_format(italic=True, bold=True),
            )
        index += 1
        worksheet.write(
            "B" + str(index),
            criteria_details["criteria_name"],
            self.all_text_format(bold=True, font_color=CRITERIA_NAME_FONT_COLOR),
        )
        worksheet.write(
            "C" + str(index),
            criteria_details["amount"],
            self.all_text_format(
                bold=True,
                font_color=(
                    self.font_color_for_amount(criteria_details["amount"])
                    if commission_view_type
                    != CommissionViewType.EARNED_COMMISSIONS.value
                    else DEFAULT_AMOUNT_COLOR
                ),
                num_format=True,
            ),
        )
        if (
            commission_view_type == CommissionViewType.EARNED_COMMISSIONS.value
            and criteria_type in ("Quota", "CustomQuota")
        ):
            criteria_qe_total = self.criteria_qe_map.get(str(criteria_id), 0)
            worksheet.write(
                "E" + str(index),
                criteria_qe_total,
                self.all_text_format(bold=True, num_format=True),
            )
        if self.show_commission_percent:
            if self.payroll_details["variable_pay"] != 0:
                percentage = (
                    "="
                    + (xl_rowcol_to_cell(index - 1, 2) or "")
                    + "/"
                    + str(self.payroll_details["variable_pay"])
                )
            else:
                percentage = 0
            worksheet.write(
                "D" + str(index),
                percentage,
                self.bold_percentage_format,
            )
        date_format = self.workbook.add_format(
            {"num_format": "dd-Mmm-yyyy", "font_size": 9}
        )
        index += 1
        worksheet.write("A" + str(index), "#", self.italic)
        headers = []
        data_headers = []
        has_commission_column = False
        for column in criteria_data["columns"]:
            header = column
            set_date_format = False
            set_number_format = False
            set_font_color = DEFAULT_AMOUNT_COLOR
            if column in self.datasheet_variable_map[criteria_details["datasheet_id"]]:
                header = self.datasheet_variable_map[criteria_details["datasheet_id"]][
                    column
                ]["display_name"]
                if (
                    self.datasheet_variable_map[criteria_details["datasheet_id"]][
                        column
                    ]["data_type"]
                    == 2
                ):
                    set_date_format = True
                if (
                    self.datasheet_variable_map[criteria_details["datasheet_id"]][
                        column
                    ]["data_type"]
                    == 1
                ):
                    set_number_format = True
            elif column == "commission":
                has_commission_column = True
                if is_line_item_level:
                    header = (
                        localized_comm
                        + " ("
                        + str(self.payroll_details["pay_currency"])
                        + ")"
                    )
                else:
                    header = (
                        localized_comm_summation
                        + " ("
                        + str(self.payroll_details["pay_currency"])
                        + ")"
                    )
                set_number_format = True
                set_font_color = DEFAULT_AMOUNT_COLOR
            elif column == "quotaErosion":
                header = localized_quota_erosion
                column = "quota_erosion"
                set_number_format = True
            data_headers.append(column)
            if set_date_format:
                headers.append({"header": header, "format": date_format})
            elif set_number_format:
                headers.append(
                    {
                        "header": header,
                        "format": self.all_text_format(
                            num_format=True, font_color=set_font_color
                        ),
                    }
                )
            else:
                headers.append(
                    {
                        "header": header,
                        "format": self.all_text_format(),
                    }
                )

        records = []
        commission = None
        for record in criteria_data["records"]:
            if not is_line_item_level:
                commission = record["commission"]
            row = []
            for key in data_headers:
                if key in record:
                    row.append(record[key])
                else:
                    row.append(None)
            records.append(row)

        table_row_count = len(records)
        table_column_count = len(headers)
        for i in range(table_row_count):
            worksheet.write("A" + str(index + 1 + i), i + 1)

        if len(headers) != 0 and len(records) != 0:
            worksheet.add_table(
                index - 1,
                1,
                index + table_row_count,
                table_column_count,
                {"data": records, "columns": headers, "style": None},
            )
        worksheet.set_row(
            index - 1, None, self.all_text_format(bold=True)
        )  # Setting format of table heading row

        if not is_line_item_level and len(records) and has_commission_column:
            merge_format = self.workbook.add_format(
                {
                    "align": "right",
                    "valign": "vcenter",
                    "font_color": (
                        self.font_color_for_amount(commission)
                        if commission_view_type
                        != CommissionViewType.EARNED_COMMISSIONS.value
                        else DEFAULT_AMOUNT_COLOR
                    ),
                    "font_size": 9,
                }
            )
            worksheet.merge_range(
                index,
                table_column_count,
                index + table_row_count - 1,
                table_column_count,
                commission,
                merge_format,
            )
        if (
            has_commission_column
            and commission_view_type != CommissionViewType.EARNED_COMMISSIONS.value
        ):
            worksheet.conditional_format(
                index,
                table_column_count,
                index + table_row_count,
                table_column_count,
                {
                    "type": "cell",
                    "criteria": ">",
                    "value": 0,
                    "format": self.all_text_format(font_color=AMOUNT_GREEN_COLOR),
                },
            )
            worksheet.conditional_format(
                index,
                table_column_count,
                index + table_row_count,
                table_column_count,
                {
                    "type": "cell",
                    "criteria": "<",
                    "value": 0,
                    "format": self.all_text_format(font_color=AMOUNT_RED_COLOR),
                },
            )
        index += table_row_count + 2
        return (worksheet, index)

    def create_plan_details_sheet(self):
        current_month_year = self.get_period_label_for_sheet_name()
        localized_earned_comms = get_localized_message_utils(
            StatementTerms.EARNED_COMMISSIONS.name, self.client_id
        )
        localized_payout_details = get_localized_message_utils(
            StatementTerms.PAYOUT_DETAILS.name, self.client_id
        )
        worksheet_name = (
            f"{localized_earned_comms} {current_month_year}"
            if self.statements_data["has_settlement_rules_history_for_active_plans"]
            else localized_payout_details
        )
        worksheet = get_valid_worksheet(self.workbook, worksheet_name)

        localized_payout = get_localized_message_utils(
            StatementTerms.PAYOUT.name, self.client_id
        )
        localized_plan_name = get_localized_message_utils("PLAN_NAME", self.client_id)
        index = 1
        # Sort plans by display_order
        earned_commission_details = self.statements_data["earned_commission_details"]
        sorted_commission_plan_ids = self.sort_plans_by_type_and_order(
            earned_commission_details
        )
        for plan_id in sorted_commission_plan_ids:
            plan_name = earned_commission_details[plan_id]["plan_name"]
            plan_criteria = earned_commission_details[plan_id]["criteria_details"]
            worksheet.write(
                "A" + str(index),
                localized_plan_name,
                self.all_text_format(bold=True),
            )
            worksheet.write(
                "B" + str(index),
                localized_payout
                + " ("
                + str(self.payroll_details["pay_currency"])
                + ")",
                self.all_text_format(bold=True, num_format=True, font_size=10),
            )
            index += 1
            worksheet.write(
                "A" + str(index),
                plan_name,
                self.all_text_format(
                    bold=True, font_color="white", bg_color="black", font_size=10
                ),
            )
            worksheet.write(
                "B" + str(index),
                earned_commission_details[plan_id]["amount"],
                self.all_text_format(bold=True, num_format=True, font_size=10),
            )
            index = index + 2
            plan_criteria_for_user_logged = get_criteria_details_for_logged_in_user(
                self.client_id, self.login_user_id, plan_criteria
            )
            for criteria in plan_criteria_for_user_logged:
                criteria_details = plan_criteria.get(criteria, {})
                orderby_fields = get_orderby_fields(criteria_details)
                criteria_data = get_payout_details_for_criteria(
                    self.client_id,
                    self.psd,
                    self.ped,
                    self.payee_email,
                    plan_id,
                    criteria,
                    CommissionViewType.EARNED_COMMISSIONS.value,
                    self.login_user_id,
                    orderby_fields=orderby_fields,
                )
                visible_cols = get_visible_columns_for_logged_in_user(
                    self.client_id,
                    self.login_user_id,
                    plan_id,
                    criteria,
                    criteria_data["columns"],
                )
                criteria_data["columns"] = visible_cols

                (worksheet, index) = self.create_criteria_data_table(
                    worksheet,
                    index,
                    CommissionViewType.EARNED_COMMISSIONS.value,
                    plan_criteria[criteria],
                    criteria_data,
                )
            index += 1

        worksheet.set_column(0, worksheet.dim_colmax, CELL_WIDTH)

    def get_period_label_for_sheet_name(self):
        logger.info("BEGIN: Getting period label for sheet name")
        has_period_label_feature = has_feature(self.client_id, "payout_period_label")
        final_label = ""
        if has_period_label_feature:
            period_tuple = []
            period_tuple.append(
                (
                    make_aware_wrapper(first_day_of_month(self.ped)),
                    make_aware_wrapper(self.ped),
                )
            )
            final_label = get_custom_period_label(self.client_id, period_tuple)
        if not final_label:
            final_label = self.ped.strftime("%b %Y")

        logger.info(f"END: Period label for sheet name: {final_label}")
        return final_label

    def create_deferred_plan_details_sheet(self):
        current_month_year = self.get_period_label_for_sheet_name()
        localized_def_comms = get_localized_message_utils(
            StatementTerms.DEFERRED_COMMISSIONS.name, self.client_id
        )
        worksheet_name = f"{localized_def_comms} {current_month_year}"
        worksheet = get_valid_worksheet(self.workbook, worksheet_name)

        localized_payout = get_localized_message_utils(
            StatementTerms.PAYOUT.name, self.client_id
        )
        localized_plan_name = get_localized_message_utils("PLAN_NAME", self.client_id)
        index = 1
        # Sort plans by display_order
        deferred_commission_details = self.statements_data[
            "deferred_commission_details"
        ]
        sorted_deferred_commission_plan_ids = self.sort_plans_by_type_and_order(
            deferred_commission_details
        )
        for plan_id in sorted_deferred_commission_plan_ids:
            plan_name = deferred_commission_details[plan_id]["plan_name"]
            plan_criteria = deferred_commission_details[plan_id]["criteria_details"]
            worksheet.write(
                "A" + str(index),
                localized_plan_name,
                self.all_text_format(bold=True),
            )
            worksheet.write(
                "B" + str(index),
                localized_payout
                + " ("
                + str(self.payroll_details["pay_currency"])
                + ")",
                self.all_text_format(bold=True, num_format=True, font_size=10),
            )
            index += 1
            worksheet.write(
                "A" + str(index),
                plan_name,
                self.all_text_format(
                    bold=True, font_color="white", bg_color="black", font_size=10
                ),
            )
            worksheet.write(
                "B" + str(index),
                deferred_commission_details[plan_id]["amount"],
                self.all_text_format(
                    bold=True,
                    font_color=self.font_color_for_amount(
                        deferred_commission_details[plan_id]["amount"]
                    ),
                    num_format=True,
                    font_size=10,
                ),
            )
            index = index + 2
            plan_criteria_for_user_logged = get_criteria_details_for_logged_in_user(
                self.client_id, self.login_user_id, plan_criteria
            )
            for criteria in plan_criteria_for_user_logged:
                criteria_details = plan_criteria.get(criteria, {})
                orderby_fields = get_orderby_fields(criteria_details)
                criteria_data = get_payout_details_for_criteria(
                    self.client_id,
                    self.psd,
                    self.ped,
                    self.payee_email,
                    plan_id,
                    criteria,
                    CommissionViewType.DEFERRED_COMMISSIONS.value,
                    self.login_user_id,
                    orderby_fields=orderby_fields,
                )
                visible_cols = get_visible_columns_for_logged_in_user(
                    self.client_id,
                    self.login_user_id,
                    plan_id,
                    criteria,
                    criteria_data["columns"],
                )
                criteria_data["columns"] = visible_cols
                (worksheet, index) = self.create_criteria_data_table(
                    worksheet,
                    index,
                    CommissionViewType.DEFERRED_COMMISSIONS.value,
                    plan_criteria[criteria],
                    criteria_data,
                )
            index += 1

        worksheet.set_column(0, worksheet.dim_colmax, CELL_WIDTH)

    def create_previous_deferred_plan_details_sheet(self):
        localized_past_def_comm = get_localized_message_utils(
            StatementTerms.PAST_DEF_COMM_PAYOUT.name, self.client_id
        )
        localized_payout = get_localized_message_utils(
            StatementTerms.PAYOUT.name, self.client_id
        )
        localized_plan_name = get_localized_message_utils("PLAN_NAME", self.client_id)

        worksheet_name = localized_past_def_comm
        worksheet = get_valid_worksheet(self.workbook, worksheet_name)

        index = 1
        for period_key in self.statements_data["previous_commission_deferred_details"]:
            # Sort plans by display_order
            period_plans_details = self.statements_data[
                "previous_commission_deferred_details"
            ][period_key]["plan_details"]
            sorted_period_plans_ids = self.sort_plans_by_type_and_order(
                period_plans_details
            )
            comission_period = self.statements_data[
                "previous_commission_deferred_details"
            ][period_key]["period"]
            comm_period_date = self.statements_data[
                "previous_commission_deferred_details"
            ][period_key]["comm_period"]
            comm_ped = make_aware(end_of_day(parse(comm_period_date)))
            worksheet.write(
                "A" + str(index),
                comission_period,
                self.all_text_format(bold=True, bg_color="#FEF2CC", font_size=10),
            )
            index += 2
            for plan_id in sorted_period_plans_ids:
                plan_name = period_plans_details[plan_id]["plan_name"]
                plan_criteria = period_plans_details[plan_id]["criteria_details"]
                worksheet.write(
                    "A" + str(index),
                    localized_plan_name,
                    self.all_text_format(bold=True),
                )
                worksheet.write(
                    "B" + str(index),
                    localized_payout
                    + " ("
                    + str(self.payroll_details["pay_currency"])
                    + ")",
                    self.all_text_format(bold=True, num_format=True, font_size=10),
                )
                index += 1
                worksheet.write(
                    "A" + str(index),
                    plan_name,
                    self.all_text_format(
                        bold=True, font_color="white", bg_color="black", font_size=10
                    ),
                )
                worksheet.write(
                    "B" + str(index),
                    period_plans_details[plan_id]["amount"],
                    self.all_text_format(
                        bold=True,
                        font_color=self.font_color_for_amount(
                            period_plans_details[plan_id]["amount"]
                        ),
                        num_format=True,
                        font_size=10,
                    ),
                )

                index = index + 2
                plan_criteria_for_user_logged = get_criteria_details_for_logged_in_user(
                    self.client_id, self.login_user_id, plan_criteria
                )
                for criteria in plan_criteria_for_user_logged:
                    criteria_details = plan_criteria.get(criteria, {})
                    orderby_fields = get_orderby_fields(criteria_details)
                    criteria_data = get_payout_details_for_criteria(
                        self.client_id,
                        self.psd,
                        self.ped,
                        self.payee_email,
                        plan_id,
                        criteria,
                        CommissionViewType.PREV_DEFERRED_COMMISSIONS.value,
                        self.login_user_id,
                        comm_ped=comm_ped,
                        orderby_fields=orderby_fields,
                    )
                    visible_cols = get_visible_columns_for_logged_in_user(
                        self.client_id,
                        self.login_user_id,
                        plan_id,
                        criteria,
                        criteria_data["columns"],
                    )
                    criteria_data["columns"] = visible_cols

                    (worksheet, index) = self.create_criteria_data_table(
                        worksheet,
                        index,
                        CommissionViewType.PREV_DEFERRED_COMMISSIONS.value,
                        plan_criteria[criteria],
                        criteria_data,
                    )

                index += 1

        worksheet.set_column(0, worksheet.dim_colmax, CELL_WIDTH)

    def create_current_period_payout_details_sheet(self):
        localized_current_payout = get_localized_message_utils(
            StatementTerms.CURRENT_PERIOD_PAYOUT.name, self.client_id
        )
        localized_payout = get_localized_message_utils(
            StatementTerms.PAYOUT.name, self.client_id
        )
        localized_plan_name = get_localized_message_utils("PLAN_NAME", self.client_id)

        worksheet_name = localized_current_payout
        worksheet = get_valid_worksheet(self.workbook, worksheet_name)

        index = 1
        # Sort plans by display_order
        current_payout_details = self.statements_data["current_payout_details"]
        sorted_current_payout_plan_ids = self.sort_plans_by_type_and_order(
            current_payout_details
        )
        for plan_id in sorted_current_payout_plan_ids:
            plan_name = current_payout_details[plan_id]["plan_name"]
            plan_criteria = current_payout_details[plan_id]["criteria_details"]
            worksheet.write(
                "A" + str(index),
                localized_plan_name,
                self.all_text_format(bold=True),
            )
            worksheet.write(
                "B" + str(index),
                localized_payout
                + " ("
                + str(self.payroll_details["pay_currency"])
                + ")",
                self.all_text_format(bold=True, num_format=True, font_size=10),
            )
            index += 1
            worksheet.write(
                "A" + str(index),
                plan_name,
                self.all_text_format(
                    bold=True, font_color="white", bg_color="black", font_size=10
                ),
            )
            worksheet.write(
                "B" + str(index),
                current_payout_details[plan_id]["amount"],
                self.all_text_format(
                    bold=True,
                    font_color=self.font_color_for_amount(
                        current_payout_details[plan_id]["amount"]
                    ),
                    num_format=True,
                    font_size=10,
                ),
            )
            index = index + 2
            plan_criteria_for_user_logged = get_criteria_details_for_logged_in_user(
                self.client_id, self.login_user_id, plan_criteria
            )
            for criteria in plan_criteria_for_user_logged:
                criteria_details = plan_criteria.get(criteria, {})
                orderby_fields = get_orderby_fields(criteria_details)
                criteria_data = get_payout_details_for_criteria(
                    self.client_id,
                    self.psd,
                    self.ped,
                    self.payee_email,
                    plan_id,
                    criteria,
                    CommissionViewType.CURRENT_PERIOD_PAYOUT.value,
                    self.login_user_id,
                    orderby_fields=orderby_fields,
                )
                visible_cols = get_visible_columns_for_logged_in_user(
                    self.client_id,
                    self.login_user_id,
                    plan_id,
                    criteria,
                    criteria_data["columns"],
                )
                criteria_data["columns"] = visible_cols

                (worksheet, index) = self.create_criteria_data_table(
                    worksheet,
                    index,
                    CommissionViewType.CURRENT_PERIOD_PAYOUT.value,
                    plan_criteria[criteria],
                    criteria_data,
                )
            index += 1

        worksheet.set_column(0, worksheet.dim_colmax, CELL_WIDTH)

    def create_adjustments_sheet(self):
        current_month_year = self.get_period_label_for_sheet_name()
        localized_payout_perent = get_localized_message_utils(
            StatementTerms.PAYOUT_PERCENT.name, self.client_id
        )
        localized_comm_adjustments = get_localized_message_utils(
            StatementTerms.COMM_ADJUSTMENTS.name, self.client_id
        )
        localized_adjustments = get_localized_message_utils(
            StatementTerms.ADJUSTMENTS.name, self.client_id
        )
        localized_amount = get_localized_message_utils("AMOUNT", self.client_id)
        worksheet_name = f"{localized_comm_adjustments} {current_month_year}"
        worksheet = get_valid_worksheet(self.workbook, worksheet_name)
        data = self.statements_data.get("adjustments_details", [])
        index = 1
        amount_column = f'{localized_amount} ( {self.payroll_details["pay_currency"]} )'
        worksheet.write(
            "C" + str(index),
            amount_column,
            self.all_text_format(italic=True, bold=True),
        )
        worksheet.write(
            "D" + str(index),
            localized_payout_perent,
            self.all_text_format(italic=True, bold=True),
        )

        index += 1
        worksheet.write("B" + str(index), localized_adjustments, self.bold)
        worksheet.write(
            "C" + str(index),
            self.adjustment_total,
            self.all_text_format(
                bold=True,
                num_format=True,
                font_color=self.font_color_for_amount(self.adjustment_total),
            ),
        )
        if self.show_commission_percent:
            if self.payroll_details["variable_pay"] != 0:
                percentage = (
                    "="
                    + (xl_rowcol_to_cell(index - 1, 2) or "")
                    + "/"
                    + str(self.payroll_details["variable_pay"])
                )
            else:
                percentage = 0
            worksheet.write(
                "D" + str(index),
                percentage,
                self.percentage_format,
            )

        localized_reason = get_localized_message_utils("REASON", self.client_id)
        localized_reason_category = get_localized_message_utils(
            "REASON_CATEGORY", self.client_id
        )
        localized_plan = get_localized_message_utils("PLAN", self.client_id)
        localized_criteria = get_localized_message_utils("CRITERIA", self.client_id)
        localized_line_item = get_localized_message_utils("LINE_ITEM", self.client_id)

        columns = [
            localized_reason,
            localized_reason_category,
            localized_plan,
            localized_criteria,
            localized_line_item,
            amount_column,
        ]
        headers = []
        for col in columns:
            if col == amount_column:
                headers.append(
                    {
                        "header": col,
                        "format": self.all_text_format(
                            num_format=True, font_color=AMOUNT_GREEN_COLOR
                        ),
                    }
                )
            else:
                headers.append({"header": col})
        records = []

        for adj in data:
            plan_name = (
                self.plan_criteria_map[adj["plan_id"]]["plan_name"]
                if adj["plan_id"] in self.plan_criteria_map
                else None
            )
            criteria_name = (
                self.plan_criteria_map[adj["plan_id"]]["criteria"][adj["criteria_id"]][
                    "criteria_name"
                ]
                if adj["plan_id"] in self.plan_criteria_map
                and adj["criteria_id"]
                in self.plan_criteria_map[adj["plan_id"]]["criteria"]
                else None
            )
            records.append(
                [
                    adj["reason"],
                    adj["reason_category"],
                    plan_name,
                    criteria_name,
                    adj["line_item_id"],
                    adj["amount"],
                ]
            )
        index += 1
        worksheet.write("A" + str(index), "#", self.italic)

        table_row_count = len(records)
        table_column_count = len(headers)
        for i in range(table_row_count):
            worksheet.write("A" + str(index + 1 + i), i + 1)
        worksheet.add_table(
            index - 1,
            1,
            index + table_row_count,
            table_column_count,
            {"data": records, "columns": headers, "style": None},
        )
        worksheet.conditional_format(
            index,
            table_column_count,
            index + table_row_count,
            table_column_count,
            {
                "type": "cell",
                "criteria": ">",
                "value": 0,
                "format": self.all_text_format(font_color=AMOUNT_GREEN_COLOR),
            },
        )
        worksheet.conditional_format(
            index,
            table_column_count,
            index + table_row_count,
            table_column_count,
            {
                "type": "cell",
                "criteria": "<",
                "value": 0,
                "format": self.all_text_format(font_color=AMOUNT_RED_COLOR),
            },
        )
        worksheet.conditional_format(
            index,
            table_column_count,
            index + table_row_count,
            table_column_count,
            {
                "type": "cell",
                "criteria": "+=",
                "value": 0,
                "format": self.all_text_format(font_color=DEFAULT_AMOUNT_COLOR),
            },
        )
        worksheet.set_row(
            index - 1, None, self.all_text_format(bold=True)
        )  # Setting format of table heading row
        worksheet.set_column(0, worksheet.dim_colmax, CELL_WIDTH)

    def create_draws_adjustments(self):
        localized_recoverable_draw = get_localized_message_utils(
            "RECOVERABLE_DRAW", self.client_id
        )
        worksheet = self.workbook.add_worksheet(localized_recoverable_draw)
        index = 1
        localized_payout_percent = get_localized_message_utils(
            StatementTerms.PAYOUT_PERCENT.name, self.client_id
        )
        localized_amount = get_localized_message_utils("AMOUNT", self.client_id)
        localized_draw_availed = get_localized_message_utils(
            "DRAW_AVAILED", self.client_id
        )
        localized_draw_recovered = get_localized_message_utils(
            "DRAW_RECOVERED", self.client_id
        )
        localized_recoverable_draw = get_localized_message_utils(
            "RECOVERABLE_DRAW", self.client_id
        )
        amount_column = f'{localized_amount} ( {self.payroll_details["pay_currency"]} )'
        worksheet.write("C" + str(index), amount_column, self.bold_italic)
        worksheet.write("D" + str(index), localized_payout_percent, self.bold_italic)

        index += 1
        worksheet.write("B" + str(index), localized_recoverable_draw, self.bold_italic)
        worksheet.write(
            "C" + str(index),
            self.statements_data["draws"],
            self.all_text_format(num_format=True),
        )
        if self.show_commission_percent:
            if self.payroll_details["variable_pay"] != 0:
                percentage = (
                    "="
                    + (xl_rowcol_to_cell(index - 1, 2) or "")
                    + "/"
                    + str(self.payroll_details["variable_pay"])
                )
            else:
                percentage = 0
            worksheet.write(
                "D" + str(index),
                percentage,
                self.bold_percentage_format,
            )

        index += 2
        worksheet.write("B" + str(index), localized_draw_availed, self.bold_italic)
        worksheet.write("C" + str(index), localized_draw_recovered, self.bold_italic)
        index += 1
        worksheet.write(
            "B" + str(index),
            self.statements_data["draws_details"][0]["draw_availed"],
            self.all_text_format(num_format=True, font_color=AMOUNT_GREEN_COLOR),
        )
        worksheet.write(
            "C" + str(index),
            Decimal(self.statements_data["draws_details"][0]["draw_recovered"]) * -1,
            self.all_text_format(num_format=True, font_color=AMOUNT_RED_COLOR),
        )
        worksheet.set_column(0, worksheet.dim_colmax, CELL_WIDTH)

    def export_statements_data(self):
        if self.client_id == 85:
            write_axogen_custom_report_to_excel(
                self.workbook, self.psd, self.ped, self.payee_email
            )
            self.workbook.close()
            self.output.seek(0)
            return self.output

        self.create_payout_summary_sheet()
        show_settlement_view = self.statements_data.get(
            "has_settlement_rules_history_for_active_plans", False
        )

        datasheet_variable_query = DatasheetVariableAccessor(
            self.client_id
        ).get_variables_for_ds_ids(list(self.datasheet_ids), as_dicts=True)
        for record in datasheet_variable_query:
            datasheet_id = str(record["datasheet_id"])
            if datasheet_id not in self.datasheet_variable_map:
                self.datasheet_variable_map[datasheet_id] = {}
            self.datasheet_variable_map[datasheet_id][record["system_name"]] = {
                "display_name": record["display_name"],
                "data_type": record["data_type_id"],
            }

        if show_settlement_view:
            if len(self.statements_data.get("current_payout_details", {}).keys()) != 0:
                self.create_current_period_payout_details_sheet()
            if (
                len(
                    self.statements_data.get(
                        "previous_commission_deferred_details", {}
                    ).keys()
                )
                != 0
            ):
                self.create_previous_deferred_plan_details_sheet()

        if len(self.statements_data.get("adjustments_details", [])) != 0:
            self.create_adjustments_sheet()

        if len(self.statements_data.get("draws_details", [])) != 0:
            self.create_draws_adjustments()

        if (
            show_settlement_view
            and len(self.statements_data.get("deferred_commission_details", {}).keys())
            != 0
        ):
            self.create_deferred_plan_details_sheet()
        if len(self.statements_data.get("earned_commission_details", {}).keys()) != 0:
            self.create_plan_details_sheet()
        self.workbook.close()
        # Rewind the buffer.
        self.output.seek(0)
        return self.output


class ExportStatementsPdf:
    """
    Class to Export Statements data as CSV.
    """

    def __init__(
        self, client_id, payee_email, psd, ped, login_user_id, is_settlement_view=False
    ):
        self.client_id = client_id
        self.payee_email = payee_email
        self.psd = psd
        self.ped = ped
        self.is_settlement_view = is_settlement_view
        self.payee_details = {}
        self.payout_details = {}
        self.payroll_details = {}
        self.selected_user_fields = {}
        self.client = get_client(client_id)
        self.show_commission_percent = False
        if (
            "show_commission_percent" in self.client.client_features
            and self.client.client_features["show_commission_percent"]
        ):
            self.show_commission_percent = True
        self.use_aggrid_for_pdf_export = self.client.client_features.get(
            "use_aggrid_for_pdf_export", False
        )
        self.show_payout_table_breakdown = False
        if (
            "show_payout_table_breakdown" in self.client.client_features
            and self.client.client_features["show_payout_table_breakdown"]
        ):
            self.show_payout_table_breakdown = True

        self.plan_criteria_map = {}
        self.datasheet_ids = set()
        self.datasheet_variable_map = {}
        self.adjustment_total = Decimal(0)
        self.login_user_id = login_user_id
        self.payout_summary = {}
        self.breakdown_summary = {}
        self.commission_summary = {}
        self.quota_attainment = {}
        self.current_pd_payout_details = []
        self.current_pd_criteria_details = {}
        self.previous_deferred_comm_pd_payout_details = []
        self.previous_deferred_comm_pdf_criteria_details = {}
        self.earned_commission_details = []
        self.earned_commission_criteria_details = {}
        self.deferred_commission_details = []
        self.deferred_commission_criteria_details = {}
        self.custom_fields = {}
        self.table_headers = {}
        self.json_data = ""
        self.adjustments = {}
        self.criteria_qe_map = {}
        self.localized_terms = {}

    def format_amount(self, amount, symbol):
        if amount < 0:
            return f"-{symbol}{str(format(abs(amount), ',.2f'))}"
        else:
            return f"{symbol}{str(format(amount, ',.2f'))}"

    def create_criteria_data_table(self, criteria_details, criteria_data):
        is_line_item_level = criteria_data["is_line_item_level"]
        localized_comm_summation = get_localized_message_utils(
            StatementTerms.COMM_SUMMATION.name, self.client_id
        )
        localized_comm = get_localized_message_utils(
            StatementTerms.COMMISSION.name, self.client_id
        )
        localized_quota_erosion = get_localized_message_utils(
            StatementTerms.QUOTA_EROSION.name, self.client_id
        )

        headers = []
        data_headers = []
        count = 1
        header_field_map = {}
        for column in criteria_data["columns"]:
            header = column
            set_date_format = False
            set_number_format = False
            set_percent_format = False
            if column in self.datasheet_variable_map[criteria_details["datasheet_id"]]:
                field_info = self.datasheet_variable_map[
                    criteria_details["datasheet_id"]
                ][column]
                header = field_info["display_name"]
                data_type = field_info["data_type"]

                header_field_map[str(count)] = column

                set_date_format = data_type == 2
                set_number_format = data_type == 1
                set_percent_format = data_type == 6

            elif column == "commission":
                header_field_map[str(count)] = column
                if is_line_item_level:
                    header = (
                        localized_comm
                        + " ("
                        + str(self.payroll_details["pay_currency"])
                        + ")"
                    )
                else:
                    header = (
                        localized_comm_summation
                        + " ("
                        + str(self.payroll_details["pay_currency"])
                        + ")"
                    )
                set_number_format = True
            elif column == "quotaErosion":
                header_field_map[str(count)] = "quota_erosion"
                header = localized_quota_erosion
                set_number_format = True
            else:
                header_field_map[str(count)] = column
            data_headers.append(column)
            if set_date_format:
                headers.append(
                    {
                        "headerName": header,
                        "type": "date",
                        "field": str(count),
                        "actualHeader": column,
                    }
                )
            elif set_number_format:
                headers.append(
                    {
                        "headerName": header,
                        "type": "number",
                        "field": str(count),
                        "actualHeader": column,
                    }
                )
            elif set_percent_format:
                headers.append(
                    {
                        "headerName": header,
                        "type": "percent",
                        "field": str(count),
                        "actualHeader": column,
                    }
                )
            else:
                headers.append(
                    {
                        "headerName": header,
                        "type": "text",
                        "field": str(count),
                        "actualHeader": column,
                    }
                )
            count += 1
        records = []
        line_item_count = 0
        for record in criteria_data["records"]:
            row = {}
            for key, value in header_field_map.items():
                if value in record:
                    if isinstance(record[value], bool):
                        record[value] = str(record[value])
                    if record[value] or record[value] == 0:
                        # For commission column values, append currency symbol before the value
                        if value == "commission":
                            # If the commisisons are not calculated at row level, then only first row will have commission value
                            if not is_line_item_level:
                                # For rows other than first row, print empty string
                                if line_item_count != 0:
                                    row[key] = ""
                                # For first row, print the commission value with currency symbol
                                else:
                                    row[key] = self.format_amount(
                                        record[value],
                                        self.payroll_details["currency_format"],
                                    )
                            # Print the currency symbol along with commissions for all rows
                            else:
                                row[key] = self.format_amount(
                                    record[value],
                                    self.payroll_details["currency_format"],
                                )
                        else:
                            if (
                                isinstance(record[value], float)
                                or isinstance(record[value], int)
                                or isinstance(record[value], Decimal)
                            ):
                                # Values from backend will be in decimal format. If the value isn't a whole number, then round it off
                                if math.floor(record[value]) != math.ceil(
                                    record[value]
                                ):
                                    row[key] = round(record[value], 2)
                                else:
                                    row[key] = record[value]
                                # Add comma separator for values
                                row[key] = f"{row[key]:,}"
                            else:
                                row[key] = record[value]
                    else:
                        row[key] = "-"
                    if isinstance(record[value], Decimal) and record[value] < 0:
                        headers[int(key) - 1]["cellStyle"] = {
                            "color": DEERRED_AMOUNT_COLOR
                        }
            records.append(row)
            line_item_count += 1
        if len(records) == 0:
            records.append({str(i): "-" for i in range(1, count)})

        return (headers, records)

    def add_selected_user_fields(self):
        custom_fields_data = CustomFieldDataAccessor(
            self.client_id
        ).get_custom_field_data_based_on_sec_kd(self.ped, self.payee_email)
        custom_fields_data = custom_fields_data.data if custom_fields_data else {}
        custom_fields = CustomFieldsAccessor(self.client_id).get_all_custom_fields()
        custom_fields_type_map = {}
        custom_fields_name_map = {}
        custom_fields_options_map = {}
        for custom_field in custom_fields:
            custom_fields_type_map[custom_field["system_name"]] = custom_field[
                "field_type"
            ]
            custom_fields_name_map[custom_field["system_name"]] = custom_field[
                "display_name"
            ]
            custom_fields_options_map[custom_field["system_name"]] = {
                item["system_name"]: item["display_name"]
                for item in custom_field["options"]
            }
        for selected_field in self.selected_user_fields:
            # if the field is a custom field
            if selected_field.startswith("cf_"):
                # if the type of custom_field is checkbox then set the value accordingly
                if custom_fields_type_map[selected_field] == "Checkbox":
                    self.custom_fields[custom_fields_name_map[selected_field]] = (
                        "True"
                        if custom_fields_data.get(selected_field, None)
                        else "False"
                    )
                elif custom_fields_type_map[selected_field] == "Dropdown":
                    self.custom_fields[custom_fields_name_map[selected_field]] = (
                        custom_fields_options_map[selected_field].get(
                            custom_fields_data.get(selected_field, None), None
                        )
                    )
                else:
                    self.custom_fields[custom_fields_name_map[selected_field]] = (
                        custom_fields_data.get(selected_field, None)
                    )
            else:
                localized_terminology = get_localized_message_utils(
                    PROFILE_CARD_DEFAULT_FIELDS_MAP[selected_field], self.client_id
                )
                if selected_field in ["variable_pay", "fixed_pay"]:
                    self.custom_fields[localized_terminology] = self.payroll_details[
                        "currency_format"
                    ] + str(self.payroll_details[selected_field])
                elif selected_field == "bcr":
                    self.add_bcr()
                else:
                    self.custom_fields[localized_terminology] = self.payroll_details[
                        selected_field
                    ]

    def add_teams_details(self):
        ids = MembershipAccessor(self.client_id).get_all_teams_of_whole_team(
            self.ped, [self.payee_email]
        )

        team_ids = []
        for team_id in ids:
            team_ids.append(team_id["team_id"])
        teams = TeamAccessor(self.client_id).get_all_teams_of_type_team_id(
            team_ids, "team", as_dicts=False
        )
        team_name_list = []
        for team in teams:
            team_name_list.append(team.team_name)
        return team_name_list

    def add_pods_details(self):
        team = TeamAccessor(self.client_id).get_team_id_of_pod_owner(self.payee_email)
        if team:
            member_list = MembershipAccessor(
                self.client_id
            ).get_team_members_ed_in_team(team.team_id, self.ped, False)
        else:
            member_list = []
        pod_member_ids_list = []
        for pod_data in member_list:
            pod_member_ids_list.append(pod_data.group_member_email_id)
        member_name_list = EmployeeAccessor(self.client_id).get_employees_name(
            pod_member_ids_list
        )
        pod_member_names = []
        for member in member_name_list:
            pod_member_names.append(f"{member['first_name']} {member['last_name']}")

        return pod_member_names

    def add_customised_statement_fields(self):
        config_record = ClientConfigAccessor(self.client_id).get_profile_fields()
        configs = {}
        localized_team_name = get_localized_message_utils("TEAM_NAME", self.client_id)
        localized_pod_name = get_localized_message_utils("POD_NAME", self.client_id)
        if config_record:
            configs = config_record.value
        else:
            (
                configs["options"],
                configs["selected_fields"],
            ) = get_default_options_and_fields()
        self.selected_user_fields = configs["selected_fields"]
        if configs["options"].get("user_fields", False):
            self.add_selected_user_fields()
        if configs["options"].get("teams", False):
            team_name = self.add_teams_details()
            if len(team_name) > 0:
                self.custom_fields[localized_team_name] = ",".join(team_name)

        if configs["options"].get("pod_details", False):
            pod_name = self.add_pods_details()
            if len(pod_name) > 0:
                self.custom_fields[localized_pod_name] = ",".join(pod_name)

    def add_localized_terms(self):
        preferred_language = self.client.client_features.get("preferred_language", "")
        if preferred_language:
            localized_last_calculated = StatementTerms.LAST_CALCULATED.value
            localized_downloaded = StatementTerms.DOWNLOADED.value
            localized_period = StatementTerms.PERIOD.value
            localized_from_current_period = StatementTerms.FROM_CURRENT_PERIOD.value
        else:
            localized_last_calculated = get_localized_message_utils(
                StatementTerms.LAST_CALCULATED.name, self.client_id
            )
            localized_downloaded = get_localized_message_utils(
                StatementTerms.DOWNLOADED.name, self.client_id
            )
            localized_period = get_localized_message_utils(
                StatementTerms.PERIOD.name, self.client_id
            )
            localized_from_current_period = get_localized_message_utils(
                StatementTerms.FROM_CURRENT_PERIOD.name, self.client_id
            )

        self.localized_terms = {
            "last_calculated": localized_last_calculated,
            "downloaded": localized_downloaded,
            "period": localized_period,
            "from_current_period": localized_from_current_period,
        }

    def add_comm_adj_in_payout_summary(self, data: dict) -> None:
        """
        Calculates the %Payout of Draw Adjustment with variable pay and adds it to the Payout Summary table

        Both settlement and non-settlement view need Commission Adjustment in payouts summary table
        We don't show adjustments when it's zero
        """

        if data.get("adjustments_details"):
            localized_payout = get_localized_message_utils(
                StatementTerms.PAYOUT.name, self.client_id
            )
            localized_adjustments = get_localized_message_utils(
                StatementTerms.ADJUSTMENTS.name, self.client_id
            )
            localized_commission = get_localized_message_utils(
                StatementTerms.COMMISSION.name, self.client_id
            )
            for item in data["adjustments_details"]:
                self.adjustment_total += Decimal(item["amount"])
            adjustments_details = {}
            adjustments_details["type"] = (
                localized_payout
                + " from "
                + localized_commission
                + " "
                + localized_adjustments
            )
            adjustments_details["commission"] = format(
                float(self.adjustment_total), ",.2f"
            )
            if self.show_commission_percent:
                if self.payroll_details["variable_pay"] != 0:
                    percentage = (
                        float(self.adjustment_total)
                        / self.payroll_details["variable_pay"]
                    ) * 100
                else:
                    percentage = 0
                adjustments_details["payout"] = format(percentage, ".2f") + "%"
            self.payout_summary["rows"].append(adjustments_details)

    def add_draw_adj_to_payout_summary(self, data: dict) -> None:
        """
        Calculates the %Payout of Draw Adjustment with variable pay and adds it to the Payout Summary table

        Both settlement and non-settlement view need Draw Adjustment in payouts summary table
        We don't show draws when it's zero
        """
        if data.get("draws"):
            localized_payout = get_localized_message_utils(
                StatementTerms.PAYOUT.name, self.client_id
            )
            localized_adjustments = get_localized_message_utils(
                StatementTerms.ADJUSTMENTS.name, self.client_id
            )
            recoverable_draw = {}
            recoverable_draw["type"] = (
                localized_payout + " from Draw " + localized_adjustments
            )
            recoverable_draw["commission"] = format(data["draws"], ",.2f")
            if self.show_commission_percent:
                if self.payroll_details["variable_pay"] != 0:
                    percentage = (
                        data["draws"] / self.payroll_details["variable_pay"] * 100
                    )
                else:
                    percentage = 0
                recoverable_draw["payout"] = format(percentage, ".2f") + "%"
            self.payout_summary["rows"].append(recoverable_draw)

    def add_bcr(self):
        bcr_list = get_bcr_metrics_for_payee_period(
            self.client_id, self.payee_email, self.psd, self.ped
        )

        for bcr in bcr_list:
            for key, val in bcr.items():
                self.custom_fields[key] = f"{val}"

    def create_payout_summary_sheet(self, data):
        logger.info("BEGIN: Creating Payout Summary Sheet")
        localized_payout_summary = get_localized_message_utils(
            StatementTerms.PAYOUT_SUMMARY.name, self.client_id
        )
        localized_payout_percent = get_localized_message_utils(
            StatementTerms.PAYOUT_PERCENT.name, self.client_id
        )
        localized_current_period_payout = get_localized_message_utils(
            StatementTerms.CURRENT_PERIOD_PAYOUT.name, self.client_id
        )
        localized_prev_def_payout = get_localized_message_utils(
            StatementTerms.PREV_DEF_COMM_PAYOUT_lc.name, self.client_id
        )
        localized_payout = get_localized_message_utils(
            StatementTerms.PAYOUT.name, self.client_id
        )
        localized_payout_arrears = get_localized_message_utils(
            StatementTerms.PAYOUT_ARREARS.name, self.client_id
        )
        localized_total_payout = get_localized_message_utils(
            StatementTerms.TOTAL_PAYOUT.name, self.client_id
        )
        localized_commission = get_localized_message_utils(
            StatementTerms.COMMISSION.name, self.client_id
        )
        localized_comm_summary = get_localized_message_utils(
            StatementTerms.COMMISSION_SUMMARY.name, self.client_id
        )
        localized_quota_attainment = get_localized_message_utils(
            StatementTerms.QUOTA_ATTAINMENT.name, self.client_id
        )
        localized_quota_period = get_localized_message_utils(
            StatementTerms.QUOTA_PERIOD.name, self.client_id
        )
        localized_quota_att_percent = get_localized_message_utils(
            StatementTerms.QUOTA_ATTAINMENT_PERCENT.name, self.client_id
        )
        localized_quota = get_localized_message_utils(
            StatementTerms.QUOTA.name, self.client_id
        )
        localized_earned_comms = get_localized_message_utils(
            StatementTerms.EARNED_COMMISSIONS.name, self.client_id
        )
        localized_deferred_comms = get_localized_message_utils(
            StatementTerms.DEFERRED_COMMISSIONS.name, self.client_id
        )
        localized_prev_deferred_comms = get_localized_message_utils(
            StatementTerms.PREV_DEF_COMM_PAYOUT_PERIOD_lc.name, self.client_id
        )
        localized_commissions = get_localized_message_utils(
            StatementTerms.COMMISSIONS.name, self.client_id
        )
        localized_detailed_payout_insights = get_localized_message_utils(
            StatementTerms.DETAILED_PAYOUT_INSIGHTS.name, self.client_id
        )
        localized_payout_by_plan_and_criteria = get_localized_message_utils(
            StatementTerms.PAYOUT_BY_PLAN_AND_CRITERIA.name, self.client_id
        )
        localized_detailed_commission_insights = get_localized_message_utils(
            StatementTerms.DETAILED_COMMISSION_INSIGHTS.name, self.client_id
        )
        localized_commissions_by_plan_and_criteria = get_localized_message_utils(
            StatementTerms.COMMISSION_BY_PLAN_AND_CRITERIA.name, self.client_id
        )
        localized_adjustments = get_localized_message_utils(
            StatementTerms.ADJUSTMENTS.name, self.client_id
        )
        localized_attained = get_localized_message_utils(
            StatementTerms.ATTAINED.name, self.client_id
        )
        localized_manager_individual = get_localized_message_utils(
            StatementTerms.MANAGER_OR_INDIVIDUAL.name, self.client_id
        )
        localized_statements_header = get_localized_message_utils(
            StatementTranslationTerms.STATEMENT_FOR.name, self.client_id
        )
        overview_obj = CommissionsOverview(
            client_id=self.client_id,
            psd=self.psd,
            ped=self.ped,
            payee_email=self.payee_email,
        )
        current_time = timezone.now().strftime("%d %b %Y, %I:%M%p")
        is_payroll = is_view_payroll_permission(
            self.client_id, self.login_user_id
        ) and has_view_payroll_data_permission(
            self.client_id, self.login_user_id, self.payee_email
        )

        preferred_language = self.client.client_features.get("preferred_language", "")
        if preferred_language:
            message_to_be_translated = {}
            for message in StatementTranslationTerms:
                message_to_be_translated[message.name] = message.value

            translated_messages = get_translated_message_for_statements(
                message_to_be_translated,
                self.client_id,
                preferred_language,
            )

            self.table_headers["payout_summary"] = translated_messages[
                StatementTranslationTerms.PAYOUT_SUMMARY.value
            ]
            self.table_headers["payout_by_plan_and_criteria"] = translated_messages[
                StatementTranslationTerms.PAYOUT_BY_PLAN_AND_CRITERIA.value
            ]
            self.table_headers["commission_by_plan_and_criteria"] = translated_messages[
                StatementTranslationTerms.COMMISSION_BY_PLAN_AND_CRITERIA.value
            ]
            self.table_headers["total_payout"] = translated_messages[
                StatementTranslationTerms.TOTAL_PAYOUT.value
            ]
            self.table_headers["detailed_payout_insights"] = translated_messages[
                StatementTranslationTerms.DETAILED_PAYOUT_INSIGHTS_BY_PLAN_AND_CRITERIA.value
            ]
            self.table_headers["detailed_commission_insights"] = translated_messages[
                StatementTranslationTerms.DETAILED_COMMISSION_INSIGHTS_BY_PLAN_AND_CRITERIA.value
            ]

            self.table_headers["statement_for"] = translated_messages[
                StatementTranslationTerms.STATEMENT_FOR.value
            ]
            localized_total_payout = translated_messages[
                StatementTranslationTerms.TOTAL_PAYOUT.value
            ]

        else:
            self.table_headers["payout_summary"] = localized_payout_summary
            self.table_headers["payout_by_plan_and_criteria"] = (
                localized_payout_by_plan_and_criteria
            )
            self.table_headers["commission_by_plan_and_criteria"] = (
                localized_commissions_by_plan_and_criteria
            )
            self.table_headers["total_payout"] = localized_total_payout
            self.table_headers["detailed_payout_insights"] = (
                localized_detailed_payout_insights
            )
            self.table_headers["detailed_commission_insights"] = (
                localized_detailed_commission_insights
            )
            self.table_headers["statement_for"] = localized_statements_header

        self.table_headers["commission_summary"] = localized_comm_summary
        self.table_headers["quota_attainment"] = localized_quota_attainment
        self.table_headers["payout_from_deferred_period"] = (
            localized_prev_deferred_comms
        )
        self.table_headers["commissions_by_plan_and_criteria"] = localized_commissions
        self.table_headers["earned_commission"] = localized_earned_comms
        self.table_headers["deferred_commission"] = localized_deferred_comms
        self.table_headers["payout"] = localized_payout
        self.table_headers["commission"] = localized_commission
        self.table_headers["commission_adjustments"] = (
            localized_payout
            + " from "
            + localized_commission
            + " "
            + localized_adjustments
        )
        self.table_headers["draw_adjustments"] = (
            localized_payout + " from Draw " + localized_adjustments
        )

        localized_name = get_localized_message_utils("NAME", self.client_id)
        localized_email = get_localized_message_utils("EMAIL", self.client_id)
        localized_designation = get_localized_message_utils(
            "DESIGNATION", self.client_id
        )
        localized_period_start_date = get_localized_message_utils(
            "PERIOD_START_DATE", self.client_id
        )
        localized_period_end_date = get_localized_message_utils(
            "PERIOD_END_DATE", self.client_id
        )
        localized_last_calculated = get_localized_message_utils(
            "LAST_CALCULATED", self.client_id
        )
        localized_downloaded = get_localized_message_utils("DOWNLOADED", self.client_id)
        localized_status = get_localized_message_utils("STATUS", self.client_id)

        self.payee_details = {
            localized_name: data["payee_name"],
            localized_email: self.payee_email,
            localized_designation: data["designation"],
            localized_period_start_date: self.psd.strftime("%d-%b-%Y"),
            localized_period_end_date: self.ped.strftime("%d-%b-%Y"),
            localized_last_calculated: "",
            localized_downloaded: current_time,
            localized_status: "",
        }
        self.payout_details = {
            "payout_status": data.get("approval_status"),
            "payout_request_date": (
                data["approval_status_requested_time"].strftime("%b %d, %Y")
                if data.get("approval_status_requested_time")
                else None
            ),
            "payout_completion_date": (
                data["approval_status_completion_time"].strftime("%b %d, %Y")
                if data.get("approval_status_completion_time")
                else None
            ),
        }
        self.payroll_details = {
            "pay_currency": (
                data["payee_currency"] if data.get("payee_currency") else None
            ),
            "payout_frequency": (
                data["payout_frequency"] if data.get("payout_frequency") else None
            ),
            "variable_pay": (
                float(data["variable_pay_as_per_period"]) if is_payroll else 0
            ),
            "reporting_manager": (
                data["reporting_manager"] if data.get("reporting_manager") else None
            ),
            "employment_country": (
                data["employment_country"] if data.get("employment_country") else None
            ),
            "fixed_pay": (
                format(data.get("fixed_pay") if data.get("fixed_pay") else 0, ",.2f")
                if is_payroll
                else 0
            ),
            "employee_id": data["employee_id"] if data.get("employee_id") else None,
            "total_payout": format(data.get("total_payout", 0), ",.2f"),
        }
        self.payroll_details["currency_format"] = data.get("payee_currency_symbol")
        self.add_customised_statement_fields()

        self.add_localized_terms()

        localized_last_calculated = get_localized_message_utils(
            "LAST_CALCULATED", self.client_id
        )
        localized_status = get_localized_message_utils("STATUS", self.client_id)

        self.payee_details[localized_last_calculated] = (
            data["lastUpdatedTime"].strftime("%d %b %Y, %I:%M%p")
            if data["lastUpdatedTime"]
            else None
        )
        self.payee_details[localized_status] = data["comm_calc_status"]

        # add payout details
        commissions_list = [
            "earned_commission_details",
            "deferred_commission_details",
            "previous_commission_deferred_details",
        ]

        for key in commissions_list:
            if key != "previous_commission_deferred_details":
                for item in list(data[key].values()):
                    if (
                        key == "deferred_commission_details"
                        and not data["deferred_commission"]
                    ):
                        continue
                    if key == "earned_commission_details":
                        self.plan_criteria_map[item["plan_id"]] = {
                            "plan_name": item["plan_name"],
                            "criteria": item["criteria_details"],
                        }
                    self.datasheet_ids = self.datasheet_ids.union(
                        {
                            str(item["datasheet_id"])
                            for item in list(item["criteria_details"].values())
                        }
                    )
            else:
                for plan in list(data[key].values()):
                    for item in list(plan["plan_details"].values()):
                        self.datasheet_ids = self.datasheet_ids.union(
                            {
                                str(item["datasheet_id"])
                                for item in list(item["criteria_details"].values())
                            }
                        )

        self.payout_summary["table"] = localized_payout_summary
        self.payout_summary["headers"] = []

        if self.is_settlement_view:
            type_localized = get_localized_message_utils("TYPE", self.client_id)
            self.payout_summary["headers"].append(type_localized)
            self.payout_summary["headers"].append(
                localized_commission
                + " ( "
                + str(self.payroll_details["pay_currency"])
                + " )"
            )
            self.payout_summary["headers"].append(localized_payout_percent)

            self.payout_summary["rows"] = []
            if data.get("current_payout_details"):
                current_payout_details = {}
                current_payout_details["type"] = localized_current_period_payout
                current_payout_details["commission"] = format(
                    data["current_payout"], ",.2f"
                )
                if self.show_commission_percent:
                    if self.payroll_details["variable_pay"] != 0:
                        percentage = (
                            data["current_payout"]
                            / self.payroll_details["variable_pay"]
                            * 100
                        )
                    else:
                        percentage = 0
                    current_payout_details["payout"] = format(percentage, ".2f") + "%"
                self.payout_summary["rows"].append(current_payout_details)

            if data.get("previous_commission_deferred"):
                previous_commission_deferred = {}
                previous_commission_deferred["type"] = (
                    localized_payout + " " + localized_prev_def_payout
                )
                previous_commission_deferred["commission"] = format(
                    data["previous_commission_deferred"], ",.2f"
                )
                if self.show_commission_percent:
                    if self.payroll_details["variable_pay"] != 0:
                        percentage = (
                            data["previous_commission_deferred"]
                            / self.payroll_details["variable_pay"]
                        ) * 100
                    else:
                        percentage = 0
                    previous_commission_deferred["payout"] = (
                        format(percentage, ".2f") + "%"
                    )
                self.payout_summary["rows"].append(previous_commission_deferred)

            if data.get("payout_arrears"):
                payout_arrears = {}
                payout_arrears["type"] = localized_payout_arrears
                payout_arrears["commission"] = format(data["payout_arrears"], ",.2f")

                if self.show_commission_percent:
                    if self.payroll_details["variable_pay"] != 0:
                        percentage = (
                            data["payout_arrears"]
                            / self.payroll_details["variable_pay"]
                        ) * 100
                    else:
                        percentage = 0
                    payout_arrears["payout"] = format(percentage, ".2f") + "%"
                self.payout_summary["rows"].append(payout_arrears)

            # We add the adjustments details to the payout summary table
            self.add_comm_adj_in_payout_summary(data)
            self.add_draw_adj_to_payout_summary(data)

            total_payout = {}
            total_payout["type"] = localized_total_payout
            total_payout["commission"] = format(data["total_payout"], ",.2f")
            if self.show_commission_percent:
                if self.payroll_details["variable_pay"] != 0:
                    percentage = (
                        data["total_payout"]
                        / self.payroll_details["variable_pay"]
                        * 100
                    )
                else:
                    percentage = 0
                total_payout["payout"] = format(percentage, ".2f") + "%"
            self.payout_summary["rows"].append(total_payout)

            self.commission_summary["headers"] = []
            self.commission_summary["headers"].append(localized_payout_summary)
            self.commission_summary["headers"].append(
                localized_commission
                + " ( "
                + str(self.payroll_details["pay_currency"])
                + " )"
            )
            self.commission_summary["headers"].append(localized_payout_percent)

            self.commission_summary["rows"] = []
            self.commission_summary["table"] = localized_comm_summary
            self.commission_summary["rows"].append(
                {
                    "type": localized_earned_comms,
                    "commission": format(data["earned_commission"], ",.2f"),
                }
            )
            self.commission_summary["rows"].append(
                {
                    "type": localized_deferred_comms,
                    "commission": format(data["deferred_commission"], ",.2f"),
                }
            )
        else:
            # For non settlement view payout summary will be the plan details
            type_localized = get_localized_message_utils("TYPE", self.client_id)
            self.payout_summary["headers"].append(type_localized)
            self.payout_summary["headers"].append(
                localized_commission
                + " ( "
                + str(self.payroll_details["pay_currency"])
                + " )"
            )
            variable_pay = float(data["variable_pay"])
            self.payout_summary["headers"].append(localized_payout_percent)
            self.payout_summary["rows"] = []

            # We enter the plan-wise commissions to the payout summary table
            if data.get("current_payout_details"):
                main_payouts = []
                spiff_payouts = []

                # Iterate through current_payout_details and categorize payouts
                for value in data["current_payout_details"].values():
                    if value.get("plan_type") == "MAIN":
                        main_payouts.append(value)
                    elif value.get("plan_type") == "SPIFF":
                        spiff_payouts.append(value)

                # Sort both lists separately based on plan_display_order
                main_payouts = sorted(
                    main_payouts,
                    key=lambda x: (
                        x.get("plan_display_order")
                        if x.get("plan_display_order")
                        else sys.maxsize
                    ),
                )
                spiff_payouts = sorted(
                    spiff_payouts,
                    key=lambda x: (
                        x.get("plan_display_order")
                        if x.get("plan_display_order")
                        else sys.maxsize
                    ),
                )

                # Concatenate both lists
                sorted_payouts = main_payouts + spiff_payouts
                for value in sorted_payouts:
                    temp = {}
                    temp["type"] = value["plan_name"]
                    temp["commission"] = format(value["amount"], ",.2f")
                    payout_percent = 0
                    if variable_pay > 0.0:
                        payout_percent = value["amount"] * 100 / variable_pay
                    temp["payout"] = format(payout_percent, ",.2f") + "%"
                    self.payout_summary["rows"].append(temp)

            # We add the adjustments details to the payout summary table
            self.add_comm_adj_in_payout_summary(data)
            self.add_draw_adj_to_payout_summary(data)

            # We enter the total payout(commissions + adjustments) details under the payout_summary
            total_payout = {}
            total_payout["type"] = localized_total_payout
            total_payout["commission"] = format(data["total_payout"], ",.2f")
            total_payout["payout"] = (
                format(data["total_payout"] * 100 / variable_pay, ",.2f") + "%"
                if variable_pay > 0.0
                else "0.00%"
            )
            self.payout_summary["rows"].append(total_payout)

        # add quota details
        quota_details = overview_obj.get_quota_attainment_details()
        criteria_qe_data = quota_details.get("criteria_quota_erosion_map", {})
        quota_data = quota_details.get("quota_data", [])
        has_reportees = quota_details.get("has_reportees", False)
        self.criteria_qe_map = {
            item["criteria_id"]: (
                item["currency_symbol"] + str(round(item["quota_erosion"], 2))
                if item["quota_erosion"]
                else 0
            )
            for item in criteria_qe_data
        }
        quota_columns = [
            localized_quota,
            localized_manager_individual,
            localized_quota_period,
            localized_attained,
            localized_quota_att_percent,
        ]

        self.quota_attainment["table"] = localized_quota_attainment
        self.quota_attainment["headers"] = quota_columns
        self.quota_attainment["rows"] = []

        user_permissions = get_ui_permissions(self.client_id, self.login_user_id)
        is_authorized = authorize_for_profile_lookup_v2(
            self.client_id,
            self.login_user_id,
            self.payee_email,
            RBACComponent.QUOTAS_DRAWS.value,
        )
        show_quota_details = (
            is_authorized and RbacPermissions.VIEW_QUOTAS.value in user_permissions
        )

        quota_category_names = [item["quota_category_name"] for item in quota_data]
        qs = QuotaAccessor(self.client_id).get_quota_category_display_name_map(
            quota_category_names
        )
        quota_display_names = {
            record["quota_category_name"]: record["display_name"] for record in qs
        }
        if "Primary" in quota_category_names:
            quota_display_names["Primary"] = get_localized_message_utils(
                "PRIMARY_QUOTA", self.client_id
            )

        if quota_data and show_quota_details:
            for item in quota_data:
                quota_object = {}
                quota_type_localized = item["quota_schedule_localized"] or "NA"
                quota_object["name"] = quota_display_names.get(
                    item["quota_category_name"]
                )
                quota_object["user_role"] = (
                    get_localized_message_utils("AS_INDIVIDUAL", self.client_id)
                    if not has_reportees
                    else (
                        get_localized_message_utils("AS_MANAGER", self.client_id)
                        if item["is_team"]
                        else get_localized_message_utils(
                            "AS_INDIVIDUAL", self.client_id
                        )
                    )
                )
                quota_object["quota"] = format(item["quota_value"], ",.2f")
                quota_object["quota_period"] = quota_type_localized
                quota_object["attained"] = format(item["attained_quota"], ",.2f")
                if item["quota_value"]:
                    percentage = (item["attained_quota"] / item["quota_value"]) * 100
                else:
                    percentage = 0
                quota_object["quota_attainment"] = format(percentage, ".2f") + "%"
                self.quota_attainment["rows"].append(quota_object)
        if len(self.quota_attainment["rows"]) == 0:
            self.quota_attainment = {}

        logger.info("END: Creating Payout Summary Sheet")

    def create_plan_details_sheet(self, data):
        logger.info("BEGIN: Creating Plan Details Sheet")

        earned_comm_details = []
        main_payouts = []
        spiff_payouts = []
        for plan_id, details in data["earned_commission_details"].items():
            if details.get("plan_type") == "MAIN":
                main_payouts.append((plan_id, details))
            elif details.get("plan_type") == "SPIFF":
                spiff_payouts.append((plan_id, details))
        main_payouts = sorted(
            main_payouts,
            key=lambda x: (
                x[1].get("plan_display_order")
                if x[1].get("plan_display_order")
                else sys.maxsize
            ),
        )
        spiff_payouts = sorted(
            spiff_payouts,
            key=lambda x: (
                x[1].get("plan_display_order")
                if x[1].get("plan_display_order")
                else sys.maxsize
            ),
        )
        earned_commission_details = main_payouts + spiff_payouts
        for plan_id, details in earned_commission_details:
            payout_obj = {"criteria": []}
            plan_name = details["plan_name"]
            plan_criteria = details["criteria_details"]
            payout_obj["plan_name"] = plan_name
            payout_obj["payout_details"] = format(details["amount"], ",.2f")
            criteria_data_map = {}
            plan_criteria_for_user_logged = get_criteria_details_for_logged_in_user(
                self.client_id, self.login_user_id, plan_criteria
            )
            for criteria, value in plan_criteria_for_user_logged.items():
                temp = {}
                temp["id"] = value["criteria_id"]
                temp["criteria"] = value["criteria_name"]
                temp["amount"] = format(value["amount"], ",.2f")

                com_qe_total = {"commission": temp["amount"]}
                criteria_type = value["criteria_type"]
                if criteria_type in ("Quota", "CustomQuota"):
                    com_qe_total["quota_erosion"] = self.criteria_qe_map.get(
                        value["criteria_id"], 0
                    )
                payout_obj["criteria"].append(temp)

                criteria_details = plan_criteria.get(criteria, {})
                orderby_fields = get_orderby_fields(criteria_details)
                criteria_data = get_payout_details_for_criteria(
                    self.client_id,
                    self.psd,
                    self.ped,
                    self.payee_email,
                    plan_id,
                    criteria,
                    CommissionViewType.EARNED_COMMISSIONS.value,
                    self.login_user_id,
                    orderby_fields=orderby_fields,
                )
                visible_cols = get_visible_columns_for_logged_in_user(
                    self.client_id,
                    self.login_user_id,
                    plan_id,
                    criteria,
                    criteria_data["columns"],
                )
                criteria_data["columns"] = visible_cols

                (headers, records) = self.create_criteria_data_table(
                    plan_criteria[criteria],
                    criteria_data,
                )
                criteria_data_map[criteria] = {
                    "headers": headers,
                    "rows": records,
                    "totals": com_qe_total,
                }
            earned_comm_details.append(payout_obj)
            if self.is_settlement_view:
                self.earned_commission_criteria_details[plan_name] = criteria_data_map
            else:
                self.current_pd_criteria_details[plan_name] = criteria_data_map

        logger.info("END: Creating Plan Details Sheet")

        return earned_comm_details

    def create_deferred_plan_details_sheet(self, data):
        logger.info("BEGIN: Creating Deferred Plan Details Sheet")

        defered_plan_details = []
        main_payouts = []
        spiff_payouts = []
        for plan_id, details in data["deferred_commission_details"].items():
            if details.get("plan_type") == "MAIN":
                main_payouts.append((plan_id, details))
            elif details.get("plan_type") == "SPIFF":
                spiff_payouts.append((plan_id, details))
        main_payouts = sorted(
            main_payouts,
            key=lambda x: (
                x[1].get("plan_display_order")
                if x[1].get("plan_display_order")
                else sys.maxsize
            ),
        )
        spiff_payouts = sorted(
            spiff_payouts,
            key=lambda x: (
                x[1].get("plan_display_order")
                if x[1].get("plan_display_order")
                else sys.maxsize
            ),
        )
        deferred_commission_details = main_payouts + spiff_payouts
        for plan_id, details in deferred_commission_details:
            payout_obj = {"criteria": []}
            plan_name = details["plan_name"]
            plan_criteria = details["criteria_details"]
            payout_obj["plan_name"] = plan_name
            payout_obj["payout_details"] = format(details["amount"], ",.2f")
            criteria_data_map = {}
            plan_criteria_for_user_logged = get_criteria_details_for_logged_in_user(
                self.client_id, self.login_user_id, plan_criteria
            )
            for criteria, value in plan_criteria_for_user_logged.items():
                temp = {}
                temp["id"] = value["criteria_id"]
                temp["criteria"] = value["criteria_name"]
                temp["amount"] = format(value["amount"], ",.2f")
                payout_obj["criteria"].append(temp)

                criteria_details = plan_criteria.get(criteria, {})
                orderby_fields = get_orderby_fields(criteria_details)
                criteria_data = get_payout_details_for_criteria(
                    self.client_id,
                    self.psd,
                    self.ped,
                    self.payee_email,
                    plan_id,
                    criteria,
                    CommissionViewType.DEFERRED_COMMISSIONS.value,
                    self.login_user_id,
                    is_zero_comm_line_item_included=True,  # To get column names if the commission is zero and no line items are present
                    orderby_fields=orderby_fields,
                )
                visible_cols = get_visible_columns_for_logged_in_user(
                    self.client_id,
                    self.login_user_id,
                    plan_id,
                    criteria,
                    criteria_data["columns"],
                )
                criteria_data["columns"] = visible_cols

                (headers, records) = self.create_criteria_data_table(
                    plan_criteria[criteria], criteria_data
                )
                criteria_data_map[criteria] = {
                    "headers": headers,
                    "rows": records,
                    "totals": {
                        "commission": temp["amount"],
                    },
                }
                self.deferred_commission_criteria_details[plan_name] = criteria_data_map
            defered_plan_details.append(payout_obj)

        logger.info("END: Creating Deferred Plan Details Sheet")

        return defered_plan_details

    def create_previous_deferred_plan_details_sheet(self, data):
        logger.info("BEGIN: Creating Previous Deferred Plan Details Sheet")

        previous_deferred_details = []
        for period_key in data["previous_commission_deferred_details"]:
            comission_period = data["previous_commission_deferred_details"][period_key][
                "period"
            ]
            comm_period_date = data["previous_commission_deferred_details"][period_key][
                "comm_period"
            ]
            comm_ped = make_aware(end_of_day(parse(comm_period_date)))
            main_payouts = []
            spiff_payouts = []
            for plan_id, details in data["previous_commission_deferred_details"][
                period_key
            ]["plan_details"].items():
                if details.get("plan_type") == "MAIN":
                    main_payouts.append((plan_id, details))
                elif details.get("plan_type") == "SPIFF":
                    spiff_payouts.append((plan_id, details))
            main_payouts = sorted(
                main_payouts,
                key=lambda x: (
                    x[1].get("plan_display_order")
                    if x[1].get("plan_display_order")
                    else sys.maxsize
                ),
            )
            spiff_payouts = sorted(
                spiff_payouts,
                key=lambda x: (
                    x[1].get("plan_display_order")
                    if x[1].get("plan_display_order")
                    else sys.maxsize
                ),
            )
            period_plans_details = main_payouts + spiff_payouts
            period_dict = {}
            comission_period_key = comission_period.lower().replace(" ", "")
            period_dict["comission_period"] = comission_period
            period_dict["period_key"] = comission_period_key
            period_dict["deferred_plan"] = []

            for plan_id, details in period_plans_details:
                payout_obj = {"criteria": []}
                plan_name = details["plan_name"]
                plan_criteria = details["criteria_details"]
                payout_obj["plan_name"] = plan_name
                payout_obj["payout_details"] = format(details["amount"], ",.2f")
                criteria_data_map = {}
                plan_criteria_for_user_logged = get_criteria_details_for_logged_in_user(
                    self.client_id, self.login_user_id, plan_criteria
                )
                for criteria, value in plan_criteria_for_user_logged.items():
                    temp = {}
                    temp["id"] = value["criteria_id"]
                    temp["criteria"] = value["criteria_name"]
                    temp["amount"] = format(value["amount"], ",.2f")
                    payout_obj["criteria"].append(temp)

                    criteria_details = plan_criteria.get(criteria, {})
                    orderby_fields = get_orderby_fields(criteria_details)
                    criteria_data = get_payout_details_for_criteria(
                        self.client_id,
                        self.psd,
                        self.ped,
                        self.payee_email,
                        plan_id,
                        criteria,
                        CommissionViewType.PREV_DEFERRED_COMMISSIONS.value,
                        self.login_user_id,
                        comm_ped=comm_ped,
                        is_zero_comm_line_item_included=True,  # To get column names if the commission is zero and no line items are present
                        orderby_fields=orderby_fields,
                    )
                    visible_cols = get_visible_columns_for_logged_in_user(
                        self.client_id,
                        self.login_user_id,
                        plan_id,
                        criteria,
                        criteria_data["columns"],
                    )
                    criteria_data["columns"] = visible_cols

                    (headers, records) = self.create_criteria_data_table(
                        plan_criteria[criteria], criteria_data
                    )
                    criteria_data_map[criteria + comission_period_key] = {
                        "headers": headers,
                        "rows": records,
                        "totals": {
                            "commission": temp["amount"],
                        },
                    }
                self.previous_deferred_comm_pdf_criteria_details[
                    plan_name + comission_period_key
                ] = criteria_data_map
                period_dict["deferred_plan"].append(payout_obj)
            previous_deferred_details.append(period_dict)

        logger.info("END: Creating Previous Deferred Plan Details Sheet")

        return previous_deferred_details

    def create_current_period_payout_details_sheet(self, data):
        logger.info("BEGIN: Creating Current Period Payout Details Sheet")
        main_payouts = []
        spiff_payouts = []
        for plan_id, details in data["current_payout_details"].items():
            if details.get("plan_type") == "MAIN":
                main_payouts.append((plan_id, details))
            elif details.get("plan_type") == "SPIFF":
                spiff_payouts.append((plan_id, details))
        main_payouts = sorted(
            main_payouts,
            key=lambda x: (
                x[1].get("plan_display_order")
                if x[1].get("plan_display_order")
                else sys.maxsize
            ),
        )
        spiff_payouts = sorted(
            spiff_payouts,
            key=lambda x: (
                x[1].get("plan_display_order")
                if x[1].get("plan_display_order")
                else sys.maxsize
            ),
        )

        sorted_current_payout_details = main_payouts + spiff_payouts
        current_pd_details = []
        for plan_id, details in sorted_current_payout_details:
            payout_obj = {"criteria": []}
            plan_name = details["plan_name"]
            plan_criteria = details["criteria_details"]
            payout_obj["plan_name"] = plan_name
            payout_obj["payout_details"] = format(details["amount"], ",.2f")

            criteria_data_map = {}
            plan_criteria_for_user_logged = get_criteria_details_for_logged_in_user(
                self.client_id, self.login_user_id, plan_criteria
            )
            for criteria, value in plan_criteria_for_user_logged.items():
                temp = {}
                temp["id"] = value["criteria_id"]
                temp["criteria"] = value["criteria_name"]
                temp["amount"] = format(value["amount"], ",.2f")
                payout_obj["criteria"].append(temp)
                criteria_details = plan_criteria.get(criteria, {})
                orderby_fields = get_orderby_fields(criteria_details)

                criteria_data = get_payout_details_for_criteria(
                    self.client_id,
                    self.psd,
                    self.ped,
                    self.payee_email,
                    plan_id,
                    criteria,
                    CommissionViewType.CURRENT_PERIOD_PAYOUT.value,
                    self.login_user_id,
                    is_zero_comm_line_item_included=True,  # To get column names if the commission is zero and no line items are present
                    orderby_fields=orderby_fields,
                )
                visible_cols = get_visible_columns_for_logged_in_user(
                    self.client_id,
                    self.login_user_id,
                    plan_id,
                    criteria,
                    criteria_data["columns"],
                )
                criteria_data["columns"] = visible_cols

                headers, records = self.create_criteria_data_table(
                    plan_criteria[criteria], criteria_data
                )

                criteria_data_map[criteria] = {
                    "headers": headers,
                    "rows": records,
                    "totals": {
                        "commission": temp["amount"],
                    },
                }

            self.current_pd_criteria_details[plan_name] = criteria_data_map

            current_pd_details.append(payout_obj)

        logger.info("END: Creating Current Period Payout Details Sheet")

        return current_pd_details

    def create_adjustments_sheet(self, data):
        logger.info("BEGIN: Creating Adjustments Sheet")
        adjustments = {"adjustments": {}}
        localized_commission = get_localized_message_utils(
            StatementTerms.COMMISSION.name, self.client_id
        )
        amount_column = (
            localized_commission
            + " ( "
            + str(self.payroll_details["pay_currency"])
            + " )"
        )
        adjustments_data = data.get("adjustments_details", [])

        localized_reason = get_localized_message_utils("REASON", self.client_id)
        localized_reason_category = get_localized_message_utils(
            "REASON_CATEGORY", self.client_id
        )
        localized_plan = get_localized_message_utils("PLAN", self.client_id)
        localized_criteria = get_localized_message_utils("CRITERIA", self.client_id)
        localized_line_item = get_localized_message_utils("LINE_ITEM", self.client_id)

        columns = [
            localized_reason,
            localized_reason_category,
            localized_plan,
            localized_criteria,
            localized_line_item,
            amount_column,
        ]
        headers = []
        for i in range(0, len(columns)):
            if columns[i] == amount_column:
                headers.append(
                    {
                        "headerName": columns[i],
                        "type": "numericColumn",
                        "field": str(i + 1),
                        "maxWidth": 100,
                    }
                )
            else:
                # Since the number of columns is fixed we are hardcoding the column width to max width
                headers.append(
                    {
                        "headerName": columns[i],
                        "field": str(i + 1),
                        "maxWidth": MAX_COL_WIDTH,
                    }
                )
        adjustments["adjustments"] = {
            "headers": headers,
            "rows": [],
            "total": format(self.adjustment_total, ",.2f"),
        }

        records = []

        for adj in adjustments_data:
            plan_name = (
                self.plan_criteria_map[adj["plan_id"]]["plan_name"]
                if adj["plan_id"] in self.plan_criteria_map
                else "-"
            )
            criteria_name = (
                self.plan_criteria_map[adj["plan_id"]]["criteria"][adj["criteria_id"]][
                    "criteria_name"
                ]
                if adj["plan_id"] in self.plan_criteria_map
                and adj["criteria_id"]
                in self.plan_criteria_map[adj["plan_id"]]["criteria"]
                else "-"
            )
            records.append(
                {
                    "1": adj.get("reason") if adj.get("reason") else "-",
                    "2": (
                        adj.get("reason_category")
                        if adj.get("reason_category")
                        else "-"
                    ),
                    "3": plan_name,
                    "4": criteria_name,
                    "5": adj.get("line_item_id") if adj.get("line_item_id") else "-",
                    "6": self.payroll_details["currency_format"]
                    + format(adj["amount"], ",.2f"),
                }
            )
        adjustments["adjustments"]["rows"] = records
        logger.info("END: Creating Adjustments Sheet")
        return adjustments

    def create_breakdown_summary(self):
        payout_view_data = SettlementTotalService(
            client_id=self.client_id, ped=self.ped, payee_emails=[self.payee_email]
        ).get_payouts_view_data()

        breakdown_data = payout_view_data.get(self.payee_email, [])
        if not breakdown_data:
            return {}
        preferred_language = self.client.client_features.get("preferred_language", "")
        table = ""
        columns = []
        rows = []
        if preferred_language:
            message_to_be_translated = {}
            for message in StatementTranslationTerms:
                message_to_be_translated[message.name] = message.value

            translated_messages = get_translated_message_for_statements(
                message_to_be_translated,
                self.client_id,
                preferred_language,
            )
            date_localized = translated_messages[StatementTranslationTerms.DATE.value]
            amount_localized = translated_messages[
                StatementTranslationTerms.AMOUNT.value
            ]
            comments_localized = translated_messages[
                StatementTranslationTerms.COMMENTS.value
            ]
            columns = [date_localized, amount_localized, comments_localized]
            table = translated_messages[StatementTranslationTerms.BREAKDOWN.value]
        else:
            date_localized = get_localized_message_utils(
                StatementTranslationTerms.DATE.name, self.client_id
            )
            amount_localized = get_localized_message_utils(
                StatementTranslationTerms.AMOUNT.name, self.client_id
            )
            comments_localized = get_localized_message_utils(
                StatementTranslationTerms.COMMENTS.name, self.client_id
            )
            columns = [date_localized, amount_localized, comments_localized]
            table = get_localized_message_utils(
                StatementTranslationTerms.BREAKDOWN.name, self.client_id
            )

        rows = [
            {
                "date": (
                    datetime.strptime(data["date"], "%Y-%m-%d").strftime("%d %b %Y")
                    if data.get("date") is not None
                    else "-"
                ),
                "amount": f"{data.get('payee_currency_symbol', '')}{format(float(data.get('amount', 0)), ',.2f')}",
                "comments": data.get("comment", "-"),
                "is_paid": data.get("is_paid"),
            }
            for data in breakdown_data
        ]
        breakdown_summary = {"table": table, "headers": columns, "rows": rows}
        logger.info("END: Creating Breakdown Summary Sheet")
        return breakdown_summary

    def create_draws_adjustments(self, data):
        logger.info("BEGIN: Creating Draws Adjustments")
        localized_commission = get_localized_message_utils(
            StatementTerms.COMMISSION.name, self.client_id
        )
        adjustments = {"adjustments": {}}
        amount_column = (
            localized_commission
            + " ( "
            + str(self.payroll_details["pay_currency"])
            + " )"
        )

        draw_availed_localized = get_localized_message_utils(
            "DRAW_AVAILED", self.client_id
        )
        draw_recovered_localized = get_localized_message_utils(
            "DRAW_RECOVERED", self.client_id
        )

        columns = [
            draw_availed_localized,
            draw_recovered_localized,
            amount_column,
        ]
        headers = []
        for i in range(0, len(columns)):
            if columns[i] == amount_column:
                headers.append(
                    {
                        "headerName": columns[i],
                        "type": "numericColumn",
                        "field": str(i + 1),
                        "maxWidth": 100,
                    }
                )
            else:
                headers.append(
                    {
                        "headerName": columns[i],
                        "field": str(i + 1),
                        "maxWidth": MAX_COL_WIDTH,
                    }
                )
        adjustments["adjustments"] = {
            "headers": headers,
            "rows": [],
            "total": format(data["draws"], ",.2f"),
        }

        recoverable_draw = format(data["draws"], ",.2f")
        draw_availed = format(data["draws_details"][0]["draw_availed"], ",.2f")
        draw_recovered = format(data["draws_details"][0]["draw_recovered"], ",.2f")
        adjustments["adjustments"]["rows"].append(
            {
                "1": draw_availed,
                "2": draw_recovered,
                "3": self.payroll_details["currency_format"] + recoverable_draw,
            }
        )
        logger.info("END: Creating Draws Adjustments")
        return adjustments

    def export_statements_data(self, data):
        # The data will be formatted as per the statements template
        logger.info("BEGIN: export statements data")
        self.create_payout_summary_sheet(data)
        datasheet_variable_query = DatasheetVariableAccessor(
            self.client_id
        ).get_variables_for_ds_ids(list(self.datasheet_ids), as_dicts=True)

        for record in datasheet_variable_query:
            datasheet_id = str(record["datasheet_id"])
            if datasheet_id not in self.datasheet_variable_map:
                self.datasheet_variable_map[datasheet_id] = {}
            self.datasheet_variable_map[datasheet_id][record["system_name"]] = {
                "display_name": record["display_name"],
                "data_type": record["data_type_id"],
            }

        criteria_details = {}

        if self.is_settlement_view:
            if len(data.get("current_payout_details", {}).keys()) != 0:
                self.current_pd_payout_details = (
                    self.create_current_period_payout_details_sheet(data)
                )
            if len(data.get("previous_commission_deferred_details", {}).keys()) != 0:
                self.previous_deferred_comm_pd_payout_details = (
                    self.create_previous_deferred_plan_details_sheet(data)
                )
            if len(data.get("deferred_commission_details", {}).keys()) != 0:
                self.deferred_commission_details = (
                    self.create_deferred_plan_details_sheet(data)
                )
        if len(data.get("earned_commission_details", {}).keys()) != 0:
            earned_comm_details = self.create_plan_details_sheet(data)
            if self.is_settlement_view:
                self.earned_commission_details = earned_comm_details
            else:
                self.current_pd_payout_details = earned_comm_details

        if len(data.get("adjustments_details", [])) != 0:
            self.adjustments["commission_adjustments"] = self.create_adjustments_sheet(
                data
            )
            criteria_details["commission_adjustments"] = self.adjustments[
                "commission_adjustments"
            ]
        if len(data.get("draws_details", [])) != 0:
            self.adjustments["draw_adjustments"] = self.create_draws_adjustments(data)
            criteria_details["draw_adjustments"] = self.adjustments["draw_adjustments"]

        if self.show_payout_table_breakdown:
            self.breakdown_summary = self.create_breakdown_summary()

        preferred_language = self.client.client_features.get("preferred_language", "")
        if preferred_language != "en" and preferred_language != "":
            statement_period = (
                self.psd.strftime("%d.%m.%Y") + "-" + self.ped.strftime("%d.%m.%Y")
            )
        else:
            statement_period = find_statement_period(
                self.client_id,
                self.payee_email,
                self.psd,
                self.ped,
                self.client.fiscal_start_month,
            )
        localized_name = get_localized_message_utils("NAME", self.client_id)
        localized_email = get_localized_message_utils("EMAIL", self.client_id)
        localized_designation = get_localized_message_utils(
            "DESIGNATION", self.client_id
        )
        localized_period_start_date = get_localized_message_utils(
            "PERIOD_START_DATE", self.client_id
        )
        localized_period_end_date = get_localized_message_utils(
            "PERIOD_END_DATE", self.client_id
        )
        localized_last_calculated = get_localized_message_utils(
            "LAST_CALCULATED", self.client_id
        )
        localized_downloaded = get_localized_message_utils("DOWNLOADED", self.client_id)
        localized_status = get_localized_message_utils("STATUS", self.client_id)

        payee_details_dict = {
            "name": self.payee_details[localized_name],
            "email": self.payee_details[localized_email],
            "designation": self.payee_details[localized_designation],
            "period_start_date": self.payee_details[localized_period_start_date],
            "period_end_date": self.payee_details[localized_period_end_date],
            "last_calculated": self.payee_details[localized_last_calculated],
            "downloaded": self.payee_details[localized_downloaded],
            "status": self.payee_details[localized_status],
            "settlement_status": self.is_settlement_view,
            "payout_month": statement_period,
        }
        self.payee_details = payee_details_dict

        if self.is_settlement_view:
            criteria_details["previous_deferred_comm_pdf_criteria_details"] = (
                format_criteria_data(
                    self.previous_deferred_comm_pdf_criteria_details,
                )
            )

            criteria_details["deferred_commission_criteria_details"] = (
                format_criteria_data(
                    self.deferred_commission_criteria_details,
                )
            )
            criteria_details["earned_commission_criteria_details"] = (
                format_criteria_data(
                    self.earned_commission_criteria_details,
                )
            )
        criteria_details["current_pd_criteria_details"] = format_criteria_data(
            self.current_pd_criteria_details
        )
        criteria_details["headers"] = self.table_headers
        self.json_data = json.dumps(criteria_details)

        pdf_formatted_data = self.get_context_data()

        # The data will be converted to a dictionary as render_to_string in django
        # accepts context only as dictionary
        pdf_formatted_data_dict = pdf_formatted_data.model_dump()

        logger.info("END: export statements data")
        return pdf_formatted_data_dict

    def get_context_data(self) -> StatementPDFModel:
        """
        This function returns the context data for the statements template

        Returns:
            context:StatementPDFModel - context data for the statements template

        """

        context = StatementPDFModel(
            client_id=self.client_id,
            use_aggrid=self.use_aggrid_for_pdf_export,
            payout_summary=self.payout_summary,
            commission_summary=self.commission_summary,
            quota_attainment=self.quota_attainment,
            current_pd_payout_details=self.current_pd_payout_details,
            previous_deferred_comm_pd_payout_details=self.previous_deferred_comm_pd_payout_details,
            deferred_commission_details=self.deferred_commission_details,
            earned_commission_details=self.earned_commission_details,
            payee_details=self.payee_details,
            payroll_details=self.payroll_details,
            custom_fields=self.custom_fields,
            table_headers=self.table_headers,
            image_url=os.getenv("REACT_APP_EVERSTAGE_ASSETS_URL", ""),
            banner="light" if self.client.statement_logo_url else "dark",
            json_data=self.json_data,
            adjustments=self.adjustments,
            payout_status_details=self.payout_details,
            breakdown_summary=self.breakdown_summary,
            show_payout_table_breakdown=self.show_payout_table_breakdown,
            localized_terms=self.localized_terms,
        )

        return context

    def render_statements_to_html(
        self, context, use_react=False, is_local_env=False, bulk_key=None
    ):
        logger.info("BEGIN: render statements to html")
        if use_react:
            logger.info("Using html template for react")
            template_name = "../templates/user-template.html"  # Django template path
        else:
            logger.info("Using html template for django")
            template_name = "../templates/index.html"
        # The context will be passed to the Django template and the html string will be returned
        pdf_data = self.django_template_to_pdf(
            template_name, context, is_local_env, bulk_key
        )
        logger.info("END: render statements to html")
        return pdf_data

    def django_template_to_pdf(
        self,
        template_name,
        context,
        is_local_env=False,
        bulk_key=None,
    ):
        logger.info("BEGIN: django template to pdf")
        # After rendering the Django template, it will be converted to HTML string
        html_string = render_to_string(template_name, context)
        # If render_statements_to_html services is called from python function(local development), then save the html file in Downloads folder
        if is_local_env:
            downloads_path = os.path.join(os.path.expanduser("~"), "Downloads")
            file_path = os.path.join(downloads_path, "example.html")
            f1 = open(file_path, "w", encoding="utf-8")
            f1.write(html_string)
            f1.close()
            return

        # The rendered html file is temporarily stored in the current working directory
        f = open(
            StatementsPdf.TEMP_FILE_PATH.value + "_" + self.payee_email + ".html",
            "w",
            encoding="utf-8",
        )
        f.write(html_string)
        f.close()

        # The header and footer templates are read and the dynamic values are replaced
        footer_template = self.get_footer_template()

        header_template = self.get_header_template(
            context["payee_details"]["payout_month"],
            context["payee_details"]["status"],
            context["table_headers"]["statement_for"],
        )

        # The html_path and pdf_path are created in the current working directory as temporary files
        html_path = os.path.join(
            os.getcwd(),
            StatementsPdf.TEMP_FILE_PATH.value + "_" + self.payee_email + ".html",
        )
        pdf_path = os.path.join(
            os.getcwd(),
            StatementsPdf.TEMP_FILE_PATH.value + "_" + self.payee_email + ".pdf",
        )
        margin = {
            "top": "125px",
            "bottom": "90px",
        }
        s3_html = (
            self.payee_email
            + "_"
            + str(self.psd.strftime("%Y%m%d"))
            + "_"
            + str(self.ped.strftime("%Y%m%d"))
            + "_"
            + str(timezone.now().strftime("%Y%m%d%H%M%S"))
            + ".html"
        )
        pdf_data = convert_html_to_pdf(
            client_id=self.client_id,
            html_path=html_path,
            pdf_path=pdf_path,
            s3_html=s3_html,
            bulk_key=bulk_key,
            is_statements=True,
            header_template=header_template,
            footer_template=footer_template,
            margin=margin,
            display_header_footer=True,
            print_background=True,
        )
        if os.path.exists(pdf_path):
            logger.info("Deleting temporary pdf file for client: %s", self.client_id)
            os.remove(pdf_path)
        if os.path.exists(html_path):
            logger.info("Deleting temporary html file for client: %s", self.client_id)
            os.remove(html_path)
        logger.info("END: django template to pdf")
        return pdf_data

    def get_footer_template(self):
        """
        This function renders the footer template and returns the html string

        Returns:
            footer_template: html string of the footer template
        """
        logger.info("BEGIN: get footer template")
        footer_template_name = "../templates/footer.html"  # Django template path
        footer_context = {}

        perspective_icon = (
            os.getenv("REACT_APP_EVERSTAGE_ASSETS_URL", "")
            + StatementsPdf.PERSPECTIVE_ICON.value
        )
        perspective_icon_base64 = convert_img_to_base64(perspective_icon)
        footer_context["perspective_icon"] = perspective_icon_base64

        # if statement logo exists, then custom logo pdf should be generated
        if self.client.statement_logo_url:
            everstage_dark_icon = (
                os.getenv("REACT_APP_EVERSTAGE_ASSETS_URL", "")
                + StatementsPdf.EVERSTAGE_LOGO_DARK.value
            )
            everstage_icon_base64 = convert_img_to_base64(everstage_dark_icon)
            footer_context["is_custom_pdf"] = True
            footer_context["everstage_logo"] = everstage_icon_base64

        # if statement logo does not exist, then default logo pdf should be generated
        else:
            footer_context["is_custom_pdf"] = False

        footer_context["page_label"] = get_localized_message_utils(
            "PAGE", self.client_id
        )
        footer_context["of_label"] = get_localized_message_utils("OF", self.client_id)
        footer_context["powered_by_label"] = get_localized_message_utils(
            "POWERED_BY", self.client_id
        )
        footer_context["help_label"] = get_localized_message_utils(
            "STATEMENT_FOOTER_HELP", self.client_id
        )

        footer_template = render_to_string(footer_template_name, footer_context)
        logger.info("END: get footer template")
        return footer_template

    def get_header_template(self, payout_month, lock_status, translated_statement):
        """
        This function renders the header template and returns the html string

        Returns:
            header_template: html string of the header template
        """
        logger.info("BEGIN: get header template")
        header_template_name = "../templates/header.html"  # Django template path
        header_context = {}
        banner_theme = "light" if self.client.statement_logo_url else "dark"
        # if statement logo exists, then custom logo pdf should be generated
        if banner_theme == "light":
            everstage_icon_base64 = convert_img_to_base64(
                self.client.statement_logo_url
            )
            header_context = {
                "is_custom_pdf": True,
                "everstage_logo": everstage_icon_base64,
            }
        # if statement logo does not exist, then default logo pdf should be generated
        else:
            everstage_light_icon = (
                os.getenv("REACT_APP_EVERSTAGE_ASSETS_URL", "")
                + StatementsPdf.EVERSTAGE_LOGO_LIGHT.value
            )
            everstage_icon_base64 = convert_img_to_base64(everstage_light_icon)
            header_context = {
                "is_custom_pdf": False,
                "everstage_logo": everstage_icon_base64,
            }
        # Here based on the lock status of the payout, the lock/unlock icon is rendered
        if lock_status == "Frozen":
            if banner_theme == "light":
                lock_icon = (
                    os.getenv("REACT_APP_EVERSTAGE_ASSETS_URL", "")
                    + StatementsPdf.LIGHT_THEME_LOCK.value
                )
            else:
                lock_icon = (
                    os.getenv("REACT_APP_EVERSTAGE_ASSETS_URL", "")
                    + StatementsPdf.DARK_THEME_LOCK.value
                )
            lock_icon_base64 = convert_img_to_base64(lock_icon)
            header_context["lock_status_icon"] = lock_icon_base64
        else:
            if banner_theme == "light":
                unlock_icon = (
                    os.getenv("REACT_APP_EVERSTAGE_ASSETS_URL", "")
                    + StatementsPdf.LIGHT_THEME_UNLOCK.value
                )
            else:
                unlock_icon = (
                    os.getenv("REACT_APP_EVERSTAGE_ASSETS_URL", "")
                    + StatementsPdf.DARK_THEME_UNLOCK.value
                )
            unlock_icon_base64 = convert_img_to_base64(unlock_icon)
            header_context["lock_status_icon"] = unlock_icon_base64
        header_context["payout_month"] = payout_month
        header_context["lock_status"] = (
            get_localized_message_utils(StatementsPdf.LOCKED.name, self.client_id)
            if lock_status == "Frozen"
            else get_localized_message_utils(
                StatementsPdf.UNLOCKED.name, self.client_id
            )
        )
        header_context["statement_for"] = translated_statement
        header_template = render_to_string(header_template_name, header_context)

        logger.info("END: get header template")
        return header_template


def get_statements_data(client_id, ped, payee_email):
    # This fetches the statements data required for pdf export
    statements_data = SettlementTotalService(
        client_id=client_id, ped=ped, payee_emails=[payee_email]
    ).get_statements_data(export_details=True)
    return statements_data


def export_statements_as_pdf(
    client_id=None,
    login_user_id=None,
    payee_email=None,
    psd=None,
    ped=None,
    is_settlement_view=False,
    use_react=True,
) -> bytes:
    export_start_time = T()
    psd = make_aware(start_of_day(datetime.combine(psd, time(0, 0, 0))))
    ped = make_aware(end_of_day(datetime.combine(ped, time(23, 59, 59))))
    data = get_statements_data(client_id, ped, payee_email)
    export_obj = ExportStatementsPdf(
        client_id,
        payee_email,
        psd,
        ped,
        login_user_id,
        is_settlement_view,
    )
    pdf_formatted_data = export_obj.export_statements_data(data)
    if use_react:
        pdf_formatted_data = {
            "data": json.dumps(pdf_formatted_data),
            "payee_details": pdf_formatted_data["payee_details"],
            "table_headers": pdf_formatted_data["table_headers"],
            "payout_status_details": pdf_formatted_data["payout_status_details"],
        }
    localized_messages = {
        "period_label": get_localized_message_utils("PERIOD", str(client_id)),
        "last_calculated_label": get_localized_message_utils(
            "LAST_CALCULATED", str(client_id)
        ),
        "downloaded_label": get_localized_message_utils("DOWNLOADED", str(client_id)),
    }

    pdf_formatted_data["localized_messages"] = localized_messages
    pdf_bytes = export_obj.render_statements_to_html(
        pdf_formatted_data, use_react=use_react
    )
    log_segment_data(login_user_id, psd, ped, payee_email)
    logger.info(
        "Time taken to export statements pdf for %s in period [%s,%s]: %s",
        payee_email,
        psd.strftime("%d-%b-%Y"),
        ped.strftime("%d-%b-%Y"),
        round(T() - export_start_time, 2),
    )

    return pdf_bytes


def log_segment_data(login_user_id, psd, ped, payee_email):
    analytics_data = {
        "user_id": login_user_id,
        "event_name": SegmentEvents.EXPORT_STATEMENTS_PDF.value,
        "event_properties": {
            SegmentProperties.PERIOD_START.value: psd,
            SegmentProperties.PERIOD_END.value: ped,
            SegmentProperties.PAYEE_NAME.value: payee_email,
        },
    }
    analytics = CoreAnalytics(analyser_type="segment")
    analytics.send_analytics(analytics_data)


def format_criteria_data(
    criteria_details,
):
    """
    This function formats the criteria data to be rendered in the pdf
    This calculates the width of each column based on the data type of the column
    Sets the width of the table based on the maximum width of the table

    Datetime Obj will be converted to String
    Percent Field will be appended with %
    Max width for text column is MAX_COL_WIDTH
    Max width for number column is 100

    Params:
        criteria_details(dict): The criteria details of the plan

    Returns:
        criteria_details_abs(dict): The formatted criteria details of the plan
    """
    logger.info("BEGIN: format criteria data")
    criteria_details_abs = {}
    for identifiers in criteria_details.values():
        header_records_map = {}
        for data in identifiers.values():
            for header in data["headers"]:
                header_records_map[header["field"]] = header["type"]
                if header["type"] == "text":
                    header["maxWidth"] = MAX_COL_WIDTH
                    del header["type"]
                elif header["type"] == "number":
                    header["maxWidth"] = 100
                    header["type"] = "numericColumn"
                else:
                    header["maxWidth"] = 110
                    del header["type"]

            for row in data["rows"]:
                for key, value in row.items():
                    if value is not None and not isinstance(value, str):
                        if header_records_map[key] == "date":
                            row[key] = value.strftime("%d-%b-%Y")
                        elif header_records_map[key] == "percent":
                            row[key] = str(value) + "%"
                        else:
                            row[key] = str(row[key])

        for _, details in criteria_details.items():
            for criteria, data in details.items():
                criteria_details_abs[criteria] = data

    logger.info("END: format criteria data")
    return criteria_details_abs


def get_criteria_details_for_logged_in_user(
    client_id, login_user_id, criteria_data, show_hidden_criteria_for_admin=False
):
    """
    get_criteria_details_for_logged_in_user function returns only the criteria details which the logged_in_user
    has permission to view and returns the criteria details in the order of criteria display order

    Args:
        client_id (int): client id
        login_user_id (str): login user email
        criteria_data (dict): criteria data
        show_hidden_criteria_for_admin (bool): show hidden criteria for admin

    Returns:
        criteria_data_filtered (dict): criteria data sorted and filtered based on the permissions of the logged_in_user
    """
    criteria_data_filtered = {}
    ui_permissions = get_ui_permissions(client_id, login_user_id)

    for criteria, value in sorted(
        criteria_data.items(), key=lambda x: x[1]["criteria_display_order"]
    ):
        if show_hidden_criteria_for_admin:
            if (
                RbacPermissions.VIEW_HIDDENCRITERIA.value in ui_permissions
                or not value.get("is_hidden_criteria", False)
            ):
                criteria_data_filtered[criteria] = value
        else:
            if not value.get("is_hidden_criteria", False):
                criteria_data_filtered[criteria] = value
    return criteria_data_filtered


def get_translated_message_for_statements(
    message_to_be_translated,
    client_id,
    preferred_language,
):
    """
    get_translated_message_for_statements function translates the messages to the preferred language

    Args:
        message_to_be_translated(dict): dict of messages
        client_id(int): client id
        preferred_language(str): preferred language

    Returns:
        translated_messages(dict): dict of translated messages
    """
    translated_messages = {}
    for key, value in message_to_be_translated.items():
        translated_messages[key] = get_statements_translated_message_service(
            value,
            client_id,
            preferred_language,
        )

    return translated_messages


def get_orderby_fields(criteria_details: Dict[str, Any]) -> List[Dict[str, str]]:
    """
    Generates a list of order-by fields based on the given criteria details
    """
    sort_cols = criteria_details.get("sort_cols") or []

    orderby_fields = [{"column": column, "order": order} for column, order in sort_cols]

    criteria_type = criteria_details.get("criteria_type") or ""
    if criteria_type in {"CustomTier", "Tier", "CustomQuota", "Quota"}:
        orderby_fields.extend(
            [
                {"column": "row_key", "order": "asc"},
                {"column": "tierName", "order": "asc"},
            ]
        )

    return orderby_fields
