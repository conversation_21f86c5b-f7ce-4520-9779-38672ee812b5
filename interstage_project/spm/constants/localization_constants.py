from enum import Enum

from commission_engine.utils.general_data import LocalizedEnum


class LocalizationEngMessages(Enum):
    PUBLISH_PLAN_DELETETED_DATASHEET = (
        "The $COMMISSION plan has deleted datasheet variables"
    )
    DELETE_PLAN_FROZEN_COMMISSION = "Plan has payees with locked $COMMISSION_lc."
    PAYOUT_FREQ_CHANGE__COMMISSION_LOCK = (
        "Cannot update the $PAYOUT_lc frequency for a period where $COMMISSIONS_lc are "
        "locked for the user."
    )
    ESD_CHANGE_COMMISSION_LOCK = "Cannot update the effective start date for a period where $COMMISSIONS_lc are locked for the user."
    PAYOUT_FREQ_CHANGE__PLAN = (
        "Cannot update the $PAYOUT_lc frequency for a period when the user has active "
        "$COMMISSION_lc plans."
    )
    ESD_CHANGE_PLAN = "Cannot update the effective start date for a period when the user has active $COMMISSION_lc plans."
    PAYOUT_FREQ_CHANGE__FORECAST_PLAN = (
        "Cannot update the $PAYOUT_lc frequency for a period when the user has active "
        "Forecast $COMMISSION_lc plans."
    )
    ESD_CHANGE_FORECAST_PLAN = "Cannot update the effective start date for a period when the user has active Forecast $COMMISSION_lc plans."
    PAYOUT_FREQ_CHANGE__QUOTA = (
        "Cannot change the $PAYOUT_lc frequency for a period when the user has active "
        "$QUOTAS_lc."
    )
    PAYOUT_FREQ_CHANGE__NOT_CONFIGURED = "Cannot change/update the $PAYOUT_lc frequency since the period has not configured."

    PAYOUT_FREQ__NOT_EXISTS = "Invalid $PAYOUT_lc frequency"

    ADJUSTMENT_EXIST = "$ADJUSTMENT already exists"
    NO_ADJUSTMENT_ID = "No $ADJUSTMENT ID in request"
    ADJUSTMENT_DOES_NOT_EXIST = "$ADJUSTMENT does not exist"
    QUOTA_CATEGORY_EXISTS = (
        "The $QUOTA category name is already in use. Please choose another name."
    )
    RENAME_QUOTA_SUCCESS_MSG = "$QUOTA category updated successfully"
    REMOVE_EMPLOYEE_QUOTA_CATEGORY = "The payee is already associated with a plan in which the $QUOTA category has been utilized. "
    DELETE_QUOTA_CATEGORY = "The commission plan {plans} utilizes this $QUOTA category. To remove the $QUOTA category, you should delete the plan"
    DELETE_QUTOA_CATEGORY_FORECAST = "The forecast plan {plans} utilizes this $QUOTA category. To remove the $QUOTA category, you should delete the plan"
    CANNOT_ASSIGN_VIEWER = "Error: Cannot assign the Viewer role since user is part of these $COMMISSION_lc plans - {}"
    ARREARS_IGNORED = "$ARREARS Ignored"
    ERROR_IGNORE_ARREARS = "Error in $ARREARS Ignore"
    ARREARS_PROCESSED = "$ARREARS Processed"
    ERROR_PROCESS_ARREARS = "Error in Processing $ARREARS"
    CANNOT_CREATE_QUOTA = (
        "$QUOTAS cannot be created for a time-period that is already locked."
    )
    INVALID_COMM_SCHEDULE = "$COMMISSION Schedule cannot be {comm_schedule} when $QUOTA_lc period is {quota_period}."
    GREATER_QUOTA_PERIOD = (
        "$QUOTA period must be greater than or equal to Employee $PAYOUT Frequency."
    )
    GREATER_COMM_SCHEDULE = "$COMMISSION Schedule must be greater than or equal to Employee $PAYOUT Frequency."
    REQUIRED_PAYOUT_FREQUENCY = (
        "$PAYOUT frequency is required to set $QUOTA for a payee."
    )
    CONSISTENT_ACROSS_QUOTA = "$QUOTA period and $COMMISSION Schedule must be consistent across a $QUOTA_lc category."
    NO_MODIFY_QUOTA_PERMISSION = (
        "You don't have permission to modify $QUOTA_lc for this user."
    )
    EMPTY_QUOTA_CATEGORY = "$QUOTA Category cannot be empty."
    INVALID_QUOTA_NAME = (
        "$QUOTA cannot be empty or contain special characters or letters."
    )
    INVALID_MANAGER_QUOTA = (
        "Invalid $QUOTA as manager value. Set it as yes or no, or leave it blank."
    )
    INVALID_QUOTA_ORDER = (
        "$QUOTAS across schedule should be equal or in increasing order."
    )
    EMPLOYEE_QUOTA_NOT_FOUND = (
        "The $QUOTA doesn’t exist for the payee for the fiscal year."
    )
    QUOTA_EFFECTIVE_DATE_EXISTS = "Effective $QUOTA already exists for this period."
    MID_QUOTA_PERIOD = "Mid $QUOTA period changes are not allowed"
    SAME_QUOTA_PERIOD = "Same $QUOTA period changes are not allowed"
    MISSING_QUOTA = "$QUOTA missing for {months}"
    POSSIBLE_QUOTA_PERIOD = (
        "$QUOTA period can only be Annual, Halfyearly, Quarterly, or Monthly."
    )
    POSSIBLE_COMMISSION_SCHEDULE = (
        "$COMMISSION schedule can only be Annual, Halfyearly, Quarterly, or Monthly."
    )
    NEW_QUOTA_CATEGORY = "$QUOTA Category Created"
    DUPLICATE_QUOTA_CATEGORY = "Found duplicate, $QUOTA category is case insensitive."
    DELETE_PLAN_WITH_COMM_ADJ = "Certain $COMMISSION_lc $ADJUSTMENTS_lc here associated with this plan need to be removed before the plan can be deleted."
    COMM_ADJ_AMT_WITHIN_THRESHOLD_RANGE = (
        "As the $ADJUSTMENT_lc amount is within the threshold range."
    )
    ADJ_SKIPPED_APPROVAL = "The $ADJUSTMENT_lc was skipped for approval."
    ADJ_ADDED_PRIOR_FEATURE = "Since the $ADJUSTMENT_lc was added prior to the activation of approval settings."
    COMM_LOCK_NOT_SET = "$COMMISSION lock date is not set"


class SLACK_MESSAGES(Enum):
    NOT_ADDED_TO_COMMISSION_PLAN = "You've not been added to a $COMMISSION_lc plan yet."
    CHECK_OUT_COMMISSIONS = "Check out your $COMMISSIONS_lc"
    SELECT_QUOTA_CATEGORY = (
        "Hi {emp_name}! {emoji}\n\n Please select a $QUOTA_lc category"
    )
    SELECT_QUOTA = "Select $QUOTA"
    UNABLE_TO_POST_QUOTA = "Unable to post $QUOTA. Please contact the admin."
    CHANGE_QUOTA_CATEGORY = "Change $QUOTA_lc category"
    CHECK_OUT_QUOTA = "Hey {emp_name}! {emoji}\n\nCheck out your $QUOTAS_lc for *{quota_category_name}*:"
    MY_QUOTAS = "My $QUOTAS_lc"
    DONT_HAVE_QUOTAS = "Hey {emp_name}! {emoji}\n\n You don't have $QUOTAS_lc set!"
    CHECK_OUT_QUOTA_ATTAINMENT = "Hey {emp_name}! {emoji}\n\nCheck out your $QUOTA_lc attainment for *{quota_time_period}*:"
    COMMISSION = "$COMMISSION"
    ADJUSTMENTS = "$ADJUSTMENTS"
    COMMISSIONS = "$COMMISSIONS"
    ADJUSTMENT = "$ADJUSTMENT"
    PAYOUT = "$PAYOUT"
    PAYOUTS = "$PAYOUTS"
    QUOTA = "$QUOTA"
    QUOTAS = "$QUOTAS"


class MS_TEAMS_MESSAGES(Enum):
    QUOTA_CATEGORY = "<b>$QUOTA:</b> {quota_category}<br><br>"
    QUOTA_ATTAINMENT = "<b>$QUOTA Attainment {emoji} %:</b> {quota_attainment}<br><br>"
    VIEW_COMMISSIONS = "View $COMMISSIONS"
    COMMISSIONS = "$COMMISSIONS"
    ADJUSTMENTS = "$ADJUSTMENTS"
    PAYOUTS = "$PAYOUTS"
    QUOTAS = "$QUOTAS"
    COMMISSION = "$COMMISSION"
    ADJUSTMENT = "$ADJUSTMENT"
    PAYOUT = "$PAYOUT"
    QUOTA = "$QUOTA"


class CommissionPlanValidationMessages(Enum):
    PAYEE_PLAN_DIFF_FREQ = "Payee has a different $PAYOUT_lc frequency ({payee_payout_frequency}) than other payees ({plan_payout_frequency}). Fix by updating frequency or removing payee"
    EFFECTIVE_END_LESSER_THAN_CUSTOM_PAYROLL_END = "Effective end date should be less than last $PAYOUT_lc period date: {custom_payroll_end_date}"
    EFFECTIVE_END_GREATER_THAN_CUSTOM_PAYROLL_START = "Effective start date should be greater than first $PAYOUT_lc period date: {custom_payroll_start_date}"
    MULTIPLE_PAYOUT_FREQ = "Selected duration falls under multiple $PAYOUT_lc periods"
    LOCKED_PAYOUTS = "$PAYOUTS have been locked"
    ACTIVE_COMMISSION_ADJUSTMENTS = "Active $COMMISSION $ADJUSTMENTS_lc are present"
    PED_GREATER_THAN_CC_END_DATE = "Since the plan $PAYOUT_lc frequency is a custom calendar, the commission plan end date cannot exceed the calendar’s end date: {last_payout_period_date}"
    LOCKED_PAYOUT_PED_REDUCED = (
        "Plan end date cannot be reduced as the $PAYOUT_lc is locked for payee(s)"
    )
    SETT_DATE_LOCKED_PAYOUT = (
        "Settlement end date cannot be reduced as the $PAYOUT_lc is locked for payee(s)"
    )
    EMPTY_PAYOUT_FREQ = "The $PAYOUT_lc frequency cannot be empty"
    INVALID_PAYOUT_FREQ = "Invalid $PAYOUT_lc frequency"


class UserExportTerms(Enum):
    PRIMARY_COMM_PLAN = "Primary $COMMISSION Plan"
    PAYOUT_FREQUENCY = "$PAYOUT Frequency"
    PAYOUT_CURRENCY = "$PAYOUT Currency"


class PayoutExportTerms(Enum):
    PAYOUT = "$PAYOUT"
    COMMISSION = "$COMMISSION"
    PRIMARY_COMM_PLAN = "Primary $COMMISSION Plan"
    COMM_PERCENT = "$COMMISSION %"
    PAYOUT_FREQUENCY = "$PAYOUT Frequency"


class LocalizationTerms(Enum):
    QUOTA = "$QUOTA"
    QUOTAS = "$QUOTAS"
    QUOTA_EROSION = "$QUOTA_EROSION"
    COMMISSION = "$COMMISSION"
    COMMISSIONS = "$COMMISSIONS"
    PAYOUT = "$PAYOUT"
    PAYOUTS = "$PAYOUTS"
    ADJUSTMENT = "$ADJUSTMENT"
    ADJUSTMENTS = "$ADJUSTMENTS"
    ARREARS = "$ARREARS"
    ARREAR = "$ARREAR"
    DEFERRED = "$DEFERRED"
    EARNED = "$EARNED"
    ON_TARGET_VARIABLE_PAY = "$ON_TARGET_VARIABLE_PAY"
    PAYOUT_LC = "$PAYOUT_lc"
    PAYOUTS_LC = "$PAYOUTS_lc"
    QUOTA_lc = "$QUOTA_lc"
    COMMISSION_lc = "$COMMISSION_lc"
    COMMISSIONS_lc = "$COMMISSIONS_lc"


class StatementTerms(Enum):
    PAYOUT_SUMMARY = "$PAYOUT Summary"
    PAYOUT_PERCENT = "$PAYOUT (%)"
    CURRENT_PERIOD_PAYOUT = "$PAYOUT from current period"
    DEFERRED_COMM_PAYOUT = "$PAYOUT from past $DEFERRED_lc $COMMISSIONS_lc"
    PAYOUT_ARREARS = "$PAYOUT $ARREARS"
    TOTAL_PAYOUT = "Total $PAYOUT"
    PAYOUT = "$PAYOUT"
    PAST_DEF_COMM_PAYOUT = "From Past $DEFERRED $COMMISSION"
    COMMISSION = "$COMMISSION"
    COMMISSION_SUMMARY = "$COMMISSION Summary"
    PAYOUT_DETAILS = "$PAYOUT details"
    EARNED_COMMISSION = "$EARNED $COMMISSION"
    EARNED_COMMISSIONS = "$EARNED $COMMISSIONS"
    DEFERRED_COMMISSION = "$DEFERRED $COMMISSION"
    COMM_SUMMATION = "$COMMISSION (Summation)"
    DEFERRED_COMMISSIONS = "$DEFERRED $COMMISSIONS"
    COMM_ADJUSTMENTS = "$COMMISSION $ADJUSTMENTS"
    QUOTA_ATTAINMENT = "$QUOTA Attainment"
    QUOTA_PERIOD = "$QUOTA Period"
    QUOTA_ATTAINMENT_PERCENT = "$QUOTA Attainment (%)"
    QUOTA_EROSION = "$QUOTA_EROSION"
    QUOTA = "$QUOTA"
    ADJUSTMENTS = "$ADJUSTMENTS"
    COMMISSION_lc = "$COMMISSION_lc"
    COMMISSIONS = "$COMMISSIONS"
    PREV_DEF_COMM_PAYOUT_lc = "from previously $DEFERRED_lc $COMMISSIONS_lc"
    PREV_DEF_COMM_PAYOUT_PERIOD_lc = (
        "from previously $DEFERRED_lc $COMMISSION_lc period"
    )
    DETAILED_PAYOUT_INSIGHTS = "Detailed $PAYOUT Insights by Plan and Criteria"
    PAYOUT_BY_PLAN_AND_CRITERIA = "$PAYOUT by Plan and Criteria"
    DETAILED_COMMISSION_INSIGHTS = "Detailed $COMMISSION Insights by Plan and Criteria"
    COMMISSION_BY_PLAN_AND_CRITERIA = "$COMMISSION by Plan and Criteria"
    COMM_CALCULATION = "$COMMISSION calculation"
    ATTAINED = "Attained"
    MANAGER_OR_INDIVIDUAL = "Manager/Individual"
    LAST_CALCULATED = "Last Calculated"
    DOWNLOADED = "Downloaded"
    PERIOD = "Period"
    FROM_CURRENT_PERIOD = "from current period"


class StatementTranslationTerms(Enum):
    PAYOUT_SUMMARY = "PAYOUT_SUMMARY"
    TOTAL_PAYOUT = "TOTAL_PAYOUT"
    PAYOUT_BY_PLAN_AND_CRITERIA = "PAYOUT_BY_PLAN_AND_CRITERIA"
    COMMISSION_BY_PLAN_AND_CRITERIA = "COMMISSION_BY_PLAN_AND_CRITERIA"
    DETAILED_PAYOUT_INSIGHTS_BY_PLAN_AND_CRITERIA = (
        "DETAILED_PAYOUT_INSIGHTS_BY_PLAN_AND_CRITERIA"
    )
    STATEMENT_FOR = "STATEMENT_FOR"
    DETAILED_COMMISSION_INSIGHTS_BY_PLAN_AND_CRITERIA = (
        "DETAILED_COMMISSION_INSIGHTS_BY_PLAN_AND_CRITERIA"
    )
    DATE = "DATE"
    AMOUNT = "AMOUNT"
    COMMENTS = "COMMENTS"
    BREAKDOWN = "BREAKDOWN"


statement_translation_map = {
    "de": {  # German translation for statement specific terms (McMakler)
        StatementTranslationTerms.PAYOUT_SUMMARY.value: "Übersicht der Auszahlung",
        StatementTranslationTerms.STATEMENT_FOR.value: "Provisionsabrechnung für Periode",
        StatementTranslationTerms.PAYOUT_BY_PLAN_AND_CRITERIA.value: "Auszahlung nach Plan und Kriterien",
        StatementTranslationTerms.DETAILED_PAYOUT_INSIGHTS_BY_PLAN_AND_CRITERIA.value: "Auszahlung nach Art und Opportunity",
        StatementTranslationTerms.TOTAL_PAYOUT.value: "Gesamte Auszahlung",
        StatementTranslationTerms.DATE.value: "Datum",
        StatementTranslationTerms.AMOUNT.value: "Wert",
        StatementTranslationTerms.COMMENTS.value: "Kommentar",
        StatementTranslationTerms.BREAKDOWN.value: "Aufschlüsselung",
    },
}

default_statement_translation_map = {
    "en": {  # English translation for statement specific terms (BYOT will be applied on top of this)
        StatementTranslationTerms.PAYOUT_SUMMARY.value: "$PAYOUT Summary",
        StatementTranslationTerms.STATEMENT_FOR.value: "Statement for",
        StatementTranslationTerms.PAYOUT_BY_PLAN_AND_CRITERIA.value: "$PAYOUT by Plan and Criteria",
        StatementTranslationTerms.DETAILED_PAYOUT_INSIGHTS_BY_PLAN_AND_CRITERIA.value: "Detailed $PAYOUT Insights by Plan and Criteria",
        StatementTranslationTerms.TOTAL_PAYOUT.value: "Total $PAYOUT",
        StatementTranslationTerms.COMMISSION_BY_PLAN_AND_CRITERIA.value: "$COMMISSION by Plan and Criteria",
        StatementTranslationTerms.DETAILED_COMMISSION_INSIGHTS_BY_PLAN_AND_CRITERIA.value: "Detailed $COMMISSION Insights by Plan and Criteria",
        StatementTranslationTerms.DATE.value: "Date",
        StatementTranslationTerms.AMOUNT.value: "Amount",
        StatementTranslationTerms.COMMENTS.value: "Comments",
        StatementTranslationTerms.BREAKDOWN.value: "Breakdown",
    },
}


class CrystalTerms(Enum):
    PAYOUT_SUMMARY = "$PAYOUT Summary"
    PAYOUT = "$PAYOUT"
    OTHER_ADJUSTMENTS = "Other $ADJUSTMENTS"
    DRAW_ADJUSTMENTS = "Draw $ADJUSTMENTS"


rbac_permission_localization_map = {
    "view:dashboard": {
        "permission_name": "View dashboards page",
        "permission_description": "Users can only see the dashboards shared with them.",
    },
    "manage:dashboard": {
        "permission_name": "Create & Edit custom dashboards",
        "permission_description": "",
    },
    "delete:dashboard": {
        "permission_name": "Delete custom dashboards",
        "permission_description": "",
    },
    "view:admindashboard": {
        "permission_name": " View default admin dashboard",
        "permission_description": "",
    },
    "view:payeedashboard": {
        "permission_name": "View default payee dashboard ",
        "permission_description": "Admins who are part of $COMMISSION_lc plans can see the payee dashboard even without this permission.",
    },
    "view:databook": {
        "permission_name": "View databooks page",
        "permission_description": "Users will get read-only access to datasheets.",
    },
    "manage:databook": {
        "permission_name": "Create & Edit databooks",
        "permission_description": "Datasheet level row and column restrictions won't apply to users with this permission.",
    },
    "manage:datasheetpermissions": {
        "permission_name": "Manage datasheet permissions",
        "permission_description": "Datasheet level row and column restrictions won't apply to users with this permission.",
    },
    "manage:datasheetadjustments": {
        "permission_name": "Adjust records in datasheet",
        "permission_description": "Datasheet level row and column restrictions won't apply to users with this permission.",
    },
    "export:datasheet": {
        "permission_name": "Export datasheet",
        "permission_description": "",
    },
    "delete:datasheet": {
        "permission_name": "Delete databooks",
        "permission_description": "",
    },
    "view:commissionplan": {
        "permission_name": "View $COMMISSION_lc plans",
        "permission_description": "",
    },
    "edit:commissionplan": {
        "permission_name": "Edit $COMMISSION_lc plans",
        "permission_description": "",
    },
    "create:commissionplan": {
        "permission_name": "Create $COMMISSION_lc plans",
        "permission_description": "",
    },
    "delete:commissionplan": {
        "permission_name": "Delete published plans",
        "permission_description": "Plans in draft state can be deleted by users with 'manage $COMMISSION_lc plans' permission.",
    },
    "register:payouts": {
        "permission_name": "Register $PAYOUTS_lc & $ARREARS_lc",
        "permission_description": "",
    },
    "invalidate:payouts": {
        "permission_name": "Invalidate $PAYOUTS_lc & $ARREARS_lc",
        "permission_description": "",
    },
    "view:commissionfeed": {
        "permission_name": "View $COMMISSION_lc feed page",
        "permission_description": "",
    },
    "manage:commissionfeed": {
        "permission_name": "Manage $COMMISSION_lc feeds",
        "permission_description": "",
    },
    "view:payouts": {
        "permission_name": "View $PAYOUTS_lc page",
        "permission_description": "",
    },
    "manage:payouts": {
        "permission_name": "Manage $PAYOUTS_lc & $ARREARS_lc",
        "permission_description": "Users can lock $PAYOUTS_lc, email statements, manage approval stages.",
    },
    "view:requestapprovals": {
        "permission_name": "Request approvals",
        "permission_description": "",
    },
    "export:payouts": {
        "permission_name": "Export $PAYOUTS_lc",
        "permission_description": "",
    },
    "view:statements": {
        "permission_name": "View Statement tab on navigation bar",
        "permission_description": "",
    },
    "export:statement": {
        "permission_name": "Export statements",
        "permission_description": "",
    },
    "view:hiddencriteria": {
        "permission_name": "View hidden criteria in statements",
        "permission_description": "Users can see hidden criteria configured in $COMMISSION_lc plans on the statement.",
    },
    "manage:commissionadjustment": {
        "permission_name": "Manage $COMMISSION_lc $ADJUSTMENTS_lc",
        "permission_description": "",
    },
    "view:payoutvalueothers": {
        "permission_name": "View the $PAYOUTS_lc value of others",
        "permission_description": "",
    },
    "manage:crystal": {
        "permission_name": "Manage crystal views",
        "permission_description": "",
    },
    "view:quotas": {
        "permission_name": "View $QUOTAS_lc page",
        "permission_description": "",
    },
    "manage:quotas": {
        "permission_name": "Manages $QUOTAS_lc",
        "permission_description": "",
    },
    "manage:quotasettings": {
        "permission_name": "Manages $QUOTA_lc settings",
        "permission_description": "",
    },
    "view:hiddenquotas": {
        "permission_name": "View hidden $QUOTA_lc categories",
        "permission_description": "",
    },
    "view:draws": {
        "permission_name": "View draws page",
        "permission_description": "",
    },
    "manage:draws": {
        "permission_name": "Manage draws",
        "permission_description": "",
    },
    "view:users": {
        "permission_name": "View users page",
        "permission_description": "",
    },
    "delete:users": {
        "permission_name": "Delete Users",
        "permission_description": "",
    },
    "manage:users": {
        "permission_name": "Create & Edit Users",
        "permission_description": "",
    },
    "view:payroll": {
        "permission_name": "View Base Pay & Variable Pay",
        "permission_description": "Users can see their base pay and variable pay numbers by default. This permission lets them see payroll information of others based on their data permissions.",
    },
    "edit:payroll": {
        "permission_name": "Edit Base Pay & Variable Pay",
        "permission_description": "",
    },
    "export:users": {
        "permission_name": "Export users",
        "permission_description": "",
    },
    "allow:impersonation": {
        "permission_name": "Can login as others",
        "permission_description": "Users with current role can login as users who belong to these roles",
    },
    "view:teams": {
        "permission_name": "View teams & pods page",
        "permission_description": "",
    },
    "manage:usergroups": {
        "permission_name": "Manage user groups",
        "permission_description": "",
    },
    "manage:usercustomfield": {
        "permission_name": "Manage user custom fields",
        "permission_description": "",
    },
    "view:queries": {
        "permission_name": "View queries page",
        "permission_description": "",
    },
    "create:queries": {
        "permission_name": "Create queries",
        "permission_description": "",
    },
    "edit:queries": {
        "permission_name": "Edit queries",
        "permission_description": "",
    },
    "delete:queries": {
        "permission_name": "Delete queries",
        "permission_description": "",
    },
    "manage:config": {
        "permission_name": "Manage general configurations",
        "permission_description": "Users can modify basic account settings, notifications, customise statements and approval workflows.",
    },
    "manage:contracts": {
        "permission_name": "Manage contracts",
        "permission_description": "Users can setup integrations with contract management systems like DocuSign and manage contracts within Everstage.",
    },
    "manage:datasettings": {
        "permission_name": "Manage data & data integrations",
        "permission_description": "Users can set up integrations, custom objects, upload data to objects, view audit logs, access $COMMISSIONS_lc & data sync page.",
    },
    "manage:accountnotifications": {
        "permission_name": "Manage account notifications",
        "permission_description": "This permission allows users to manage notifications at an account level.",
    },
    "manage:roles": {
        "permission_name": "Manage roles",
        "permission_description": "Users cannot modify their own role.",
    },
    "manage:owndata": {
        "permission_name": "Modify their own data across Everstage",
        "permission_description": "Users can modify plans they belong to & their own $QUOTAS_lc, draws, $COMMISSION_lc/draw $ADJUSTMENT_lc, user information.",
    },
    "manage:agentworkbench": {
        "permission_name": "Can access agent workbench",
        "permission_description": "Users can access the chat interface to interact with AI Agents.",
    },
    "manage:autogendescription": {
        "permission_name": "Manage autogenerated descriptions",
        "permission_description": "Enables datasheet descriptions and provides option to generate descriptions for datasheets.",
    },
    "manage:commissionformulageneration": {
        "permission_name": "Manage commission formula generation",
        "permission_description": "Users can access the commission formula generation AI to create and manage formulas.",
    },
    "manage:datasheetaigeneration": {
        "permission_name": "Manage datasheet AI generation",
        "permission_description": "Users can access the datasheet AI generation to create and manage AI generated datasheets.",
    },
    "manage:reportenrich": {
        "permission_name": "Manage tags & report enrichment",
        "permission_description": "",
    },
    "manage:alladmins": {
        "permission_name": "Manage All Admins",
        "permission_description": "Users with this permission are considered as Power Admins in Everstage (for internal use).",
    },
    "manage:analytics": {
        "permission_name": "Manage analytics",
        "permission_description": "Users can access analytics portal to create and manage dashboards.",
    },
    "view:everstage": {
        "permission_name": "General permission for common api's",
        "permission_description": "",
    },
    "view:approvals": {
        "permission_name": "General permission for approval workflow APIs",
        "permission_description": "",
    },
    "manage:adminui": {
        "permission_name": "Admin UI permission",
        "permission_description": "",
    },
    "manage:approvalworkflows": {
        "permission_name": "Manage approval workflows",
        "permission_description": "",
    },
    "publish:commissionplan": {
        "permission_name": "Publish $COMMISSION_lc plans",
        "permission_description": "",
    },
    "access:helpcenter": {
        "permission_name": "Allow help center access",
        "permission_description": "Users can access help center articles and raise support tickets.",
    },
    "view:globalsearch": {
        "permission_name": "View global search",
        "permission_description": "Users can search across users, statements and other modules in Everstage.",
    },
    "view:territoryplans": {
        "permission_name": "View plans",
        "permission_description": "Users can view plans and their configurations.",
    },
    "explore:territoryplans": {
        "permission_name": "Explore plans",
        "permission_description": "Users can explore plans and their configurations.",
    },
    "edit:territoryplans": {
        "permission_name": "Edit plans",
        "permission_description": "Users can create, edit, delete, and clone plans.",
    },
    "manage:traceai": {
        "permission_name": "Enable AI Explanations in Trace",
        "permission_description": "Granting this permission allows users to view AI-generated explanations for trace values.",
    },
    "view:admincontent": {
        "permission_name": "View admin content in Academy",
        "permission_description": " Users can access content in the Academy that is admin-focussed.",
    },
    "view:payeecontent": {
        "permission_name": "View non-admin content in Academy",
        "permission_description": "Users can access content in the Academy that is not admin-focused.",
    },
}

rbac_component_localization_map = {
    "dashboard": {
        "component_display_name": "Dashboard",
    },
    "databooks": {
        "component_display_name": "Databooks",
    },
    "commission_plans": {
        "component_display_name": "$COMMISSION Plans",
    },
    "commission_feed": {
        "component_display_name": "$COMMISSION Feed",
    },
    "payouts_statements": {
        "component_display_name": "$PAYOUTS & Statements",
    },
    "crystal": {
        "component_display_name": "Crystal",
    },
    "quotas_draws": {
        "component_display_name": "$QUOTAS & Draws",
    },
    "manage_users": {
        "component_display_name": "Teams, Groups & Users",
    },
    "queries": {
        "component_display_name": "Queries",
    },
    "settings": {
        "component_display_name": "Settings",
    },
    "advanced_permissions": {
        "component_display_name": "Advanced Admin Controls",
    },
    "everstage": {
        "component_display_name": "Everstage",
    },
    "everai": {
        "component_display_name": "EverAI",
    },
    "global_search": {
        "component_display_name": "Global Search",
    },
    "territory_plans": {
        "component_display_name": "Planning",
    },
    "learning": {"component_display_name": "Learning"},
}


class CommissionTraceMessages(LocalizedEnum):
    ACTUAL_TIER_NAME = "Actual Tier Name"
    TIER_NAME = "Tier Name"
    QUOTA_EROSION = "$QUOTA_EROSION"
    CUMULATIVE_QE = "Cumulative $QUOTA_EROSION"
    QUOTA_ATTAINMENT = "$QUOTA Attainment"
    CUMULATIVE_QA = "Cumulative $QUOTA Attainment"
    QUOTA = "$QUOTA"
    TOTAL_QE = "Total $QUOTA_EROSION"
    COMMISSION = "$COMMISSION"


class DashboardMessages(Enum):
    PAYOUT_OPEN = "PAYOUT_OPEN"
    PAYOUT_LOCKED = "PAYOUT_LOCKED"
    PAYOUT_PROCESSED = "PAYOUT_PROCESSED"


class DatabookMessages(Enum):
    ADJUSTMENT_ADDED = "New $ADJUSTMENTS_lc have been added. Click on the Update Data button to view the latest data"
