# pylint: disable=unsubscriptable-object, no-member, unsupported-membership-test
import os

import graphene
from graphene_django.types import DjangoObjectType, ObjectType

from commission_engine.accessors.client_accessor import get_client
from commission_engine.models import Client
from commission_engine.utils.general_data import DATABOOK_SYNC_STRATEGY, RbacPermissions
from commission_engine.utils.report_enums import RunSettlementReport
from everstage_infra.aws_infra.constants.environments import ENVIRONMENT
from everstage_infra.aws_infra.ecs import is_prod_env
from interstage_project.auth_utils import permission_required
from spm.accessors.config_accessors.client_config_accessor import ClientConfigAccessor
from spm.models.config_models.client_config_models import ClientConfig
from spm.services.analytics_default_dashboard import DefaultDashboardStatus
from spm.services.config_services.client_config_services import (
    get_profile_card_config,
    get_query_setting_config,
)

CRYSTAL_CUSTOM_PERIODS_DEFAULT = 6


class BasicMetaInfo(ObjectType):
    updated_at = graphene.Field(graphene.String)
    updated_by = graphene.Field(graphene.String)

    def resolve_updated_at(self, info):
        return self["updated_at"] if "updated_at" in self else None

    def resolve_updated_by(self, info):
        return self["updated_by"] if "updated_by" in self else None


class MetaInfo(ObjectType):
    is_live = graphene.Field(graphene.Boolean)
    created_at = graphene.Field(graphene.String)
    is_test = graphene.Field(graphene.Boolean)
    support_email = graphene.Field(graphene.String)
    delete_notified = graphene.Field(graphene.Boolean)
    password = graphene.Field(graphene.String)
    updated_at = graphene.Field(graphene.String)
    updated_by = graphene.Field(graphene.String)

    def resolve_is_live(self, info):
        return self["is_live"] if "is_live" in self else None

    def resolve_created_at(self, info):
        return self["created_at"] if "created_at" in self else None

    def resolve_is_test(self, info):
        return self["is_test"] if "is_test" in self else None

    def resolve_support_email(self, info):
        if (
            "super_admin_user" in self
            and self["super_admin_user"]
            and "email" in self["super_admin_user"]
        ):
            return self["super_admin_user"]["email"]
        else:
            return None

    def resolve_delete_notified(self, info):
        return self.get("delete_notified", False)

    def resolve_password(self, info):
        if (
            "super_admin_user" in self
            and self["super_admin_user"]
            and "password" in self["super_admin_user"]
        ):
            return self["super_admin_user"]["password"]
        else:
            return None

    def resolve_updated_at(self, info):
        return self["updated_at"] if "updated_at" in self else None

    def resolve_updated_by(self, info):
        return self["updated_by"] if "updated_by" in self else None


class ClientFeatures(ObjectType):
    hide_categories = graphene.List(graphene.String)
    freeze_date = graphene.Field(graphene.Int)
    show_commission_percent = graphene.Field(graphene.Boolean)
    upstream_etl = graphene.Field(graphene.Boolean)
    commission_etl = graphene.Field(graphene.Boolean)
    show_etl_technique_options = graphene.Field(graphene.Boolean)
    show_commission_buddy = graphene.Field(graphene.Boolean)
    show_territory_plan = graphene.Field(graphene.Boolean)
    show_superset_dashboard = graphene.Field(graphene.Boolean)
    show_return_v1_button = graphene.Field(graphene.Boolean)
    hide_payee_dashboard = graphene.Field(graphene.Boolean)
    show_salesforce_integration = graphene.Field(graphene.Boolean)
    show_statements_v2 = graphene.Field(graphene.Boolean)
    crystal_version = graphene.Field(graphene.String)
    enable_ever_comparison = graphene.Field(graphene.Boolean)
    databook_sync_strategy = graphene.Field(graphene.String)
    manager_rollup_ed = graphene.Field(graphene.Boolean)
    show_approval_feature = graphene.Field(graphene.Boolean)
    expose_comm_reports_in_plan = graphene.Field(graphene.Boolean)
    show_roles = graphene.Field(graphene.Boolean)
    show_datasheet_permission = graphene.Field(graphene.Boolean)
    show_custom_object_permission = graphene.Field(graphene.Boolean)
    is_new_frozen_payroll_etl = graphene.Field(graphene.Boolean)
    show_simulation_v2 = graphene.Field(graphene.Boolean)
    enable_concurrent_sessions = graphene.Field(graphene.Boolean)
    datasheet_v2 = graphene.Field(graphene.Boolean)
    show_datasheet_v2 = graphene.Field(graphene.Boolean)
    datasheet_builder = graphene.Field(graphene.Boolean)
    enable_support_user_access = graphene.Field(graphene.Boolean)
    enable_tsar_webapp_custom_roles = graphene.Field(graphene.Boolean)
    is_secure_admin_ui_auth0_user_mgmt = graphene.Field(graphene.Boolean)
    chrome_extension_enabled = graphene.Field(graphene.Boolean)
    enable_everai = graphene.Field(graphene.Boolean)
    allow_adjustments_to_frozen_commission = graphene.Field(graphene.Boolean)
    salesforce_env = graphene.Field(graphene.String)
    show_chatgpt = graphene.Field(graphene.Boolean)
    show_payout_table_breakdown = graphene.Field(graphene.Boolean)
    isolated_snowflake_database = graphene.Field(graphene.Boolean)
    plan_summary_model = graphene.Field(graphene.String)
    evaluation_mode = graphene.Field(graphene.String)
    profile_picture_permission = graphene.Field(graphene.String)
    warn_on_unlock = graphene.Field(graphene.String)
    expressionbox_version = graphene.Field(graphene.String)
    databook_expressionbox_version = graphene.Field(graphene.String)
    payout_snapshot_etl = graphene.Field(graphene.Boolean)
    settlement_v2 = graphene.Field(graphene.Boolean)
    edit_locked_quota = graphene.Field(graphene.Boolean)
    run_settlement_report = graphene.Field(graphene.String)
    show_advanced_filter = graphene.Field(graphene.Boolean)
    show_statements_pdf = graphene.Field(graphene.Boolean)
    statements_export_in_lock_email = graphene.Field(graphene.Boolean)
    upstream_etl_version = graphene.Field(graphene.String)
    custom_calendar = graphene.Field(graphene.Boolean)
    commission_plan_version = graphene.Field(graphene.String)
    show_forecast = graphene.Field(graphene.Boolean)
    quota_effective_dated = graphene.Field(graphene.Boolean)
    allow_quota_settings_override = graphene.Field(graphene.Boolean)
    optimized_payout_snapshot = graphene.Field(graphene.Boolean)
    snapshot_data_for_statements = graphene.Field(graphene.Boolean)
    crm_hyperlinks = graphene.Field(graphene.Boolean)
    help_doc_user_role = graphene.List(graphene.String)
    documentation_url = graphene.Field(graphene.String)
    enable_hris_integration = graphene.Field(graphene.Boolean)
    split_summation_to_li = graphene.Field(graphene.Boolean)
    show_metrics = graphene.Field(graphene.Boolean)
    avoid_iframe_in_contracts = graphene.Field(graphene.Boolean)
    insert_meta_data_to_vec_db = graphene.Field(graphene.Boolean)
    take_ds_snapshot = graphene.Field(graphene.Boolean)
    use_multi_engine_stormbreaker = graphene.Field(graphene.Boolean)
    mfa = graphene.Field(graphene.JSONString)
    object_knowledge_date_query_strategy = graphene.Field(graphene.String)
    global_search = graphene.Field(graphene.Boolean)
    show_get_user_property_commission = graphene.Field(graphene.Boolean)
    enable_custom_workflows = graphene.Field(graphene.Boolean)
    enable_rounding_in_tier_functions = graphene.Field(graphene.Boolean)
    enable_contract_permissions = graphene.Field(graphene.Boolean)
    show_data_sources_v2 = graphene.Field(
        graphene.Boolean
    )  # TODO : EX : MyClientAndBaseCurrencySymbol gql fails because this flag is stored as string in backend(cid: 10036 in unit-test-temp)
    fivetran_sync = graphene.Field(graphene.Boolean)
    crystal_custom_calendar_future_periods = graphene.Field(graphene.Int)
    analytics_default_dashboard_status = graphene.Field(graphene.String)
    use_aggrid_for_pdf_export = graphene.Field(graphene.Boolean)
    enable_custom_theme = graphene.Field(graphene.Boolean)
    enable_sidebar_v3 = graphene.Field(graphene.Boolean)
    enable_multi_language_support = graphene.Field(graphene.Boolean)
    show_g2_review_form = graphene.Field(graphene.String)
    modules = graphene.List(graphene.String)
    is_commission_adjustment_v2_enabled = graphene.Field(graphene.Boolean)
    enable_plan_exclusion = graphene.Field(graphene.Boolean)
    allow_annual_quota_effective_dated = graphene.Field(graphene.Boolean)
    notification_v2 = graphene.Field(graphene.Boolean)
    allow_only_admins_to_modify_user_name = graphene.Field(graphene.Boolean)
    run_sync_for_multiple_period = graphene.Field(graphene.Boolean)
    is_auto_enrich_report = graphene.Field(graphene.Boolean)
    async_export_datasheet = graphene.Field(graphene.Boolean)
    upload_excel_files_in_custom_object = graphene.Field(graphene.Boolean)
    can_avoid_locking_with_pending_changes = graphene.Field(graphene.Boolean)
    avoid_concurrent_register_payment = graphene.Field(graphene.Boolean)
    allow_csv_upload_bulk_payment_register = graphene.Field(graphene.Boolean)
    crystal_calc_fields_override_logic_v2 = graphene.Field(graphene.Boolean)
    show_observable_notification = graphene.Field(graphene.Boolean)
    is_show_dangling_adjustments_enabled = graphene.Field(graphene.Boolean)

    def resolve_hide_categories(self, info):
        return self["hide_categories"] if "hide_categories" in self else None

    def resolve_freeze_date(self, info):
        return self["freeze_date"] if "freeze_date" in self else None

    def resolve_help_doc_user_role(self, info):
        return self["help_doc_user_role"] if "help_doc_user_role" in self else []

    def resolve_documentation_url(self, info):
        return self["documentation_url"] if "documentation_url" in self else ""

    def resolve_show_commission_percent(self, info):
        return (
            self["show_commission_percent"]
            if "show_commission_percent" in self
            else False
        )

    def resolve_upstream_etl(self, info):
        return self["is_upstream_etl"] if "is_upstream_etl" in self else True

    def resolve_commission_etl(self, info):
        return self["is_db_comm_etl"] if "is_db_comm_etl" in self else True

    def resolve_show_etl_technique_options(self, info):
        return (
            self["show_etl_technique_options"]
            if "show_etl_technique_options" in self
            else False
        )

    def resolve_show_commission_buddy(self, info):
        return (
            self["show_commission_buddy"] if "show_commission_buddy" in self else True
        )

    def resolve_show_territory_plan(self, info):
        return self["show_territory_plan"] if "show_territory_plan" in self else False

    def resolve_show_salesforce_integration(self, info):
        return self.get("show_salesforce_integration", True)

    def resolve_show_superset_dashboard(self, info):
        return (
            self["show_superset_dashboard"]
            if "show_superset_dashboard" in self
            else False
        )

    def resolve_show_return_v1_button(self, info):
        return (
            self["show_return_v1_button"] if "show_return_v1_button" in self else False
        )

    def resolve_salesforce_env(self, info):
        return self.get("salesforce_env", "production")

    def resolve_hide_payee_dashboard(self, info):
        return self.get("hide_payee_dashboard", False)

    def resolve_show_statements_v2(self, info):
        return self.get("show_statements_v2", True)  # enabling it as default

    def resolve_crystal_version(self, info):
        return self.get("crystal_version", "3")

    def resolve_enable_ever_comparison(self, info):
        return self.get("enable_ever_comparison", True)

    def resolve_databook_sync_strategy(self, info):
        return self.get(
            "databook_sync_strategy", DATABOOK_SYNC_STRATEGY.SNOWFLAKE.value
        )

    def resolve_manager_rollup_ed(self, info):
        return self.get("manager_rollup_ed", False)

    def resolve_show_approval_feature(self, info):
        return self.get("show_approval_feature", False)

    def resolve_expose_comm_reports_in_plan(self, info):
        return self.get("expose_comm_reports_in_plan", False)

    def resolve_show_roles(self, info):
        return self.get("show_roles", True)

    def resolve_show_datasheet_permission(self, info):
        return self.get("show_datasheet_permission", True)

    def resolve_show_custom_object_permission(self, info):
        return self.get("show_custom_object_permission", True)

    def resolve_is_new_frozen_payroll_etl(self, info):
        return self.get("is_new_frozen_payroll_etl", True)

    def resolve_show_simulation_v2(self, info):
        return self.get("show_simulation_v2", True)

    def resolve_enable_concurrent_sessions(self, info):
        return self.get("enable_concurrent_sessions", False)

    def resolve_datasheet_v2(self, info):
        return self.get("datasheet_v2", False)

    def resolve_show_datasheet_v2(self, info):
        # Do not allow to switch back to v1 for staging and local env
        if os.getenv("ENV") in [
            ENVIRONMENT["STAGING"],
            ENVIRONMENT["LOCAL"],
            ENVIRONMENT["SANDBOX"],
        ] and self.get("show_data_sources_v2", False):
            return False
        return self.get("show_datasheet_v2", False)

    def resolve_datasheet_builder(self, info):
        return self.get("datasheet_builder", False)

    def resolve_enable_support_user_access(self, info):
        return self.get("enable_support_user_access", is_prod_env())

    def resolve_enable_tsar_webapp_custom_roles(self, info):
        return self.get("enable_tsar_webapp_custom_roles", False)

    def resolve_is_secure_admin_ui_auth0_user_mgmt(self, info):
        return self.get("is_secure_admin_ui_auth0_user_mgmt", False)

    def resolve_chrome_extension_enabled(self, info):
        return self.get("chrome_extension_enabled", False)

    def resolve_enable_everai(self, info):
        return self.get("enable_everai", False)

    def resolve_allow_adjustments_to_frozen_commission(self, info):
        return self.get("allow_adjustments_to_frozen_commission", False)

    def resolve_show_chatgpt(self, info):
        return self.get("show_chatgpt", False)

    def resolve_show_payout_table_breakdown(self, info):
        return self.get("show_payout_table_breakdown", False)

    def resolve_isolated_snowflake_database(self, info):
        return self.get("isolated_snowflake_database", False)

    def resolve_plan_summary_model(self, info):
        return self.get("plan_summary_model", "gpt-3")

    def resolve_evaluation_mode(self, info):
        return self.get("evaluation_mode", "vectorize")

    def resolve_profile_picture_permission(self, info):
        return self.get("profile_picture_permission", "ALL")

    def resolve_warn_on_unlock(self, info):
        return self.get("warn_on_unlock", "NONE")

    def resolve_expressionbox_version(self, info):
        return self.get("expressionbox_version", "v1")

    def resolve_databook_expressionbox_version(self, info):
        return self.get("databook_expressionbox_version", "v1")

    def resolve_payout_snapshot_etl(self, info):
        return self.get("payout_snapshot_etl", True)

    def resolve_settlement_v2(self, info):
        return self.get("settlement_v2", False)

    def resolve_edit_locked_quota(self, info):
        return self.get("edit_locked_quota", False)

    def resolve_run_settlement_report(self, info):
        return self.get("run_settlement_report", RunSettlementReport.ALWAYS.value)

    def resolve_advanced_filter(self, info):
        return self.get("show_advanced_filter", True) and (
            self.get("ds_etl_strategy", "snowflake") == "snowflake"
        )

    def resolve_show_statements_pdf(self, info):
        return self.get("show_statements_pdf", True)

    def resolve_statements_export_in_lock_email(self, info):
        return self.get("statements_export_in_lock_email", False)

    def resolve_upstream_etl_version(self, info):
        return self.get("upstream_etl_version", "v1")

    def resolve_custom_calendar(self, info):
        return self.get("custom_calendar", False)

    def resolve_commission_plan_version(self, info):
        return self.get("commission_plan_version", "v2")

    def resolve_show_forecast(self, info):
        return self.get("show_forecast", False)

    def resolve_quota_effective_dated(self, info):
        return self.get("quota_effective_dated", False)

    def resolve_allow_quota_settings_override(self, info):
        return self.get("allow_quota_settings_override", False)

    def resolve_crm_hyperlinks(self, info) -> bool:
        return self.get("crm_hyperlinks", True)

    def resolve_enable_hris_integration(self, info):
        return self.get("enable_hris_integration", False)

    def resolve_use_aggrid_for_pdf_export(self, info):
        return self.get("use_aggrid_for_pdf_export", True)

    def resolve_split_summation_to_li(self, info):
        return self.get("split_summation_to_li", False)

    def resolve_show_metrics(self, info):
        return self.get("show_metrics", False)

    def resolve_avoid_iframe_in_contracts(self, info):
        return self.get("avoid_iframe_in_contracts", False)

    def resolve_insert_meta_data_to_vec_db(self, info):
        return self.get("insert_meta_data_to_vec_db", False)

    def resolve_take_ds_snapshot(self, info):
        return self.get("take_ds_snapshot", True)

    def resolve_use_multi_engine_stormbreaker(self, info):
        return self.get("use_multi_engine_stormbreaker", False)

    def resolve_mfa(self, info):
        return self.get("mfa", None)

    def resolve_object_knowledge_date_query_strategy(self, info):
        return self.get("object_knowledge_date_query_strategy", "postgres")

    def resolve_global_search(self, info):
        return self.get("global_search", {}).get("enabled", False)

    def resolve_optimized_payout_snapshot(self, info):
        return self.get("optimized_payout_snapshot", False)

    def resolve_snapshot_data_for_statements(self, info):
        return self.get("snapshot_data_for_statements", False)

    def resolve_show_get_user_property_commission(self, info):
        return self.get("show_get_user_property_commission", True)

    def resolve_enable_custom_workflows(self, info):
        return self.get("enable_custom_workflows", False)

    def resolve_enable_rounding_in_tier_functions(self, info):
        return self.get("enable_rounding_in_tier_functions", False)

    def resolve_show_data_sources_v2(
        self, info
    ):  # TODO : Find the cause why this flag was stored as string
        value = self.get("show_data_sources_v2", False)
        if isinstance(value, str):
            return value.lower() == "true"
        return bool(value)

    def resolve_enable_contract_permissions(self, info):
        return self.get("enable_contract_permissions", False)

    def resolve_fivetran_sync(self, info):
        return self.get("fivetran_sync", False)

    def resolve_crystal_custom_calendar_future_periods(self, info):
        return self.get(
            "crystal_custom_calendar_future_periods", CRYSTAL_CUSTOM_PERIODS_DEFAULT
        )

    def resolve_analytics_default_dashboard_status(self, info):
        return self.get(
            "analytics_default_dashboard_status", DefaultDashboardStatus.NONE.value
        )

    def resolve_enable_custom_theme(self, info):
        return self["enable_custom_theme"] if "enable_custom_theme" in self else False

    def resolve_enable_sidebar_v3(self, info):
        return self["enable_sidebar_v3"] if "enable_sidebar_v3" in self else False

    def resolve_enable_multi_language_support(self, info):
        return (
            self["enable_multi_language_support"]
            if "enable_multi_language_support" in self
            else False
        )

    def resolve_enable_plan_exclusion(self, info):
        return self.get("enable_plan_exclusion", False)

    def resolve_show_g2_review_form(self, info):
        return self.get("show_g2_review_form", "Off")

    def resolve_modules(self, info):
        return self.get("modules", [])

    def resolve_is_commission_adjustment_v2_enabled(self, info):
        return self.get("is_commission_adjustment_v2_enabled", False)

    def resolve_allow_annual_quota_effective_dated(self, info):
        return self.get("allow_annual_quota_effective_dated", False)

    def resolve_notification_v2(self, info):
        return self.get("notification_v2", False)

    def resolve_crystal_calc_fields_override_logic_v2(self, info):
        return self.get("crystal_calc_fields_override_logic_v2", False)

    def resolve_allow_only_admins_to_modify_user_name(self, info):
        return self.get("allow_only_admins_to_modify_user_name", False)

    def resolve_is_auto_enrich_report(self, info):
        return self.get("is_auto_enrich_report", False)

    def resolve_async_export_datasheet(self, info):
        return self.get("async_export_datasheet", False)

    def resolve_upload_excel_files_in_custom_object(self, info):
        return self.get("upload_excel_files_in_custom_object", False)

    def resolve_can_avoid_locking_with_pending_changes(self, info):
        return self.get("can_avoid_locking_with_pending_changes", False)

    def resolve_avoid_concurrent_register_payment(self, info):
        return self.get("avoid_concurrent_register_payment", False)

    def resolve_allow_csv_upload_bulk_payment_register(self, info):
        return self.get("allow_csv_upload_bulk_payment_register", False)

    def resolve_is_show_dangling_adjustments_enabled(self, info):
        return self.get("is_show_dangling_adjustments_enabled", False)


class ClientFeaturesAdmin(ClientFeatures):
    delete_approvers = graphene.Field(graphene.List(graphene.String))
    subscription_plan = graphene.Field(graphene.String)

    def resolve_delete_approvers(self, info):
        return self.get("delete_approvers", [])

    def resolve_subscription_plan(self, info):
        return self.get("subscription_plan", "BASIC")


class ClientType(DjangoObjectType):
    fiscal_start_month_zero = graphene.Field(graphene.Int)
    meta_info = graphene.Field(BasicMetaInfo)
    client_features = graphene.Field(ClientFeatures)

    def resolve_meta_info(self, info):
        return self.meta_info

    def resolve_fiscal_start_month_zero(self, info):
        return self.fiscal_start_month - 1 if self.fiscal_start_month else 0

    def resolve_client_features(self, info):
        return self.client_features

    class Meta:
        model = Client


class ClientAdminType(DjangoObjectType):
    fiscal_start_month_zero = graphene.Field(graphene.Int)
    meta_info = graphene.Field(MetaInfo)
    client_features = graphene.Field(ClientFeaturesAdmin)
    customer_id = graphene.Field(graphene.String)

    def resolve_meta_info(self, info):
        return self.meta_info

    def resolve_fiscal_start_month_zero(self, info):
        return self.fiscal_start_month - 1 if self.fiscal_start_month else 0

    def resolve_client_features(self, info):
        return self.client_features

    def resolve_customer_id(self, info):
        return (
            info.context.customer_id_loader.load(self.crm_company_id)
            if self.crm_company_id
            else None
        )

    class Meta:
        model = Client


class DateFxPair(ObjectType):
    date = graphene.Field(graphene.String)
    fx_rate = graphene.Field(graphene.Float)

    def resolve_date(self, info):
        return self[0]

    def resolve_fx_rate(self, info):
        return self[1]


class FxRate(ObjectType):
    currency = graphene.Field(graphene.String)
    fx_rates_by_date = graphene.List(DateFxPair)

    def resolve_currency(self, info):
        return self["currency"]

    def resolve_fx_rates_by_date(self, info):
        return self["fx_rates"].items()


class FxRates(DjangoObjectType):
    fx_rates_by_currency = graphene.List(FxRate)

    def resolve_fx_rates_by_currency(self, info):
        return self.value

    class Meta:
        model = ClientConfig


class RevenueLeaderType(DjangoObjectType):
    class Meta:
        model = ClientConfig


class ClientQuery(object):
    my_client = graphene.Field(ClientType)
    client_fx_rates = graphene.Field(FxRates)
    revenue_leader = graphene.List(RevenueLeaderType)
    client_query_setting = graphene.Field(graphene.JSONString)

    @permission_required(RbacPermissions.VIEW_EVERSTAGE.value)
    def resolve_my_client(self, info, **kwargs):
        client_id = info.context.client_id
        return get_client(client_id)

    @permission_required(RbacPermissions.MANAGE_CONFIG.value)
    def resolve_client_fx_rates(self, info, **kwargs):
        client_id = info.context.client_id
        return ClientConfigAccessor(client_id).get_fx_rates()

    @permission_required(
        [
            RbacPermissions.MANAGE_CONFIG.value,
            RbacPermissions.VIEW_ADMINDASHBOARD.value,
        ]
    )
    def resolve_revenue_leader(self, info, **kwargs):
        """Returns the revenue leader. Used in admin settings and admin dashboard"""
        client_id = info.context.client_id
        return ClientConfigAccessor(client_id).get_revenue_leader()

    @permission_required(
        [RbacPermissions.VIEW_QUERIES.value, RbacPermissions.MANAGE_CONFIG.value]
    )
    def resolve_client_query_setting(self, info, **kwargs):
        client_id = info.context.client_id
        return get_query_setting_config(client_id)


class ClientProfileCardConfig:
    client_profile_fields = graphene.Field(graphene.JSONString)

    @permission_required(
        [RbacPermissions.MANAGE_CONFIG.value, RbacPermissions.VIEW_STATEMENTS.value]
    )
    def resolve_client_profile_fields(self, info, **kwargs):
        client_id = info.context.client_id
        return get_profile_card_config(client_id)
