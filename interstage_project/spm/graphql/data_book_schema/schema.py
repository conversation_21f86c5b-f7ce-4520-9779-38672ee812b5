# pylint: disable=unsubscriptable-object, no-member, unsupported-membership-test
import json
import traceback

import graphene
from djangorestframework_camel_case.util import camelize
from graphene.types import generic
from graphene_django.types import DjangoObjectType, ObjectType

import commission_engine.utils.report_utils as report_utils
from commission_engine.accessors.custom_object_accessor import (
    CustomObjectAccessor,
    CustomObjectVariableAccessor,
)
from commission_engine.accessors.databook_accessor import (
    DatabookAccessor,
    DatasheetAccessor,
    DatasheetFilterAccessor,
    DatasheetVariableAccessor,
    DSFilterOperatorsAccessor,
)
from commission_engine.accessors.ever_object_accessor import (
    EverO<PERSON>Accessor,
    EverObjectVariableAccessor,
)
from commission_engine.models.databook_models import (
    Databook,
    Datasheet,
    DatasheetFilter,
    DatasheetVariable,
    DSFilterOperators,
)
from commission_engine.models.skd_pkd_map_models import DbkdPkdMap
from commission_engine.services.datasheet_data_services.datasheet_retrieval_service import (
    fetch_adjustment_data,
    fetch_datasheet_data,
    fetch_json_filtered_datasheet_data,
)
from commission_engine.services.ever_object_service import (
    get_all_objects as get_all_report_objects,
)
from commission_engine.services.hris_integration_services.user_sheet_service import (
    UserSheetDataService,
)
from commission_engine.utils.general_data import (
    RbacPermissions,
    SegmentEvents,
    SegmentProperties,
)
from interstage_project.auth_utils import permission_required
from interstage_project.utils import add_log_context
from spm.accessors.commission_plan_accessor import PlanCriteriaAccessor
from spm.accessors.variable_accessor import VariableDataTypeAccessor
from spm.models.variable_models import VariableDataType
from spm.services.analytics_services.analytics_service import CoreAnalytics
from spm.services.custom_object_services.co_permission_services import (
    get_objects_excluded_for_user,
)
from spm.services.databook_services import datasheet_source_details


class SourceVariableType(ObjectType):
    system_name = graphene.Field(graphene.String)
    display_name = graphene.Field(graphene.String)
    data_type_id = graphene.Field(graphene.Int)
    source_cf_meta_data = generic.GenericScalar()

    def resolve_id(self, info):
        return self["system_name"]

    def resolve_name(self, info):
        return self["display_name"]

    def resolve_data_type_id(self, info):
        return self["data_type_id"]

    def resolve_source_cf_meta_data(self, info):
        return (
            camelize(self["source_cf_meta_data"])
            if self.get("source_cf_meta_data")
            else None
        )


class DatasheetVariablesType(DjangoObjectType):
    meta_data = generic.GenericScalar()

    def resolve_meta_data(self, info):
        if self.meta_data:
            return camelize(self.meta_data)
        return None

    class Meta:
        model = DatasheetVariable


class DatasheetType(DjangoObjectType):
    id = graphene.Field(graphene.String)
    name = graphene.Field(graphene.String)
    pk = graphene.List(graphene.String)
    order = graphene.Field(graphene.Int)
    # transformation_spec = graphene.List(graphene.JSONString)
    variables = graphene.List(DatasheetVariablesType)
    variables_with_permission = graphene.List(DatasheetVariablesType)
    source_databook_id = graphene.Field(graphene.String)

    def resolve_id(self, info):
        return self.datasheet_id

    def resolve_name(self, info):
        return self.name

    def resolve_pk(self, info):
        return self.primary_key

    def resolve_order(self, info):
        return self.order

    # def resolve_transformation_spec(self, info):
    #     return self.transformation_spec

    def resolve_variables(self, info):
        return (
            info.context.datasheet_variable_loader.load(self.datasheet_id)
            if self.datasheet_id is not None
            else None
        )

    def resolve_variables_with_permission(self, info):
        return (
            info.context.datasheet_variable_with_permission_loader.load(
                self.datasheet_id
            )
            if self.datasheet_id is not None
            else None
        )

    class Meta:
        model = Datasheet


class DbkdPkdMapType(DjangoObjectType):
    class Meta:
        model = DbkdPkdMap


class DatabookType(DjangoObjectType):
    datasheet = graphene.List(DatasheetType)
    last_update = graphene.Field(DbkdPkdMapType)

    def resolve_datasheet(self, info):
        return (
            info.context.datasheet_for_db_loader.load(self.databook_id)
            if self.databook_id is not None
            else None
        )

    def resolve_last_update(self, info):
        return (
            info.context.dbkd_for_db_loader.load(self.databook_id)
            if self.databook_id is not None
            else None
        )

    class Meta:
        model = Databook


class SourceObjectType(ObjectType):
    id = graphene.Field(graphene.String)
    name = graphene.Field(graphene.String)
    pk = graphene.List(graphene.String)
    order = graphene.Field(graphene.Int)
    data_origin = graphene.Field(graphene.String)

    def resolve_id(self, info):
        return self["custom_object_id"]

    def resolve_name(self, info):
        return self["name"]

    def resolve_pk(self, info):
        return self["primary_key"]

    def resolve_order(self, info):
        return 0

    def resolve_data_origin(self, info):
        return "custom_object"


class ReportObjectType(ObjectType):
    id = graphene.Field(graphene.String)
    name = graphene.Field(graphene.String)
    pk = graphene.List(graphene.String)
    order = graphene.Field(graphene.Int)
    data_origin = graphene.Field(graphene.String)

    def resolve_id(self, info):
        return self["id"]

    def resolve_name(self, info):
        return self.get("name")

    def resolve_pk(self, info):
        return self["primary_key"]

    def resolve_order(self, info):
        return 0

    def resolve_data_origin(self, info):
        return self.get("data_origin")


class ReportVariableDataType(DjangoObjectType):
    class Meta:
        model = VariableDataType


class EverObjectVariableType(ObjectType):
    custom_object_id = graphene.Field(graphene.String)
    display_name = graphene.Field(graphene.String)
    knowledge_begin_date = graphene.Field(graphene.DateTime)
    system_name = graphene.Field(graphene.String)
    data_type = graphene.Field(ReportVariableDataType)

    @permission_required(RbacPermissions.VIEW_DATABOOK.value)
    def resolve_data_type(self, info, **kwargs):
        return VariableDataTypeAccessor().get_type_by_id(self.data_type_id)

    def resolve_custom_object_id(self, info):
        return self.ever_object_id

    def resolve_display_name(self, info):
        return self.display_name

    def resolve_knowledge_begin_date(self, info):
        return self.knowledge_begin_date

    def resolve_system_name(self, info):
        return self.system_name


class EverObjectType(ObjectType):
    custom_object_id = graphene.Field(graphene.String)
    name = graphene.Field(graphene.String)
    knowledge_begin_date = graphene.Field(graphene.DateTime)
    primary_key = graphene.Field(graphene.JSONString)
    snapshot_key = graphene.Field(graphene.JSONString)
    created_at = graphene.Field(graphene.DateTime)
    created_by = graphene.Field(graphene.String)
    ordered_columns = graphene.List(graphene.String)
    access_token_config_id = graphene.Field(graphene.String)
    custom_object_variables = graphene.List(EverObjectVariableType)

    @permission_required(
        [RbacPermissions.VIEW_DATABOOK.value, RbacPermissions.MANAGE_DATASETTINGS.value]
    )
    def resolve_custom_object_variables(self, info, **kwargs):
        return EverObjectVariableAccessor().get_ever_object_variable_by_id(
            self.ever_object_id
        )

    def resolve_custom_object_id(self, info):
        return self.ever_object_id

    def resolve_name(self, info):
        return self.name

    def resolve_knowledge_begin_date(self, info):
        return self.knowledge_begin_date

    def resolve_primary_key(self, info):
        return self.primary_key

    def resolve_snapshot_key(self, info):
        return []

    def resolve_created_at(self, info):
        return None

    def resolve_created_by(self, info):
        return None

    def resolve_ordered_columns(self, info):
        return None

    def resolve_access_token_config_id(self, info):
        return None


class DatasheetDataType(ObjectType):
    data = graphene.Field(graphene.String)
    adjustment_data = graphene.Field(graphene.String)
    data_count = graphene.Field(graphene.Int)
    pivot_columns = graphene.Field(graphene.String)
    # page_num = graphene.Field(graphene.Int)
    # total_pages = graphene.Field(graphene.Int)

    def resolve_data(self, info):
        data = json.dumps(
            self.get("data") if isinstance(self, dict) else self, default=str
        )
        return data

    def resolve_adjustment_data(self, info):
        adjustment_data = json.dumps(
            self.get("adjustment_data") if isinstance(self, dict) else self,
            default=str,
        )
        return adjustment_data

    def resolve_pivot_columns(self, info):
        pivot_columns = json.dumps(
            self.get("pivot_columns") if isinstance(self, dict) else self,
            default=str,
        )
        return pivot_columns

    def resolve_data_count(self, info):
        return self.get("data_count") if isinstance(self, dict) else None

    def resolve_page_num(self, info):
        return self.get("page_num") if isinstance(self, dict) else None

    def resolve_total_pages(self, info):
        return self.get("total_pages") if isinstance(self, dict) else None


class UsersheetDataType(ObjectType):
    data = graphene.Field(graphene.String)
    adjustment_data = graphene.Field(graphene.String)
    existing_users_count = graphene.Field(graphene.Int)
    new_users_count = graphene.Field(graphene.Int)
    pivot_columns = graphene.Field(graphene.String)
    # page_num = graphene.Field(graphene.Int)
    # total_pages = graphene.Field(graphene.Int)

    def resolve_data(self, info):
        data = json.dumps(
            self.get("data") if isinstance(self, dict) else self, default=str
        )
        return data

    def resolve_adjustment_data(self, info):
        adjustment_data = json.dumps(
            self.get("adjustment_data") if isinstance(self, dict) else self,
            default=str,
        )
        return adjustment_data

    def resolve_pivot_columns(self, info):
        pivot_columns = json.dumps(
            self.get("pivot_columns") if isinstance(self, dict) else self,
            default=str,
        )
        return pivot_columns

    def resolve_data_count(self, info):
        return self.get("data_count") if isinstance(self, dict) else None

    def resolve_page_num(self, info):
        return self.get("page_num") if isinstance(self, dict) else None

    def resolve_total_pages(self, info):
        return self.get("total_pages") if isinstance(self, dict) else None


class AdjustmentDataType(ObjectType):
    adjustment_data = graphene.Field(graphene.String)
    dangling_rows = graphene.Field(graphene.String)

    def resolve_adjustment_data(self, info):
        adjustment_data = json.dumps(
            self.get("adjustment_data") if isinstance(self, dict) else self,
            default=str,
        )
        return adjustment_data

    def resolve_dangling_rows(self, info):
        dangling_rows = json.dumps(
            self.get("dangling_rows") if isinstance(self, dict) else self,
            default=str,
        )
        return dangling_rows


class DataBookQuery(object):
    source_variables = graphene.List(
        SourceVariableType,
        source_type=graphene.String(),
        databook_id=graphene.String(),
        object_id=graphene.String(),
        source_databook_id=graphene.String(),
    )
    all_databook_details = graphene.List(DatabookType)
    databook_details = graphene.Field(
        DatabookType,
        databook_id=graphene.String(),
        expression_box_version=graphene.String(),
    )
    all_source_objects = graphene.List(SourceObjectType)
    datasheet_data = graphene.Field(
        DatasheetDataType,
        databook_id=graphene.String(),
        databook_name=graphene.String(),
        datasheet_id=graphene.String(),
        page_number=graphene.Int(),
        page_size=graphene.Int(),
        session_kd=graphene.String(),
        filters=graphene.List(graphene.JSONString),
        pivots=graphene.JSONString(),
        reader=graphene.String(),
    )
    usersheet_data = graphene.Field(
        UsersheetDataType,
        databook_id=graphene.String(),
        databook_name=graphene.String(),
        datasheet_id=graphene.String(),
        page_number=graphene.Int(),
        page_size=graphene.Int(),
        session_kd=graphene.String(),
        data_filters=graphene.List(graphene.JSONString),
        status_filter=graphene.String(),
        view_type=graphene.String(),
        is_grouped_view=graphene.Boolean(),
    )
    adjustment_data = graphene.Field(
        AdjustmentDataType,
        databook_id=graphene.String(),
        databook_name=graphene.String(),
        datasheet_id=graphene.String(),
    )
    databook_data = graphene.Field(graphene.String, databook_id=graphene.String())
    datasheet = graphene.Field(
        DatasheetType,
        databook_id=graphene.String(),
        datasheet_id=graphene.String(),
        expression_box_version=graphene.String(),
    )
    all_datasheet_data_with_adjustments = graphene.Field(
        DatasheetDataType,
        databook_id=graphene.String(),
        datasheet_id=graphene.String(),
        session_kd=graphene.String(),
    )
    all_criteria_databook = graphene.List(DatabookType)
    all_report_objects = graphene.List(ReportObjectType)
    report_objects_for_integration = graphene.List(EverObjectType)
    datasheet_order = graphene.Field(DatabookType, databook_id=graphene.String())
    databooks_count_with_search = graphene.Int(search_term=graphene.String())
    databooks_list_with_search_and_limit = graphene.List(
        DatabookType,
        search_term=graphene.String(),
        offset_value=graphene.Int(),
        limit_value=graphene.Int(),
    )
    datasheets_count_with_search = graphene.Int(
        databook_id=graphene.String(required=True), search_term=graphene.String()
    )
    datasheets_list_with_search_and_limit = graphene.List(
        DatasheetType,
        databook_id=graphene.String(required=True),
        search_term=graphene.String(),
        offset_value=graphene.Int(),
        limit_value=graphene.Int(),
    )

    @permission_required(RbacPermissions.VIEW_DATABOOK.value)
    def resolve_source_variables(self, info, **kwargs):
        source_type = kwargs.get("source_type")
        client_id = info.context.client_id
        if source_type == "object":
            # custom object variables
            object_id = kwargs.get("object_id")
            co_variables = CustomObjectVariableAccessor(
                client_id
            ).all_variables_in_object(object_id, as_dicts=True)
            return co_variables
        elif source_type == "datasheet":
            # datasheet variables
            # For API coming from V1 databook id and allow_datasheet_from_other_databook flag is turned off,
            # databook id is used for querying variables
            db_id = kwargs.get("source_databook_id") or kwargs.get("databook_id")
            ds_id = kwargs.get("object_id")
            ds_variables = DatasheetVariableAccessor(client_id).get_variables_for_db_ds(
                db_id, ds_id
            )
            return ds_variables
        elif source_type == "report":
            object_id = kwargs.get("object_id")
            ro_variables = report_utils.get_report_variables(
                report_object_id=object_id, client_id=client_id
            )
            return ro_variables
        else:
            raise Exception("Incorrect source type")

    @permission_required(
        [
            RbacPermissions.VIEW_COMMISSIONPLAN.value,
            RbacPermissions.MANAGE_DATASETTINGS.value,
            RbacPermissions.VIEW_DATABOOK.value,
            RbacPermissions.MANAGE_ALLADMINS.value,
            RbacPermissions.MANAGE_REPORTENRICH.value,
            RbacPermissions.VIEW_PAYOUTS.value,
            RbacPermissions.VIEW_STATEMENTS.value,
        ]
    )
    def resolve_all_databook_details(self, info, **kwargs):
        client_id = info.context.client_id
        return list(DatabookAccessor(client_id).client_latest_kd_aware_with_hidden())

    @permission_required(RbacPermissions.VIEW_DATABOOK.value)
    def resolve_databook_details(self, info, **kwargs):
        client_id = info.context.client_id
        login_user_id = info.context.user.username
        databook_id = kwargs.get("databook_id")
        expression_box_version = kwargs.get("expression_box_version", "v1")
        info.context.expression_box_version = expression_box_version
        databook = DatabookAccessor(client_id).get_databook_by_id(databook_id)
        analytics_data = {
            "user_id": login_user_id,
            "event_name": SegmentEvents.VIEW_DATABOOK.value,
            "event_properties": {
                SegmentProperties.DATABOOK_NAME.value: getattr(databook, "name"),
                SegmentProperties.DATABOOK_ID.value: databook_id,
            },
        }
        analytics = CoreAnalytics(analyser_type="segment")
        analytics.send_analytics(analytics_data)

        return databook

    @permission_required(
        [RbacPermissions.MANAGE_DATASETTINGS.value, RbacPermissions.VIEW_DATABOOK.value]
    )
    def resolve_all_source_objects(self, info, **kwargs):
        cust_objects = list(
            CustomObjectAccessor(info.context.client_id).client_kd_aware().values()
        )

        # return std_objects + cust_objects
        return cust_objects

    @permission_required(RbacPermissions.VIEW_DATABOOK.value)
    @add_log_context("datasheet_data")
    def resolve_datasheet_data(self, info, **kwargs):
        try:
            client_id = info.context.client_id
            login_user_id = info.context.user.username
            databook_id = kwargs.get("databook_id")
            databook_name = kwargs.get("databook_name")
            datasheet_id = kwargs.get("datasheet_id")
            page_number = kwargs.get("page_number")
            page_size = kwargs.get("page_size")
            logger = kwargs.get("logger")
            filters = kwargs.get("filters")
            pivots = kwargs.get("pivots")
            reader = kwargs.get("reader")
            params_dict = {
                "client_id": client_id,
                "databook_id": databook_id,
                "datasheet_id": datasheet_id,
                "filters": filters,
                "pivots": pivots,
                "page_size": page_size,
                "page_num": page_number,
                "logged_in_user_email": login_user_id,
                "reader": reader,
            }
            if pivots:
                result = fetch_datasheet_data(params_dict, logger)
            else:
                result = fetch_json_filtered_datasheet_data(params_dict, logger)

            (
                source_type,
                source_name,
                datasheet_name,
            ) = datasheet_source_details(client_id, databook_id, datasheet_id)
            if filters:
                analytics_data = {
                    "user_id": login_user_id,
                    "event_name": SegmentEvents.FILTER_DATASHEET.value,
                    "event_properties": {
                        SegmentProperties.DATABOOK_NAME.value: databook_name,
                        SegmentProperties.DATABOOK_ID.value: databook_id,
                        SegmentProperties.DATASHEET_NAME.value: datasheet_name,
                    },
                }
                analytics = CoreAnalytics(analyser_type="segment")
                analytics.send_analytics(analytics_data)
            analytics_data = {
                "user_id": login_user_id,
                "event_name": SegmentEvents.VISIT_DATASHEET.value,
                "event_properties": {
                    SegmentProperties.DATABOOK_NAME.value: databook_name,
                    SegmentProperties.DATABOOK_ID.value: str(databook_id),
                    SegmentProperties.DATASHEET_NAME.value: str(datasheet_name),
                    SegmentProperties.DATASHEET_DATA_SOURCE.value: str(source_type),
                    SegmentProperties.DATASHEET_SOURCE_NAME.value: str(source_name),
                },
            }
            analytics = CoreAnalytics(analyser_type="segment")
            analytics.send_analytics(analytics_data)

            return result
        except Exception as e:
            traceback.print_exc()
            # logger.error("Exception in Resolve Datasheet Data", error_dict)
            return {
                "data": {"status": "Error", "message": e},
                "data_count": 0,
                "pivot_columns": {},
            }

    @permission_required(RbacPermissions.VIEW_DATABOOK.value)
    @add_log_context("datasheet_data")
    def resolve_adjustment_data(self, info, **kwargs):
        try:
            client_id = info.context.client_id
            login_user_id = info.context.user.username
            databook_id = kwargs.get("databook_id")
            datasheet_id = kwargs.get("datasheet_id")

            result = fetch_adjustment_data(
                client_id=client_id,
                databook_id=databook_id,
                datasheet_id=datasheet_id,
                loggedin_user_email=login_user_id,
            )

            return result
        except Exception as e:
            traceback.print_exc()
            return {
                "adjustment_data": {"status": "Error", "message": e},
            }

    @permission_required(RbacPermissions.VIEW_DATABOOK.value)
    def resolve_datasheet(self, info, **kwargs):
        client_id = info.context.client_id
        databook_id = kwargs.get("databook_id")
        datasheet_id = kwargs.get("datasheet_id")
        expression_box_version = kwargs.get("expression_box_version", "v1")
        info.context.expression_box_version = expression_box_version

        ds = DatasheetAccessor(client_id).get_data_by_datasheet_id(
            databook_id, datasheet_id
        )
        return ds[0] if len(ds) > 0 else None

    @permission_required(RbacPermissions.VIEW_DATABOOK.value)
    @add_log_context("datasheet_data")
    def resolve_all_datasheet_data_with_adjustments(self, info, **kwargs):
        try:
            return {
                "data": [],
                "total_pages": None,
                "data_count": None,
                "page_num": None,
            }
        except Exception as e:
            traceback.print_exc()
            # logger.error("Exception in Resolve Datasheet Data", error_dict)
            return {
                "data": {"status": "Error", "message": e},
                "total_pages": None,
                "data_count": None,
                "page_num": None,
            }

    @permission_required(RbacPermissions.VIEW_DATABOOK.value)
    def resolve_all_criteria_databook(self, info):
        client_id = info.context.client_id
        pca = PlanCriteriaAccessor(client_id)
        criterias = list(pca.client_kd_aware())
        db_ids = set()
        for criteria in criterias:
            criteria_data = criteria.criteria_data
            if "databook_id" in criteria_data and criteria_data["databook_id"]:
                db_id = criteria_data["databook_id"]
                db_ids.add(db_id)
        return DatabookAccessor(client_id).get_databook_by_db_ids(list(db_ids))

    @permission_required(
        [RbacPermissions.VIEW_DATABOOK.value, RbacPermissions.MANAGE_DATASETTINGS.value]
    )
    def resolve_all_report_objects(self, info, **kwargs):
        client_id = info.context.client_id
        report_objects = get_all_report_objects(client_id)
        return [
            {
                "id": report_object["ever_object_id"],
                "name": report_object["name"],
                "primary_key": report_object["primary_key"],
                "data_origin": report_object["data_origin"],
            }
            for report_object in report_objects
        ]

    @permission_required([RbacPermissions.MANAGE_DATASETTINGS.value])
    def resolve_report_objects_for_integration(self, info):
        """
        Specifically developed for displaying report objects in connectors
        Returns all the report objects that are not excluded for the user
        """
        login_user = info.context.user
        client_id = info.context.client_id
        object_ids = list(
            get_objects_excluded_for_user(client_id, login_user, object_type="report")
        )
        return list(EverObjectAccessor().get_ever_objects_excluding_ids(object_ids))

    @permission_required(RbacPermissions.VIEW_DATABOOK.value)
    def resolve_datasheet_order(self, info, **kwargs):
        databook_id = kwargs.get("databook_id")
        return DatabookAccessor(
            info.context.client_id
        ).get_datasheet_order_by_databook_id(databook_id)

    @permission_required(RbacPermissions.VIEW_DATABOOK.value)
    def resolve_databooks_count_with_search(self, info, **kwargs):
        search_term = kwargs.get("search_term")
        res = DatabookAccessor(info.context.client_id).get_databooks_count_with_search(
            search_term=search_term,
        )
        return res

    @permission_required(RbacPermissions.VIEW_DATABOOK.value)
    def resolve_databooks_list_with_search_and_limit(self, info, **kwargs):
        search_term = kwargs.get("search_term")
        offset = kwargs.get("offset_value") or 0
        limit = kwargs.get("limit_value") or 10000

        res = DatabookAccessor(
            info.context.client_id
        ).get_databooks_list_with_search_and_limit(
            search_term=search_term,
            offset=offset,
            limit=limit,
        )
        return res

    @permission_required(RbacPermissions.VIEW_DATABOOK.value)
    def resolve_datasheets_count_with_search(self, info, **kwargs):
        databook_id = kwargs.get("databook_id")
        search_term = kwargs.get("search_term")
        res = DatasheetAccessor(
            info.context.client_id
        ).get_datasheets_count_with_search(
            databook_id=databook_id,
            search_term=search_term,
        )
        return res

    @permission_required(RbacPermissions.VIEW_DATABOOK.value)
    def resolve_datasheets_list_with_search_and_limit(self, info, **kwargs):
        databook_id = kwargs.get("databook_id")
        search_term = kwargs.get("search_term")
        offset = kwargs.get("offset_value") or 0
        limit = kwargs.get("limit_value") or 10000

        res = DatasheetAccessor(
            info.context.client_id
        ).get_datasheets_list_with_search_and_limit(
            databook_id=databook_id,
            search_term=search_term,
            offset=offset,
            limit=limit,
        )
        return res

    @permission_required(RbacPermissions.MANAGE_DATASETTINGS.value)
    @add_log_context("user_datasheet_data")
    def resolve_usersheet_data(self, info, **kwargs):
        try:
            client_id = info.context.client_id
            login_user_id = info.context.user.username
            databook_id = kwargs.get("databook_id")
            datasheet_id = kwargs.get("datasheet_id")
            page_number = kwargs.get("page_number")
            page_size = kwargs.get("page_size")
            data_filters = kwargs.get("data_filters", [])
            status_filter = kwargs.get("status_filter", [])
            view_type = kwargs.get("view_type")
            is_grouped_view = kwargs.get("is_grouped_view", False)

            result = UserSheetDataService(
                client_id=client_id, databook_id=databook_id, datasheet_id=datasheet_id
            ).get_user_sheet_data(
                login_user_id=login_user_id,
                view_type=view_type,
                data_filters=data_filters,
                status_filter=status_filter,
                page_size=page_size,
                page_num=page_number,
                is_grouped_view=is_grouped_view,
            )

            # todo : add segments for user datasheet data

            return result
        except Exception as e:
            traceback.print_exc()
            # logger.error("Exception in Resolve Datasheet Data", error_dict)
            return {
                "data": {"status": "Error", "message": e},
                "data_count": 0,
            }


class DatasheetFilterType(DjangoObjectType):
    class Meta:
        model = DatasheetFilter


class DatasheetFilterQuery(object):
    all_ds_filter_list = graphene.List(
        DatasheetFilterType,
        databook_id=graphene.String(),
        datasheet_id=graphene.String(),
        session_kd=graphene.String(),
    )

    @permission_required(RbacPermissions.VIEW_DATABOOK.value)
    def resolve_all_ds_filter_list(self, info, **kwargs):
        databook_id = kwargs.get("databook_id")
        datasheet_id = kwargs.get("datasheet_id")
        return DatasheetFilterAccessor(info.context.client_id).get_all_ds_filters(
            databook_id, datasheet_id
        )


class DSFilterOperatorsType(DjangoObjectType):
    class Meta:
        model = DSFilterOperators


class DSFilterOperatorsQuery(object):
    ds_filter_operators = graphene.List(DSFilterOperatorsType)

    @permission_required(
        [
            RbacPermissions.MANAGE_DATASETTINGS.value,
            RbacPermissions.MANAGE_CONTRACTS.value,
            RbacPermissions.VIEW_DATABOOK.value,
            RbacPermissions.MANAGE_ALLADMINS.value,
        ]
    )
    def resolve_ds_filter_operators(self, info, **kwargs):
        return DSFilterOperatorsAccessor().get_all_operators()
