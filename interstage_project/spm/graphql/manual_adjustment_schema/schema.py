# pylint: disable=unsubscriptable-object, no-member, unsupported-membership-test

import graphene
from dateutil.parser import parse
from django.http import JsonResponse
from django.utils.timezone import make_aware
from graphene_django.types import DjangoObjectType
from graphql import GraphQLError
from pydash import snake_case

from commission_engine.utils.date_utils import end_of_day, start_of_day
from commission_engine.utils.general_data import (
    GRAPHQL__PERMISSION_DENIED,
    RBAC,
    RBACComponent,
    RbacPermissions,
)
from commission_engine.utils.general_utils import get_statements_url_for_payee
from interstage_project.auth_utils import (
    authorize_for_email_lookup,
    permission_required,
)
from interstage_project.utils import LogWithContext
from spm.accessors.manual_adjustment_accessor import (
    DrawAdjustmentAccessor,
    IgnoreAdjustmentAccessor,
    SplitAdjustmentAccessor,
)
from spm.commission_adjustment_approvals.services.approval_config_services import (
    allow_froozen_comm_adj_feature,
    get_commission_adjustment_approvals_flag,
)
from spm.constants.approval_workflow_constants import APPROVAL_WORKFLOW_STATUS
from spm.graphql.employee_config_schema.schema import BaseEmployeeType
from spm.models.manual_adjustment_models import DrawRecover, Ignore, Split
from spm.services.manual_adjustment_services.commission_adjustment_services import (
    get_all_commission_adjustments,
)
from spm.services.rbac_services import get_data_permission, get_valid_payee_emails
from spm.sort_utils import SortInfoType

logger = LogWithContext()


class CommissionAdjustmentTypeV2(graphene.ObjectType):
    # NOTE: Every new column added here should be added in
    # `spm.services.manual_adjustment_services.sort_commission_adjustments`
    # when the added column doesn't need to be sorted or not visible in UI.
    adjustment_id = graphene.UUID()
    adjustment_type = graphene.String()
    approval_status = graphene.String()
    employee_email_id = graphene.String()
    employee_name = graphene.String()
    period_start_date = graphene.DateTime()
    period_end_date = graphene.DateTime()
    amount = graphene.Decimal()
    currency = graphene.String()
    reason = graphene.String()
    reason_category_id = graphene.String()
    reason_category = graphene.String()
    plan_id = graphene.String()
    plan_name = graphene.String()
    criteria_id = graphene.String()
    criteria_name = graphene.String()
    line_item_id = graphene.String()
    commission_locked = graphene.Boolean()
    template_id = graphene.UUID()
    is_approval_skipped = graphene.Boolean()
    is_auto_approved = graphene.Boolean()
    skip_approval_reason = graphene.String()
    can_edit = graphene.Boolean()
    can_delete = graphene.Boolean()
    profile_picture = graphene.String()
    statement_url = graphene.String()


class CommissionAdjCount(graphene.ObjectType):
    all = graphene.Int()
    pending = graphene.Int()


class CommissionAdjTotalCount(graphene.ObjectType):
    total_users = graphene.Int()
    total_disable_delete = graphene.Int()


class SplitAdjustmentType(DjangoObjectType):
    payee = graphene.Field(BaseEmployeeType)

    def resolve_payee(self, info):
        return (
            info.context.employee_loader.load(self.split_payee_id)
            if self.split_payee_id is not None
            else None
        )

    class Meta:
        model = Split


class DrawAdjustmentType(DjangoObjectType):
    payee = graphene.Field(BaseEmployeeType)

    def resolve_payee(self, info):
        return (
            info.context.employee_loader.load(self.payee_id)
            if self.payee_id is not None
            else None
        )

    class Meta:
        model = DrawRecover


class IgnoreAdjustmentType(DjangoObjectType):
    class Meta:
        model = Ignore


class AdjustmentQuery(object):
    all_commission_adjustments_v2 = graphene.Field(
        graphene.List(CommissionAdjustmentTypeV2),
        status=graphene.String(),
        search_term=graphene.String(),
        offset_value=graphene.Int(),
        limit_value=graphene.Int(),
        ped=graphene.String(),
        plan_id=graphene.String(),
        sort_fields=graphene.List(graphene.String),
        sort_orders=graphene.List(graphene.String),
        filters=graphene.List(graphene.JSONString),
    )
    all_split_adjustments = graphene.List(SplitAdjustmentType)
    all_draw_adjustments = graphene.List(DrawAdjustmentType)
    all_ignore_adjustments = graphene.List(IgnoreAdjustmentType)
    all_draw_adjustments_of_payee = graphene.List(
        DrawAdjustmentType, payee_id=graphene.String()
    )
    all_draw_adjustments_of_payee_for_kd = graphene.List(
        DrawAdjustmentType,
        payee_id=graphene.String(),
        psd=graphene.String(),
        ped=graphene.String(),
    )
    commission_adjustments_count = graphene.Field(
        CommissionAdjCount,
        status=graphene.String(),
        search_term=graphene.String(),
        offset_value=graphene.Int(),
        limit_value=graphene.Int(),
        ped=graphene.String(),
        plan_id=graphene.String(),
        filters=graphene.List(graphene.JSONString),
    )
    total_users = graphene.Field(
        CommissionAdjTotalCount,
        status=graphene.String(),
        search_term=graphene.String(),
        ped=graphene.String(),
        plan_id=graphene.String(),
        sort_fields=graphene.List(graphene.String),
        sort_orders=graphene.List(graphene.String),
        filters=graphene.List(graphene.JSONString),
    )

    @permission_required(RbacPermissions.MANAGE_COMMISSIONADJUSTMENT.value)
    def resolve_all_commission_adjustments_v2(self, info, **kwargs):
        logger.info("All commission adjustments query v2 is getting called")

        client_id = info.context.client_id
        login_user_id = info.context.user.username
        limit = kwargs.get("limit_value")
        offset = kwargs.get("offset_value")
        status = kwargs.get("status")
        ped = kwargs.get("ped")
        search_term = kwargs.get("search_term")
        plan_id = kwargs.get("plan_id")
        sort_fields = kwargs.get("sort_fields", [])
        sort_orders = kwargs.get("sort_orders", [])
        filters = kwargs.get("filters", [])
        orderby_fields: list[SortInfoType] = [
            {"column": snake_case(col), "order": order}
            for col, order in zip(sort_fields, sort_orders)
        ]
        filters = [
            {snake_case(key): filter[key] for key in filter} for filter in filters
        ]

        if ped == "all":
            ped = None
        if status == "all":
            status = None
        comm_adj_approvals_feature = get_commission_adjustment_approvals_flag(client_id)
        if not comm_adj_approvals_feature:
            status = "approved"

        # Get commisssion adjustment according to data perm of logged in user: payout statements
        data_permission = get_data_permission(
            client_id, login_user_id, RBACComponent.PAYOUTS_STATEMENTS.value
        )
        if not data_permission:
            logger.info(
                f"User: {login_user_id} don't have permission to access commission adjustments"
            )
            raise GraphQLError(GRAPHQL__PERMISSION_DENIED)

        if data_permission["type"] == RBAC.ALL_DATA.value:
            valid_payee_emails = []
        else:
            valid_payee_emails = get_valid_payee_emails(
                client_id, login_user_id, data_permission
            )

        data = get_all_commission_adjustments(
            client_id,
            valid_payee_emails,
            logger,
            limit=limit,
            offset=offset,
            search_term=search_term,
            req_status=status,
            ped=ped,
            plan_id=plan_id,
            orderby_fields=orderby_fields,
            filters=filters,
        )

        # If feature is off then allow edit and delete with no restrictions
        # Else Dont't allow edit and delete if commission is locked and approval status is approved
        # Also Don't allow to edit when commission is locked since while locking we will cancel requested
        for comm_adj in data:
            comm_adj["can_edit"] = True
            comm_adj["can_delete"] = True
            comm_adj["adjustment_type"] = "Commission"  # TODO: Use enum
            if comm_adj["commission_locked"]:
                comm_adj["can_edit"] = False
                comm_adj["can_delete"] = False
            if comm_adj["commission_locked"] and allow_froozen_comm_adj_feature(
                client_id
            ):
                comm_adj["can_edit"] = True
                comm_adj["can_delete"] = True
            if comm_adj["approval_status"] != "approved":
                comm_adj["can_delete"] = True
            comm_adj["statement_url"] = get_statements_url_for_payee(
                comm_adj["employee_email_id"],
                comm_adj["period_start_date"].strftime("%Y-%m-%d"),
                comm_adj["period_end_date"].strftime("%Y-%m-%d"),
            )
        return data

    @permission_required(RbacPermissions.MANAGE_COMMISSIONADJUSTMENT.value)
    def resolve_total_users(self, info, **kwargs):
        logger.info("All commission adjustments query v2 is getting called")

        client_id = info.context.client_id
        login_user_id = info.context.user.username
        status = kwargs.get("status")
        ped = kwargs.get("ped")
        search_term = kwargs.get("search_term")
        plan_id = kwargs.get("plan_id")
        sort_fields = kwargs.get("sort_fields", [])
        sort_orders = kwargs.get("sort_orders", [])
        filters = kwargs.get("filters", [])
        orderby_fields: list[SortInfoType] = [
            {"column": snake_case(col), "order": order}
            for col, order in zip(sort_fields, sort_orders)
        ]
        filters = [
            {snake_case(key): filter[key] for key in filter} for filter in filters
        ]

        if ped == "all":
            ped = None
        if status == "all":
            status = None
        comm_adj_approvals_feature = get_commission_adjustment_approvals_flag(client_id)
        if not comm_adj_approvals_feature:
            status = "approved"

        # Get commisssion adjustment according to data perm of logged in user: payout statements
        data_permission = get_data_permission(
            client_id, login_user_id, RBACComponent.PAYOUTS_STATEMENTS.value
        )
        if not data_permission:
            logger.info(
                f"User: {login_user_id} don't have permission to access commission adjustments"
            )
            raise GraphQLError(GRAPHQL__PERMISSION_DENIED)

        if data_permission["type"] == RBAC.ALL_DATA.value:
            valid_payee_emails = []
        else:
            valid_payee_emails = get_valid_payee_emails(
                client_id, login_user_id, data_permission
            )
        total_users = 0
        data = get_all_commission_adjustments(
            client_id,
            valid_payee_emails,
            logger,
            search_term=search_term,
            req_status=status,
            ped=ped,
            plan_id=plan_id,
            orderby_fields=orderby_fields,
            filters=filters,
        )
        total_users = len(data)
        total_disable_delete = 0
        for comm_adj in data:
            comm_adj["can_delete"] = True
            comm_adj["adjustment_type"] = "Commission"  # TODO: Use enum
            if comm_adj["commission_locked"]:
                comm_adj["can_delete"] = False
            if comm_adj["commission_locked"] and allow_froozen_comm_adj_feature(
                client_id
            ):
                comm_adj["can_delete"] = True
            if comm_adj["approval_status"] != "approved":
                comm_adj["can_delete"] = True
        for comm_adj in data:
            if not comm_adj["can_delete"]:
                total_disable_delete += 1
        return {
            "total_users": total_users,
            "total_disable_delete": total_disable_delete,
        }

    @permission_required(RbacPermissions.MANAGE_COMMISSIONADJUSTMENT.value)
    def resolve_commission_adjustments_count(self, info, **kwargs):
        logger.info("All commission adjustments Count query is getting called")

        client_id = info.context.client_id
        login_user_id = info.context.user.username
        limit = kwargs.get("limit_value")
        offset = kwargs.get("offset_value")
        ped = kwargs.get("ped")
        search_term = kwargs.get("search_term")
        plan_id = kwargs.get("plan_id")
        filters = kwargs.get("filters", [])
        filters = [
            {snake_case(key): filter[key] for key in filter} for filter in filters
        ]
        if ped == "all":
            ped = None
        comm_adj_approvals_feature = get_commission_adjustment_approvals_flag(client_id)
        if not comm_adj_approvals_feature:
            status = "approved"
        else:
            status = None
        # Get commisssion adjustment according to data perm of logged in user: payout statements
        data_permission = get_data_permission(
            client_id, login_user_id, RBACComponent.PAYOUTS_STATEMENTS.value
        )
        if not data_permission:
            logger.info(
                f"User: {login_user_id} don't have permission to access commission adjustments count"
            )
            raise GraphQLError(GRAPHQL__PERMISSION_DENIED)

        if data_permission["type"] == RBAC.ALL_DATA.value:
            valid_payee_emails = []
        else:
            valid_payee_emails = get_valid_payee_emails(
                client_id, login_user_id, data_permission
            )

        data = get_all_commission_adjustments(
            client_id,
            valid_payee_emails,
            logger,
            limit=limit,
            offset=offset,
            search_term=search_term,
            ped=ped,
            plan_id=plan_id,
            req_status=status,
            is_count=True,
            filters=filters,
        )
        adjustments_count = {"all": 0, "pending": 0}
        for status_count in data:
            if (
                status_count["approval_status"]
                == APPROVAL_WORKFLOW_STATUS.REQUESTED.value
            ):
                adjustments_count["pending"] = status_count["count"]
            adjustments_count["all"] += status_count["count"]
        return adjustments_count

    @permission_required(RbacPermissions.MANAGE_DATASHEETADJUSTMENTS.value)
    def resolve_all_split_adjustments(self, info, **kwargs):
        return SplitAdjustmentAccessor(
            info.context.client_id
        ).get_all_split_adjustment()

    @permission_required(RbacPermissions.MANAGE_DRAWS.value)
    def resolve_all_draw_adjustments(self, info, **kwargs):
        logger.info("All draw adjustments query is getting called")
        return DrawAdjustmentAccessor(info.context.client_id).get_all_draw_adjustment()

    @permission_required(RbacPermissions.MANAGE_DATASHEETADJUSTMENTS.value)
    def resolve_all_ignore_adjustments(self, info, **kwargs):
        return IgnoreAdjustmentAccessor(
            info.context.client_id
        ).get_all_ignore_adjustment()

    @permission_required(
        [
            RbacPermissions.VIEW_STATEMENTS.value,
            RbacPermissions.VIEW_PAYOUTS.value,
            RbacPermissions.MANAGE_DRAWS.value,
        ]
    )
    def resolve_all_draw_adjustments_of_payee(self, info, **kwargs):
        logger.info("All draw adjustments of payee query is getting called")
        payee_id = kwargs.get("payee_id")
        login_user_id = info.context.user.username
        is_authorized = authorize_for_email_lookup(
            info.context.client_id,
            login_user_id,
            [payee_id],
            RBACComponent.QUOTAS_DRAWS.value,
        )
        if is_authorized:
            return DrawAdjustmentAccessor(
                info.context.client_id
            ).get_all_draw_adjustment_of_payee(payee_id)
        else:
            response = JsonResponse(
                {"message": "You don't have permission to access this data"}
            )
            response.status_code = 403
            logger.error("ALL DRAW ADJUSTMENTS OF PAYEE - PERMISSION REQUIRED")
            return response

    @permission_required(
        [RbacPermissions.VIEW_STATEMENTS.value, RbacPermissions.VIEW_PAYOUTS.value]
    )
    def resolve_all_draw_adjustments_of_payee_for_kd(self, info, **kwargs):
        logger.info("All draw adjustments of payee for kd query is getting called")
        payee_id = kwargs.get("payee_id")
        psd = make_aware(start_of_day(parse(kwargs.get("psd"), dayfirst=True)))
        ped = make_aware(end_of_day(parse(kwargs.get("ped"), dayfirst=True)))
        login_user_id = info.context.user.username
        is_authorized = authorize_for_email_lookup(
            info.context.client_id,
            login_user_id,
            [payee_id],
            RBACComponent.QUOTAS_DRAWS.value,
        )
        if is_authorized:
            from spm.services.draws_services import get_draw_adjustments

            return get_draw_adjustments(
                client_id=info.context.client_id, payee_id=payee_id, psd=psd, ped=ped
            )
        else:
            response = JsonResponse(
                {"message": "You don't have permission to access this data"}
            )
            response.status_code = 403
            logger.error("ALL DRAW ADJUSTMENTS OF PAYEE FOR KD - PERMISSION REQUIRED")
            return response
