"""
Unit tests for MS Teams tenant services.
"""

from datetime import datetime
from unittest.mock import MagicMock, patch

import pytest
from django.core.exceptions import ObjectDoesNotExist
from django.utils import timezone

from commission_engine.utils.general_data import (
    IntegrationType,
    NotificationChannelConnectionStatus,
    SegmentEvents,
    SegmentProperties,
)
from ms_teams_everstage.accessors.msteams_tenant_details_accessor import (
    MsteamsClientDetailsAccessor,
)
from ms_teams_everstage.models import MsteamsAdminTokenDetails
from ms_teams_everstage.services.tenant_services import (
    add_default_msteams_notifications,
    disconnect_msteams_for_client,
    get_admin_token_details,
    get_employee_in_msteams_from_email_id,
    get_msteams_users_details_for_client,
    install_app_for_new_users,
    install_msteams_app_for_client,
    install_published_app_for_users,
    integrate_msteams_for_newly_added_users,
    persist_msteams_tenant_details,
    save_conversation_id_for_employee,
    save_tenant_token_details,
    update_msteams_tenant_details,
    update_published_app_for_users,
    update_tenant_token_details,
)
from spm.accessors.config_accessors.integration_config_accessor import (
    IntegrationConfigAccessor,
)
from spm.tests.models import (
    create_employee_objects,
    create_integration_config_object,
    create_role_permissions,
)


@pytest.fixture
def mock_graph_api_responses():
    """Mock responses for Graph API calls."""
    with patch(
        "ms_teams_everstage.services.tenant_services.get_admin_access_token"
    ) as mock_get_admin_token, patch(
        "ms_teams_everstage.services.tenant_services.get_tenant_access_token"
    ) as mock_get_tenant_token, patch(
        "ms_teams_everstage.services.tenant_services.get_app_details"
    ) as mock_get_app_details, patch(
        "ms_teams_everstage.services.tenant_services.get_all_users"
    ) as mock_get_users, patch(
        "ms_teams_everstage.services.tenant_services.install_app_for_user"
    ) as mock_install_app, patch(
        "ms_teams_everstage.services.tenant_services.get_conversation_id"
    ) as mock_get_conversation_id, patch(
        "ms_teams_everstage.services.tenant_services.get_chat_id"
    ) as mock_get_chat_id, patch(
        "ms_teams_everstage.services.tenant_services.publish_app_to_catalog"
    ) as mock_publish_app, patch(
        "ms_teams_everstage.services.tenant_services.update_published_app_to_catalog"
    ) as mock_update_app:

        mock_get_admin_token.return_value = (
            "*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
            "*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
        )

        mock_get_tenant_token.return_value = "eyJ0eXAiOiJKV1QiLCJub25jZSI6Ii03Z3lhQ1p0OVlyV2dBckpHdDd1VmNtWnRuM2tPME9PYzR6REwzR0hJR1EiLCJhbGciOiJSUzI1NiIsIng1dCI6IkNOdjBPSTNSd3FsSEZFVm5hb01Bc2hDSDJYRSIsImtpZCI6IkNOdjBPSTNSd3FsSEZFVm5hb01Bc2hDSDJYRSJ9"

        mock_get_app_details.return_value = {
            "id": "13a5c19e-079a-4f25-ba87-77fcdf4b4533",
            "externalId": "b7913154-192a-4924-9a15-eb4cfcb10f3s",
            "appDefinitions": [{"version": "1.0.0"}],
        }

        mock_get_users.return_value = [
            {
                "mail": "<EMAIL>",
                "id": "4855ed1a-1b81-4a0a-bdb0-465b4b62ce52",
                "userPrincipalName": "<EMAIL>",
            },
            {
                "mail": "<EMAIL>",
                "id": "ec1ffc9d-ee60-435a-8f1c-1b8ba78aab8a",
                "userPrincipalName": "<EMAIL>",
            },
            {
                "mail": "<EMAIL>",
                "id": "0db349ac-ac7d-447a-b8e2-324b9ca65adf",
                "userPrincipalName": "<EMAIL>",
            },
        ]

        mock_install_app.return_value = MagicMock(status_code=200)

        mock_get_conversation_id.return_value = "0db349ac-ac7d-447a-b8e2-324b9ca65adf"
        mock_get_chat_id.return_value = "19:<EMAIL>"

        mock_publish_app.return_value = MagicMock(status_code=200)
        mock_update_app.return_value = MagicMock(status_code=200)

        yield {
            "get_admin_token": mock_get_admin_token,
            "get_tenant_token": mock_get_tenant_token,
            "get_app_details": mock_get_app_details,
            "get_users": mock_get_users,
            "install_app": mock_install_app,
            "get_conversation_id": mock_get_conversation_id,
            "get_chat_id": mock_get_chat_id,
            "publish_app": mock_publish_app,
            "update_app": mock_update_app,
        }


@pytest.fixture
def mock_jwt_decode():
    """Mock JWT token decoding."""
    with patch(
        "ms_teams_everstage.services.tenant_services.jwt_hard_decode_token"
    ) as mock_decode:
        mock_decode.return_value = {
            "tid": "4a894d22-6c6b-43a2-94db-1acc429d503a",
            "unique_name": "<EMAIL>",
        }
        yield mock_decode


@pytest.fixture
def mock_analytics():
    """Mock analytics service."""
    with patch(
        "ms_teams_everstage.services.tenant_services.CoreAnalytics"
    ) as mock_analytics:
        mock_instance = MagicMock()
        mock_analytics.return_value = mock_instance
        yield mock_instance


@pytest.fixture
def resources():
    """Create test resources."""
    create_role_permissions()
    create_employee_objects(
        "<EMAIL>",
        "User1",
        "Everstage",
        False,
        employee_config={},
        role=["2e7d875b-6651-4c1d-b5c0-7d39eda7f7e5"],
    )
    create_integration_config_object(
        client_id=1,
        integration_type=IntegrationType.MS_TEAMS.value,
        config={"msteams_conversation_id": "test"},
        employee_email_id="<EMAIL>",
    )


@pytest.mark.django_db
class TestTenantServices:
    """Test class for MS Teams tenant services."""

    @pytest.mark.parametrize(
        "client_id, tenant_id, admin_access_token, admin_refresh_token",
        [
            (
                "1",
                "4a894d22-6c6b-43a2-94db-1acc429d503b",
                "*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
                "*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
            ),
        ],
    )
    def test_get_admin_token_details(
        self, resources, client_id, tenant_id, admin_access_token, admin_refresh_token
    ):
        """Test getting admin token details."""
        updated_at = timezone.now()

        MsteamsAdminTokenDetails.objects.create(
            client_id=client_id,
            tenant_id=tenant_id,
            admin_access_token=admin_access_token,
            admin_refresh_token=admin_refresh_token,
            updated_at=updated_at,
        )
        result = get_admin_token_details(client_id)

        assert result is not None
        assert result.client_id == client_id
        assert result.tenant_id == tenant_id
        assert result.admin_access_token == admin_access_token
        assert result.admin_refresh_token == admin_refresh_token
        assert result.updated_at == updated_at

    @pytest.mark.parametrize(
        "client_id, admin_access_token, admin_refresh_token",
        [
            (
                1,
                "*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
                "*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
            ),
        ],
    )
    def test_update_tenant_token_details(
        self, resources, client_id, admin_access_token, admin_refresh_token
    ):
        """Test updating tenant token details."""
        token_details = MagicMock()
        with patch(
            "ms_teams_everstage.services.tenant_services.MsteamsAdminTokenDetailsAccessor"
        ) as mock_accessor:
            mock_accessor.return_value.get.return_value = token_details

            update_tenant_token_details(
                client_id, admin_access_token, admin_refresh_token
            )

            assert token_details.admin_access_token == admin_access_token
            assert token_details.admin_refresh_token == admin_refresh_token
            token_details.save.assert_called_once()

    @pytest.mark.parametrize(
        "client_id, tenant_id, admin_access_token, admin_refresh_token",
        [
            (
                1,
                "4a894d22-6c6b-43a2-94db-1acc429d503b",
                "eyJ0eXAiOiJKV1QiLCJub25jZSI6Ii03Z3lhQ1p0OVlyV2dBckpHdDd1VmNtWnRuM2tPME9PYzR6REwzR0hJR1EiLCJhbGciOiJSUzI1NiIsIng1dCI6IkNOdjBPSTNSd3FsSEZFVm5hb01Bc2hDSDJYRSIsImtpZCI6IkNOdjBPSTNSd3FsSEZFVm5hb01Bc2hDSDJYRSS9",
                "eyJ0eXAiOiJKV1QiLCJub25jZSI6Ii03Z3lhQ1p0OVlyV2dBckpHdDd1VmNtWnRuM2tPME9PYzR6REwzR0hJR1EiLCJhbGciOiJSUzI1NiIsIng1dCI6IkNOdjBPSTNSd3FsSEZFVm5hb01Bc2hDSDJYRSIsImtpZCI6IkNOdjBPSTNSd3FsSEZFVm5hb01Bc2hDSDJYRSS9",
            ),
        ],
    )
    def test_save_tenant_token_details(
        self, resources, client_id, tenant_id, admin_access_token, admin_refresh_token
    ):
        """Test saving tenant token details."""
        token_details = MagicMock()
        with patch(
            "ms_teams_everstage.services.tenant_services.MsteamsAdminTokenDetailsAccessor"
        ) as mock_accessor:
            mock_accessor.return_value.get.return_value = token_details

            save_tenant_token_details(
                client_id, tenant_id, admin_access_token, admin_refresh_token
            )

            assert token_details.admin_access_token == admin_access_token
            assert token_details.admin_refresh_token == admin_refresh_token
            token_details.save.assert_called_once()

    @pytest.mark.parametrize(
        "client_id, tenant_id, app_catalog_id, app_external_id, msteams_admin_email_id, login_email_id, app_version",
        [
            (
                1,
                "4a894d22-6c6b-43a2-94db-1acc429d503b",
                "13a5c19e-079a-4f25-ba87-77fcdf4b4533",
                "13a5c19e-079a-4f25-ba87-77fcdf4b4533",
                "<EMAIL>",
                "<EMAIL>",
                "1.0.0",
            ),
        ],
    )
    def test_persist_msteams_tenant_details(
        self,
        resources,
        client_id,
        tenant_id,
        app_catalog_id,
        app_external_id,
        msteams_admin_email_id,
        login_email_id,
        app_version,
    ):
        """Test persisting MS Teams tenant details."""

        persist_msteams_tenant_details(
            client_id,
            tenant_id,
            app_catalog_id,
            app_external_id,
            msteams_admin_email_id,
            login_email_id,
            app_version,
        )

        saved_details = MsteamsClientDetailsAccessor(client_id).get()

        if saved_details:
            assert saved_details.client_id == client_id
            assert saved_details.tenant_id == tenant_id
            assert saved_details.app_catalog_id == app_catalog_id
            assert saved_details.app_external_id == app_external_id
            assert saved_details.msteams_installed_by == msteams_admin_email_id
            assert saved_details.msteams_updated_by == msteams_admin_email_id
            assert saved_details.installed_by == login_email_id
            assert saved_details.updated_by == login_email_id
            assert saved_details.app_version == app_version
            assert saved_details.installed_at is not None
            assert saved_details.updated_at is not None
            assert saved_details.app_package_s3_url is not None

    @pytest.mark.parametrize(
        "client_id, email_ids, mock_permissions, mock_notification_name, mock_notification_value, expected_add_tasks_calls",
        [
            # Normal case - with permissions and enabled
            (
                1,
                ["<EMAIL>"],
                ["MANAGE_CONFIG", "VIEW_COMMISSION"],
                "COMMISSION_NOTIFICATION",
                {
                    "permissions": ["MANAGE_CONFIG"],
                    "managed_by_payee": True,
                    "enabled_on_connect": True,
                    "frequency": "DAILY",
                },
                1,
            ),
            # Multiple users case
            (
                1,
                ["<EMAIL>", "<EMAIL>"],
                ["MANAGE_CONFIG", "VIEW_COMMISSION"],
                "COMMISSION_NOTIFICATION",
                {
                    "permissions": ["MANAGE_CONFIG"],
                    "managed_by_payee": True,
                    "enabled_on_connect": True,
                    "frequency": "DAILY",
                },
                2,
            ),
            # No permissions case
            (
                1,
                ["<EMAIL>"],
                [],
                "COMMISSION_NOTIFICATION",
                {
                    "permissions": ["MANAGE_CONFIG"],
                    "managed_by_payee": True,
                    "enabled_on_connect": True,
                    "frequency": "DAILY",
                },
                1,
            ),
            # Not enabled case
            (
                1,
                ["<EMAIL>"],
                ["MANAGE_CONFIG"],
                "COMMISSION_NOTIFICATION",
                {
                    "permissions": ["MANAGE_CONFIG"],
                    "managed_by_payee": True,
                    "enabled_on_connect": False,
                    "frequency": "DAILY",
                },
                1,
            ),
        ],
    )
    def test_add_default_msteams_notifications(
        self,
        resources,
        client_id,
        email_ids,
        mock_permissions,
        mock_notification_name,
        mock_notification_value,
        expected_add_tasks_calls,
    ):
        """Test adding default MS Teams notifications for users with various scenarios."""
        mock_notification = MagicMock()
        mock_notification.name = mock_notification_name
        mock_notification.value = mock_notification_value

        with patch(
            "spm.services.rbac_services.get_ui_permissions"
        ) as mock_get_permissions, patch(
            "ms_teams_everstage.services.tenant_services.Notification"
        ) as mock_notification_enum, patch(
            "ms_teams_everstage.services.tenant_services.add_tasks_for_payee"
        ) as mock_add_tasks:

            mock_notification_enum.__iter__.return_value = [mock_notification]

            mock_get_permissions.return_value = mock_permissions

            add_default_msteams_notifications(client_id, email_ids)

            # Verify get_ui_permissions was called for each email ID
            assert mock_get_permissions.call_count == len(email_ids)
            for email_id in email_ids:
                mock_get_permissions.assert_any_call(client_id, email_id)

            # Verify add_tasks_for_payee was called the expected number of times
            assert mock_add_tasks.call_count == expected_add_tasks_calls

            if mock_permissions and mock_notification_value["enabled_on_connect"]:
                # For each email ID, verify the correct call was made
                for i, email_id in enumerate(email_ids):
                    mock_add_tasks.assert_any_call(
                        client_id,
                        [
                            {
                                "name": mock_notification_name,
                                "frequency": mock_notification_value["frequency"],
                                "ms_teams": {"enabled": True},
                            }
                        ],
                        email_id,
                    )

    @pytest.mark.parametrize(
        "client_id, tenant_access_token, app_catalog_id, msteams_user_id, employee_email_id, conversation_id, chat_id, expected_config",
        [
            (
                1,
                "eyJ0eXAiOiJKV1QiLCJub25jZSI6Ii03Z3lhQ1p0OVlyV2dBckpHdDd1VmNtWnRuM2tPME9PYzR6REwzR0hJR1EiLCJhbGciOiJSUzI1NiIsIng1dCI6IkNOdjBPSTNSd3FsSEZFVm5hb01Bc2hDSDJYRSIsImtpZCI6IkNOdjBPSTNSd3FsSEZFVm5hb01Bc2hDSDJYRSS9",
                "13a5c19e-079a-4f25-ba87-77fcdf4b4533",
                "0db349ac-ac7d-447a-b8e2-324b9ca65adf",
                "<EMAIL>",
                "0db349ac-ac7d-447a-b8e2-324b9ca65adf",
                "19:<EMAIL>",
                {
                    "msteams_user_id": "0db349ac-ac7d-447a-b8e2-324b9ca65adf",
                    "msteams_conversation_id": "19:<EMAIL>",
                },
            ),
            # Add more test cases here if needed
        ],
    )
    def test_save_conversation_id_for_employee(
        self,
        resources,
        client_id,
        tenant_access_token,
        app_catalog_id,
        msteams_user_id,
        employee_email_id,
        conversation_id,
        chat_id,
        expected_config,
    ):
        """Test saving conversation ID for employee with various scenarios."""
        with patch(
            "ms_teams_everstage.services.tenant_services.get_conversation_id"
        ) as mock_get_conversation_id, patch(
            "ms_teams_everstage.services.tenant_services.get_chat_id"
        ) as mock_get_chat_id, patch(
            "ms_teams_everstage.services.tenant_services.add_default_msteams_notifications"
        ) as mock_add_notifications:

            mock_get_conversation_id.return_value = conversation_id
            mock_get_chat_id.return_value = chat_id

            save_conversation_id_for_employee(
                client_id,
                tenant_access_token,
                app_catalog_id,
                msteams_user_id,
                employee_email_id,
            )

            config_record = (
                IntegrationConfigAccessor().get_msteams_config_record_by_mail_id(
                    client_id, employee_email_id
                )
            )

            assert config_record is not None
            assert config_record.employee_email_id == employee_email_id
            assert config_record.integration_type == IntegrationType.MS_TEAMS.value
            assert config_record.config == expected_config
            assert config_record.knowledge_end_date is None

            mock_get_conversation_id.assert_called_once_with(
                tenant_access_token, msteams_user_id, app_catalog_id
            )
            mock_get_chat_id.assert_called_once_with(
                tenant_access_token, msteams_user_id, conversation_id
            )
            mock_add_notifications.assert_called_once_with(client_id, employee_email_id)

    @pytest.mark.parametrize(
        "record_exists, expected_persist_calls, expected_save_calls, client_id, tenant_id, app_catalog_id, app_external_id, msteams_admin_email_id, login_email_id, app_version",
        [
            (
                True,
                0,
                1,
                1,
                "4a894d22-6c6b-43a2-94db-1acc429d503a",
                "13a5c19e-079a-4f25-ba87-77fcdf4b4533",
                "13a5c19e-079a-4f25-ba87-77fcdf4b4533",
                "<EMAIL>",
                "<EMAIL>",
                "1.0.0",
            ),
            (
                False,
                1,
                0,
                1,
                "4a894d22-6c6b-43a2-94db-1acc429d503a",
                "13a5c19e-079a-4f25-ba87-77fcdf4b4533",
                "13a5c19e-079a-4f25-ba87-77fcdf4b4533",
                "<EMAIL>",
                "<EMAIL>",
                "1.0.0",
            ),
        ],
    )
    def test_update_msteams_tenant_details(
        self,
        resources,
        record_exists,
        expected_persist_calls,
        expected_save_calls,
        client_id,
        tenant_id,
        app_catalog_id,
        app_external_id,
        msteams_admin_email_id,
        login_email_id,
        app_version,
    ):
        """Test updating MS Teams tenant details for both existing and non-existing records."""
        mock_tenant_details = MagicMock() if record_exists else None
        with patch(
            "ms_teams_everstage.services.tenant_services.MsteamsClientDetailsAccessor"
        ) as mock_accessor, patch(
            "ms_teams_everstage.services.tenant_services.persist_msteams_tenant_details"
        ) as mock_persist:
            mock_accessor.return_value.get.return_value = mock_tenant_details

            update_msteams_tenant_details(
                client_id,
                tenant_id,
                app_catalog_id,
                app_external_id,
                msteams_admin_email_id,
                login_email_id,
                app_version,
            )

            assert mock_persist.call_count == expected_persist_calls

            if record_exists and mock_tenant_details is not None:
                assert mock_tenant_details.updated_by == login_email_id
                assert mock_tenant_details.msteams_updated_by == msteams_admin_email_id
                assert mock_tenant_details.app_version == app_version
                assert mock_tenant_details.save.call_count == expected_save_calls

            if not record_exists:
                mock_persist.assert_called_once_with(
                    client_id,
                    tenant_id,
                    app_catalog_id,
                    app_external_id,
                    msteams_admin_email_id,
                    login_email_id,
                    app_version,
                )

    @pytest.mark.parametrize(
        "test_case, client_id, email_id, notification_type, is_allowed, expected_result, expected_exception, expected_exception_message",
        [
            (
                "success",
                1,
                "<EMAIL>",
                "COMMISSION_NOTIFICATION",
                True,
                MagicMock(employee_email_id="<EMAIL>"),
                None,
                None,
            ),
            (
                "not_found",
                1,
                "<EMAIL>",
                "COMMISSION_NOTIFICATION",
                None,
                None,
                ObjectDoesNotExist,
                "<NAME_EMAIL> not found in Everstage.",
            ),
            (
                "not_allowed",
                1,
                "<EMAIL>",
                "COMMISSION_NOTIFICATION",
                False,
                None,
                None,
                None,
            ),
        ],
    )
    def test_get_employee_in_msteams_from_email_id(
        self,
        resources,
        test_case,
        client_id,
        email_id,
        notification_type,
        is_allowed,
        expected_result,
        expected_exception,
        expected_exception_message,
    ):
        """Test getting employee in MS Teams with different scenarios."""
        with patch(
            "ms_teams_everstage.services.tenant_services.is_notification_allowed_for_user"
        ) as mock_is_allowed:
            if is_allowed is not None:
                mock_is_allowed.return_value = is_allowed

            if expected_exception:
                with pytest.raises(expected_exception) as exc_info:
                    get_employee_in_msteams_from_email_id(
                        client_id, email_id, notification_type
                    )
                assert str(exc_info.value) == expected_exception_message
            else:
                result = get_employee_in_msteams_from_email_id(
                    client_id, email_id, notification_type
                )

                if test_case == "success":
                    assert result is not None
                    assert result.employee_email_id == email_id
                elif test_case == "not_allowed":
                    assert result is None

            if is_allowed is not None:
                mock_is_allowed.assert_called_once_with(
                    client_id, email_id, notification_type
                )

    @pytest.mark.parametrize(
        "test_case, client_id, user_access_token, mock_users, mock_employees, expected_user_ids, expected_employee_count",
        [
            (
                "success",
                1,
                "eyJ0eXAiOiJKV1QiLCJub25jZSI6Ii03Z3lhQ1p0OVlyV2dBckpHdDd1VmNtWnRuM2tPME9PYzR6REwzR0hJR1EiLCJhbGciOiJSUzI1NiIsIng1dCI6IkNOdjBPSTNSd3FsSEZFVm5hb01Bc2hDSDJYRSIsImtpZCI6IkNOdjBPSTNSd3FsSEZFVm5hb01Bc2hDSDJYRSS9",
                [
                    {
                        "mail": "<EMAIL>",
                        "id": "d2f5af20-ff01-4c7a-9f8e-3485fc7faaf0",
                        "userPrincipalName": "<EMAIL>",
                    },
                    {
                        "mail": None,
                        "id": "a2cd34f9-5b09-466b-bcec-5867ef563b0d",
                        "userPrincipalName": "<EMAIL>",
                    },
                ],
                [
                    MagicMock(employee_email_id="<EMAIL>"),
                    MagicMock(employee_email_id="<EMAIL>"),
                ],
                {
                    "<EMAIL>": "d2f5af20-ff01-4c7a-9f8e-3485fc7faaf0",
                    "<EMAIL>": "a2cd34f9-5b09-466b-bcec-5867ef563b0d",
                },
                2,
            ),
            (
                "empty_users",
                1,
                "eyJ0eXAiOiJKV1QiLCJub25jZSI6Ii03Z3lhQ1p0OVlyV2dBckpHdDd1VmNtWnRuM2tPME9PYzR6REwzR0hJR1EiLCJhbGciOiJSUzI1NiIsIng1dCI6IkNOdjBPSTNSd3FsSEZFVm5hb01Bc2hDSDJYRSIsImtpZCI6IkNOdjBPSTNSd3FsSEZFVm5hb01Bc2hDSDJYRSS9",
                [],
                [],
                {},
                0,
            ),
        ],
    )
    def test_get_msteams_users_details_for_client(
        self,
        resources,
        test_case,
        client_id,
        user_access_token,
        mock_users,
        mock_employees,
        expected_user_ids,
        expected_employee_count,
    ):
        """Test getting MS Teams users details for a client."""
        with patch(
            "ms_teams_everstage.services.tenant_services.get_all_users"
        ) as mock_get_users, patch(
            "ms_teams_everstage.services.tenant_services.EmployeeAccessor"
        ) as mock_accessor:
            mock_get_users.return_value = mock_users
            mock_accessor.return_value.get_all_payees_with_status.return_value = (
                mock_employees
            )

            ms_teams_user_ids, everstage_employees = (
                get_msteams_users_details_for_client(client_id, user_access_token)
            )

            assert ms_teams_user_ids == expected_user_ids
            assert len(everstage_employees) == expected_employee_count

            if test_case == "success":
                assert (
                    ms_teams_user_ids["<EMAIL>"]
                    == "d2f5af20-ff01-4c7a-9f8e-3485fc7faaf0"
                )
                assert (
                    ms_teams_user_ids["<EMAIL>"]
                    == "a2cd34f9-5b09-466b-bcec-5867ef563b0d"
                )
            elif test_case == "empty_users":
                assert ms_teams_user_ids == {}
                assert everstage_employees == []

            mock_get_users.assert_called_once_with(user_access_token)
            mock_accessor.return_value.get_all_payees_with_status.assert_called_once_with(
                email_list=[user["userPrincipalName"] for user in mock_users],
                status=["Active", "Invited"],
            )

    @pytest.mark.parametrize(
        "client_id, user_access_token, tenant_access_token, app_catalog_id, ms_teams_user_ids, employees, expected_install_calls",
        [
            (
                1,
                "eyJ0eXAiOiJKV1QiLCJub25jZSI6Ii03Z3lhQ1p0OVlyV2dBckpHdDd1VmNtWnRuM2tPME9PYzR6REwzR0hJR1EiLCJhbGciOiJSUzI1NiIsIng1dCI6IkNOdjBPSTNSd3FsSEZFVm5hb01Bc2hDSDJYRSIsImtpZCI6IkNOdjBPSTNSd3FsSEZFVm5hb01Bc2hDSDJYRSS9",
                "eyJ0eXAiOiJKV1QiLCJub25jZSI6Ii03Z3lhQ1p0OVlyV2dBckpHdDd1VmNtWnRuM2tPME9PYzR6REwzR0hJR1EiLCJhbGciOiJSUzI1NiIsIng1dCI6IkNOdjBPSTNSd3FsSEZFVm5hb01Bc2hDSDJYRSIsImtpZCI6IkNOdjBPSTNSd3FsSEZFVm5hb01Bc2hDSDJYRSS9",
                "13a5c19e-079a-4f25-ba87-77fcdf4b4533",
                {
                    "<EMAIL>": "d2f5af20-ff01-4c7a-9f8e-3485fc7faaf0",
                    "<EMAIL>": "a2cd34f9-5b09-466b-bcec-5867ef563b0d",
                },
                [
                    MagicMock(employee_email_id="<EMAIL>"),
                    MagicMock(employee_email_id="<EMAIL>"),
                ],
                2,
            ),
            (
                1,
                "eyJ0eXAiOiJKV1QiLCJub25jZSI6Ii03Z3lhQ1p0OVlyV2dBckpHdDd1VmNtWnRuM2tPME9PYzR6REwzR0hJR1EiLCJhbGciOiJSUzI1NiIsIng1dCI6IkNOdjBPSTNSd3FsSEZFVm5hb01Bc2hDSDJYRSIsImtpZCI6IkNOdjBPSTNSd3FsSEZFVm5hb01Bc2hDSDJYRSS9",
                "eyJ0eXAiOiJKV1QiLCJub25jZSI6Ii03Z3lhQ1p0OVlyV2dBckpHdDd1VmNtWnRuM2tPME9PYzR6REwzR0hJR1EiLCJhbGciOiJSUzI1NiIsIng1dCI6IkNOdjBPSTNSd3FsSEZFVm5hb01Bc2hDSDJYRSIsImtpZCI6IkNOdjBPSTNSd3FsSEZFVm5hb01Bc2hDSDJYRSS9",
                "13a5c19e-079a-4f25-ba87-77fcdf4b4533",
                {
                    "<EMAIL>": "d2f5af20-ff01-4c7a-9f8e-3485fc7faaf0",
                    "<EMAIL>": None,
                },
                [
                    MagicMock(employee_email_id="<EMAIL>"),
                    MagicMock(employee_email_id="<EMAIL>"),
                ],
                2,
            ),
            (
                1,
                "eyJ0eXAiOiJKV1QiLCJub25jZSI6Ii03Z3lhQ1p0OVlyV2dBckpHdDd1VmNtWnRuM2tPME9PYzR6REwzR0hJR1EiLCJhbGciOiJSUzI1NiIsIng1dCI6IkNOdjBPSTNSd3FsSEZFVm5hb01Bc2hDSDJYRSIsImtpZCI6IkNOdjBPSTNSd3FsSEZFVm5hb01Bc2hDSDJYRSS9",
                "eyJ0eXAiOiJKV1QiLCJub25jZSI6Ii03Z3lhQ1p0OVlyV2dBckpHdDd1VmNtWnRuM2tPME9PYzR6REwzR0hJR1EiLCJhbGciOiJSUzI1NiIsIng1dCI6IkNOdjBPSTNSd3FsSEZFVm5hb01Bc2hDSDJYRSIsImtpZCI6IkNOdjBPSTNSd3FsSEZFVm5hb01Bc2hDSDJYRSS9",
                "13a5c19e-079a-4f25-ba87-77fcdf4b4533",
                {
                    "<EMAIL>": "d2f5af20-ff01-4c7a-9f8e-3485fc7faaf0",
                },
                [
                    MagicMock(employee_email_id="<EMAIL>"),
                ],
                1,
            ),
            (
                1,
                "eyJ0eXAiOiJKV1QiLCJub25jZSI6Ii03Z3lhQ1p0OVlyV2dBckpHdDd1VmNtWnRuM2tPME9PYzR6REwzR0hJR1EiLCJhbGciOiJSUzI1NiIsIng1dCI6IkNOdjBPSTNSd3FsSEZFVm5hb01Bc2hDSDJYRSS9",
                "eyJ0eXAiOiJKV1QiLCJub25jZSI6Ii03Z3lhQ1p0OVlyV2dBckpHdDd1VmNtWnRuM2tPME9PYzR6REwzR0hJR1EiLCJhbGciOiJSUzI1NiIsIng1dCI6IkNOdjBPSTNSd3FsSEZFVm5hb01Bc2hDSDJYRSS9",
                "13a5c19e-079a-4f25-ba87-77fcdf4b4533",
                {},
                [],
                0,
            ),
        ],
    )
    def test_install_published_app_for_users(
        self,
        resources,
        client_id,
        user_access_token,
        tenant_access_token,
        app_catalog_id,
        ms_teams_user_ids,
        employees,
        expected_install_calls,
    ):
        """Test installing published app for users with different scenarios."""
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"status": "success"}

        with patch(
            "ms_teams_everstage.services.tenant_services.install_app_for_user"
        ) as mock_install_app, patch(
            "ms_teams_everstage.services.tenant_services.save_conversation_id_for_employees"
        ) as mock_save_conversation:
            mock_install_app.return_value = mock_response
            mock_save_conversation.return_value = {
                "<EMAIL>": {"config": "test"}
            }

            install_published_app_for_users(
                client_id,
                user_access_token,
                tenant_access_token,
                app_catalog_id,
                ms_teams_user_ids,
                employees,
            )

            assert mock_install_app.call_count == expected_install_calls
            mock_save_conversation.assert_called_once_with(
                client_id,
                tenant_access_token,
                app_catalog_id,
                ms_teams_user_ids,
                employees,
            )

    @pytest.mark.parametrize(
        "test_case, client_id, everstage_employees, mock_is_connected, mock_get_token, mock_get_user, mock_get_app, mock_install_app, mock_update_token, expected_result",
        [
            # Success case
            (
                "success",
                1,
                ["<EMAIL>"],
                True,
                ("user_access_token", "refresh_token"),
                [
                    {
                        "mail": "<EMAIL>",
                        "id": "d2f5af20-ff01-4c7a-9f8e-3485fc7faaf0",
                    }
                ],
                {"id": "13a5c19e-079a-4f25-ba87-77fcdf4b4533"},
                MagicMock(status_code=200),
                None,
                {"<EMAIL>": {"config": "test"}},
            ),
            # Multiple employees case
            (
                "multiple_employees",
                1,
                ["<EMAIL>", "<EMAIL>"],
                True,
                ("user_access_token", "refresh_token"),
                [
                    {
                        "mail": "<EMAIL>",
                        "id": "d2f5af20-ff01-4c7a-9f8e-3485fc7faaf0",
                    },
                    {
                        "mail": "<EMAIL>",
                        "id": "a2cd34f9-5b09-466b-bcec-5867ef563b0d",
                    },
                ],
                {"id": "13a5c19e-079a-4f25-ba87-77fcdf4b4533"},
                MagicMock(status_code=200),
                None,
                {
                    "<EMAIL>": {"config": "test"},
                    "<EMAIL>": {"config": "test"},
                },
            ),
            # Not connected case
            (
                "not_connected",
                1,
                ["<EMAIL>"],
                False,
                None,
                None,
                None,
                None,
                None,
                None,
            ),
            # No employees case
            (
                "no_employees",
                1,
                [],
                True,
                None,
                None,
                None,
                None,
                None,
                None,
            ),
            # No token case
            (
                "no_token",
                1,
                ["<EMAIL>"],
                True,
                (None, None),
                None,
                None,
                None,
                None,
                None,
            ),
            # Exception case
            (
                "exception",
                1,
                ["<EMAIL>"],
                True,
                Exception("Test error"),
                None,
                None,
                None,
                None,
                None,
            ),
        ],
    )
    def test_integrate_msteams_for_newly_added_users(
        self,
        resources,
        test_case,
        client_id,
        everstage_employees,
        mock_is_connected,
        mock_get_token,
        mock_get_user,
        mock_get_app,
        mock_install_app,
        mock_update_token,
        expected_result,
    ):
        """Test integrating MS Teams for newly added users with various scenarios."""
        with patch(
            "ms_teams_everstage.services.tenant_services.is_msteams_connected"
        ) as mock_is_connected_func, patch(
            "ms_teams_everstage.services.tenant_services.get_admin_access_token_from_refresh_token"
        ) as mock_get_token_func, patch(
            "ms_teams_everstage.services.tenant_services.get_single_user"
        ) as mock_get_user_func, patch(
            "ms_teams_everstage.services.tenant_services.get_app_details"
        ) as mock_get_app_func, patch(
            "ms_teams_everstage.services.tenant_services.install_app_for_user"
        ) as mock_install_app_func, patch(
            "ms_teams_everstage.services.tenant_services.update_tenant_token_details"
        ) as mock_update_token_func, patch(
            "ms_teams_everstage.services.tenant_services.save_conversation_id_for_employees"
        ) as mock_save_conversation, patch(
            "ms_teams_everstage.services.tenant_services.get_all_users"
        ) as mock_get_all_users_func:

            mock_is_connected_func.return_value = mock_is_connected
            if isinstance(mock_get_token, Exception):
                mock_get_token_func.side_effect = mock_get_token
            else:
                mock_get_token_func.return_value = mock_get_token
            mock_get_user_func.return_value = mock_get_user
            mock_get_app_func.return_value = mock_get_app
            mock_install_app_func.return_value = mock_install_app
            mock_save_conversation.return_value = expected_result

            if test_case == "multiple_employees":
                mock_get_all_users_func.return_value = [
                    {"mail": email, "id": mock_get_user[i]["id"]}
                    for i, email in enumerate(everstage_employees)
                ]

            result = integrate_msteams_for_newly_added_users(
                client_id, everstage_employees
            )

            assert result == expected_result
            mock_is_connected_func.assert_called_once_with(client_id)

            if test_case == "success":
                mock_get_token_func.assert_called_once_with(client_id)
                mock_get_user_func.assert_called_once_with(
                    "user_access_token", everstage_employees[0]
                )
                mock_get_app_func.assert_called_once_with("user_access_token")
                mock_install_app_func.assert_called_once()
                mock_update_token_func.assert_called_once_with(
                    client_id, "user_access_token", "refresh_token"
                )
                mock_save_conversation.assert_called_once()
            elif test_case == "multiple_employees":
                mock_get_token_func.assert_called_once_with(client_id)
                mock_get_all_users_func.assert_called_once_with("user_access_token")
                mock_get_app_func.assert_called_once_with("user_access_token")
                assert mock_install_app_func.call_count == 2
                mock_update_token_func.assert_called_once_with(
                    client_id, "user_access_token", "refresh_token"
                )
                mock_save_conversation.assert_called_once()
            elif test_case == "no_token":
                mock_get_token_func.assert_called_once_with(client_id)
            elif test_case == "exception":
                mock_get_token_func.assert_called_once_with(client_id)

    @pytest.mark.parametrize(
        "ms_teams_user_ids, everstage_employees, mock_is_integrated_return, expected_install_calls, expected_employees_for_save, expected_result, client_id, user_access_token, tenant_access_token, app_catalog_id",
        [
            # Success case - both users get installed
            (
                {
                    "<EMAIL>": "d2f5af20-ff01-4c7a-9f8e-3485fc7faaf0",
                    "<EMAIL>": "a2cd34f9-5b09-466b-bcec-5867ef563b0d",
                },
                ["<EMAIL>", "<EMAIL>"],
                False,
                2,
                ["<EMAIL>", "<EMAIL>"],
                {"<EMAIL>": {"config": "test"}},
                1,
                "eyJ0eXAiOiJKV1QiLCJub25jZSI6Ii03Z3lhQ1p0OVlyV2dBckpHdDd1VmNtWnRuM2tPME9PYzR6REwzR0hJR1EiLCJhbGciOiJSUzI1NiIsIng1dCI6IkNOdjBPSTNSd3FsSEZFVm5hb01Bc2hDSDJYRSIsImtpZCI6IkNOdjBPSTNSd3FsSEZFVm5hb01Bc2hDSDJYRSS9",
                "eyJ0eXAiOiJKV1QiLCJub25jZSI6Ii03Z3lhQ1p0OVlyV2dBckpHdDd1VmNtWnRuM2tPME9PYzR6REwzR0hJR1EiLCJhbGciOiJSUzI1NiIsIng1dCI6IkNOdjBPSTNSd3FsSEZFVm5hb01Bc2hDSDJYRSIsImtpZCI6IkNOdjBPSTNSd3FsSEZFVm5hb01Bc2hDSDJYRSS9",
                "13a5c19e-079a-4f25-ba87-77fcdf4b4533",
            ),
            # Partial success case - only one user gets installed
            (
                {
                    "<EMAIL>": "d2f5af20-ff01-4c7a-9f8e-3485fc7faaf0",
                    "<EMAIL>": None,
                },
                ["<EMAIL>", "<EMAIL>"],
                False,
                1,
                ["<EMAIL>"],
                {"<EMAIL>": {"config": "test"}},
                1,
                "eyJ0eXAiOiJKV1QiLCJub25jZSI6Ii03Z3lhQ1p0OVlyV2dBckpHdDd1VmNtWnRuM2tPME9PYzR6REwzR0hJR1EiLCJhbGciOiJSUzI1NiIsIng1dCI6IkNOdjBPSTNSd3FsSEZFVm5hb01Bc2hDSDJYRSIsImtpZCI6IkNOdjBPSTNSd3FsSEZFVm5hb01Bc2hDSDJYRSS9",
                "eyJ0eXAiOiJKV1QiLCJub25jZSI6Ii03Z3lhQ1p0OVlyV2dBckpHdDd1VmNtWnRuM2tPME9PYzR6REwzR0hJR1EiLCJhbGciOiJSUzI1NiIsIng1dCI6IkNOdjBPSTNSd3FsSEZFVm5hb01Bc2hDSDJYRSIsImtpZCI6IkNOdjBPSTNSd3FsSEZFVm5hb01Bc2hDSDJYRSS9",
                "13a5c19e-079a-4f25-ba87-77fcdf4b4533",
            ),
            # Already connected case
            (
                {
                    "<EMAIL>": "d2f5af20-ff01-4c7a-9f8e-3485fc7faaf0",
                },
                ["<EMAIL>"],
                True,
                0,
                [],
                {},
                1,
                "eyJ0eXAiOiJKV1QiLCJub25jZSI6Ii03Z3lhQ1p0OVlyV2dBckpHdDd1VmNtWnRuM2tPME9PYzR6REwzR0hJR1EiLCJhbGciOiJSUzI1NiIsIng1dCI6IkNOdjBPSTNSd3FsSEZFVm5hb01Bc2hDSDJYRSIsImtpZCI6IkNOdjBPSTNSd3FsSEZFVm5hb01Bc2hDSDJYRSS9",
                "eyJ0eXAiOiJKV1QiLCJub25jZSI6Ii03Z3lhQ1p0OVlyV2dBckpHdDd1VmNtWnRuM2tPME9PYzR6REwzR0hJR1EiLCJhbGciOiJSUzI1NiIsIng1dCI6IkNOdjBPSTNSd3FsSEZFVm5hb01Bc2hDSDJYRSIsImtpZCI6IkNOdjBPSTNSd3FsSEZFVm5hb01Bc2hDSDJYRSS9",
                "13a5c19e-079a-4f25-ba87-77fcdf4b4533",
            ),
        ],
    )
    def test_install_app_for_new_users(
        self,
        resources,
        ms_teams_user_ids,
        everstage_employees,
        mock_is_integrated_return,
        expected_install_calls,
        expected_employees_for_save,
        expected_result,
        client_id,
        user_access_token,
        tenant_access_token,
        app_catalog_id,
    ):
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"status": "success"}

        with patch(
            "ms_teams_everstage.services.tenant_services.is_msteams_integrated"
        ) as mock_is_integrated, patch(
            "ms_teams_everstage.services.tenant_services.install_app_for_user"
        ) as mock_install_app, patch(
            "ms_teams_everstage.services.tenant_services.save_conversation_id_for_employees"
        ) as mock_save_conversation:
            mock_is_integrated.return_value = mock_is_integrated_return
            mock_install_app.return_value = mock_response
            mock_save_conversation.return_value = expected_result

            result = install_app_for_new_users(
                client_id,
                user_access_token,
                tenant_access_token,
                app_catalog_id,
                ms_teams_user_ids,
                everstage_employees,
            )

            assert result == expected_result
            assert mock_is_integrated.call_count == len(everstage_employees)
            assert mock_install_app.call_count == expected_install_calls
            mock_save_conversation.assert_called_once_with(
                client_id,
                tenant_access_token,
                app_catalog_id,
                ms_teams_user_ids,
                expected_employees_for_save,
                should_update_employee_config=True,
            )

    @pytest.mark.parametrize(
        "client_id, user_access_token, tenant_access_token, app_catalog_id, ms_teams_user_ids, everstage_employees, mock_response_status, expected_update_app_calls, expected_employees_for_save",
        [
            # Success case - both users get updated
            (
                1,
                "eyJ0eXAiOiJKV1QiLCJub25jZSI6Ii03Z3lhQ1p0OVlyV2dBckpHdDd1VmNtWnRuM2tPME9PYzR6REwzR0hJR1EiLCJhbGciOiJSUzI1NiIsIng1dCI6IkNOdjBPSTNSd3FsSEZFVm5hb01Bc2hDSDJYRSIsImtpZCI6IkNOdjBPSTNSd3FsSEZFVm5hb01Bc2hDSDJYRSS9",
                "eyJ0eXAiOiJKV1QiLCJub25jZSI6Ii03Z3lhQ1p0OVlyV2dBckpHdDd1VmNtWnRuM2tPME9PYzR6REwzR0hJR1EiLCJhbGciOiJSUzI1NiIsIng1dCI6IkNOdjBPSTNSd3FsSEZFVm5hb01Bc2hDSDJYRSIsImtpZCI6IkNOdjBPSTNSd3FsSEZFVm5hb01Bc2hDSDJYRSS9",
                "13a5c19e-079a-4f25-ba87-77fcdf4b4533",
                {
                    "<EMAIL>": "d2f5af20-ff01-4c7a-9f8e-3485fc7faaf0",
                    "<EMAIL>": "a2cd34f9-5b09-466b-bcec-5867ef563b0d",
                },
                [
                    MagicMock(employee_email_id="<EMAIL>"),
                    MagicMock(employee_email_id="<EMAIL>"),
                ],
                200,
                2,
                ["<EMAIL>", "<EMAIL>"],
            ),
            # Partial success case - only one user gets updated
            (
                1,
                "eyJ0eXAiOiJKV1QiLCJub25jZSI6Ii03Z3lhQ1p0OVlyV2dBckpHdDd1VmNtWnRuM2tPME9PYzR6REwzR0hJR1EiLCJhbGciOiJSUzI1NiIsIng1dCI6IkNOdjBPSTNSd3FsSEZFVm5hb01Bc2hDSDJYRSIsImtpZCI6IkNOdjBPSTNSd3FsSEZFVm5hb01Bc2hDSDJYRSS9",
                "eyJ0eXAiOiJKV1QiLCJub25jZSI6Ii03Z3lhQ1p0OVlyV2dBckpHdDd1VmNtWnRuM2tPME9PYzR6REwzR0hJR1EiLCJhbGciOiJSUzI1NiIsIng1dCI6IkNOdjBPSTNSd3FsSEZFVm5hb01Bc2hDSDJYRSIsImtpZCI6IkNOdjBPSTNSd3FsSEZFVm5hb01Bc2hDSDJYRSS9",
                "13a5c19e-079a-4f25-ba87-77fcdf4b4533",
                {
                    "<EMAIL>": "d2f5af20-ff01-4c7a-9f8e-3485fc7faaf0",
                    "<EMAIL>": None,
                },
                [
                    MagicMock(employee_email_id="<EMAIL>"),
                    MagicMock(employee_email_id="<EMAIL>"),
                ],
                200,
                1,
                ["<EMAIL>"],
            ),
            # Failed response case
            (
                1,
                "eyJ0eXAiOiJKV1QiLCJub25jZSI6Ii03Z3lhQ1p0OVlyV2dBckpHdDd1VmNtWnRuM2tPME9PYzR6REwzR0hJR1EiLCJhbGciOiJSUzI1NiIsIng1dCI6IkNOdjBPSTNSd3FsSEZFVm5hb01Bc2hDSDJYRSIsImtpZCI6IkNOdjBPSTNSd3FsSEZFVm5hb01Bc2hDSDJYRSS9",
                "eyJ0eXAiOiJKV1QiLCJub25jZSI6Ii03Z3lhQ1p0OVlyV2dBckpHdDd1VmNtWnRuM2tPME9PYzR6REwzR0hJR1EiLCJhbGciOiJSUzI1NiIsIng1dCI6IkNOdjBPSTNSd3FsSEZFVm5hb01Bc2hDSDJYRSIsImtpZCI6IkNOdjBPSTNSd3FsSEZFVm5hb01Bc2hDSDJYRSS9",
                "13a5c19e-079a-4f25-ba87-77fcdf4b4533",
                {
                    "<EMAIL>": "d2f5af20-ff01-4c7a-9f8e-3485fc7faaf0",
                },
                [MagicMock(employee_email_id="<EMAIL>")],
                400,
                1,
                [],
            ),
        ],
    )
    def test_update_published_app_for_users(
        self,
        resources,
        client_id,
        user_access_token,
        tenant_access_token,
        app_catalog_id,
        ms_teams_user_ids,
        everstage_employees,
        mock_response_status,
        expected_update_app_calls,
        expected_employees_for_save,
    ):
        mock_response = MagicMock()
        mock_response.status_code = mock_response_status

        with patch(
            "ms_teams_everstage.services.tenant_services.update_app_installed_for_user"
        ) as mock_update_app, patch(
            "ms_teams_everstage.services.tenant_services.save_conversation_id_for_employees"
        ) as mock_save_conversation:
            mock_update_app.return_value = mock_response
            mock_save_conversation.return_value = {}

            update_published_app_for_users(
                client_id,
                user_access_token,
                tenant_access_token,
                app_catalog_id,
                ms_teams_user_ids,
                everstage_employees,
            )

            assert mock_update_app.call_count == expected_update_app_calls

            actual_call = mock_save_conversation.call_args
            actual_args = actual_call[0]

            actual_employees = actual_args[4]
            actual_employee_emails = [emp.employee_email_id for emp in actual_employees]

            assert actual_employee_emails == expected_employees_for_save

    @pytest.mark.parametrize(
        "client_id, mail_id, ked",
        [
            (1, "<EMAIL>", timezone.now()),
        ],
    )
    def test_disconnect_msteams_for_client(
        self,
        resources,
        client_id,
        mail_id,
        ked,
    ):
        with patch(
            "ms_teams_everstage.services.tenant_services.set_client_feature"
        ) as mock_set_feature, patch(
            "ms_teams_everstage.services.tenant_services.timezone.now"
        ) as mock_timezone, patch(
            "ms_teams_everstage.services.tenant_services.IntegrationConfigAccessor"
        ) as mock_integration_accessor, patch(
            "ms_teams_everstage.services.tenant_services.update_msteams_notification_channel_connection_status"
        ) as mock_update_status, patch(
            "ms_teams_everstage.services.tenant_services.CoreAnalytics"
        ) as mock_analytics:

            mock_timezone.return_value = ked
            mock_integration_accessor_instance = MagicMock()
            mock_integration_accessor.return_value = mock_integration_accessor_instance
            mock_analytics_instance = MagicMock()
            mock_analytics.return_value = mock_analytics_instance

            disconnect_msteams_for_client(client_id, mail_id)

            mock_set_feature.assert_called_once_with(
                client_id, "msteams_connected", False
            )

            mock_integration_accessor_instance.invalidate_all_employees_in_msteams_team.assert_called_once_with(
                client_id=client_id, end_time=ked
            )

            mock_update_status.assert_called_once_with(
                client_id,
                NotificationChannelConnectionStatus.DISCONNECTED.value,
                mail_id,
            )

            mock_analytics.assert_called_once_with(analyser_type="segment")
            mock_analytics_instance.send_analytics.assert_called_once()

            analytics_call = mock_analytics_instance.send_analytics.call_args[0][0]
            assert analytics_call["user_id"] == mail_id
            assert (
                analytics_call["event_name"]
                == SegmentEvents.MS_TEAMS_DISCONNECTED.value
            )
            assert (
                analytics_call["event_properties"][
                    SegmentProperties.MS_TEAMS_EMAIL.value
                ]
                == mail_id
            )
            assert analytics_call["event_properties"][
                SegmentProperties.DISCONNECT_MS_TEAMS_DATE.value
            ] == datetime.now().strftime("%d-%m-%Y")

    @pytest.mark.parametrize(
        "client_id, code, login_email_id, user_access_token, refresh_token, tenant_id, msteams_admin_email_id, tenant_access_token, app_catalog_id, app_external_id, app_version, ms_teams_user_ids, everstage_employees",
        [
            (
                1,
                "e133123-vs324d1-324123-123123",
                "<EMAIL>",
                "eyJ0eXAiOiJKV1QiLCJub25jZSI6Ii03Z3lhQ1p0OVlyV2dBckpHdDd1VmNtWnRuM2tPME9PYzR6REwzR0hJR1EiLCJhbGciOiJSUzI1NiIsIng1dCI6IkNOdjBPSTNSd3FsSEZFVm5hb01Bc2hDSDJYRSIsImtpZCI6IkNOdjBPSTNSd3FsSEZFVm5hb01Bc2hDSDJYRSS9",
                "eyJ0eXAiOiJKV1QiLCJub25jZSI6Ii03Z3lhQ1p0OVlyV2dBckpHdDd1VmNtWnRuM2tPME9PYzR6REwzR0hJR1EiLCJhbGciOiJSUzI1NiIsIng1dCI6IkNOdjBPSTNSd3FsSEZFVm5hb01Bc2hDSDJYRSIsImtpZCI6IkNOdjBPSTNSd3FsSEZFVm5hb01Bc2hDSDJYRSS9",
                "4a894d22-6c6b-43a2-94db-1acc429d503a",
                "<EMAIL>",
                "eyJ0eXAiOiJKV1QiLCJub25jZSI6Ii03Z3lhQ1p0OVlyV2dBckpHdDd1VmNtWnRuM2tPME9PYzR6REwzR0hJR1EiLCJhbGciOiJSUzI1NiIsIng1dCI6IkNOdjBPSTNSd3FsSEZFVm5hb01Bc2hDSDJYRSIsImtpZCI6IkNOdjBPSTNSd3FsSEZFVm5hb01Bc2hDSDJYRSS9",
                "13a5c19e-079a-4f25-ba87-77fcdf4b4533",
                "13a5c19e-079a-4f25-ba87-77fcdf4b4533",
                "1.0.0",
                {"<EMAIL>": "d2f5af20-ff01-4c7a-9f8e-3485fc7faaf0"},
                ["<EMAIL>"],
            ),
        ],
    )
    def test_install_msteams_app_for_client(
        self,
        resources,
        client_id,
        code,
        login_email_id,
        user_access_token,
        refresh_token,
        tenant_id,
        msteams_admin_email_id,
        tenant_access_token,
        app_catalog_id,
        app_external_id,
        app_version,
        ms_teams_user_ids,
        everstage_employees,
    ):
        """Test successful installation of MS Teams app for a new client."""
        mock_app_details = {
            "id": app_catalog_id,
            "externalId": app_external_id,
            "appDefinitions": [{"version": app_version}],
        }

        mock_publish_response = MagicMock()
        mock_publish_response.status_code = 200
        mock_publish_response.json.return_value = {"status": "success"}

        with patch(
            "ms_teams_everstage.services.tenant_services.get_admin_access_token"
        ) as mock_get_token, patch(
            "ms_teams_everstage.services.tenant_services.jwt_hard_decode_token"
        ) as mock_decode, patch(
            "ms_teams_everstage.services.tenant_services.get_tenant_access_token"
        ) as mock_get_tenant_token, patch(
            "ms_teams_everstage.services.tenant_services.get_app_details"
        ) as mock_get_app, patch(
            "ms_teams_everstage.services.tenant_services.get_msteams_users_details_for_client"
        ) as mock_get_users, patch(
            "ms_teams_everstage.services.tenant_services.publish_app_to_catalog"
        ) as mock_publish_app, patch(
            "ms_teams_everstage.services.tenant_services.persist_msteams_tenant_details"
        ) as mock_persist_details, patch(
            "ms_teams_everstage.services.tenant_services.install_published_app_for_users"
        ) as mock_install_app, patch(
            "ms_teams_everstage.services.tenant_services.set_client_feature"
        ) as mock_set_feature, patch(
            "ms_teams_everstage.services.tenant_services.save_tenant_token_details"
        ) as mock_save_token, patch(
            "ms_teams_everstage.services.tenant_services.update_msteams_notification_channel_connection_status"
        ) as mock_update_status, patch(
            "ms_teams_everstage.services.tenant_services.CoreAnalytics"
        ) as mock_analytics:

            mock_get_token.return_value = (user_access_token, refresh_token)
            mock_decode.return_value = {
                "tid": tenant_id,
                "unique_name": msteams_admin_email_id,
            }
            mock_get_tenant_token.return_value = tenant_access_token
            mock_get_app.side_effect = [None, mock_app_details]
            mock_get_users.return_value = (ms_teams_user_ids, everstage_employees)
            mock_publish_app.return_value = mock_publish_response
            mock_analytics_instance = MagicMock()
            mock_analytics.return_value = mock_analytics_instance

            install_msteams_app_for_client(client_id, code, login_email_id)

            mock_get_token.assert_called_once_with(
                code, login_email_id, redirect_uri="settings/notifications"
            )
            mock_decode.assert_called_once_with(user_access_token)
            mock_get_tenant_token.assert_called_once_with(tenant_id)
            assert mock_get_app.call_count == 2
            mock_get_users.assert_called_once_with(client_id, user_access_token)
            mock_publish_app.assert_called_once_with(user_access_token)
            mock_persist_details.assert_called_once_with(
                client_id,
                tenant_id,
                app_catalog_id,
                app_external_id,
                msteams_admin_email_id,
                login_email_id,
                app_version,
            )
            mock_install_app.assert_called_once_with(
                client_id,
                user_access_token,
                tenant_access_token,
                app_catalog_id,
                ms_teams_user_ids,
                everstage_employees,
            )
            mock_set_feature.assert_called_once_with(
                client_id, "msteams_connected", True
            )
            mock_save_token.assert_called_once_with(
                client_id, tenant_id, user_access_token, refresh_token
            )
            mock_update_status.assert_called_once_with(
                client_id,
                NotificationChannelConnectionStatus.CONNECTED.value,
                login_email_id,
            )
            mock_analytics_instance.send_analytics.assert_called_once()
