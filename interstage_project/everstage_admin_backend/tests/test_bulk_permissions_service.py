"""
Unit tests for the bulk permissions service.

This module tests the core functionality of the bulk permission management system.
The service now expects complete role data with permissions already organized by components
from the frontend, eliminating the need for backend permission mapping logic.
"""

from unittest.mock import MagicMock, patch

import pytest
from rest_framework import status
from rest_framework.response import Response

from everstage_admin_backend.services.bulk_permissions_service import (
    bulk_add_permissions,
)


class TestBulkAddPermissions:
    """Test cases for the bulk_add_permissions function."""

    @pytest.fixture
    def mock_logger(self):
        """Create a mock logger for testing."""
        return MagicMock()

    @pytest.fixture
    def mock_audit(self):
        """Create a mock audit object for testing."""
        return {"user_id": "test_user", "action": "bulk_add_permissions"}

    @pytest.fixture
    def sample_role_updates(self):
        """Sample role updates data for testing with complete permission structure."""
        return [
            {
                "client_id": 123,
                "role_permission_id": "role-uuid-1",
                "display_name": "Test Role 1",
                "description": "Test role 1 description",
                "permissions": {
                    "dashboard": {
                        "permissions": ["view:dashboard", "edit:dashboard"],
                        "dataPermission": None,
                    },
                    "databooks": {
                        "permissions": ["view:databook"],
                        "dataPermission": {
                            "type": "ALL_DATA",
                            "selectedUserGroups": [],
                            "isUserGroupsSelected": False,
                            "isReportingTeamSelected": False,
                        },
                    },
                },
            },
            {
                "client_id": 456,
                "role_permission_id": "role-uuid-2",
                "display_name": "Test Role 2",
                "description": "Test role 2 description",
                "permissions": {
                    "settings": {
                        "permissions": ["manage:config", "manage:datasettings"],
                        "dataPermission": None,
                    }
                },
            },
        ]

    def test_bulk_add_permissions_empty_input(self, mock_logger, mock_audit):
        """Test bulk_add_permissions with empty role updates."""
        result = bulk_add_permissions([], mock_logger, mock_audit)

        assert result.status_code == status.HTTP_400_BAD_REQUEST
        assert "No role updates provided" in result.data["error_message"]

    def test_bulk_add_permissions_missing_fields(self, mock_logger, mock_audit):
        """Test bulk_add_permissions with missing required fields."""
        invalid_updates = [
            {"client_id": 123},  # Missing required fields
            {
                "role_permission_id": "role-uuid-1",
                "display_name": "Test Role",
            },  # Missing client_id and permissions
        ]

        result = bulk_add_permissions(invalid_updates, mock_logger, mock_audit)

        assert result.status_code == status.HTTP_207_MULTI_STATUS
        assert result.data["total_updates"] == 2
        assert result.data["successful_updates"] == 0
        assert result.data["failed_updates"] == 2
        assert len(result.data["errors"]) == 2

    @patch("everstage_admin_backend.services.bulk_permissions_service.edit_role")
    def test_bulk_add_permissions_success(
        self,
        mock_edit_role,
        sample_role_updates,
        mock_logger,
        mock_audit,
    ):
        """Test successful bulk permission addition."""
        mock_edit_role.return_value = Response(
            {"message": "Success"}, status=status.HTTP_201_CREATED
        )

        result = bulk_add_permissions(sample_role_updates, mock_logger, mock_audit)

        assert result.status_code == status.HTTP_200_OK
        assert result.data["total_updates"] == 2
        assert result.data["successful_updates"] == 2
        assert result.data["failed_updates"] == 0
        assert len(result.data["results"]) == 2

        # Verify edit_role was called correctly
        assert mock_edit_role.call_count == 2

    @patch("everstage_admin_backend.services.bulk_permissions_service.edit_role")
    def test_bulk_add_permissions_edit_role_failure(
        self,
        mock_edit_role,
        sample_role_updates,
        mock_logger,
        mock_audit,
    ):
        """Test bulk_add_permissions when edit_role fails."""
        mock_edit_role.return_value = Response(
            {"error": "Edit failed"}, status=status.HTTP_400_BAD_REQUEST
        )

        result = bulk_add_permissions(sample_role_updates, mock_logger, mock_audit)

        assert result.status_code == status.HTTP_207_MULTI_STATUS
        assert result.data["total_updates"] == 2
        assert result.data["successful_updates"] == 0
        assert result.data["failed_updates"] == 2

    def test_bulk_add_permissions_top_level_exception(self, mock_logger, mock_audit):
        """Test bulk_add_permissions top-level exception handling."""
        invalid_updates = "not a list"

        result = bulk_add_permissions(invalid_updates, mock_logger, mock_audit)

        assert result.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
        assert "Internal server error during bulk add" in result.data["error_message"]
