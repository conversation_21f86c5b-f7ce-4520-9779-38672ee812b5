"""
Bulk Permissions Service

This service provides functionality for bulk addition of permissions to multiple roles
across multiple clients. It uses the existing edit_role service internally to ensure
data integrity and consistency with existing permission management logic.

The frontend sends complete role data with permissions already organized by components,
eliminating the need for backend permission mapping logic.
"""

import traceback

from rest_framework import status
from rest_framework.response import Response

# Import the existing rbac service for safe permission updates
from spm.services.rbac_services import edit_role


def bulk_add_permissions(
    role_updates,  # List of complete role data with permissions already organized
    logger,
    audit,
):
    """
    Bulk add permissions for multiple roles across multiple clients.

    This function processes a list of role updates with complete permission structures
    that are already organized by components from the frontend. It uses the existing
    edit_role service internally to ensure data integrity and maintain consistency
    with existing permission management logic.

    Args:
        role_updates: List of dicts with structure:
            {
                'client_id': int,                    # Target client ID
                'role_permission_id': str,           # Role permission ID to update
                'display_name': str,                 # Role display name
                'description': str,                  # Role description
                'permissions': dict                  # Complete permissions structure organized by components
                    # Format: {component_name: {'permissions': [list], 'dataPermission': obj}}
            }
        logger: Logger instance for tracking operations and errors
        audit: Audit information for tracking changes

    Returns:
        Response: REST framework response with:
            - total_updates: Total number of updates attempted
            - successful_updates: Number of successful updates
            - failed_updates: Number of failed updates
            - results: List of successful update details
            - errors: List of error messages for failed updates

        Status codes:
            - 200: All updates successful
            - 207: Partial success (some updates failed)
            - 400: Invalid input
            - 500: Internal server error
    """

    # Validate input: ensure we have role updates to process
    if not role_updates:
        return Response(
            {"error_message": "No role updates provided"},
            status=status.HTTP_400_BAD_REQUEST,
        )

    # Initialize tracking variables for results and errors
    results = []  # Successful operations
    errors = []  # Failed operations with error messages

    try:
        # Process each role update request
        for update in role_updates:
            # Extract required fields from the update request
            client_id = update.get("client_id")
            role_permission_id = update.get("role_permission_id")
            display_name = update.get("display_name")
            description = update.get("description", "")
            permissions = update.get("permissions")

            print(description)
            # Validate that all required fields are present
            if (
                not client_id
                or not role_permission_id
                or not display_name
                or not permissions
            ):
                error_msg = f"Missing required fields for update: {update}"
                errors.append(error_msg)
                logger.error(error_msg)
                continue  # Skip this update and continue with the next one

            try:
                # Use the existing edit_role service to safely update the role
                # This maintains consistency with existing permission management logic
                result = edit_role(
                    client_id=client_id,
                    role_permission_id=role_permission_id,
                    permissions=permissions,
                    display_name=display_name,
                    description=description,
                    logger=logger,
                    audit=audit,
                )

                # Check if the role update was successful
                if result.status_code == status.HTTP_201_CREATED:
                    # Record successful update
                    results.append(
                        {
                            "client_id": client_id,
                            "role_permission_id": role_permission_id,
                            "status": "success",
                            "message": "Permissions updated successfully",
                        }
                    )
                    logger.info(
                        f"Successfully updated permissions for role {role_permission_id} in client {client_id}"
                    )
                else:
                    # Record failed update with error details
                    error_msg = f"Failed to update role {role_permission_id} in client {client_id}: {result.data}"
                    errors.append(error_msg)
                    logger.error(error_msg)

            except Exception as e:
                # Handle any exceptions that occur during individual role processing
                error_msg = f"Exception updating role {role_permission_id} in client {client_id}: {str(e)}"
                errors.append(error_msg)
                logger.error(error_msg)
                logger.error(
                    traceback.format_exc()
                )  # Log full stack trace for debugging

        # Prepare comprehensive response with operation summary
        response_data = {
            "total_updates": len(role_updates),
            "successful_updates": len(results),
            "failed_updates": len(errors),
            "results": results,
            "errors": errors,
        }

        # Return appropriate status based on success/failure ratio
        if errors:
            # Some operations failed - return partial success status
            logger.warning(
                f"Bulk update completed with {len(errors)} errors out of {len(role_updates)} total updates"
            )
            return Response(
                response_data,
                status=status.HTTP_207_MULTI_STATUS,  # Partial success
            )
        else:
            # All operations succeeded
            logger.info(
                f"Bulk update completed successfully for all {len(role_updates)} updates"
            )
            return Response(
                response_data,
                status=status.HTTP_200_OK,
            )

    except Exception as exc:
        # Handle any unexpected exceptions at the top level
        print(f"Exception in bulk_add_permissions: {exc}")
        error_dict = {"trace_back": traceback.format_exc()}
        logger.error("Exception in bulk_add_permissions", error_dict)
        return Response(
            {"error_message": "Internal server error during bulk add"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )
