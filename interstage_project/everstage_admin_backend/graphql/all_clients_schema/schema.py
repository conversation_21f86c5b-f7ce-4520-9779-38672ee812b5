# pylint: disable=unsubscriptable-object, no-member, unsupported-membership-test, unused-import

import graphene
from django.db.models.functions import Lower

from commission_engine.models.client_models import Client
from commission_engine.utils.general_data import RbacPermissions
from interstage_project.auth_utils import permission_required
from spm.accessors.rbac_accessors import <PERSON><PERSON>sAccessor, RolePermissionsAccessor
from spm.graphql.client_schema.schema import ClientAdminType, ClientType

# List of permission IDs to exclude from queries
EXCLUDED_PERMISSION_IDS = [
    "view:commissionplan",
    "edit:commissionplan",
    "create:commissionplan",
    "delete:commissionplan",
    "publish:commissionplan",
    "allow:impersonation",
]


class PermissionType(graphene.ObjectType):
    id = graphene.String()
    name = graphene.String()
    description = graphene.String()
    parentId = graphene.String()
    componentSystemName = graphene.String()
    showDataPermissions = graphene.Boolean()


class ClientPermissionsType(graphene.ObjectType):
    clientId = graphene.Int()
    permissions = graphene.List(PermissionType)


class RoleType(graphene.ObjectType):
    rolePermissionId = graphene.String()
    displayName = graphene.String()
    clientId = graphene.Int()
    permissions = graphene.JSONString()
    description = graphene.String()


class ClientQuery(object):
    all_clients = graphene.List(ClientType, sort=graphene.String())
    all_client_admin_console = graphene.List(ClientAdminType)
    client_roles = graphene.List(RoleType, client_ids=graphene.List(graphene.Int))
    all_permissions = graphene.List(PermissionType, client_id=graphene.Int())
    batch_client_permissions = graphene.List(
        ClientPermissionsType, client_ids=graphene.List(graphene.Int)
    )

    def resolve_all_clients(self, info, **kwargs):
        # Add sorting as conditional param
        sort = kwargs.get("sort", "asc")
        if sort == "asc":
            res = Client.objects.all().order_by(Lower("name"))
        else:
            res = Client.objects.all()
        return res

    @permission_required(
        [
            RbacPermissions.MANAGE_ADMINUI.value,
            RbacPermissions.MANAGE_AGENT_STUDIO.value,
            RbacPermissions.MANAGE_CUSTOMERS.value,
        ]
    )
    def resolve_all_client_admin_console(self, info, **kwargs):
        return Client.objects.all()

    @permission_required(RbacPermissions.MANAGE_ADMINUI.value)
    def resolve_all_permissions(self, info, client_id=None, **kwargs):
        """
        Resolve all permissions from the system.
        If client_id is provided, return permissions for that client.
        If client_id is None or 1, return all distinct system permissions.
        """
        if client_id is None or client_id == 1:
            # Return all distinct system permissions
            permissions_accessor = PermissionsAccessor(
                1
            )  # Client ID doesn't matter for system-wide query
            all_permissions = (
                permissions_accessor.get_all_distinct_permissions_system_wide()
            )
        else:
            # Get permissions for the specific client
            permissions_accessor = PermissionsAccessor(client_id)
            all_permissions = permissions_accessor.get_all_permissions()

        # Convert to PermissionType objects
        permission_list = []

        for perm in all_permissions:
            # Skip excluded permissions
            if perm["permission_id"] in EXCLUDED_PERMISSION_IDS:
                continue

            permission_list.append(
                PermissionType(
                    id=perm["permission_id"],
                    name=perm["permission_name"],
                    description=perm.get("permission_description", ""),
                    parentId=perm.get("parent_id", ""),
                    componentSystemName=perm.get("component_system_name", ""),
                    showDataPermissions=perm.get("show_data_permissions", False),
                )
            )

        return permission_list

    @permission_required(RbacPermissions.MANAGE_ADMINUI.value)
    def resolve_client_roles(self, info, client_ids=None, **kwargs):
        """
        Resolve client roles and their permissions for multiple clients.
        If client_ids is provided, return roles for those clients.
        If client_ids is None, return empty list.
        """
        if client_ids is None or len(client_ids) == 0:
            return []

        all_roles = []

        for client_id in client_ids:
            # Get roles for this client
            roles = RolePermissionsAccessor(client_id).client_kd_aware()

            for role in roles:

                # Add client_id to the role for frontend identification
                role_obj = RoleType(
                    rolePermissionId=str(role.role_permission_id),
                    displayName=role.display_name,
                    clientId=client_id,
                    permissions=role.permissions,
                    description=role.description,
                )
                all_roles.append(role_obj)

        return all_roles

    @permission_required(RbacPermissions.MANAGE_ADMINUI.value)
    def resolve_batch_client_permissions(self, info, client_ids=None, **kwargs):
        """
        Resolve permissions for multiple clients in a single query.
        Returns a list of ClientPermissionsType objects.
        """
        if not client_ids or len(client_ids) == 0:
            return []

        result = []

        for client_id in client_ids:
            # Get permissions for this client
            permissions_accessor = PermissionsAccessor(client_id)
            all_permissions = (
                permissions_accessor.get_all_permissions_with_show_data_permissions()
            )

            # Convert to PermissionType objects with filtering
            permission_list = []
            for perm in all_permissions:
                # Skip excluded permissions
                if perm["permission_id"] in EXCLUDED_PERMISSION_IDS:
                    continue

                permission_list.append(
                    PermissionType(
                        id=perm["permission_id"],
                        name=perm["permission_name"],
                        description=perm.get("permission_description", ""),
                        parentId=perm.get("parent_id", ""),
                        componentSystemName=perm.get("component_system_name", ""),
                        showDataPermissions=perm.get("show_data_permissions", False),
                    )
                )

            result.append(
                ClientPermissionsType(clientId=client_id, permissions=permission_list)
            )

        return result
