from django.db import transaction
from django.utils.decorators import method_decorator
from rest_framework.views import APIView

from commission_engine.utils.general_data import RbacPermissions
from everstage_admin_backend.services.bulk_permissions_service import (
    bulk_add_permissions,
)
from interstage_project.auth_utils import requires_scope
from interstage_project.utils import add_log_context_view


class BulkAddPermissions(APIView):
    # TODO: Uncomment this line once the user has the proper permissions
    @method_decorator(
        requires_scope(RbacPermissions.MANAGE_ADMINUI.value), name="dispatch"
    )
    @transaction.atomic
    @add_log_context_view("BulkAddPermissions")
    def post(self, request):
        """
        Bulk add permissions for multiple roles across multiple clients.

        Expected request body:
        {
            "role_updates": [
                {
                    "client_id": 123,
                    "rolePermissionId": "uuid-string",
                    "displayName": "Role Name",
                    "description": "Role description",
                    "permissions": {
                        "component_name": {
                            "permissions": ["permission_id_1", "permission_id_2"],
                            "dataPermission": {...} or null
                        },
                        ...
                    }
                },
                ...
            ]
        }
        """
        role_updates = request.data.get("role_updates", [])
        logger = request.logger
        audit = request.audit

        return bulk_add_permissions(
            role_updates=role_updates,
            logger=logger,
            audit=audit,
        )
