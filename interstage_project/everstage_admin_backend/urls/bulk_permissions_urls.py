"""
Bulk Permissions URL Configuration

This module defines the URL patterns for the bulk permissions management feature.
It provides endpoints for performing bulk operations on user permissions across
multiple clients and roles.

The bulk permissions feature allows administrators to:
- Add permissions to multiple roles simultaneously
- Process permissions across multiple clients in a single operation
- Maintain data integrity through validated bulk operations

URL Endpoints:
- bulk_permissions_add: POST endpoint for adding permissions in bulk
"""

from django.urls import path

# Import the view class that handles bulk permission operations
from everstage_admin_backend.views.bulk_permissions_view import BulkAddPermissions

# URL patterns for bulk permissions management
urlpatterns = [
    path(
        "bulk_permissions_add",  # URL path: /everstage_admin/bulk_permissions_add
        BulkAddPermissions.as_view(),  # View class that handles the request
        name="bulk_add_permissions",  # Named URL pattern for reverse lookups
    ),
    # Future bulk operations can be added here:
    # - bulk_permissions_remove: for removing permissions in bulk
    # - bulk_permissions_update: for updating permission sets
    # - bulk_permissions_validate: for validating permission assignments
]
