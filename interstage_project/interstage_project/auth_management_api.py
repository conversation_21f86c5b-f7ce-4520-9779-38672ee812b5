import json
import os
import secrets
import string
from distutils.log import error
from typing import Dict, List, Optional
from urllib.parse import quote

import requests
from django.conf import settings
from django.core.cache import cache
from requests.exceptions import HTTPError, RequestException, URLRequired
from sendgrid.helpers.mail import Mail

from commission_engine.accessors.client_accessor import get_auth_connection_name
from everstage_infra.aws_infra.ecs import is_prod_env
from interstage_project.session_utils import restricted_usage___get_login_user
from interstage_project.utils import LogWithContext, log_me
from slack_everstage.utils.slack_utils import get_site_url
from spm.accessors.config_accessors.employee_accessor import EmployeeAccessor
from spm.services.email_services.email_services import send_email

JWT_AUTH = getattr(settings, "JWT_AUTH", None)
MGMT_API = getattr(settings, "MGMT_API", None)
AUTH0_DB_CONNECTION = "Username-Password-Authentication"
allowed_password_chars = string.ascii_letters + string.digits + string.punctuation

logger = LogWithContext()
mgmt_api_base_url = MGMT_API["AUDIENCE"]

# Settings data {'JWT_PAYLOAD_GET_USERNAME_HANDLER': 'interstage_project.auth_utils.jwt_get_username_from_payload_handler',
# 'JWT_DECODE_HANDLER': 'interstage_project.auth_utils.jwt_decode_token', 'JWT_ALGORITHM': 'RS256', 'JWT_AUDIENCE': 'https://everstage-icm',
# 'JWT_ISSUER': 'https://everstage-dev.us.auth0.com/', 'JWT_AUTH_HEADER_PREFIX': 'Bearer'}


def get_management_api_access_token():
    try:
        mgmt_api_access_token = cache.get("mgmt_api_access_token")
        if mgmt_api_access_token is None:
            payload = {
                "grant_type": "client_credentials",
                "client_id": MGMT_API["CLIENT_ID"],
                "client_secret": MGMT_API["CLIENT_SECRET"],
                "audience": MGMT_API["AUDIENCE"],
            }
            issuer = MGMT_API["AUTH_MGMT_ISSUER"]
            base_url = f"{issuer}oauth/token"
            print(f"getting access token payload {payload}")
            res = requests.post(base_url, data=payload)
            oauth = res.json()
            access_token = oauth.get("access_token")
            cache.set("mgmt_api_access_token", access_token, 86000)
            return access_token
        else:
            return mgmt_api_access_token
    except Exception as e:
        print(e)


def get_header_with_mgmt_api_bearer():
    access_token = get_management_api_access_token()
    header = {
        "Authorization": "Bearer " + access_token,
        "Content-Type": "application/json",
    }
    return header


def create_oauth_user(user_data):
    email = user_data["employee_email_id"]
    try:
        access_token = get_management_api_access_token()
        header = {
            "Authorization": "Bearer " + access_token,
            "Content-Type": "application/json",
        }
        user_detail = _get_auth0_user_detail(email, header)
        if user_detail and "user_id" in user_detail and user_detail["user_id"]:
            response = update_user_password_by_id(user_detail["user_id"])
            password = response["password"]
        else:
            password = create_strong_password()
            user_id = _create_user(user_data, header, password)
            print("User {} created".format(email))
        return {"email": email, "password": password}
    except Exception as e:
        print(e)
        raise Exception(f"Error while Creating user in Auth Server: {e}")


def create_user_in_auth(client_id, user_data, client_connection_aware=True):
    email = user_data["employee_email_id"]
    if client_connection_aware:
        connection_name = get_auth_connection_name(client_id)
        print("Creating user with connection_name {}".format(connection_name))
        if connection_name and connection_name != "email-password":
            print("Skipping user creation since conneciton is not email password")
            send_welcome_email(client_id, email)
            return
    try:
        access_token = get_management_api_access_token()
        header = {
            "Authorization": "Bearer " + access_token,
            "Content-Type": "application/json",
        }

        if does_user_exists_with_email_password_connection(email, header):
            print("Skipping user creation since user already exists")
            print(f"User exists {email} , so not creating user in Auth0")
        else:
            password = create_strong_password()
            user_id = _create_user(user_data, header, password)
            # NOTE: Password is being logged here!! REMOVE THIS
            print(
                f"User {email} created with user_id {user_id} and role {user_data['user_role']}"
            )
    except Exception as e:
        print(e)
        raise Exception(f"Some issue while Creating user in Auth Server {e}")


def create_strong_password():
    while True:
        password = "".join(secrets.choice(allowed_password_chars) for i in range(9))
        if (
            any(c.islower() for c in password)
            and any(c.isupper() for c in password)
            and any(c.isdigit() for c in password)
            and any(c in string.punctuation for c in password)
        ):
            break
    return password


def send_welcome_email(client_id, email):
    print("Sending welcome email {}".format(email))
    # emp_details = EmployeeAccessor(client_id).get_employee(email)
    # message = Mail(
    #     from_email='<EMAIL>',
    #     to_emails='<EMAIL>',
    # )
    # message.template_id = 'd-cbb7ed27b0b2444ca8ff491f1896898c'
    # message.dynamic_template_data = {
    #     "name": emp_details.first_name + " " + emp_details.last_name,
    #     "email": email,
    #     "login_url": "https://app.everstage.com",
    #     "action_url": "https://app.everstage.com/verify"
    # }
    # send_email.delay(message = message, type = "send_welcome_email")
    pass


def _create_user(user_data, header, password):
    res = {}
    try:
        email = user_data["employee_email_id"]
        req_url = f"{MGMT_API['AUDIENCE']}users"
        full_name = f"{user_data['first_name']} {user_data['last_name']}"

        payload = {
            "email": email,
            "name": full_name,
            "connection": AUTH0_DB_CONNECTION,
            "password": password,
            "verify_email": False,
        }
        if "email_verified" in user_data and user_data["email_verified"]:
            payload["email_verified"] = user_data["email_verified"]
        res = requests.post(req_url, json=payload, headers=header)
        print(f"******MGMT_API Create user - /users {password}")
        print_api_status(res)
        created_user = res.json()
        print(f"Created user...{created_user}")
        user_id = created_user["user_id"]
        return user_id
    except Exception as e:
        print("Exception - {}".format(e))
        raise Exception(f"Some issue while _creating user in Auth Server {res}")


def _get_users(pattern):
    header = get_header_with_mgmt_api_bearer()
    req_url = f"{MGMT_API['AUDIENCE']}users"
    params = {"q": pattern}
    # print(f"getting auth0 users...{header}")
    res = requests.get(req_url, headers=header, params=params)
    return res


def delete_user(user_id):
    header = get_header_with_mgmt_api_bearer()
    req_url = f"{MGMT_API['AUDIENCE']}users/{user_id}"
    res = requests.delete(req_url, headers=header)
    return res


def remove_roles_from_a_user(user_id):
    header = get_header_with_mgmt_api_bearer()
    roles = get_all_roles(header)
    roles_id = [role.get("id") for role in roles]
    req_url = f"{MGMT_API['AUDIENCE']}users/{user_id}/roles"
    res = requests.delete(req_url, headers=header, json={"roles": roles_id})
    return res


def print_api_status(res):
    try:
        print(
            f"API Stats - {res.status_code} -> {res.headers['x-ratelimit-limit']} | "
            f"{res.headers['x-ratelimit-remaining']} | {res.headers['x-ratelimit-reset']}"
        )
    except Exception as e:
        print(res.__dict__)
        raise Exception(
            f"Some issue while print_api_status user in Auth Server {res.__dict__}"
        )


def update_user_meta_data(user_id, payload, header):
    if header is None:
        header = get_header_with_mgmt_api_bearer()
    try:
        req_url = f"{MGMT_API['AUDIENCE']}users/{user_id}"
        res = requests.patch(req_url, json=payload, headers=header)
        return res
    except Exception as e:
        print(e)
        raise Exception("Some issue while updating role for user in Auth Server")


def does_user_exists_with_email_password_connection(email, header):
    try:
        print("******MGMT_API Checking if user exists - /users-by-email")
        req_url = f"{MGMT_API['AUDIENCE']}users-by-email"
        params = {"email": email}
        res = requests.get(req_url, headers=header, params=params)
        print_api_status(res)
        user = res.json()
        if not user:
            return False
        else:
            for user_detail in user:
                for identity in user_detail["identities"]:
                    if identity["connection"] == AUTH0_DB_CONNECTION:
                        return True
            return False
    except Exception as e:
        print(e)
        raise Exception("Some issue while checking if user exists in Auth Server")


def get_all_roles(header):
    try:
        auth0_roles = cache.get("auth0_roles")
        auth0_roles = None
        if auth0_roles is None:
            print("******MGMT_API Getting all roles - /roles")
            req_url = f"{MGMT_API['AUDIENCE']}roles"
            res = requests.get(req_url, headers=header)
            print_api_status(res)
            roles = res.json()
            cache.set("auth0_roles", roles, None)
            return roles
        else:
            return auth0_roles
    except Exception as e:
        print(e)
        raise Exception("Some issue while fetching all roles from Auth Server")


def send_password_reset_link(email):
    try:
        if not is_prod_env():
            email = "<EMAIL>"
        req_url = f"{MGMT_API['AUTH_MGMT_ISSUER']}dbconnections/change_password"
        payload = {
            "client_id": MGMT_API["CLIENT_ID"],
            "email": email,
            "connection": AUTH0_DB_CONNECTION,
        }
        res = requests.post(req_url, data=payload)
        print(
            "******MGMT_API Sending password reset link to - dbconnections/change_password"
        )
        print_api_status(res)
        return res.status_code == 200
    except Exception as e:
        print(e)
        raise Exception("Some issue while sending password reset email")


def send_password_reset_link_management_cmd(email, MGMT_API=None):
    try:
        req_url = f"{MGMT_API['AUTH_MGMT_ISSUER']}dbconnections/change_password"
        payload = {
            "client_id": MGMT_API["CLIENT_ID"],
            "email": email,
            "connection": AUTH0_DB_CONNECTION,
        }
        res = requests.post(req_url, data=payload)
        print(
            "******MGMT_API Sending password reset link to - dbconnections/change_password"
        )
        print_api_status(res)
    except Exception as e:
        print(e)
        raise Exception("Some issue while sending password reset email")


def _get_user(email, header, connection_type="Username-Password-Authentication"):
    res = {}
    try:
        print("******MGMT_API Get user - /users_email", email)
        req_url = f"{MGMT_API['AUDIENCE']}users-by-email"
        params = {"email": email}
        res = requests.get(req_url, headers=header, params=params)
        user_details = res.json()
        user_id = None
        if user_details and len(user_details) > 0:
            for user_detail in user_details:
                for identity in user_detail["identities"]:
                    if identity["connection"] == connection_type:
                        user_id = user_detail["user_id"]
        return user_id
    except Exception as e:
        print("Exception - {}".format(e))
        raise Exception(f"Some issue while _get_user in Auth Server {res}")


def get_user_details_by_email(email):
    """Returns an array containing user details with exact email match,
    if no match then returns empty array"""
    try:
        header = get_header_with_mgmt_api_bearer()
        logger.info(f"******MGMT_API Get user details - /users-by-email {email}")
        req_url = f"{MGMT_API['AUDIENCE']}users-by-email"
        params = {"email": email}
        res = requests.get(req_url, headers=header, params=params)
        return res.json()
    except Exception as e:
        logger.error("Exception - {}".format(e))
        raise Exception("Some issue while get_user_details_by_email in Auth Server")


def _get_auth0_user_detail(email, header):
    res = {}
    try:
        print("******MGMT_API Get user - /users", email)
        req_url = f"{MGMT_API['AUDIENCE']}users"
        params = {
            "q": f"email:{email} AND identities.connection:Username-Password-Authentication AND identities.provider:auth0"
        }
        res = requests.get(req_url, headers=header, params=params)
        user_details = res.json()
        user_detail = None
        if user_details and len(user_details) > 0:
            user_detail = user_details[0]
        return user_detail
    except Exception as e:
        print("Exception - {}".format(e))
        raise Exception(f"Some issue while _get_user in Auth Server {res}")


def update_user_name(user_data):
    res = {}
    try:
        email = user_data["employee_email_id"]
        access_token = get_management_api_access_token()
        header = {
            "Authorization": "Bearer " + access_token,
            "Content-Type": "application/json",
        }
        user_id = _get_user(email, header)
        if user_id:
            logger.info(f"******MGMT_API Update user name - /users_id {user_id}")
            req_url = f"{MGMT_API['AUDIENCE']}users/{user_id}"
            payload = {
                "name": f"{user_data['first_name']} {user_data['last_name']}",
            }
            res = requests.patch(req_url, json=payload, headers=header)
            return res.status_code == 200
        return False
    except Exception as e:
        print("Exception - {}".format(e))
        raise Exception(f"Some issue while update_user_name in Auth Server {res}")


def remove_specific_role_from_user(user_id, role_id):
    """Remove a specific role of user"""
    try:
        header = get_header_with_mgmt_api_bearer()
        logger.info(
            f"******MGMT_API Remove user role - {role_id}  - /users_id {user_id}"
        )
        req_url = f"{MGMT_API['AUDIENCE']}users/{user_id}/roles"
        res = requests.delete(req_url, headers=header, json={"roles": list(role_id)})
        return res.status_code == 204
    except Exception as e:
        logger.error("Exception in remove_specific_role_from_user - {}".format(e))
        raise Exception(
            f"Some issue while remove_specific_role_from_user in Auth Server"
        )


def replace_user_role(user_data):
    """Replacing existing role of user with given role"""
    try:
        header = get_header_with_mgmt_api_bearer()
        roles = get_all_roles(header)
        role_id = (
            [
                next(
                    role for role in roles if role["name"] == user_data["previous_role"]
                ).get("id")
            ]
            if roles
            else None
        )
        user_details = get_user_details_by_email(user_data["employee_email_id"])
        user_ids = [user["user_id"] for user in user_details] if user_details else None
        if user_ids:
            for user_id in user_ids:
                logger.info(f"******MGMT_API Replace user roles - /users_id {user_id}")
                remove_specific_role_from_user(user_id, role_id) if role_id else None
    except Exception as e:
        logger.error("Exception in replace_user_roles - {}".format(e))
        raise Exception(f"Some issue while replace_user_roles in Auth Server")


def get_user_by_user_id(user_id):
    """Retrieve user details for the given user_id"""
    access_token = get_management_api_access_token()
    header = {
        "Authorization": "Bearer " + access_token,
        "Content-Type": "application/json",
    }
    req_url = f"{MGMT_API['AUDIENCE']}users/{user_id}"
    res = requests.get(req_url, headers=header)
    user_details = res.json()
    return user_details


def get_password_reset_link(email):
    res = {}
    access_token = get_management_api_access_token()
    header = {
        "Authorization": "Bearer " + access_token,
        "Content-Type": "application/json",
    }
    try:
        user_id = _get_user(email, header)
        if user_id:
            print(
                f"******MGMT_API Get password reset link for email  password user - /user_id {user_id}"
            )
            req_url = f"{MGMT_API['AUDIENCE']}tickets/password-change"
            payload = (
                {
                    "user_id": user_id,
                    "result_url": get_site_url(),
                    "mark_email_as_verified": True,
                }
                if is_prod_env()
                else {
                    "user_id": user_id,
                    "mark_email_as_verified": True,
                }
            )
            res = requests.post(req_url, json=payload, headers=header)
            if res and "ticket" in res.json().keys():
                return res.json()["ticket"]
        return None
    except Exception as e:
        print("Exception - {}".format(e))
        return None


def update_user_email(user_data):
    try:
        email = user_data["employee_email_id"]
        headers = get_header_with_mgmt_api_bearer()
        user_id = _get_user(email, headers)
        if user_id:
            req_url = f"{MGMT_API['AUDIENCE']}users/{user_id}"
            payload = {
                "email": user_data["new_email"],
                "email_verified": False,
                "verify_email": False,
            }
            res = requests.patch(req_url, headers=headers, json=payload)
            return res.status_code == 200
        return False
    except Exception as e:
        print("Exception - {}".format(e))
        raise Exception(f"Some issue while update_user_email in Auth Server")


def update_user_password(user_data):
    errors = {}
    try:
        emails = user_data["employee_email_ids"]
        access_token = get_management_api_access_token()
        header = {
            "Authorization": "Bearer " + access_token,
            "Content-Type": "application/json",
        }
        for email in emails:
            user_id = _get_user(email, header)
            if user_id:
                logger.info(f"******MGMT_API Update user - /users_id {user_id}")
                req_url = f"{MGMT_API['AUDIENCE']}users/{user_id}"
                # password and email_verified can't be updated at once
                # updating password
                payload = {
                    "password": "evertest$100",
                }
                res = requests.patch(req_url, json=payload, headers=header)
                if res.status_code != 200:
                    errors[email] = res.json()["message"]
                    continue

                # updating email_verified
                payload = {
                    "email_verified": True,
                }
                res = requests.patch(req_url, json=payload, headers=header)
                if res.status_code != 200:
                    errors[email] = res.json()["message"]
            else:
                errors[email] = "User does not exist"
        return errors
    except Exception as e:
        print("Exception - {}".format(e))
        raise Exception(f"Some issue while update_user in Auth Server {res}")


def update_user_password_by_id(user_id):
    response = {}
    try:
        access_token = get_management_api_access_token()
        header = {
            "Authorization": "Bearer " + access_token,
            "Content-Type": "application/json",
        }
        logger.info(f"******MGMT_API Update user - /users_id {user_id}")
        req_url = f"{MGMT_API['AUDIENCE']}users/{user_id}"
        # password and email_verified can't be updated at once
        # updating password
        password = create_strong_password()
        response["password"] = password
        payload = {
            "password": password,
        }
        res = requests.patch(req_url, json=payload, headers=header)
        if res.status_code != 200:
            raise Exception(res.json()["message"])

        # updating email_verified
        payload = {
            "email_verified": True,
        }
        res = requests.patch(req_url, json=payload, headers=header)
        if res.status_code != 200:
            raise Exception(res.json()["message"])
        return response
    except Exception as e:
        print("Exception - {}".format(e))
        raise Exception(f"Some issue while update_user in Auth Server {e}")


def login_as_user(user_id: str, user_detail: Dict):
    # Lookup for actual logged-in user in threadlocal
    # If request is by support-user, then admin_email should be supportuser's email (actual email of staff-member)
    login_email: str = restricted_usage___get_login_user().username

    # TODO: Test what happens if provided `admin_email` is not present in Auth0. Will it throw an error ?
    try:
        payload = {
            "user_metadata": {
                "admin_email": login_email,
                "name": user_detail["name"],
                # If needed "nickname" & "picture" can be taken from Auth0
                # "nickname": user_details[0]["nickname"],
                # "picture": user_details[0]["picture"],
                "email": user_detail["email"],
            }
        }
        update_meta_data_response = update_user_meta_data(user_id, payload, None)
        if update_meta_data_response.status_code != 200:
            raise Exception(f"Some issue while update_user in Auth Server")
    except Exception as e:
        print("Exception - {}".format(e))
        raise Exception(f"Some issue while login_as_user in Auth Server {e}")


def logout_as_user(user_id):
    try:
        payload = {"user_metadata": {}}
        update_meta_data_response = update_user_meta_data(user_id, payload, None)
        if update_meta_data_response.status_code != 200:
            raise Exception(f"Some issue while update_user in Auth Server")
    except Exception as e:
        print("Exception - {}".format(e))
        raise Exception(f"Some issue while update_user in Auth Server {e}")


def fetch_staff_members(
    emails: Optional[List[str]] = None, search_text: Optional[str] = None
) -> List[Dict]:
    """
    Fetches staff members from Auth0.

    Args:
        emails: The list of emails.
        search_text: The search text.

    Returns:
        List: The list of staff members.

    Response sample from auth0:
        data = {
            "start": 0,
            "limit": 100,
            "length": 61,
            "users": [{"email": "<EMAIL>", "name": "Ankur Gupta"}, ...],
            "total": 61
        }

    Returns: data["users"]
        [{"email": "<EMAIL>", "name": "Ankur Gupta"}, ...]

    Limitations:
        1. Auth0 API has a limit of 100 users per page.
        2. Auth0 allows a maximum of 1000 users to be fetched using this api even with pagination (even if the total number of users is more than 1000).
        Ref: https://auth0.com/docs/manage-users/user-search/retrieve-users-with-get-users-endpoint#limitations

    """
    try:
        url = f"{mgmt_api_base_url}users"
        headers = get_header_with_mgmt_api_bearer()

        # Construct search query
        search_query = ""
        if search_text:
            search_query += f" AND (name:*{search_text}* OR email:*{search_text}*)"

        # Filter users by specified emails
        if emails:
            search_query += f" AND email:({' OR '.join(emails)})"

        # Fetch users in batches of 100(max limit per page). At max 1000 users can be fetched. So, max 10 iterations.
        users = []
        page = 0
        while True:
            params = {
                "search_engine": "v3",
                "q": f'email.domain:"everstage.com" AND identities.connection:"google-oauth2" {search_query}',
                "include_totals": "true",
                "per_page": 100,
                "page": page,
                "sort": "name:1",
                "fields": "email,name,user_id,picture",
            }

            response = requests.get(url, headers=headers, params=params, timeout=10)

            if response.status_code == 200:
                data = response.json()
                users.extend(data["users"])
                if data["total"] <= len(users):
                    break
                page += 1
            else:
                raise Exception(response.text)
        return users
    except Exception:
        logger.exception("Exception in fetching staff members from Auth0")
        return []


def fetch_user_auth0_roles(auth0_user_id: str) -> List[Dict]:
    """
    Fetches the roles of a user from Auth0.

    Args:
        auth0_user_id: The Auth0 user ID.

    Returns:
        List: The list of roles.

        [
            {
                "name": "SupportAdmin",
                "description": "admin role for TSAR management",
                "id": "rol_rBNF6PfrVbZluEMZ"
            },
            ...
        ]
    """
    # check if the user has the support admin role
    url = f"{mgmt_api_base_url}users/{auth0_user_id}/roles"
    headers = get_header_with_mgmt_api_bearer()
    response = requests.get(url, headers=headers, timeout=10)
    if response.status_code == 200:
        roles_data = response.json()
        return roles_data
    raise Exception(response.text)


def fetch_user_auth0_permissions(auth0_user_id: str) -> List[Dict]:
    """
    Fetches the permissions of a user from Auth0.

    Args:
        auth0_user_id: The Auth0 user ID.

    Returns:
        List: The list of permissions.

        [
            {
                "permission_name": "manage:supportuseraccess",
                "description": "admin permission for TSAR management",
                "resource_server_name": "es-admin-ui",
                "resource_server_identifier": "https://es-admin-ui",
                "sources": [
                    {
                        "source_id": "rol_rBNF6PfrVbZluEMZ",
                        "source_name": "SupportAdmin",
                        "source_type": "ROLE"
                    }
                ]
            },
            ...
        ]
    """
    # check if the user has the support admin role
    url = f"{mgmt_api_base_url}users/{auth0_user_id}/permissions"
    headers = get_header_with_mgmt_api_bearer()
    response = requests.get(url, headers=headers, timeout=10)
    if response.status_code == 200:
        permissions_data = response.json()
        return permissions_data
    raise Exception(response.text)


def fetch_user_auth0_permissions_by_email_and_resource_server(
    email: str, resource_server: str
) -> List[Dict]:
    """
    Fetches the permissions of a user from Auth0.

    Args:
        auth0_user_id: The Auth0 user ID.
        resource_server_identifier: The resource server identifier.

    Returns:
        List: The list of permissions.

        [
            {
                "permission_name": "manage:supportuseraccess",
                "description": "admin permission for TSAR management",
                "resource_server_name": "es-admin-ui",
                "resource_server_identifier": "https://es-admin-ui",
                "sources": [
                    {
                        "source_id": "rol_rBNF6PfrVbZluEMZ",
                        "source_name": "SupportAdmin",
                        "source_type": "ROLE"
                    }
                ]
            },
            ...
        ]
    """
    auth0_users: List[Dict] = fetch_staff_members(emails=[email])
    if not auth0_users:
        return []
    auth0_user_id: str = auth0_users[0]["user_id"]

    # check if the user has the manage:supportuseraccess permission
    permissions_data = fetch_user_auth0_permissions(auth0_user_id)
    return [
        permission
        for permission in permissions_data
        if permission["resource_server_identifier"] == resource_server
    ]
