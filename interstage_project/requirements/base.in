# -*- coding: utf-8 -*-
# -*- coding: utf-8 -*-

# This file contains the application dependencies (dependencies which are needed for app to run).

## Not used anymore
# billiard==3.6.4.0
# amqp==5.0.6
# cryptography~=3.4
# dask[complete]
# eventlet==0.31.0
# pycurl==7.43.0.6
# model-bakery==1.1.0
# pymssql==2.2.7

aiobotocore==2.13.0
analytics-python==1.4.0
arrow==1.3.0
asgiref==3.6.0
aws-msk-iam-sasl-signer-python==1.0.1
awswrangler==3.9.0
beautifulsoup4==4.12.1
botbuilder-core==4.15.0
boto3==1.34.106
botocore==1.34.106
celery===5.4.0
confluent-kafka==2.3.0
cachetools===5.3.3
datadog-api-client==2.12.0
deepdiff==6.7.1
Django==4.2.21
django-celery-beat==2.5.0
django-concurrency==2.5.0
django-cors-headers==4.3.1
django-crum==0.7.9
django-csp==3.7
django-extensions==3.2.3 # Django 4 support added from 3.2.x - https://github.com/django-extensions/django-extensions/blob/main/CHANGELOG.md#320
django-graphql-jwt~=0.3.1
django-ninja==1.1.0
django-redis==5.2.0
django-storages==1.11.1
djangorestframework==3.15.2  # Django 4 support added from 3.15.x - https://www.django-rest-framework.org/community/release-notes/#3150
#Note while upgrading version of camel-case that adds underscorize before a number
# e.g. v2Counter -> v_2_counter which can be prevented using setting
# Ref - https://github.com/vbabiy/djangorestframework-camel-case?tab=readme-ov-file#underscoreize-options
djangorestframework-camel-case==1.4.2
docusign-esign==3.23.0
duckdb==0.10.1
drf-jwt~=1.19.1
Faker==20.0.3
flower==2.0.1
google-auth==2.40.1
graphene-django==3.1.2
graphql-core-promise==3.2.3.post1
gspread==6.2.1
gunicorn==23.0.0
ibis-framework[snowflake]==9.2.0
jsonschema==4.17.3
kombu==5.4.2
# llama-index
mmh3==4.1.0
nested-lookup==0.2.25
networkx==3.0
openpyxl==3.1.2
pandas==2.0.3
parameterized==0.9.0
paramiko==3.5.0
Pillow==10.3.0
prison==0.2.1 # Version utilised in superset repo(to be maintained)
psycopg2-binary==2.9.6
pyarrow==14.0.1
pycryptodome==3.19.1
pydantic==2.7.4
pydash==7.0.4
pyjwt==2.6.0
pyngrok==5.1.0
pyotp==2.9.0
pyodbc==4.0.39
PyPika==0.48.9
python-gnupg==0.5.4
python-json-logger==2.0.7
pytz==2023.3
pyzstd==0.15.1
redis==4.6.0
requests==2.32.0
s3fs==2024.9.0
schema==0.7.5
sendgrid==6.10.0
slack-bolt==1.9.1
slack-sdk==3.11.1
snowflake-connector-python==3.15.0
snowflake-snowpark-python==1.10.0
sqlalchemy==1.4.46
sqlparse==0.5.0
sqlglot==25.1.0
supabase==2.4.2
greenlet==3.0.0
playwright==1.39.0
pyee==11.0.1
typing-extensions==4.12.2
numpy==1.26.4
swifter==1.4.0
tabulate==0.9.0
typer==0.16.0
ua-parser[re2]==0.18.0
ujson==5.8.0
xlsxwriter==3.0.3  # Downgrading to 3.0.3 to fix issue with xlxswriter "OverlapRange Exception"
nest-asyncio==1.5.6 # Temporary fix for asyncio issue
debugpy==1.5.1 # Fix Debugpy issue in debug mode
chardet==5.2.0
fuzzywuzzy==0.18.0
python-Levenshtein==0.23.0
JayDeBeApi==1.2.3
JPype1==1.4.1
mysql-connector-python==9.2.0
typesense==0.21.0
# CDC dependencies
rq==2.3.3
avro==1.12.0
grpcio==1.71.0
grpcio-tools==1.71.0
supervisor==4.2.5
protobuf==5.29.0
bitstring==4.3.1
# CDC
xlrd==2.0.1
setproctitle==1.2.2
git+https://<EMAIL>/Everstage/vectorized_evaluate.git@v2.2.1-prod

# AI
langchain==0.3.14
langchain-openai==0.3.7
langchain-community==0.3.2
langchain_anthropic==0.3.7
langchain-groq==0.3.2
langchain-google-genai==2.1.3
