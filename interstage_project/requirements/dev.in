
# This file contains the development dependencies (dependencies which support development activites).
#  TODO: This file can be further split into multiple files based on the type of dependencies. This helps avoid conflicts with the base dependencies.
#  https://interstage.atlassian.net/browse/INTER-6106?focusedCommentId=13984

allure-pytest==2.9.45
PyYAML==6.0.1 #To force awscli to use this version (Do not upgrade)
awscli
black==24.3.0
boto3
chromedriver-autoinstaller==0.3.1
deepdiff==6.7.1
debugpy   #unpining this to resolve pip compile error
diff_cover==7.5.0
docker==6.0.1
icecream==2.1.3
import-linter==2.0.0
invoke==1.7.0
ipython==8.14.0
isort==5.12.0
notebook==6.4.12
pre-commit
pylint~=2.13
pylint-django==2.5.3
pylint-plugin-utils==0.7
pylint-pytest==1.1.2
pylint-unittest==0.1.3
pytest==7.2.0
pytest-cov==3.0.0
pytest-depends==1.0.1
pytest-django==4.5.2
pytest-html==4.0.1
pytest-html-reporter==0.2.6
pytest-xdist==3.3.1
pytest-testmon~=2.0.12
python-dotenv==0.20.0
requests==2.32.2
rich==13.3.4
ruff~=0.1.15
selenium==4.1.0
stringcase~=1.2.0
typer==0.16.0
bandit==1.7.10
