# SHA1:46ef41aab48dd7f27de87d8d4379b4e317e278a2
#
# This file was generated by pip-compile-multi.
# To update, run:
#
#    requirements upgrade
#
aiobotocore==2.13.0
    # via
    #   -r requirements/base.in
    #   s3fs
aiohappyeyeballs==2.6.1
    # via aiohttp
aiohttp==3.12.13
    # via
    #   aiobotocore
    #   langchain
    #   langchain-community
    #   s3fs
aioitertools==0.12.0
    # via aiobotocore
aiosignal==1.3.2
    # via aiohttp
amqp==5.3.1
    # via kombu
analytics-python==1.4.0
    # via -r requirements/base.in
annotated-types==0.7.0
    # via pydantic
anthropic==0.55.0
    # via langchain-anthropic
anyio==4.9.0
    # via
    #   anthropic
    #   groq
    #   httpx
    #   openai
arrow==1.3.0
    # via -r requirements/base.in
asgiref==3.6.0
    # via
    #   -r requirements/base.in
    #   django
    #   django-cors-headers
asn1crypto==1.5.1
    # via snowflake-connector-python
async-timeout==4.0.3
    # via
    #   aiohttp
    #   langchain
    #   redis
atpublic==4.1.0
    # via ibis-framework
attrs==25.3.0
    # via
    #   aiohttp
    #   jsonschema
avro==1.12.0
    # via -r requirements/base.in
aws-msk-iam-sasl-signer-python==1.0.1
    # via -r requirements/base.in
awswrangler==3.9.0
    # via -r requirements/base.in
azure-core==1.34.0
    # via msrest
backoff==1.10.0
    # via analytics-python
bcrypt==4.3.0
    # via paramiko
beautifulsoup4==4.12.1
    # via -r requirements/base.in
billiard==4.2.1
    # via celery
bitarray==3.4.3
    # via bitstring
bitstring==4.3.1
    # via -r requirements/base.in
botbuilder-core==4.15.0
    # via -r requirements/base.in
botbuilder-schema==4.15.0
    # via
    #   botbuilder-core
    #   botframework-connector
    #   botframework-streaming
botframework-connector==4.15.0
    # via
    #   botbuilder-core
    #   botframework-streaming
botframework-streaming==4.15.0
    # via botbuilder-core
boto3==1.34.106
    # via
    #   -r requirements/base.in
    #   aws-msk-iam-sasl-signer-python
    #   awswrangler
    #   snowflake-connector-python
botocore==1.34.106
    # via
    #   -r requirements/base.in
    #   aiobotocore
    #   aws-msk-iam-sasl-signer-python
    #   awswrangler
    #   boto3
    #   s3transfer
    #   snowflake-connector-python
cachetools===5.3.3
    # via
    #   -r requirements/base.in
    #   google-auth
celery===5.4.0
    # via
    #   -r requirements/base.in
    #   django-celery-beat
    #   flower
certifi==2025.6.15
    # via
    #   datadog-api-client
    #   docusign-esign
    #   httpcore
    #   httpx
    #   msrest
    #   requests
    #   snowflake-connector-python
cffi==1.17.1
    # via
    #   cryptography
    #   pynacl
    #   snowflake-connector-python
chardet==5.2.0
    # via -r requirements/base.in
charset-normalizer==3.4.2
    # via
    #   requests
    #   snowflake-connector-python
click==8.2.1
    # via
    #   aws-msk-iam-sasl-signer-python
    #   celery
    #   click-didyoumean
    #   click-plugins
    #   click-repl
    #   dask
    #   rq
    #   typer
    #   vectorized-evaluate
click-didyoumean==0.3.1
    # via celery
click-plugins==*******
    # via celery
click-repl==0.3.0
    # via celery
cloudpickle==2.0.0
    # via
    #   dask
    #   snowflake-snowpark-python
confluent-kafka==2.3.0
    # via -r requirements/base.in
contextlib2==21.6.0
    # via schema
cron-descriptor==1.4.5
    # via django-celery-beat
cryptography==45.0.4
    # via
    #   docusign-esign
    #   msal
    #   paramiko
    #   pyjwt
    #   pyopenssl
    #   snowflake-connector-python
dask[array,dataframe]==2024.8.0
    # via
    #   dask-expr
    #   swifter
dask-expr==1.1.10
    # via dask
dataclasses-json==0.6.7
    # via langchain-community
datadog-api-client==2.12.0
    # via -r requirements/base.in
debugpy==1.5.1
    # via -r requirements/base.in
deepdiff==6.7.1
    # via -r requirements/base.in
deprecation==2.1.0
    # via postgrest
distro==1.9.0
    # via
    #   anthropic
    #   groq
    #   openai
django==4.2.21
    # via
    #   -r requirements/base.in
    #   django-celery-beat
    #   django-cors-headers
    #   django-crum
    #   django-csp
    #   django-extensions
    #   django-graphql-jwt
    #   django-ninja
    #   django-redis
    #   django-storages
    #   django-timezone-field
    #   djangorestframework
    #   drf-jwt
    #   graphene-django
django-celery-beat==2.5.0
    # via -r requirements/base.in
django-concurrency==2.5
    # via -r requirements/base.in
django-cors-headers==4.3.1
    # via -r requirements/base.in
django-crum==0.7.9
    # via -r requirements/base.in
django-csp==3.7
    # via -r requirements/base.in
django-extensions==3.2.3
    # via -r requirements/base.in
django-graphql-jwt==0.3.4
    # via -r requirements/base.in
django-ninja==1.1.0
    # via -r requirements/base.in
django-redis==5.2.0
    # via -r requirements/base.in
django-storages==1.11.1
    # via -r requirements/base.in
django-timezone-field==7.1
    # via django-celery-beat
djangorestframework==3.15.2
    # via
    #   -r requirements/base.in
    #   drf-jwt
djangorestframework-camel-case==1.4.2
    # via -r requirements/base.in
docusign-esign==3.23.0
    # via -r requirements/base.in
drf-jwt==1.19.2
    # via -r requirements/base.in
duckdb==0.10.1
    # via
    #   -r requirements/base.in
    #   vectorized-evaluate
et-xmlfile==2.0.0
    # via openpyxl
exceptiongroup==1.3.0
    # via anyio
faker==20.0.3
    # via -r requirements/base.in
filelock==3.18.0
    # via snowflake-connector-python
filetype==1.2.0
    # via langchain-google-genai
flower==2.0.1
    # via -r requirements/base.in
frozenlist==1.7.0
    # via
    #   aiohttp
    #   aiosignal
fsspec==2024.9.0
    # via
    #   dask
    #   s3fs
fuzzywuzzy==0.18.0
    # via -r requirements/base.in
google-ai-generativelanguage==0.6.18
    # via langchain-google-genai
google-api-core[grpc]==2.25.1
    # via google-ai-generativelanguage
google-auth==2.40.1
    # via
    #   -r requirements/base.in
    #   google-ai-generativelanguage
    #   google-api-core
    #   google-auth-oauthlib
    #   gspread
google-auth-oauthlib==1.2.2
    # via gspread
googleapis-common-protos==1.70.0
    # via
    #   google-api-core
    #   grpcio-status
gotrue==2.11.4
    # via supabase
graphene==3.4.3
    # via
    #   django-graphql-jwt
    #   graphene-django
graphene-django==3.1.2
    # via
    #   -r requirements/base.in
    #   django-graphql-jwt
graphql-core==3.2.6
    # via
    #   graphene
    #   graphene-django
    #   graphql-relay
graphql-core-promise==3.2.3.post1
    # via -r requirements/base.in
graphql-relay==3.2.0
    # via
    #   graphene
    #   graphene-django
greenlet==3.0.0
    # via
    #   -r requirements/base.in
    #   playwright
groq==0.28.0
    # via langchain-groq
grpcio==1.71.0
    # via
    #   -r requirements/base.in
    #   google-api-core
    #   grpcio-status
    #   grpcio-tools
grpcio-status==1.71.0
    # via google-api-core
grpcio-tools==1.71.0
    # via -r requirements/base.in
gspread==6.2.1
    # via -r requirements/base.in
gunicorn==23.0.0
    # via -r requirements/base.in
h11==0.16.0
    # via httpcore
h2==4.2.0
    # via httpx
hpack==4.1.0
    # via h2
httpcore==1.0.9
    # via httpx
httpx[http2]==0.27.2
    # via
    #   anthropic
    #   gotrue
    #   groq
    #   langsmith
    #   openai
    #   postgrest
    #   storage3
    #   supabase
    #   supafunc
humanize==4.12.3
    # via flower
hyperframe==6.1.0
    # via h2
ibis-framework[snowflake]==9.2.0
    # via -r requirements/base.in
idna==3.10
    # via
    #   anyio
    #   httpx
    #   requests
    #   snowflake-connector-python
    #   yarl
importlib-metadata==8.7.0
    # via dask
isodate==0.7.2
    # via msrest
jaydebeapi==1.2.3
    # via -r requirements/base.in
jiter==0.10.0
    # via
    #   anthropic
    #   openai
jmespath==1.0.1
    # via
    #   boto3
    #   botocore
jpype1==1.4.1
    # via
    #   -r requirements/base.in
    #   jaydebeapi
jsonpatch==1.33
    # via langchain-core
jsonpickle==1.4.2
    # via botbuilder-core
jsonpointer==3.0.0
    # via jsonpatch
jsonschema==4.17.3
    # via -r requirements/base.in
kombu==5.4.2
    # via
    #   -r requirements/base.in
    #   celery
langchain==0.3.14
    # via
    #   -r requirements/base.in
    #   langchain-community
langchain-anthropic==0.3.7
    # via -r requirements/base.in
langchain-community==0.3.2
    # via -r requirements/base.in
langchain-core==0.3.63
    # via
    #   langchain
    #   langchain-anthropic
    #   langchain-community
    #   langchain-google-genai
    #   langchain-groq
    #   langchain-openai
    #   langchain-text-splitters
langchain-google-genai==2.1.3
    # via -r requirements/base.in
langchain-groq==0.3.2
    # via -r requirements/base.in
langchain-openai==0.3.7
    # via -r requirements/base.in
langchain-text-splitters==0.3.8
    # via langchain
langsmith==0.1.147
    # via
    #   langchain
    #   langchain-community
    #   langchain-core
levenshtein==0.23.0
    # via python-levenshtein
locket==1.0.0
    # via partd
markdown-it-py==3.0.0
    # via rich
marshmallow==3.26.1
    # via dataclasses-json
mdurl==0.1.2
    # via markdown-it-py
mmh3==4.1.0
    # via -r requirements/base.in
monotonic==1.6
    # via analytics-python
msal==1.32.3
    # via botframework-connector
msrest==0.7.1
    # via
    #   botbuilder-schema
    #   botframework-connector
multidict==6.5.1
    # via
    #   aiohttp
    #   yarl
mypy-extensions==1.1.0
    # via typing-inspect
mysql-connector-python==9.2.0
    # via -r requirements/base.in
nest-asyncio==1.5.6
    # via -r requirements/base.in
nested-lookup==0.2.25
    # via -r requirements/base.in
networkx==3.0
    # via -r requirements/base.in
nose==1.3.7
    # via docusign-esign
numpy==1.26.4
    # via
    #   -r requirements/base.in
    #   awswrangler
    #   dask
    #   ibis-framework
    #   langchain
    #   langchain-community
    #   pandas
    #   pyarrow
    #   vectorized-evaluate
oauthlib==3.3.1
    # via requests-oauthlib
openai==1.91.0
    # via langchain-openai
openpyxl==3.1.2
    # via -r requirements/base.in
ordered-set==4.1.0
    # via deepdiff
orjson==3.10.18
    # via langsmith
packaging==24.2
    # via
    #   awswrangler
    #   dask
    #   deprecation
    #   gunicorn
    #   jpype1
    #   langchain-core
    #   marshmallow
    #   snowflake-connector-python
pandas==2.0.3
    # via
    #   -r requirements/base.in
    #   awswrangler
    #   dask
    #   dask-expr
    #   ibis-framework
    #   swifter
    #   vectorized-evaluate
parameterized==0.9.0
    # via -r requirements/base.in
paramiko==3.5.0
    # via -r requirements/base.in
parsy==2.1
    # via ibis-framework
partd==1.4.2
    # via dask
pillow==10.3.0
    # via -r requirements/base.in
platformdirs==4.3.8
    # via snowflake-connector-python
playwright==1.39.0
    # via -r requirements/base.in
postgrest==0.16.11
    # via supabase
prison==0.2.1
    # via -r requirements/base.in
prometheus-client==0.22.1
    # via flower
promise==2.3
    # via graphene-django
prompt-toolkit==3.0.51
    # via click-repl
propcache==0.3.2
    # via
    #   aiohttp
    #   yarl
proto-plus==1.26.1
    # via
    #   google-ai-generativelanguage
    #   google-api-core
protobuf==5.29.0
    # via
    #   -r requirements/base.in
    #   google-ai-generativelanguage
    #   google-api-core
    #   googleapis-common-protos
    #   grpcio-status
    #   grpcio-tools
    #   proto-plus
psutil==7.0.0
    # via swifter
psycopg2-binary==2.9.6
    # via -r requirements/base.in
pyarrow==14.0.1
    # via
    #   -r requirements/base.in
    #   awswrangler
    #   dask-expr
    #   ibis-framework
pyarrow-hotfix==0.7
    # via ibis-framework
pyasn1==0.6.1
    # via
    #   pyasn1-modules
    #   rsa
pyasn1-modules==0.4.2
    # via google-auth
pycparser==2.22
    # via cffi
pycryptodome==3.19.1
    # via -r requirements/base.in
pydantic==2.7.4
    # via
    #   -r requirements/base.in
    #   anthropic
    #   django-ninja
    #   gotrue
    #   groq
    #   langchain
    #   langchain-anthropic
    #   langchain-core
    #   langchain-google-genai
    #   langsmith
    #   openai
    #   postgrest
    #   pydantic-settings
pydantic-core==2.18.4
    # via pydantic
pydantic-settings==2.10.1
    # via langchain-community
pydash==7.0.4
    # via
    #   -r requirements/base.in
    #   vectorized-evaluate
pyee==11.0.1
    # via
    #   -r requirements/base.in
    #   playwright
pygments==2.19.2
    # via rich
pyjwt[crypto]==2.6.0
    # via
    #   -r requirements/base.in
    #   botframework-connector
    #   django-graphql-jwt
    #   docusign-esign
    #   drf-jwt
    #   msal
    #   snowflake-connector-python
pynacl==1.5.0
    # via paramiko
pyngrok==5.1.0
    # via -r requirements/base.in
pyodbc==4.0.39
    # via -r requirements/base.in
pyopenssl==25.1.0
    # via snowflake-connector-python
pyotp==2.9.0
    # via -r requirements/base.in
pypika==0.48.9
    # via -r requirements/base.in
pyrsistent==0.20.0
    # via jsonschema
python-crontab==3.2.0
    # via django-celery-beat
python-dateutil==2.9.0.post0
    # via
    #   analytics-python
    #   arrow
    #   botocore
    #   celery
    #   datadog-api-client
    #   docusign-esign
    #   faker
    #   graphene
    #   ibis-framework
    #   pandas
    #   python-crontab
    #   realtime
    #   storage3
python-dotenv==1.1.1
    # via pydantic-settings
python-gnupg==0.5.4
    # via -r requirements/base.in
python-http-client==3.3.7
    # via sendgrid
python-json-logger==2.0.7
    # via -r requirements/base.in
python-levenshtein==0.23.0
    # via -r requirements/base.in
pytz==2023.3
    # via
    #   -r requirements/base.in
    #   flower
    #   ibis-framework
    #   pandas
    #   snowflake-connector-python
pyyaml==6.0.2
    # via
    #   dask
    #   langchain
    #   langchain-community
    #   langchain-core
    #   pyngrok
    #   snowflake-snowpark-python
pyzstd==0.15.1
    # via -r requirements/base.in
rapidfuzz==3.13.0
    # via levenshtein
realtime==1.0.6
    # via supabase
redis==4.6.0
    # via
    #   -r requirements/base.in
    #   django-redis
    #   rq
regex==2024.11.6
    # via tiktoken
requests==2.32.0
    # via
    #   -r requirements/base.in
    #   analytics-python
    #   azure-core
    #   google-api-core
    #   langchain
    #   langchain-community
    #   langsmith
    #   msal
    #   msrest
    #   requests-oauthlib
    #   requests-toolbelt
    #   snowflake-connector-python
    #   tiktoken
    #   typesense
requests-oauthlib==2.0.0
    # via
    #   google-auth-oauthlib
    #   msrest
requests-toolbelt==1.0.0
    # via langsmith
rich==13.9.4
    # via
    #   ibis-framework
    #   typer
rq==2.3.3
    # via -r requirements/base.in
rsa==4.9.1
    # via google-auth
s3fs==2024.9.0
    # via -r requirements/base.in
s3transfer==0.10.4
    # via boto3
schema==0.7.5
    # via -r requirements/base.in
sendgrid==6.10.0
    # via -r requirements/base.in
setproctitle==1.2.2
    # via -r requirements/base.in
shellingham==1.5.4
    # via typer
six==1.17.0
    # via
    #   analytics-python
    #   azure-core
    #   docusign-esign
    #   nested-lookup
    #   prison
    #   promise
    #   python-dateutil
slack-bolt==1.9.1
    # via -r requirements/base.in
slack-sdk==3.11.1
    # via
    #   -r requirements/base.in
    #   slack-bolt
sniffio==1.3.1
    # via
    #   anthropic
    #   anyio
    #   groq
    #   httpx
    #   openai
snowflake-connector-python==3.15.0
    # via
    #   -r requirements/base.in
    #   ibis-framework
    #   snowflake-snowpark-python
snowflake-snowpark-python==1.10.0
    # via -r requirements/base.in
sortedcontainers==2.4.0
    # via snowflake-connector-python
soupsieve==2.7
    # via beautifulsoup4
sqlalchemy==1.4.46
    # via
    #   -r requirements/base.in
    #   langchain
    #   langchain-community
sqlglot==25.1.0
    # via
    #   -r requirements/base.in
    #   ibis-framework
sqlparse==0.5.0
    # via
    #   -r requirements/base.in
    #   django
starkbank-ecdsa==2.2.0
    # via sendgrid
storage3==0.7.7
    # via supabase
strenum==0.4.15
    # via postgrest
supabase==2.4.2
    # via -r requirements/base.in
supafunc==0.4.7
    # via supabase
supervisor==4.2.5
    # via -r requirements/base.in
swifter==1.4.0
    # via -r requirements/base.in
tabulate==0.9.0
    # via -r requirements/base.in
tenacity==8.5.0
    # via
    #   langchain
    #   langchain-community
    #   langchain-core
text-unidecode==1.3
    # via graphene-django
tiktoken==0.9.0
    # via langchain-openai
tomlkit==0.13.3
    # via snowflake-connector-python
toolz==0.12.1
    # via
    #   dask
    #   ibis-framework
    #   partd
tornado==6.5.1
    # via flower
tqdm==4.67.1
    # via
    #   openai
    #   swifter
typer==0.16.0
    # via -r requirements/base.in
types-python-dateutil==2.9.0.********
    # via arrow
typesense==0.21.0
    # via -r requirements/base.in
typing-extensions==4.12.2
    # via
    #   -r requirements/base.in
    #   anthropic
    #   anyio
    #   awswrangler
    #   azure-core
    #   datadog-api-client
    #   exceptiongroup
    #   graphene
    #   groq
    #   ibis-framework
    #   langchain-core
    #   multidict
    #   openai
    #   pydantic
    #   pydantic-core
    #   pydash
    #   pyee
    #   pyopenssl
    #   realtime
    #   rich
    #   snowflake-connector-python
    #   snowflake-snowpark-python
    #   storage3
    #   typer
    #   typing-inspect
    #   typing-inspection
typing-inspect==0.9.0
    # via dataclasses-json
typing-inspection==0.4.1
    # via pydantic-settings
tzdata==2025.2
    # via
    #   celery
    #   django-celery-beat
    #   kombu
    #   pandas
ua-parser[re2]==0.18.0
    # via -r requirements/base.in
ujson==5.8.0
    # via -r requirements/base.in
urllib3==1.26.20
    # via
    #   botbuilder-schema
    #   botocore
    #   datadog-api-client
    #   docusign-esign
    #   requests
vectorized-evaluate @ git+https://<EMAIL>/Everstage/vectorized_evaluate.git@v2.2.1-prod
    # via -r requirements/base.in
vine==5.1.0
    # via
    #   amqp
    #   celery
    #   kombu
wcwidth==0.2.13
    # via prompt-toolkit
websockets==12.0
    # via realtime
wheel==0.45.1
    # via snowflake-snowpark-python
wrapt==1.17.2
    # via aiobotocore
xlrd==2.0.1
    # via -r requirements/base.in
xlsxwriter==3.0.3
    # via -r requirements/base.in
yarl==1.20.1
    # via aiohttp
zipp==3.23.0
    # via importlib-metadata

# The following packages are considered to be unsafe in a requirements file:
# setuptools
