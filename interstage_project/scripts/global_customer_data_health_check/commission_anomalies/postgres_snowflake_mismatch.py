"""
Script to detect and report mismatches between PostgreSQL and Snowflake data
for payee periods and settlements.
"""

import logging
import os
from dataclasses import dataclass
from datetime import date, datetime, timezone
from typing import Dict, Optional, Tuple

from celery import group, shared_task
from django.db import connection
from django.utils.timezone import make_aware

from common.celery.celery_base_task import EverCeleryBaseTask
from interstage_project.celery import TaskGroupEnum
from interstage_project.utils import get_queue_name_respect_to_task_group
from scripts.global_customer_data_health_check.anomaly_log_service import (
    log_anomaly,
    log_heartbeat,
)
from scripts.global_customer_data_health_check.commission_anomalies.helper import (
    PRIME_LOGIC_EMAIL_ID,
    AnomalyType,
    create_alert_data,
)

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class PayoutData:
    """Represents payout data for a payee period"""

    total_payout: float
    commission: float
    period_end_date: datetime


@dataclass
class SettlementData:
    """Represents settlement data for a payee period"""

    total_payout: float
    settlement: float
    period_end_date: datetime


@dataclass
class SnowflakePayoutData:
    """Represents payout data from Snowflake"""

    commission: float
    period_end_date: date


@dataclass
class SnowflakeSettlementData:
    """Represents settlement data from Snowflake"""

    settlement: float
    period_end_date: date


class PayoutMismatchDetector:
    """Detects and records mismatches between PostgreSQL and Snowflake data."""

    def __init__(self, client_id: int, created_at: Optional[datetime] = None):
        from commission_engine.accessors.client_accessor import get_client_for_id

        self.client_id = client_id
        self.created_at = created_at or datetime.now(timezone.utc)
        client = get_client_for_id(client_id)
        self.client_name = client.name if client else "Unknown"
        self.env = os.getenv("ENV", "LOCALDEV").lower()

    def _get_payout_status_query(self, base_table: str) -> str:
        """Returns SQL query for fetching payout status data."""
        q = f"""
            SELECT 
                ps.payee_email_id, 
                ps.period_end_date,
                ps.total_payout,
                ps.commission
            FROM payout_status ps
            WHERE 
                ps.client_id = {self.client_id}
                AND ps.knowledge_end_date IS NULL
                AND NOT ps.is_deleted
                AND ps.period_end_date >= '2024-01-01'
                and (((ps.payout_split_up->>'{base_table}')::numeric = 0
                        and exists (
                        select
                            1
                        from
                            {base_table} c
                        where
                            c.client_id = ps.client_id
                            and c.payee_email_id = ps.payee_email_id
                            and c.period_start_date = ps.period_start_date
                            and not c.is_deleted
                            and c.knowledge_end_date is null
                ))
                    or (ps.payout_split_up->>'{base_table}')::numeric != 0
            );
        """  # noqa: S608
        return q

    def _get_snowflake_payout_query(self) -> str:
        """Returns SQL query for fetching Snowflake payout data."""
        q = f"""
            select payee_email_id::text as payee_email_id, 
            period_end_date as period_end_date, 
            sum(commission) as commission 
            from (
                    (select payee_email_id, period_start_date, period_end_date, criteria_id, commission
                        from payout_snapshot_data_{self.client_id}
                        where (additional_data:is_line_item_level::BOOLEAN 
                            or additional_data:split_summation_to_li::BOOLEAN)
                            and period_end_date >= '2024-01-01') 
                    union all
                    (select payee_email_id, period_start_date, period_end_date, criteria_id, ANY_VALUE(commission)
                        from payout_snapshot_data_{self.client_id}
                        where (not additional_data:is_line_item_level::BOOLEAN
                            AND (not additional_data:split_summation_to_li::BOOLEAN
                                OR additional_data:split_summation_to_li IS NULL
                            )
                        )
                        and period_end_date >= '2024-01-01'
                        group by payee_email_id, period_start_date, period_end_date, criteria_id
                    )
                ) 
            group by payee_email_id, period_end_date 
            order by payee_email_id, period_end_date;
        """  # noqa: S608
        return q

    def _get_snowflake_settlement_query(self) -> str:
        """Returns SQL query for fetching Snowflake settlement data."""
        q = f"""
            select payee_email_id::text as payee_email_id, 
            period_end_date as period_end_date, 
            sum(amount) as settlement
            from settlement_snapshot_data_{self.client_id}
            where period_end_date >= '2024-01-01'
            group by payee_email_id, period_end_date;
        """  # noqa: S608
        return q

    def _get_report_query(self, report_obj) -> str:
        """Returns SQL query for fetching commission report data."""
        from everstage_ddd.snowflake_reports.snowflake_report_utils import (
            get_report_object_table_name,
        )

        report_object_data_table = get_report_object_table_name(
            self.client_id,
            report_obj.value,
            report_obj.table_name,
        )
        q = f"""
            select data:payee_email_id::text as payee_email_id,
            data:period_end_date::timestamp as period_end_date,
            from {report_object_data_table}
            where knowledge_end_date is null
            and (is_deleted = False OR  is_deleted is null)
            and lower(data:record_type) = '{report_obj.value}'
            and data:period_end_date >= '2024-01-01'
            group by data:payee_email_id, data:period_end_date
            ;
        """  # noqa: S608
        return q

    def _fetch_postgres_data(
        self,
    ) -> Tuple[
        Dict[Tuple[str, date], PayoutData], Dict[Tuple[str, date], SettlementData]
    ]:
        """Fetches payout and settlement data from PostgreSQL."""
        commission_data = {}
        settlement_data = {}

        with connection.cursor() as cursor:
            cursor.execute(self._get_payout_status_query("commission"))
            for row in cursor.fetchall():
                commission_data[(row[0], row[1].date())] = PayoutData(
                    total_payout=row[2], commission=row[3], period_end_date=row[1]
                )

            cursor.execute(self._get_payout_status_query("settlement"))
            for row in cursor.fetchall():
                settlement_data[(row[0], row[1].date())] = SettlementData(
                    total_payout=row[2], settlement=row[3], period_end_date=row[1]
                )

        return commission_data, settlement_data

    def _fetch_snowflake_data(
        self,
    ) -> Tuple[
        Dict[Tuple[str, date], SnowflakePayoutData],
        Dict[Tuple[str, date], SnowflakeSettlementData],
        Dict[Tuple[str, date], SnowflakePayoutData],
        Dict[Tuple[str, date], SnowflakeSettlementData],
    ]:
        """Fetches payout and settlement data from Snowflake."""
        payout_snapshot_data = {}
        settlement_snapshot_data = {}
        commission_report_data = {}
        settlement_report_data = {}
        from commission_engine.database.snowflake_connection import (
            create_snowpark_session,
        )
        from commission_engine.utils.general_data import ReportObject

        with create_snowpark_session(client_id=self.client_id) as snowpark_session:
            payout_results = snowpark_session.sql(
                self._get_snowflake_payout_query()
            ).collect()

            logger.info("PAYOUT MISMATCH: Fetching Snowflake payout data for client")
            for row in payout_results:
                if row["PERIOD_END_DATE"] is not None:
                    payout_snapshot_data[
                        (row["PAYEE_EMAIL_ID"], row["PERIOD_END_DATE"].date())
                    ] = SnowflakePayoutData(
                        commission=row["COMMISSION"],  # type: ignore
                        period_end_date=row["PERIOD_END_DATE"],  # type: ignore
                    )

            settlement_results = snowpark_session.sql(
                self._get_snowflake_settlement_query()
            ).collect()
            logger.info(
                "PAYOUT MISMATCH: Fetching Snowflake settlement data for client"
            )
            for row in settlement_results:
                if row["PERIOD_END_DATE"] is not None:
                    settlement_snapshot_data[
                        (row["PAYEE_EMAIL_ID"], row["PERIOD_END_DATE"].date())
                    ] = SnowflakeSettlementData(
                        settlement=row["SETTLEMENT"],  # type: ignore
                        period_end_date=row["PERIOD_END_DATE"],  # type: ignore
                    )

            commission_results = snowpark_session.sql(
                self._get_report_query(ReportObject.COMMISSION)
            ).collect()
            logger.info(
                "PAYOUT MISMATCH: Fetching Snowflake commission report data for client"
            )
            for row in commission_results:
                if row["PERIOD_END_DATE"] is not None:
                    commission_report_data[
                        (row["PAYEE_EMAIL_ID"], row["PERIOD_END_DATE"].date())
                    ] = SnowflakePayoutData(
                        commission=None,  # type: ignore
                        period_end_date=row["PERIOD_END_DATE"],  # type: ignore
                    )

            settlement_results = snowpark_session.sql(
                self._get_report_query(ReportObject.SETTLEMENT)
            ).collect()
            logger.info(
                "PAYOUT MISMATCH: Fetching Snowflake settlement report data for client"
            )
            for row in settlement_results:
                if row["PERIOD_END_DATE"] is not None:
                    settlement_report_data[
                        (row["PAYEE_EMAIL_ID"], row["PERIOD_END_DATE"].date())
                    ] = SnowflakeSettlementData(
                        settlement=None,  # type: ignore
                        period_end_date=row["PERIOD_END_DATE"],  # type: ignore
                    )

        return (
            payout_snapshot_data,
            settlement_snapshot_data,
            commission_report_data,
            settlement_report_data,
        )

    def detect_mismatches(self) -> None:
        """Detects and records mismatches between PostgreSQL and Snowflake data."""
        try:
            from commission_engine.services.report_etl_service import (
                should_run_settlement_report,
            )

            run_settlement_report = should_run_settlement_report(self.client_id)
            pg_commission_data, pg_settlement_data = self._fetch_postgres_data()
            (
                sf_payout_snapshot_data,
                sf_settlement_snapshot_data,
                sf_commission_report_data,
                sf_settlement_report_data,
            ) = self._fetch_snowflake_data()

            com_ps_entries = set(pg_commission_data.keys())
            com_snap_entries = set(sf_payout_snapshot_data.keys())
            com_report_entries = set(sf_commission_report_data.keys())

            com_ps_miss_entries = (
                com_report_entries.union(com_snap_entries) - com_ps_entries
            )
            com_snap_miss_entries = (
                com_report_entries.union(com_ps_entries) - com_snap_entries
            )
            com_report_miss_entries = (
                com_ps_entries.union(com_snap_entries) - com_report_entries
            )

            set_ps_entries = set(pg_settlement_data.keys())
            set_snap_entries = set(sf_settlement_snapshot_data.keys())
            set_report_entries = set(sf_settlement_report_data.keys())

            if run_settlement_report:
                set_ps_miss_entries = (
                    set_report_entries.union(set_snap_entries) - set_ps_entries
                )
                set_snap_miss_entries = (
                    set_report_entries.union(set_ps_entries) - set_snap_entries
                )
                set_report_miss_entries = (
                    set_ps_entries.union(set_snap_entries) - set_report_entries
                )
            else:
                set_ps_miss_entries = set_snap_entries - set_ps_entries
                set_snap_miss_entries = set_ps_entries - set_snap_entries
                set_report_miss_entries = set()

            mismatches = []

            # Payout Status Missing
            for entry in com_ps_miss_entries.union(set_ps_miss_entries):
                mismatches.append(  # noqa: PERF401
                    create_alert_data(
                        anomaly_type=AnomalyType.PAYOUT_STATUS_MISSING,
                        alert_key=f"client_{self.client_id}_data_missing_{entry[0]}_{entry[1]}",
                        check_type="payout_status_missing",
                        metadata={
                            "affected_data": {
                                "payee_email_id": entry[0],
                                "period_end_date": entry[1],
                            },
                            "expected_value": {},
                            "actual_value": {},
                            "additional_context": {},
                        },
                    )
                )

            # Payout Snapshot Missing
            for entry in com_snap_miss_entries:
                mismatches.append(  # noqa: PERF401
                    create_alert_data(
                        anomaly_type=AnomalyType.PAYOUT_SNAPSHOT_MISSING,
                        alert_key=f"client_{self.client_id}_data_missing_{entry[0]}_{entry[1]}",
                        check_type="payout_snapshot_missing",
                        metadata={
                            "affected_data": {
                                "payee_email_id": entry[0],
                                "period_end_date": entry[1],
                            },
                            "expected_value": {},
                            "actual_value": {},
                            "additional_context": {},
                        },
                    )
                )

            # Commission Report Missing
            for entry in com_report_miss_entries:
                mismatches.append(  # noqa: PERF401
                    create_alert_data(
                        anomaly_type=AnomalyType.COMMISSION_REPORT_MISSING,
                        alert_key=f"client_{self.client_id}_data_missing_{entry[0]}_{entry[1]}",
                        check_type="commission_report_missing",
                        metadata={
                            "affected_data": {
                                "payee_email_id": entry[0],
                                "period_end_date": entry[1],
                            },
                            "expected_value": {},
                            "actual_value": {},
                            "additional_context": {},
                        },
                    )
                )

            # Settlement Snapshot Missing
            for entry in set_snap_miss_entries:
                mismatches.append(  # noqa: PERF401
                    create_alert_data(
                        anomaly_type=AnomalyType.SETTLEMENT_SNAPSHOT_MISSING,
                        alert_key=f"client_{self.client_id}_data_missing_{entry[0]}_{entry[1]}",
                        check_type="settlement_snapshot_missing",
                        metadata={
                            "affected_data": {
                                "payee_email_id": entry[0],
                                "period_end_date": entry[1],
                            },
                            "expected_value": {},
                            "actual_value": {},
                            "additional_context": {},
                        },
                    )
                )

            # Settlement Report Missing
            for entry in set_report_miss_entries:
                mismatches.append(  # noqa: PERF401
                    create_alert_data(
                        anomaly_type=AnomalyType.SETTLEMENT_REPORT_MISSING,
                        alert_key=f"client_{self.client_id}_data_missing_{entry[0]}_{entry[1]}",
                        check_type="settlement_report_missing",
                        metadata={
                            "affected_data": {
                                "payee_email_id": entry[0],
                                "period_end_date": entry[1],
                            },
                            "expected_value": {},
                            "actual_value": {},
                            "additional_context": {},
                        },
                    )
                )

            if mismatches:
                for mismatch in mismatches:
                    log_anomaly(
                        client_id=self.client_id,
                        client_name=self.client_name,
                        module="scripts",
                        check_type=mismatch.get("check_type", "Anomaly"),
                        alert_name=mismatch["alert_name"],
                        severity="High",
                        created_at=datetime.now(timezone.utc),
                        description=mismatch["description"],
                        alert_key=mismatch["alert_key"],
                        logger_email_id=PRIME_LOGIC_EMAIL_ID,
                        assignee=PRIME_LOGIC_EMAIL_ID,
                        cc_email_id="",
                        env=self.env,
                        metadata=mismatch["metadata"],
                    )
            else:
                log_heartbeat(
                    client_id=self.client_id,
                    client_name=self.client_name,
                    check_type="postgres_snowflake_mismatch",
                    env=self.env,
                    module="scripts",
                )

        except Exception:
            logger.exception("Error detecting mismatches for client %s", self.client_id)


@shared_task(base=EverCeleryBaseTask)
def postgres_snowflake_mismatch(client_id):
    """
    Task to detect and report mismatches between PostgreSQL and Snowflake data
    for payee periods and settlements.

    Args:
        client_id: ID of the client to process.
    """
    logger.info("Detecting PostgreSQL-Snowflake mismatch for client %s", client_id)
    start_time = datetime.now(timezone.utc)
    created_at = make_aware(datetime.now(timezone.utc))
    detector = PayoutMismatchDetector(client_id, created_at)
    detector.detect_mismatches()
    end_time = datetime.now(timezone.utc)
    logger.info(
        "PostgreSQL-Snowflake mismatch detection for client %s completed in %s",
        client_id,
        end_time - start_time,
    )


@shared_task(base=EverCeleryBaseTask)
def detect_postgres_snowflake_mismatch_wrapper(exclude_client_ids=None):
    """
    This function detects PostgreSQL-Snowflake mismatch for all clients

    Args:
        exclude_client_ids (List[int]): List of client ids
    """
    if exclude_client_ids is None:
        exclude_client_ids = []
    from commission_engine.accessors.client_accessor import (
        get_client_id_subscription_plan_map,
    )

    client_data = get_client_id_subscription_plan_map(exclude_client_ids)
    task_group = TaskGroupEnum.ANOMALY_DETECT.value

    task = group(
        [
            postgres_snowflake_mismatch.si(client_id).set(
                queue=get_queue_name_respect_to_task_group(
                    client_id, subscription_plan or "BASIC", task_group
                )
            )
            for client_id, subscription_plan in client_data.items()
        ]
    )
    task.apply_async(compression="lzma", serializer="pickle")
