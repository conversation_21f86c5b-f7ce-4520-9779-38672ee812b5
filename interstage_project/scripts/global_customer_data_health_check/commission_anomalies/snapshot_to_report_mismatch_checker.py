import logging
import os
from datetime import datetime, timedelta, timezone

from celery import chain, group, shared_task
from pandas import DataFrame

from common.celery.celery_base_task import EverCeleryBaseTask
from interstage_project.celery import TaskGroupEnum
from interstage_project.utils import get_queue_name_respect_to_task_group
from scripts.global_customer_data_health_check.anomaly_log_service import (
    log_anomaly,
    log_heartbeat,
)
from scripts.global_customer_data_health_check.commission_anomalies.base_table_to_snapshot_mismatch_checker import (
    get_comparision_query,
)
from scripts.global_customer_data_health_check.commission_anomalies.helper import (
    PRIME_LOGIC_EMAIL_ID,
    AnomalyType,
    create_alert_data,
    fetch_snowflake_data,
)

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def compare_payout_snapshot_and_commission_report(
    client_id: int,
    client_name: str,
    start_date: datetime,
    is_inter_object: bool,  # noqa: FBT001
):
    payout_snapshot_hash_query = get_payout_snapshot_hash_query(
        client_id, start_date, is_inter_object
    )
    commission_report_hash_query = get_commission_report_hash_query(
        client_id, start_date, is_inter_object
    )
    comparision_query = get_comparision_query(
        payout_snapshot_hash_query, commission_report_hash_query
    )
    mismatch_records = fetch_snowflake_data(client_id, comparision_query, None)

    snapshot_table_name = "payout_snapshot"
    report_table_name = "commission_report"
    if is_inter_object:
        snapshot_table_name = "inter_comm_snapshot"
        report_table_name = "inter_object_data"

    if mismatch_records.empty:
        logger.info(
            f"No mismatch records found for client {client_id} from {start_date} for {snapshot_table_name} and {report_table_name}"
        )
        return

    alerts = create_snapshot_to_report_mismatch_alert_data(
        client_id, snapshot_table_name, report_table_name, mismatch_records
    )
    created_at = datetime.now(timezone.utc)
    env = os.getenv("ENV")
    if alerts:
        for alert in alerts:
            log_anomaly(
                client_id=client_id,
                client_name=client_name,
                module="scripts",
                check_type=alert["check_type"],
                alert_name=alert["alert_name"],
                severity="High",
                created_at=created_at,
                description=alert["description"],
                alert_key=alert["alert_key"],
                logger_email_id=PRIME_LOGIC_EMAIL_ID,
                assignee=PRIME_LOGIC_EMAIL_ID,
                cc_email_id="",
                env=env,
                metadata=alert["metadata"],
            )
    else:
        log_heartbeat(
            client_id=client_id,
            client_name=client_name,
            check_type="snapshot_and_report_data_mismatch",
            env=env,
            module="scripts",
        )


def compare_settlement_snapshot_and_settlement_report(
    client_id: int, client_name: str, start_date: datetime
):
    settlement_snapshot_hash_query = get_settlement_snapshot_hash_query(
        client_id, start_date
    )
    settlement_report_hash_query = get_settlement_report_hash_query(
        client_id, start_date
    )
    comparision_query = get_comparision_query(
        settlement_snapshot_hash_query, settlement_report_hash_query
    )
    mismatch_records = fetch_snowflake_data(client_id, comparision_query, None)

    if mismatch_records.empty:
        logger.info(
            f"No mismatch records found for client {client_id} from {start_date} for settlement_snapshot and settlement_report"
        )
        return

    alerts = create_snapshot_to_report_mismatch_alert_data(
        client_id, "settlement_snapshot", "settlement_report", mismatch_records
    )
    created_at = datetime.now(timezone.utc)
    env = os.getenv("ENV")
    if alerts:
        for alert in alerts:
            log_anomaly(
                client_id=client_id,
                client_name=client_name,
                module="scripts",
                check_type=alert["check_type"],
                alert_name=alert["alert_name"],
                severity="High",
                created_at=created_at,
                description=alert["description"],
                alert_key=alert["alert_key"],
                logger_email_id=PRIME_LOGIC_EMAIL_ID,
                assignee=PRIME_LOGIC_EMAIL_ID,
                cc_email_id="",
                env=env,
                metadata=alert["metadata"],
            )
    else:
        log_heartbeat(
            client_id=client_id,
            client_name=client_name,
            check_type="snapshot_and_report_data_mismatch",
            env=env,
            module="scripts",
        )


def get_payout_snapshot_hash_query(
    client_id: int, start_date: datetime, is_inter_object: bool  # noqa: FBT001
):
    snapshot_table = f"payout_snapshot_data_{client_id}"
    if is_inter_object:
        snapshot_table = f"inter_comm_snapshot_data_{client_id}"
    start_date_str = start_date.isoformat()
    return f""" 
        WITH partition_data AS (
            SELECT 
                c.payee_email_id, 
                c.period_end_date,
                c.criteria_id,
                NVL(c.line_item_id, '-') AS c_line_item_id, 
                f.value:tier_id::string AS c_tier_id,
                TO_VARCHAR(SUM(ROUND(TO_DECIMAL(f.value:commission, 38, 10), 2))) AS c_commission,
                MD5(CONCAT_WS(
                    '|', 
                    c.criteria_id, 
                    NVL(c.line_item_id, '-'), 
                    f.value:tier_id::string,
                    TO_VARCHAR(SUM(ROUND(TO_DECIMAL(f.value:commission, 38, 10), 2)))
                )) AS record_hash
            FROM {snapshot_table} c,
            LATERAL FLATTEN(input => c.tier_id_split) f
            WHERE c.period_end_date >= '{start_date_str}'
                AND f.value:tier_id::string <> '-'
                AND (
                    c.additional_data:is_line_item_level::boolean = true
                    OR
                    c.additional_data:split_summation_to_li::boolean = true
                )
            GROUP BY c.payee_email_id, c.period_end_date, c.criteria_id, NVL(c.line_item_id, '-'), f.value:tier_id::string

            UNION ALL

            SELECT 
                c.payee_email_id, 
                c.period_end_date,
                c.criteria_id,
                '-' AS c_line_item_id,
                ANY_VALUE(c.tier_id_split[0]:tier_id::string) AS c_tier_id,
                TO_VARCHAR(ROUND(ANY_VALUE(c.commission), 2)) AS c_commission,
                MD5(CONCAT_WS(
                    '|', 
                    c.criteria_id, 
                    '-', 
                    ANY_VALUE(c.tier_id_split[0]:tier_id::string),
                    TO_VARCHAR(ROUND(ANY_VALUE(c.commission), 2))
                )) AS record_hash
            FROM {snapshot_table} c
            WHERE c.period_end_date >= '{start_date_str}'
                AND c.tier_id_split[0]:tier_id::string <> '-'
                AND (
                    c.additional_data:is_line_item_level::boolean = false
                    AND
                    c.additional_data:split_summation_to_li::boolean is distinct from true
                )
            GROUP BY c.payee_email_id, c.period_end_date, c.criteria_id
        )
        SELECT 
            payee_email_id, 
            period_end_date, 
            COUNT(*) AS record_count,
            MD5(LISTAGG(record_hash, '') WITHIN GROUP (ORDER BY record_hash)) AS partition_checksum
        FROM partition_data
            GROUP BY payee_email_id, period_end_date
        """  # noqa: S608


def get_commission_report_hash_query(
    client_id: int, start_date: datetime, is_inter_object: bool  # noqa: FBT001
):
    start_date_str = start_date.isoformat()
    table_name = f"report_object_data_{client_id}_commission"
    if is_inter_object:
        table_name = f"inter_object_data_{client_id}_inter_commission"
    query = f"""
        WITH partition_data AS (
            SELECT 
                c.data:payee_email_id::string as payee_email_id, 
                c.data:period_end_date::timestamp as period_end_date,
                MD5(CONCAT_WS(
                    '|', 
                    c.data:criteria_id::string, 
                    NVL(c.data:line_item_id::string, '-'), 
                    NVL(c.data:tier_id::string, 'None'),
                    TO_VARCHAR(ROUND(TO_DECIMAL(c.data:amount, 38, 10), 2))
                )) AS record_hash
            FROM {table_name} c
            WHERE c.knowledge_end_date is null and (c.is_deleted = false or c.is_deleted is null)
                AND c.data:period_end_date::timestamp >= '{start_date_str}'
                AND c.data:record_type::string = 'Commission'
        )
        SELECT 
            payee_email_id, 
            period_end_date, 
            COUNT(*) AS record_count,
            MD5(LISTAGG(record_hash, '') WITHIN GROUP (ORDER BY record_hash)) AS partition_checksum
        FROM partition_data
            GROUP BY payee_email_id, period_end_date
    """  # noqa: S608
    return query


def get_settlement_snapshot_hash_query(client_id: int, start_date: datetime):
    start_date_str = start_date.isoformat()
    table_name = f"settlement_snapshot_data_{client_id}"
    query = f"""
            WITH partition_data AS (
            SELECT 
                c.payee_email_id, 
                c.period_end_date,
                MD5(CONCAT_WS(
                    '|', 
                    c.criteria_id, 
                    NVL(c.settlement_rule_id, '-'),
                    NVL(TO_VARCHAR(c.comm_period_start_date, 'YYYY-MM-DD'), ''),
                    NVL(TO_VARCHAR(c.comm_period_end_date, 'YYYY-MM-DD'), ''),
                    NVL(c.line_item_id, '-'), 
                    NVL(c.commission_row_key, '-'), 
                    TO_VARCHAR(ROUND(c.amount, 2)))
                ) AS record_hash
            FROM {table_name} c where c.settlement_flag = true
                AND c.period_end_date >= '{start_date_str}'
        )
        SELECT 
            payee_email_id, 
            period_end_date, 
            COUNT(*) AS record_count,
            MD5(LISTAGG(record_hash, '') WITHIN GROUP (ORDER BY record_hash)) AS partition_checksum
        FROM partition_data
            GROUP BY payee_email_id, period_end_date
    """  # noqa: S608
    return query


def get_settlement_report_hash_query(client_id: int, start_date: datetime):
    start_date_str = start_date.isoformat()
    table_name = f"report_object_data_{client_id}_settlement"
    query = f"""
            WITH partition_data AS (
            SELECT 
                s.data:payee_email_id::string as payee_email_id, 
                s.data:period_end_date::timestamp as period_end_date,
                MD5(CONCAT_WS(
                    '|', 
                    s.data:criteria_id::string, 
                    NVL(s.data:settlement_rule_id::string, '-'),
                    NVL(TO_VARCHAR(s.data:comm_period_start_date::timestamp, 'YYYY-MM-DD'), ''),
                    NVL(TO_VARCHAR(s.data:comm_period_end_date::timestamp, 'YYYY-MM-DD'), ''),
                    NVL(s.data:line_item_id::string, '-'), 
                    NVL(s.data:commission_row_key::string, '-'), 
                    TO_VARCHAR(ROUND(TO_DECIMAL(s.data:amount, 38, 10), 2)))
                ) AS record_hash
            FROM {table_name} s
            WHERE s.knowledge_end_date is null and s.is_deleted = false
                AND s.data:period_end_date::timestamp >= '{start_date_str}'
                AND s.data:record_type::string = 'Settlement'
        )
        SELECT 
            payee_email_id, 
            period_end_date, 
            COUNT(*) AS record_count,
            MD5(LISTAGG(record_hash, '') WITHIN GROUP (ORDER BY record_hash)) AS partition_checksum
        FROM partition_data
            GROUP BY payee_email_id, period_end_date
    """  # noqa: S608
    return query


def create_snapshot_to_report_mismatch_alert_data(
    client_id: int,
    snapshot_table_name: str,
    report_table_name: str,
    mismatch_records: DataFrame,
):
    alerts = []
    alert_key_prefix = f"client_{client_id}_base_table_to_snapshot_mismatch"
    mismatch_records_list = mismatch_records.to_dict(orient="records")
    for record in mismatch_records_list:
        alert_key = f"{alert_key_prefix}_{snapshot_table_name}_{report_table_name}_{record['payee_email_id']}_{record['period_end_date']}"
        alerts.append(
            create_alert_data(
                anomaly_type=AnomalyType.SNAPSHOT_REPORT_MISMATCH,
                alert_key=alert_key,
                check_type="snapshot_and_report_data_mismatch",
                metadata={
                    "affected_data": {
                        "payee_email_id": record["payee_email_id"],
                        "period_end_date": record["period_end_date"],
                        "snapshot_table_name": snapshot_table_name,
                        "report_table_name": report_table_name,
                    },
                    "expected_value": {"checksum": record["partition_checksum_1"]},
                    "actual_value": {"checksum": record["partition_checksum_2"]},
                    "additional_context": {},
                },
            )
        )
    return alerts


@shared_task(base=EverCeleryBaseTask)
def detect_payout_snapshot_to_commission_report_mismatch(
    client_id, client_name, is_inter_object, look_back_days=730
):
    """
    This function detects mismatch between payout snapshot and commission report data for the client.
    """
    start_date = datetime.now(timezone.utc) - timedelta(days=look_back_days)
    logger.info(
        f"Detecting payout snapshot to commission report mismatch with is_inter_object - {is_inter_object} for client {client_id} from {start_date}"
    )
    start_time = datetime.now(timezone.utc)
    compare_payout_snapshot_and_commission_report(
        client_id=client_id,
        client_name=client_name,
        start_date=start_date,
        is_inter_object=is_inter_object,
    )
    end_time = datetime.now(timezone.utc)
    logger.info(
        f"Detecting payout snapshot to commission report mismatch with is_inter_object - {is_inter_object} for client {client_id} completed in {end_time - start_time}"
    )


@shared_task(base=EverCeleryBaseTask)
def detect_settlement_snapshot_to_settlement_report_mismatch(
    client_id, client_name, look_back_days=730
):
    """
    This function detects mismatch between settlement snapshot and settlement report data for the client.
    """
    start_date = datetime.now(timezone.utc) - timedelta(days=look_back_days)
    logger.info(
        f"Detecting settlement snapshot to settlement report mismatch for client {client_id} from {start_date}"
    )
    start_time = datetime.now(timezone.utc)
    compare_settlement_snapshot_and_settlement_report(
        client_id=client_id,
        client_name=client_name,
        start_date=start_date,
    )
    end_time = datetime.now(timezone.utc)
    logger.info(
        f"Detecting settlement snapshot to settlement report mismatch for client {client_id} completed in {end_time - start_time}"
    )


@shared_task(base=EverCeleryBaseTask)
def detect_snapshot_to_report_mismatch(exclude_client_ids=None, look_back_days=365):
    """
    This function detects payout snapshot to commission report mismatch for all clients except the ones in the exclude_client_ids list.
    """
    from commission_engine.accessors.client_accessor import (
        get_active_clients_features_excluding_churned,
    )

    if exclude_client_ids is None:
        exclude_client_ids = []
    client_data = get_active_clients_features_excluding_churned(exclude_client_ids)
    task_group = TaskGroupEnum.ANOMALY_DETECT.value

    payout_snapshot_to_commission_report_mismatch_tasks = group(
        [
            detect_payout_snapshot_to_commission_report_mismatch.si(
                client_id=client_id,
                client_name=name,
                is_inter_object=False,
                look_back_days=look_back_days,
            ).set(
                queue=get_queue_name_respect_to_task_group(
                    client_id, features.get("subscription_plan", "BASIC"), task_group
                )
            )
            for client_id, name, features in client_data
        ]
    )

    inter_snapshot_to_inter_commission_report_mismatch_tasks = group(
        [
            detect_payout_snapshot_to_commission_report_mismatch.si(
                client_id=client_id,
                client_name=name,
                is_inter_object=True,
                look_back_days=look_back_days,
            ).set(
                queue=get_queue_name_respect_to_task_group(
                    client_id, features.get("subscription_plan", "BASIC"), task_group
                )
            )
            for client_id, name, features in client_data
            if bool(features.get("expose_comm_reports_in_plan"))
        ]
    )

    settlement_snapshot_to_settlement_report_mismatch_tasks = group(
        [
            detect_settlement_snapshot_to_settlement_report_mismatch.si(
                client_id=client_id,
                client_name=name,
                look_back_days=look_back_days,
            ).set(
                queue=get_queue_name_respect_to_task_group(
                    client_id, features.get("subscription_plan", "BASIC"), task_group
                )
            )
            for client_id, name, features in client_data
        ]
    )
    chain(
        payout_snapshot_to_commission_report_mismatch_tasks,
        inter_snapshot_to_inter_commission_report_mismatch_tasks,
        settlement_snapshot_to_settlement_report_mismatch_tasks,
    ).apply_async(compression="lzma", serializer="pickle")
