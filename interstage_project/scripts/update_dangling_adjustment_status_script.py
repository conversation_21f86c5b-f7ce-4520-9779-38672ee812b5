"""
This script updates the dangling adjustment status for datasheets.
It can be run for specific clients, datasheets, or all clients.

Examples:
    # Update for a specific client
    >>> python scripts/update_dangling_adjustment_status_script.py --client-ids 9007

    # Update for multiple clients
    >>> python scripts/update_dangling_adjustment_status_script.py --client-ids 9007,9008,9009

    # Update for a specific datasheet
    >>> python scripts/update_dangling_adjustment_status_script.py --datasheet-id 123e4567-e89b-12d3-a456-************

    # Update for a specific databook
    >>> python scripts/update_dangling_adjustment_status_script.py --databook-id 123e4567-e89b-12d3-a456-************

    # Update for all clients
    >>> python scripts/update_dangling_adjustment_status_script.py --all-clients
"""

import django

django.setup()
import datetime
import logging
import os
import time
from typing import List, Optional
from uuid import UUID

import pandas as pd
import typer
from django.conf import settings
from django.db import connection
from django.utils.timezone import make_aware

from commission_engine.accessors.client_accessor import (
    should_use_multi_engine_stormbreaker,
)
from commission_engine.accessors.databook_accessor import DatasheetAccessor
from commission_engine.data_readers.databook_reader import DatasheetDataReader
from commission_engine.database.snowflake_connection import (
    create_snowpark_session_wrapper,
)
from commission_engine.utils.databook_utils import get_datasheet_data_table_name
from everstage_ddd.datasheet.data_models import get_databook_id_from_datasheet_id
from everstage_ddd.datasheet.enums import DanglingReason
from everstage_ddd.datasheet.selectors.datasheet_adjustment_selector import (
    DatasheetAdjustmentSelector,
)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

# Initialize Typer
app = typer.Typer()


def fetch_latest_records_from_snowflake(
    client_id: int, databook_id: UUID, datasheet_id: UUID, row_keys: set
) -> pd.DataFrame:
    """
    Fetch the latest records from Snowflake for given row keys.

    Args:
        client_id: Client ID
        databook_id: Databook ID
        datasheet_id: Datasheet ID
        row_keys: Set of row keys to fetch

    Returns:
        DataFrame containing the latest records for the given row keys
    """
    if not row_keys:
        return pd.DataFrame()

    table_name = get_datasheet_data_table_name(client_id, datasheet_id)

    # Convert set to properly formatted string for SQL IN clause
    row_keys_str = ",".join(f"'{key}'" for key in row_keys)

    with create_snowpark_session_wrapper(client_id=client_id) as session:
        query = f"""
        SELECT row_key, knowledge_begin_date, knowledge_end_date
        FROM {table_name}
        WHERE client_id = {client_id}
        AND databook_id = '{databook_id}'
        AND datasheet_id = '{datasheet_id}'
        AND row_key IN ({row_keys_str})
        AND knowledge_end_date IS NOT NULL
        AND NOT is_deleted
        """

        print(query)
        df = session.sql(query).to_pandas()
        print(df)

    if df.empty:
        return pd.DataFrame()

    # Get the latest record for each row key
    latest_records = (
        df.sort_values("KNOWLEDGE_BEGIN_DATE", ascending=False)
        .groupby("ROW_KEY")
        .first()
        .reset_index()
    )

    return latest_records


def update_dangling_adjustment_status(
    client_id: int, datasheet_ids: list[UUID]
) -> None:
    """
    This function updates the adjustment status of the dangling adjustments.
    """
    # invalidated_at = datetime.datetime.now()
    for datasheet_id in datasheet_ids:
        logger.info("Fetching adjustment row keys")
        databook_id = get_databook_id_from_datasheet_id(client_id, datasheet_id)
        datasheet_adjustment_accessor = DatasheetAdjustmentSelector(client_id)
        adjustment_row_keys = (
            datasheet_adjustment_accessor.get_row_keys_for_adjustments(
                databook_id, datasheet_id
            )
        )
        if None in adjustment_row_keys:
            adjustment_row_keys.remove(None)
        if adjustment_row_keys:
            logger.info(
                "Adjustments present for this datasheet: {}".format(
                    len(adjustment_row_keys)
                )
            )
            start_time = time.monotonic()

            if should_use_multi_engine_stormbreaker(client_id):
                ds_reader = DatasheetDataReader(
                    client_id,
                    databook_id,
                    datasheet_id,
                    compute_strategy="duckdb_fallback_variant_snowflake",
                    apply_datasheet_permissions=False,
                )
            else:
                ds_reader = DatasheetDataReader(
                    client_id,
                    databook_id,
                    datasheet_id,
                    apply_datasheet_permissions=False,
                )

            try:
                logger.info("BEGIN: Fetch original data")
                data = ds_reader.fetch_data(as_data_frame=True, only_adjustments=True)
                logger.info("END: Fetch original data: {} rows".format(len(data)))
                logger.info(
                    "Time taken to fetch original data: %s seconds",
                    time.monotonic() - start_time,
                )
            except PermissionError:
                logger.exception("Permission error while fetching datasheet data")
                return

            df_adjustment_data_for_datasheet = DatasheetAdjustmentSelector(
                client_id
            ).get_adjustments_for_datasheet_as_frame(
                databook_id, datasheet_id, adjustments_type_filter=None
            )
            df_dangling_adjustments = df_adjustment_data_for_datasheet[
                df_adjustment_data_for_datasheet.dangling_reason.notnull()
            ]
            # dangling row keys are row keys present in adjustment_row_keys but are not present in data
            set_of_row_keys_in_adjustment = set(
                df_adjustment_data_for_datasheet.original_row_key
            )
            set_of_row_keys_in_dangling_adjustments = set(
                df_dangling_adjustments.original_row_key
            )
            set_of_row_keys_in_data = set(data.row_key)
            dangling_row_keys = (
                set_of_row_keys_in_adjustment
                - set_of_row_keys_in_dangling_adjustments
                - set_of_row_keys_in_data
            )

            dangling_records = df_adjustment_data_for_datasheet[
                df_adjustment_data_for_datasheet.original_row_key.isin(
                    dangling_row_keys
                )
            ].to_dict(orient="records")

            valid_records = df_dangling_adjustments[
                df_dangling_adjustments.original_row_key.isin(set_of_row_keys_in_data)
            ].to_dict(orient="records")

            for record in valid_records:
                datasheet_adjustment_accessor.update_dangling_adjustment_status(
                    adjustment_id=record.get("adjustment_id"),
                    adjustment_number=record.get("adjustment_number"),
                    sub_number=record.get("sub_adjustment_number"),
                    invalidated_at=None,
                    dangling_reason=None,
                )

            datasheet_accessor = DatasheetAccessor(client_id)
            snowflake_records = fetch_latest_records_from_snowflake(
                client_id, databook_id, datasheet_id, dangling_row_keys
            )
            if not snowflake_records.empty:
                invalidated_at = datetime.datetime.now()
            for record in dangling_records:
                snowflake_record = snowflake_records[
                    snowflake_records["ROW_KEY"] == record.get("original_row_key")
                ].iloc[0]

                invalidated_at = datetime.datetime.strptime(
                    str(snowflake_record["KNOWLEDGE_END_DATE"]), "%Y-%m-%d %H:%M:%S.%f"
                )
                primary_keys_before_update = (
                    datasheet_accessor.get_primary_columns_for_datasheet_kd_aware(
                        databook_id=databook_id,
                        datasheet_id=datasheet_id,
                        knowledge_date=record.get("knowledge_begin_date"),
                    )
                )
                latest_primary_key = (
                    datasheet_accessor.get_primary_columns_for_datasheet(
                        databook_id, datasheet_id
                    )
                )
                if primary_keys_before_update != latest_primary_key:
                    dangling_reason = DanglingReason.PRIMARY_KEY_TRANSFORMED.value
                else:
                    dangling_reason = DanglingReason.ROW_INVALIDATED.value

                logger.info(
                    f"Dangling with reason: {dangling_reason}, row_key: {record.get('row_key')}"
                )
                datasheet_adjustment_accessor.update_dangling_adjustment_status(
                    adjustment_id=record.get("adjustment_id"),
                    adjustment_number=record.get("adjustment_number"),
                    sub_number=record.get("sub_adjustment_number"),
                    invalidated_at=invalidated_at,
                    dangling_reason=dangling_reason,
                )


def get_all_client_ids() -> List[int]:
    """Get all active client IDs from the database"""
    with connection.cursor() as cursor:
        cursor.execute(
            """
            SELECT DISTINCT client_id 
            FROM interstage_clients 
            WHERE NOT is_deleted
            """
        )
        return [row[0] for row in cursor.fetchall()]


def get_datasheet_ids_for_client(client_id: int) -> List[UUID]:
    """Get all datasheet IDs for a given client"""
    with connection.cursor() as cursor:
        cursor.execute(
            """
            SELECT datasheet_id 
            FROM datasheet 
            WHERE client_id = %s 
            AND knowledge_end_date IS NULL 
            AND NOT is_deleted
            """,
            [client_id],
        )
        return [row[0] for row in cursor.fetchall()]


def get_datasheet_ids_for_databook(databook_id: UUID) -> List[UUID]:
    """Get all datasheet IDs for a given databook"""
    with connection.cursor() as cursor:
        cursor.execute(
            """
            SELECT datasheet_id 
            FROM datasheet 
            WHERE databook_id = %s 
            AND knowledge_end_date IS NULL 
            AND NOT is_deleted
            """,
            [databook_id],
        )
        return [row[0] for row in cursor.fetchall()]


def process_client(client_id: int) -> tuple[bool, Optional[str]]:
    """
    Process a single client by updating dangling adjustment status for all its datasheets.
    Returns a tuple of (success: bool, error_message: Optional[str])
    """
    logger.info(f"Processing client {client_id}...")
    try:
        datasheet_ids = get_datasheet_ids_for_client(client_id)
        if not datasheet_ids:
            logger.warning(f"No active datasheets found for client {client_id}")
            return True, None

        logger.info(f"Found {len(datasheet_ids)} datasheets for client {client_id}")

        # Track failures for individual datasheets
        failed_datasheets = []
        for datasheet_id in datasheet_ids:
            try:
                update_dangling_adjustment_status(
                    client_id=client_id, datasheet_ids=[datasheet_id]
                )
            except Exception as e:
                error_msg = f"Failed to process datasheet {datasheet_id}: {str(e)}"
                logger.exception(error_msg)
                failed_datasheets.append((datasheet_id, error_msg))

        if failed_datasheets:
            error_summary = (
                f"Client {client_id} completed with {len(failed_datasheets)} failed datasheets: "
                + ", ".join(
                    f"datasheet {ds_id} ({err})" for ds_id, err in failed_datasheets
                )
            )
            return False, error_summary

        logger.info(f"Successfully processed client {client_id}")
        return True, None

    except Exception as e:
        error_msg = f"Error processing client {client_id}: {str(e)}"
        logger.exception(error_msg)
        return False, error_msg


def get_client_id_from_datasheet_id(datasheet_id: UUID) -> int:
    """Get the client ID for a given datasheet ID"""
    with connection.cursor() as cursor:
        cursor.execute(
            """
            SELECT client_id 
            FROM datasheet 
            WHERE datasheet_id = %s
            AND knowledge_end_date IS NULL
            AND NOT is_deleted
            """,
            [datasheet_id],
        )
        result = cursor.fetchone()
        if not result:
            raise typer.BadParameter(
                f"Datasheet {datasheet_id} not found or not active"
            )
        return result[0]


def get_client_id_from_databook_id(databook_id: UUID) -> int:
    """Get the client ID for a given databook ID"""
    with connection.cursor() as cursor:
        cursor.execute(
            """
            SELECT client_id 
            FROM databook 
            WHERE databook_id = %s
            AND NOT is_deleted
            """,
            [databook_id],
        )
        result = cursor.fetchone()
        if not result:
            raise typer.BadParameter(f"Databook {databook_id} not found or not active")
        return result[0]


@app.command()
def main(
    client_ids: str = typer.Option(
        None,
        "--client-ids",
        "-c",
        help="Optional comma-separated list of client_ids (e.g., --client-ids 1,2,3)",
    ),
    datasheet_id: str = typer.Option(
        None,
        "--datasheet-id",
        "-d",
        help="Optional UUID of a specific datasheet to process",
    ),
    databook_id: str = typer.Option(
        None,
        "--databook-id",
        "-b",
        help="Optional UUID of a databook to process all its datasheets",
    ),
    all_clients: bool = typer.Option(
        False,
        "--all-clients",
        "-a",
        help="Process all active clients",
    ),
):
    """
    Update dangling adjustment status for datasheets.
    Must provide exactly one of: client_ids, datasheet_id, databook_id, or all_clients flag.
    """
    # Validate input parameters
    provided_options = sum(
        1 for x in [client_ids, datasheet_id, databook_id, all_clients] if x
    )
    if provided_options != 1:
        raise typer.BadParameter(
            "Must provide exactly one of: client_ids, datasheet_id, databook_id, or all_clients flag"
        )

    # Track all failures
    failures = []

    try:
        if client_ids:
            # Process specific clients
            try:
                parsed_client_ids = [int(cid.strip()) for cid in client_ids.split(",")]
            except ValueError:
                raise typer.BadParameter("client_ids must be comma-separated integers")

            for client_id in parsed_client_ids:
                success, error = process_client(client_id)
                if not success:
                    failures.append(error)

        elif datasheet_id:
            # Process single datasheet
            try:
                ds_id = UUID(datasheet_id)
                client_id = get_client_id_from_datasheet_id(ds_id)

                logger.info(
                    f"Processing datasheet {datasheet_id} for client {client_id}"
                )
                try:
                    update_dangling_adjustment_status(
                        client_id=client_id, datasheet_ids=[ds_id]
                    )
                except Exception as e:
                    error_msg = f"Failed to process datasheet {datasheet_id} for client {client_id}: {str(e)}"
                    logger.exception(error_msg)
                    failures.append(error_msg)

            except ValueError:
                raise typer.BadParameter("datasheet_id must be a valid UUID")

        elif databook_id:
            # Process all datasheets in a databook
            try:
                db_id = UUID(databook_id)
                client_id = get_client_id_from_databook_id(db_id)
                datasheet_ids = get_datasheet_ids_for_databook(db_id)
                logger.info(
                    f"Processing {len(datasheet_ids)} datasheets for databook {databook_id}"
                )

                # Track failures for individual datasheets
                failed_datasheets = []
                for ds_id in datasheet_ids:
                    try:
                        update_dangling_adjustment_status(
                            client_id=client_id, datasheet_ids=[ds_id]
                        )
                    except Exception as e:
                        error_msg = f"Failed to process datasheet {ds_id}: {str(e)}"
                        logger.exception(error_msg)
                        failed_datasheets.append((ds_id, str(e)))

                if failed_datasheets:
                    error_summary = (
                        f"Databook {databook_id} completed with {len(failed_datasheets)} failed datasheets: "
                        + ", ".join(
                            f"datasheet {ds_id} ({err})"
                            for ds_id, err in failed_datasheets
                        )
                    )
                    failures.append(error_summary)

            except ValueError:
                raise typer.BadParameter("databook_id must be a valid UUID")

        else:  # all_clients is True
            # Process all active clients
            all_client_ids = get_all_client_ids()
            logger.info(f"Processing {len(all_client_ids)} active clients")
            for client_id in all_client_ids:
                success, error = process_client(client_id)
                if not success:
                    failures.append(error)

    except Exception as e:
        error_msg = f"Error in main execution: {str(e)}"
        logger.exception(error_msg)
        failures.append(error_msg)

    # Print summary of failures at the end
    if failures:
        logger.error("\n=== FAILURE SUMMARY ===")
        for failure in failures:
            logger.error(failure)
        logger.error("=====================\n")
        raise typer.Exit(1)
    else:
        logger.info("All operations completed successfully!")


if __name__ == "__main__":
    # Set Django settings module
    os.environ.setdefault("DJANGO_SETTINGS_MODULE", "interstage_project.settings")
    app()
