const {
  commSyncCredsWithoutCustomCalender: { test: test2, expect },
} = require("../../../../fixtures");

test2.describe(
  "Commission Sync report etl test",
  { tag: ["@commissionsync", "@regression", "@primelogic-1"] },
  () => {
    const clickEnabledRunButton = async (adminPage) => {
      const page = adminPage.page;
      const buttons = await page.$$("button:has-text('Run')");
      for (const button of buttons) {
        const isEnabled = await button.isEnabled();
        if (isEnabled) {
          await button.click();
          break;
        }
      }
    };
    const expandMenu = async (page, buttonName) => {
      // Ensure all buttons are closed
      await page.waitForTimeout(2000);
      await page.waitForFunction(() => {
        const elements = document.querySelectorAll("div.ant-collapse-header");
        return elements.length === 6;
      });
      const buttons = await page.locator("div.ant-collapse-header").all();
      console.log(buttons);

      for (const button of buttons) {
        const ariaExpanded = await button.getAttribute("aria-expanded");
        if (ariaExpanded === "true") {
          // Close the button if it is expanded
          await button.click();
        }
      }

      // Click on buttons that are still expanded
      for (const button of buttons) {
        const ariaExpanded = await button.getAttribute("aria-expanded");
        if (ariaExpanded === "true") {
          await button.click();
        }
      }
      await page.waitForTimeout(2000);

      switch (buttonName) {
        case "Calculate Commissions Run":
          await page
            .getByRole("button", { name: "Calculate Commissions Run" })
            .click();
          break;
        case "Calculate Settlements Run":
          await page
            .getByRole("button", { name: "Calculate Settlements Run" })
            .click();
          break;
        case "Refresh Databooks Uploaded":
          await page
            .getByRole("button", { name: "Refresh Databooks Uploaded" })
            .click();
          break;
        case "Sync Data from Connectors":
          await page
            .getByRole("button", { name: "Sync Data from Connectors" })
            .click();
          break;
        case "Report ETL Run ETL for report":
          await page
            .getByRole("button", { name: "Report ETL Run ETL for report" })
            .click();
          break;
        case "Migrate data to inter objects":
          await page
            .getByRole("button", { name: "Migrate data to inter objects" })
            .click();
          break;
        default:
          break;
      }
    };

    test2("Saving the commission plans", async ({ adminPage }) => {
      const page = adminPage.page;
      await page.goto("/plans", {
        waitUntil: "networkidle",
      });
      await page.getByPlaceholder("Search by plan name").click();
      await page.getByPlaceholder("Search by plan name").fill("Plan");
      await page.getByText("plan", { exact: true }).click();
      await page.getByText("simple").first().click();
      await page.getByRole("button", { name: "Save" }).click();
      await page.waitForTimeout(3000);
      await page.getByText("sett", { exact: true }).first().click();
      await page.getByRole("button", { name: "Save" }).click();
      await page.waitForTimeout(3000);
      await page.getByRole("button", { name: "Exit Canvas" }).click();
      await page.getByLabel("close-circle").click();
      await page.getByPlaceholder("Search by plan name").click();
      await page.getByPlaceholder("Search by plan name").fill("Plan_2");
      await page.getByText("plan_2").click();
      await page.getByText("siple").first().click();
      await page.getByRole("button", { name: "Save" }).click();
      await page.waitForTimeout(3000);
    });
    test2(
      "report etl for all payee with selected period-commission",
      async ({ adminPage }) => {
        const page = adminPage.page;
        await page.goto("/settings/commissions-and-data-sync", {
          waitUntil: "networkidle",
        });
        await expandMenu(page, "Report ETL Run ETL for report");
        await page
          .getByTestId("ever-select")
          .locator("div")
          .filter({ hasText: "Choose report object" })
          .click({ timeout: 120000 });
        await page
          .getByTestId("ever-select")
          .getByText("Commission", { exact: true })
          .click();
        await page.getByPlaceholder("Select month").nth(1).click();
        await page.getByPlaceholder("Select month").nth(1).fill("01/06/2024");
        await page.getByPlaceholder("Select month").nth(1).press("Enter");
        await clickEnabledRunButton(adminPage);
        const skipAndRunButton = page.getByRole("button", {
          name: "Skip & Run",
        });
        await page.waitForTimeout(1000);
        if (await skipAndRunButton.isVisible()) {
          await skipAndRunButton.click();
        }
        await page.getByText("Running Report ETL...").click();
        await expect(
          page.getByText("ETL For Report Object Completed")
        ).toBeVisible({ timeout: 300000 });
      }
    );
    test2(
      "report etl for all payee with selected period-quota attainment",
      async ({ adminPage }, testInfo) => {
        const page = adminPage.page;
        testInfo.setTimeout(testInfo.timeout + 300000);
        await page.goto("/settings/commissions-and-data-sync", {
          waitUntil: "networkidle",
        });
        await expandMenu(page, "Report ETL Run ETL for report");
        await page
          .getByTestId("ever-select")
          .locator("div")
          .filter({ hasText: "Choose report object" })
          .click();
        await page
          .locator("div")
          .filter({ hasText: /^Quota Attainment$/ })
          .nth(1)
          .click();
        await page.getByPlaceholder("Select month").nth(1).click();
        await page.getByPlaceholder("Select month").nth(1).fill("01/06/2024");
        await page.getByPlaceholder("Select month").nth(1).press("Enter");
        await clickEnabledRunButton(adminPage);
        const skipAndRunButton = page.getByRole("button", {
          name: "Skip & Run",
        });
        await page.waitForTimeout(1000);
        if (await skipAndRunButton.isVisible()) {
          await skipAndRunButton.click();
        }
        await expect(
          page.getByText("ETL For Report Object Completed")
        ).toBeVisible({ timeout: 300000 });
      }
    );
    test2(
      "report etl for all payee with selected period- inter quota attainment",
      async ({ adminPage }) => {
        const page = adminPage.page;
        await page.goto("/settings/commissions-and-data-sync", {
          waitUntil: "networkidle",
        });
        await expandMenu(page, "Report ETL Run ETL for report");
        await page
          .getByTestId("ever-select")
          .locator("div")
          .filter({ hasText: "Choose report object" })
          .click({ timeout: 120000 });
        await page
          .locator("div")
          .filter({ hasText: /^Inter Quota Attainment$/ })
          .nth(1)
          .click();
        await page.getByPlaceholder("Select month").nth(1).click();
        await page.getByPlaceholder("Select month").nth(1).fill("01/06/2024");
        await page.getByPlaceholder("Select month").nth(1).press("Enter");
        await clickEnabledRunButton(adminPage);
        const skipAndRunButton = page.getByRole("button", {
          name: "Skip & Run",
        });
        await page.waitForTimeout(1000);
        if (await skipAndRunButton.isVisible()) {
          await skipAndRunButton.click();
        }
        await expect(
          page.getByText("ETL For Report Object Completed")
        ).toBeVisible({ timeout: 300000 });
      }
    );
    test2(
      "report etl for all payee with selected period- inter commission",
      async ({ adminPage }) => {
        const page = adminPage.page;
        await page.goto("/settings/commissions-and-data-sync", {
          waitUntil: "networkidle",
        });
        await expandMenu(page, "Report ETL Run ETL for report");
        await page
          .getByTestId("ever-select")
          .locator("div")
          .filter({ hasText: "Choose report object" })
          .click({ timeout: 120000 });
        await page
          .locator("div")
          .filter({ hasText: /^Inter Commission$/ })
          .nth(1)
          .click();
        await page.getByPlaceholder("Select month").nth(1).click();
        await page.getByPlaceholder("Select month").nth(1).fill("01/06/2024");
        await page.getByPlaceholder("Select month").nth(1).press("Enter");
        await clickEnabledRunButton(adminPage);
        const skipAndRunButton = page.getByRole("button", {
          name: "Skip & Run",
        });
        await page.waitForTimeout(1000);
        if (await skipAndRunButton.isVisible()) {
          await skipAndRunButton.click();
        }
        await expect(
          page.getByText("ETL For Report Object Completed")
        ).toBeVisible({ timeout: 300000 });
      }
    );
    test2(
      "report etl for all payee with selected period and all payees with all period- settlement",
      async ({ adminPage }) => {
        const page = adminPage.page;
        await page.goto("/settings/commissions-and-data-sync", {
          waitUntil: "networkidle",
        });
        await expandMenu(page, "Report ETL Run ETL for report");
        await page
          .getByTestId("ever-select")
          .locator("div")
          .filter({ hasText: "Choose report object" })
          .click({ timeout: 120000 });
        await page
          .locator("div")
          .filter({ hasText: /^Settlement$/ })
          .nth(1)
          .click();
        await page.getByPlaceholder("Select month").nth(1).click();
        await page.getByPlaceholder("Select month").nth(1).fill("01/06/2024");
        await page.getByPlaceholder("Select month").nth(1).press("Enter");
        await clickEnabledRunButton(adminPage);
        const skipAndRunButton = page.getByRole("button", {
          name: "Skip & Run",
        });
        await page.waitForTimeout(1000);
        if (await skipAndRunButton.isVisible()) {
          await skipAndRunButton.click();
        }
        await expect(
          page.getByText("ETL For Report Object Completed")
        ).toBeVisible({ timeout: 300000 });
        await page
          .getByTestId("ever-select")
          .locator("div")
          .filter({ hasText: "Choose report object" })
          .click();
        await page
          .locator("div")
          .filter({ hasText: /^Settlement$/ })
          .nth(1)
          .click();
        await page.getByRole("switch").click();
        await clickEnabledRunButton(adminPage);
        await expect(
          page.getByText("ETL For Report Object Completed")
        ).toBeVisible({ timeout: 300000 });
      }
    );

    test2(
      "report etl for selected payee with selected period - settlement",
      async ({ adminPage }) => {
        const page = adminPage.page;
        await page.goto("/settings/commissions-and-data-sync", {
          waitUntil: "networkidle",
        });
        await expandMenu(page, "Report ETL Run ETL for report");
        await page
          .getByTestId("ever-select")
          .locator("div")
          .filter({ hasText: "Choose report object" })
          .click({ timeout: 120000 });
        await page
          .locator("div")
          .filter({ hasText: /^Settlement$/ })
          .nth(1)
          .click();
        await page.getByLabel("Selected Payees").nth(1).check();
        await page
          .locator("div:nth-child(2) > .ant-select > .ant-select-selector")
          .click();
        await page
          .locator("div")
          .filter({ hasText: /^Admin test$/ })
          .nth(1)
          .click();
        await page
          .locator("span")
          .filter({ hasText: /^Selected Payees$/ })
          .nth(2)
          .click();
        await page.getByPlaceholder("Select month").nth(1).click();
        await page.getByPlaceholder("Select month").nth(1).fill("01/06/2024");
        await page.getByPlaceholder("Select month").nth(1).press("Enter");
        await clickEnabledRunButton(adminPage);
        const skipAndRunButton = page.getByRole("button", {
          name: "Skip & Run",
        });
        await page.waitForTimeout(1000);
        if (await skipAndRunButton.isVisible()) {
          await skipAndRunButton.click();
        }
        await expect(
          page.getByText("ETL For Report Object Completed")
        ).toBeVisible({ timeout: 300000 });
      }
    );
    test2(
      "report etl for selected payee with selected period - commission",
      async ({ adminPage }, testInfo) => {
        const page = adminPage.page;
        await page.goto("/settings/commissions-and-data-sync", {
          waitUntil: "networkidle",
        });
        testInfo.setTimeout(testInfo.timeout + 300000);
        await expandMenu(page, "Report ETL Run ETL for report");
        await page
          .getByTestId("ever-select")
          .locator("div")
          .filter({ hasText: "Choose report object" })
          .click({ timeout: 120000 });
        await page
          .getByTestId("ever-select")
          .getByText("Commission", { exact: true })
          .click();
        await page.getByLabel("Selected Payees").nth(1).check();
        await page
          .locator("div:nth-child(2) > .ant-select > .ant-select-selector")
          .click();
        await page
          .locator("div")
          .filter({ hasText: /^Admin test$/ })
          .nth(1)
          .click();
        await page
          .locator("span")
          .filter({ hasText: /^Selected Payees$/ })
          .nth(2)
          .click();
        await page.getByPlaceholder("Select month").nth(1).click();
        await page.getByPlaceholder("Select month").nth(1).fill("01/06/2024");
        await page.getByPlaceholder("Select month").nth(1).press("Enter");
        await clickEnabledRunButton(adminPage);
        const skipAndRunButton = page.getByRole("button", {
          name: "Skip & Run",
        });
        await page.waitForTimeout(1000);
        if (await skipAndRunButton.isVisible()) {
          await skipAndRunButton.click();
        }
        await expect(
          page.getByText("ETL For Report Object Completed")
        ).toBeVisible({ timeout: 300000 });
      }
    );
    test2(
      "report etl for selected payee with selected period - quota attainment",
      async ({ adminPage }, testInfo) => {
        const page = adminPage.page;
        await page.goto("/settings/commissions-and-data-sync", {
          waitUntil: "networkidle",
        });
        testInfo.setTimeout(testInfo.timeout + 300000);
        await expandMenu(page, "Report ETL Run ETL for report");
        await page
          .getByTestId("ever-select")
          .locator("div")
          .filter({ hasText: "Choose report object" })
          .click({ timeout: 120000 });
        await page
          .locator("div")
          .filter({ hasText: /^Quota Attainment$/ })
          .nth(1)
          .click();
        await page.getByLabel("Selected Payees").nth(1).check();
        await page
          .locator("div:nth-child(2) > .ant-select > .ant-select-selector")
          .click();
        await page
          .locator("div")
          .filter({ hasText: /^Admin test$/ })
          .nth(1)
          .click();
        await page
          .locator("span")
          .filter({ hasText: /^Selected Payees$/ })
          .nth(2)
          .click();
        await page.getByPlaceholder("Select month").nth(1).click();
        await page.getByPlaceholder("Select month").nth(1).fill("01/06/2024");
        await page.getByPlaceholder("Select month").nth(1).press("Enter");
        await clickEnabledRunButton(adminPage);
        const skipAndRunButton = page.getByRole("button", {
          name: "Skip & Run",
        });
        await page.waitForTimeout(1000);
        if (await skipAndRunButton.isVisible()) {
          await skipAndRunButton.click();
        }
        await expect(
          page.getByText("ETL For Report Object Completed")
        ).toBeVisible({ timeout: 300000 });
      }
    );
    test2(
      "report etl for selected payee with selected period - inter quota attainment",
      async ({ adminPage }, testInfo) => {
        const page = adminPage.page;
        await page.goto("/settings/commissions-and-data-sync", {
          waitUntil: "networkidle",
        });
        testInfo.setTimeout(testInfo.timeout + 300000);
        await expandMenu(page, "Report ETL Run ETL for report");
        await page
          .getByTestId("ever-select")
          .locator("div")
          .filter({ hasText: "Choose report object" })
          .click({ timeout: 120000 });
        await page
          .locator("div")
          .filter({ hasText: /^Inter Quota Attainment$/ })
          .nth(1)
          .click();
        await page.getByLabel("Selected Payees").nth(1).check();
        await page
          .locator("div:nth-child(2) > .ant-select > .ant-select-selector")
          .click();
        await page
          .locator("div")
          .filter({ hasText: /^Admin test$/ })
          .nth(1)
          .click();
        await page
          .locator("span")
          .filter({ hasText: /^Selected Payees$/ })
          .nth(2)
          .click();
        await page.getByPlaceholder("Select month").nth(1).click();
        await page.getByPlaceholder("Select month").nth(1).fill("01/06/2024");
        await page.getByPlaceholder("Select month").nth(1).press("Enter");
        await clickEnabledRunButton(adminPage);
        const skipAndRunButton = page.getByRole("button", {
          name: "Skip & Run",
        });
        await page.waitForTimeout(1000);
        if (await skipAndRunButton.isVisible()) {
          await skipAndRunButton.click();
        }
        await expect(
          page.getByText("ETL For Report Object Completed")
        ).toBeVisible({ timeout: 300000 });
      }
    );
    test2(
      "report etl for selected payee with selected period - inter commission",
      async ({ adminPage }, testInfo) => {
        const page = adminPage.page;
        await page.goto("/settings/commissions-and-data-sync", {
          waitUntil: "networkidle",
        });
        testInfo.setTimeout(testInfo.timeout + 300000);
        await expandMenu(page, "Report ETL Run ETL for report");
        await page
          .getByTestId("ever-select")
          .locator("div")
          .filter({ hasText: "Choose report object" })
          .click({ timeout: 120000 });
        await page
          .locator("div")
          .filter({ hasText: /^Inter Commission$/ })
          .nth(1)
          .click();
        await page.getByLabel("Selected Payees").nth(1).check();
        await page
          .locator("div:nth-child(2) > .ant-select > .ant-select-selector")
          .click();
        await page
          .locator("div")
          .filter({ hasText: /^Admin test$/ })
          .nth(1)
          .click();
        await page
          .locator("span")
          .filter({ hasText: /^Selected Payees$/ })
          .nth(2)
          .click();
        await page.getByPlaceholder("Select month").nth(1).click();
        await page.getByPlaceholder("Select month").nth(1).fill("01/06/2024");
        await page.getByPlaceholder("Select month").nth(1).press("Enter");
        await clickEnabledRunButton(adminPage);
        const skipAndRunButton = page.getByRole("button", {
          name: "Skip & Run",
        });
        await page.waitForTimeout(1000);
        if (await skipAndRunButton.isVisible()) {
          await skipAndRunButton.click();
        }
        await expect(
          page.getByText("ETL For Report Object Completed")
        ).toBeVisible({ timeout: 300000 });
      }
    );
    test2(
      "local adjustment to data and check update data button validation",
      async ({ adminPage }, testInfo) => {
        const page = adminPage.page;
        await page.goto("/databook", {
          waitUntil: "networkidle",
        });
        testInfo.setTimeout(testInfo.timeout + 300000);
        await page.getByRole("link", { name: "Book2" }).click();
        await expect(page.getByText("1,000").first()).toBeVisible();
        await page
          .getByText(
            "There are some updates in this datasheet or one of the dependencies of this datasheet. Click on the 'Update Data' button to view the latest data."
          )
          .click();
        await page.getByText("sheet_book_2").click();
        await page
          .getByRole("button", { name: "Generate Datasheet", exact: true })
          .click();
        await page.waitForTimeout(40000);
        await page.getByText("book_sheet2").click();
        await expect(page.getByText("10,000").first()).toBeVisible();
        await expect(page.getByText("1,000").first()).toBeVisible();
      }
    );

    test2(
      "locked payouts updated value reflected validation",
      async ({ adminPage }, testInfo) => {
        const page = adminPage.page;
        testInfo.setTimeout(testInfo.timeout + 300000);
        const key = "commission-view-period";
        const value = "June-2024";
        await page.evaluate(
          ({ key, value }) => {
            // Set the localStorage value for the current page
            localStorage.setItem(key, value);
          },
          { key, value }
        );
        await page.goto("/commissions", {
          waitUntil: "networkidle",
        });
        await expect(page.getByText("1,000")).toBeVisible();
        await page
          .getByRole("row", { name: "Name" })
          .getByRole("columnheader")
          .first()
          .click();
        await page.waitForTimeout(1000);
        await page
          .locator("div")
          .filter({ hasText: /^Unlock$/ })
          .click();
        // await page.waitForTimeout(2000);
        await page.getByRole("button", { name: "Unlock" }).click();
        // await page.waitForTimeout(2000);
        await page.goto("/settings/commissions-and-data-sync", {
          waitUntil: "networkidle",
        });
        await expandMenu(page, "Calculate Commissions Run");
        await page.getByPlaceholder("Select month").click();
        const currentYear = new Date().getFullYear();
        const startYearDiff = currentYear - 2024;
        for (let i = 0; i < startYearDiff; i++) {
          await page.locator("button").first().click();
        }
        await page.getByText("Jun").last().click();
        await page.waitForTimeout(2000);
        await clickEnabledRunButton(adminPage);
        const skipAndRunButton = page.getByRole("button", {
          name: "Skip & Run",
        });
        await skipAndRunButton.click();
        // await page.waitForTimeout(90000);
        await expect(
          page.getByText("Commission Calculations Completed")
        ).toBeVisible({ timeout: 300000 });
        await page.evaluate(
          ({ key, value }) => {
            // Set the localStorage value for the current page
            localStorage.setItem(key, value);
          },
          { key, value }
        );
        await page.goto("/commissions", {
          waitUntil: "networkidle",
        });
        await expect(page.getByText("10,000")).toBeVisible();
      }
    );
  }
);
