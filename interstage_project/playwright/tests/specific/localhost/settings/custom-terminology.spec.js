/* eslint-disable playwright/no-wait-for-timeout */
const {
  BYOTFixtures: { test, expect },
} = require("../../../fixtures");

test.use({ viewport: { width: 1620, height: 920 } });

test.describe(
  "Custom Terminology",
  { tag: ["@regression","@byot","@repconnect-2"] },
  () => {
    test.beforeEach(async ({ adminPage }) => {
      const page = adminPage.page;
      await page.goto("/settings/custom-terminology", {
        waitUntil: "networkidle",
      });
    });

    test("User should be able to add custom terminology and edit terms", async ({
      adminPage,
    }) => {
      const page = adminPage.page;
      await page.getByTestId("pt-Quota").click();
      await page.getByLabel("Input Editor").fill("Target");
      await page.getByTestId("pt-Quotas").click();
      await page.getByLabel("Input Editor").fill("Targets");
      await page.keyboard.press("Tab");
      await page.getByRole("button", { name: "Update" }).click();
      // await page.waitForNavigation({ waitUntil: "networkidle" });
      await page.waitForResponse(
        (response) =>
          response.url().includes("spm/localization/custom_terminologies") &&
          response.status() === 200
      );

      const quotaValue = await page.getByTestId("pt-Quota").innerText();
      expect(quotaValue).toBe("Target");
      const quotasValue = await page.getByTestId("pt-Quotas").innerText();
      expect(quotasValue).toBe("Targets");
      await page.getByTestId("pt-Quota").click();
      await page.getByLabel("Input Editor").fill("");
      await page.getByTestId("pt-Quotas").click();
      await page.getByLabel("Input Editor").fill("");
      await page.keyboard.press("Tab");
      await page.getByRole("button", { name: "Update" }).click();
      // await page.waitForNavigation({ waitUntil: "networkidle" });
      await page.waitForResponse(
        (response) =>
          response.url().includes("spm/localization/custom_terminologies") &&
          response.status() === 200
      );
    });

    test("User should be able to get new terms for 'commission','commissions'", async ({
      adminPage,
    }) => {
      const page = adminPage.page;
      await page.getByTestId("pt-Commission").click();
      await page.getByLabel("Input Editor").fill("Compensation");
      await page.getByTestId("pt-Commissions").click();
      await page.getByLabel("Input Editor").fill("Compensations");
      await page.keyboard.press("Tab");
      await page.getByRole("button", { name: "Update" }).click();
      // await page.waitForNavigation({ waitUntil: "networkidle" });
      await page.waitForResponse(
        (response) =>
          response.url().includes("spm/localization/custom_terminologies") &&
          response.status() === 200
      );
      await page.goto("/commissions", {
        waitUntil: "networkidle",
      });
      await expect(
        await page
          .locator("a[href='/commissions'] span")
          .filter({ hasText: /^Compensation$/ })
          .first()
      ).toBeVisible();
      await page.goto("/settings", {
        waitUntil: "networkidle",
      });
      await page
        .getByRole("link", { name: "Compensation & Data Sync" })
        .click();
      await expect(
        await page.getByRole("button", { name: "Calculate Compensations" })
      ).toBeTruthy();
      await page.goto("/settings/custom-terminology", {
        waitUntil: "networkidle",
      });
      await page.getByTestId("pt-Commission").click();
      await page.getByLabel("Input Editor").fill("");
      await page.getByTestId("pt-Commissions").click();
      await page.getByLabel("Input Editor").fill("");
      await page.keyboard.press("Tab");
      await page.getByRole("button", { name: "Update" }).click();
      // await page.waitForNavigation({ waitUntil: "networkidle" });
      await page.waitForResponse(
        (response) =>
          response.url().includes("spm/localization/custom_terminologies") &&
          response.status() === 200
      );
      await page.goto("/plans", {
        waitUntil: "networkidle",
      });
      await expect(
        await page
          .locator("div")
          .filter({ hasText: /^Commission Plans$/ })
          .nth(2)
      ).toBeVisible();
      await page.goto("/commissions", {
        waitUntil: "networkidle",
      });
      await expect(
        await page
          .locator("a[href='/commissions'] span")
          .filter({ hasText: /^Commission$/ })
          .first()
      ).toBeVisible();
      await page.goto("/settings", {
        waitUntil: "networkidle",
      });
      await page.getByRole("link", { name: "Commission & Data Sync" }).click();
      expect(
        await page.getByRole("button", { name: "Calculate Commissions" })
      ).toBeTruthy();
    });

    test("User should be able to get new terms for 'arrear','arrears'", async ({
      adminPage,
    }) => {
      const page = adminPage.page;
      await page.getByTestId("pt-Arrears").click();
      await page.getByLabel("Input Editor").fill("Claims");
      await page.getByTestId("pt-Arrear").click();
      await page.getByLabel("Input Editor").fill("Claim");
      await page.keyboard.press("Tab");
      await page.getByRole("button", { name: "Update" }).click();
      // await page.waitForNavigation({ waitUntil: "networkidle" });
      await page.waitForResponse(
        (response) =>
          response.url().includes("spm/localization/custom_terminologies") &&
          response.status() === 200
      );
      await page.goto("/commissions", {
        waitUntil: "networkidle",
      });
      await page.getByRole('button').getByText('Claims View').click();
      await page.goto("/settings/custom-terminology", {
        waitUntil: "networkidle",
      });
      await page.getByTestId("pt-Arrear").click();
      await page.getByLabel("Input Editor").fill("");
      await page.getByTestId("pt-Arrears").click();
      await page.getByLabel("Input Editor").fill("");
      await page.keyboard.press("Tab");
      await page.getByRole("button", { name: "Update" }).click();
      // await page.waitForNavigation({ waitUntil: "networkidle" });
      await page.waitForResponse(
        (response) =>
          response.url().includes("spm/localization/custom_terminologies") &&
          response.status() === 200
      );
      await page.goto("/commissions", {
        waitUntil: "networkidle",
      });
      await page.getByRole('button').getByText('Arrears View').click();
    });

    test("User should be able to get new terms for 'adjustment','adjustments'", async ({
      adminPage,
    }) => {
      const page = adminPage.page;
      await page.getByTestId("pt-Adjustment").click();
      await page.getByLabel("Input Editor").fill("Modification");
      await page.getByTestId("pt-Adjustments").click();
      await page.getByLabel("Input Editor").fill("Modifications");
      await page.keyboard.press("Tab");
      await page.getByRole("button", { name: "Update" }).click();
      await page.waitForResponse(
        (response) =>
          response.url().includes("spm/localization/custom_terminologies") &&
          response.status() === 200
      );
      await page.goto("/settings", {
        waitUntil: "networkidle",
      });
      await page.getByRole('link', { name: 'Manage your commission and draw modification process efficiently.'}).click();
      await page.waitForTimeout(3000);
      await expect(
        await page.getByText("No Modifications found")
      ).toBeVisible();
      await page.getByRole("button", { name: "Add Modification" }).click();
      await page.getByRole("menuitem", { name: "Commission" }).click();
      await expect(await page.getByText("Modification Type*")).toBeVisible();
      await page.goto("/approvals/commission-adjustments?status=all", {
        waitUntil: "networkidle",
      });
      await expect(
        await page
          .getByTestId("login-indicator")
          .getByText("Commission Modifications")
      ).toBeVisible();
      await page
        .getByTestId("login-indicator")
        .getByText("Commission Modifications")
        .hover();
      await expect(
        await page.getByRole("menuitem", { name: "Commission Modifications" })
      ).toBeVisible();
      await page.goto("/settings/custom-terminology", {
        waitUntil: "networkidle",
      });
      await page.getByTestId("pt-Adjustment").click();
      await page.getByLabel("Input Editor").fill("");
      await page.getByTestId("pt-Adjustments").click();
      await page.getByLabel("Input Editor").fill("");
      await page.keyboard.press("Tab");
      await page.getByRole("button", { name: "Update" }).click();
      // await page.waitForNavigation({ waitUntil: "networkidle" });
      await page.waitForResponse(
        (response) =>
          response.url().includes("spm/localization/custom_terminologies") &&
          response.status() === 200
      );
      await page.goto("/settings", {
        waitUntil: "networkidle",
      });
      await page.getByRole('link', { name: 'Manage your commission and draw adjustment process efficiently.'}).click();
      await page.waitForTimeout(3000);
      await expect(await page.getByText("No Adjustments found")).toBeVisible();
      await page.getByRole("button", { name: "Add Adjustment" }).click();
      await page.getByRole("menuitem", { name: "Commission" }).click();
      await expect(await page.getByText("Adjustment Type*")).toBeVisible();
      // await page.getByLabel("Close").getByRole("button").click();
      await page.keyboard.press("Escape");
      await page.goto("/approvals/commission-adjustments?status=all", {
        waitUntil: "networkidle",
      });
      await expect(
        await page
          .getByTestId("login-indicator")
          .getByText("Commission Adjustments")
      ).toBeVisible();
      await page
        .getByTestId("login-indicator")
        .getByText("Commission Adjustments")
        .hover();
      await page
        .getByTestId("login-indicator")
        .getByText("Commission Adjustments")
        .click();
      await expect(
        await page.getByRole("menuitem", { name: "Commission Adjustments" })
      ).toBeVisible();
    });

    test("User should be able to get new terms for 'payout' & 'payouts'", async ({
      adminPage,
    }) => {
      const page = adminPage.page;
      await page.getByTestId("pt-Payout").click();
      await page.getByLabel("Input Editor").fill("Cashout");
      await page.getByTestId("pt-Payouts").click();
      await page.getByLabel("Input Editor").fill("Cashouts");
      await page.keyboard.press("Tab");
      await page.getByRole("button", { name: "Update" }).click();
      // await page.waitForNavigation({ waitUntil: "networkidle" });
      await page.waitForResponse(
        (response) =>
          response.url().includes("spm/localization/custom_terminologies") &&
          response.status() === 200
      );
      await page.goto("/commissions", {
        waitUntil: "networkidle",
      });
      await expect(
        await page.getByRole("button", { name: "Cashouts View" })
      ).toBeVisible();
      await page.goto("/approvals/payouts", {
        waitUntil: "networkidle",
      });
      await page.locator(".ant-dropdown-trigger").getByText("Cashouts").hover();
      await expect(
        await page.getByRole("menuitem", { name: "Cashouts" })
      ).toBeVisible();
      await page.goto("/settings/custom-terminology", {
        waitUntil: "networkidle",
      });
      await page.getByTestId("pt-Payout").click();
      await page.getByLabel("Input Editor").fill("");
      await page.getByTestId("pt-Payouts").click();
      await page.getByLabel("Input Editor").fill("");
      await page.keyboard.press("Tab");
      await page.getByRole("button", { name: "Update" }).click();
      // await page.waitForNavigation({ waitUntil: "networkidle" });
      await page.waitForResponse(
        (response) =>
          response.url().includes("spm/localization/custom_terminologies") &&
          response.status() === 200
      );
      await page.goto("/commissions", {
        waitUntil: "networkidle",
      });
      await expect(
        await page.getByRole("button", { name: "Payouts View" })
      ).toBeVisible();
      await page.goto("/approvals/payouts", {
        waitUntil: "networkidle",
      });
      await page.locator(".ant-dropdown-trigger").getByText("Payouts").hover();
      await expect(
        await page.getByRole("menuitem", { name: "Payouts" })
      ).toBeVisible();
    });

    test("User should be able to get new terms for 'quota','quotas'", async ({
      adminPage,
    }) => {
      const page = adminPage.page;
      await page.getByTestId("pt-Quota").click();
      await page.getByLabel("Input Editor").fill("Target");
      await page.getByTestId("pt-Quotas").click();
      await page.getByLabel("Input Editor").fill("Targets");
      await page.keyboard.press("Tab");
      await page.getByRole("button", { name: "Update" }).click();
      // await page.waitForNavigation({ waitUntil: "networkidle" });
      await page.waitForResponse(
        (response) =>
          response.url().includes("spm/localization/custom_terminologies") &&
          response.status() === 200
      );
      await page.goto("/quotas", {
        waitUntil: "networkidle",
      });
      await page.getByTitle("Admin test").first().click();
      await page.getByRole("button", { name: "Bulk Target Upload" }).click();
      await expect(
        await page.getByText("Start setting up targets for")
      ).toBeVisible();
      await page.goto("/settings/custom-terminology", {
        waitUntil: "networkidle",
      });
      await page.getByTestId("pt-Quota").click();
      await page.getByLabel("Input Editor").fill("");
      await page.getByTestId("pt-Quotas").click();
      await page.getByLabel("Input Editor").fill("");
      await page.keyboard.press("Tab");
      await page.getByRole("button", { name: "Update" }).click();
      // await page.waitForNavigation({ waitUntil: "networkidle" });
      await page.waitForResponse(
        (response) =>
          response.url().includes("spm/localization/custom_terminologies") &&
          response.status() === 200
      );
      await page.goto("/quotas", {
        waitUntil: "networkidle",
      });
      await page.getByTitle("Admin test").first().click();
      await page.getByRole("button", { name: "Bulk Quota Upload" }).click();
      await expect(
        await page.getByText("Start setting up quotas for")
      ).toBeVisible();
    });

    test("User should be able to get new terms for 'quota erosion'", async ({
      adminPage,
    }) => {
      const page = adminPage.page;

      await page.getByTestId("pt-Quota Erosion").click();
      await page.getByLabel("Input Editor").fill("Target completion");
      await page.getByLabel("Input Editor").press("Enter");
      await page.getByRole("button", { name: "Update" }).click();
      // await page.waitForNavigation({ waitUntil: "networkidle" });
      await page.waitForResponse(
        (response) =>
          response.url().includes("spm/localization/custom_terminologies") &&
          response.status() === 200
      );
      await page.goto("/plans", {
        waitUntil: "networkidle",
      });
      await page.getByText("Quota Plan").click();
      await page.getByText("Quota comp").first().click();
      await page.getByRole("button", { name: "Simulate" }).click();
      await page.getByRole("button", { name: "Run" }).click();
      await expect(await page.getByText("Evaluating Quota comp")).toBeVisible();
      await expect(await page.getByText("Target Completion:")).toBeVisible();
      await page.getByRole("button", { name: "Exit Canvas" }).click();
      await page.goto(
        "/statements/eyJwYXllZUVtYWlsSWQiOiJwYXllZS45MDA5QGJ5b3QtdGVzdC5jb20iLCJwc2QiOiIyMDI0LTAzLTAxIiwicGVkIjoiMjAyNC0wMy0zMSJ9",
        {
          waitUntil: "networkidle",
        }
      );
      await page.getByText("Commission Summary").click();
      await page.getByText("Earned Commissions").click();
      await page.getByText("Quota Plan").click();
      await page.getByRole("button", { name: "Quota comp" }).click();
      await expect(
        await page.getByText("Target Completion").first()
      ).toBeVisible();
      await page.getByLabel("Close").click();
      await page.goto("/settings/custom-terminology", {
        waitUntil: "networkidle",
      });
      await page.getByTestId("pt-Quota Erosion").click();
      await page.getByLabel("Input Editor").fill("");
      await page.getByLabel("Input Editor").press("Enter");
      await page.getByRole("button", { name: "Update" }).click();
      // await page.waitForNavigation({ waitUntil: "networkidle" });
      await page.waitForResponse(
        (response) =>
          response.url().includes("spm/localization/custom_terminologies") &&
          response.status() === 200
      );
    });

    test("User should be able to get new terms for 'deferred'", async ({
      adminPage,
    }) => {
      const page = adminPage.page;

      await page.getByTestId("pt-Deferred").click();
      await page.getByLabel("Input Editor").click();
      await page.getByLabel("Input Editor").fill("Postpone");
      await page.getByLabel("Input Editor").press("Enter");
      await page.getByRole("button", { name: "Update" }).click();
      // await page.waitForNavigation({ waitUntil: "networkidle" });
      await page.waitForResponse(
        (response) =>
          response.url().includes("spm/localization/custom_terminologies") &&
          response.status() === 200
      );
      await page.goto(
        "/statements/eyJwYXllZUVtYWlsSWQiOiJwYXllZS45MDA5QGJ5b3QtdGVzdC5jb20iLCJwc2QiOiIyMDI0LTAzLTAxIiwicGVkIjoiMjAyNC0wMy0zMSJ9",
        {
          waitUntil: "networkidle",
        }
      );
      await page.getByText("Commission Summary").click();
      await expect(await page.getByText("Postpone Commissions")).toBeVisible();
      await page.goto("/settings/custom-terminology", {
        waitUntil: "networkidle",
      });
      await page.getByTestId("pt-Deferred").click();
      await page.getByLabel("Input Editor").fill("");
      await page.getByLabel("Input Editor").press("Enter");
      await page.getByRole("button", { name: "Update" }).click();
      // await page.waitForNavigation({ waitUntil: "networkidle" });
      await page.waitForResponse(
        (response) =>
          response.url().includes("spm/localization/custom_terminologies") &&
          response.status() === 200
      );
    });

    test("User should be able to get new terms for 'earned'", async ({
      adminPage,
    }) => {
      const page = adminPage.page;

      await page.getByTestId("pt-Earned").click();
      await page.getByLabel("Input Editor").fill("Claimed");
      await page.getByLabel("Input Editor").press("Enter");
      await page.getByRole("button", { name: "Update" }).click();
      // await page.waitForNavigation({ waitUntil: "networkidle" });
      await page.waitForResponse(
        (response) =>
          response.url().includes("spm/localization/custom_terminologies") &&
          response.status() === 200
      );
      await page.goto(
        "/statements/eyJwYXllZUVtYWlsSWQiOiJwYXllZS45MDA5QGJ5b3QtdGVzdC5jb20iLCJwc2QiOiIyMDI0LTAzLTAxIiwicGVkIjoiMjAyNC0wMy0zMSJ9",
        {
          waitUntil: "networkidle",
        }
      );
      await page.getByText("Commission Summary").click();
      await expect(await page.getByText("Claimed Commissions")).toBeVisible();
      await page.getByText("Claimed Commissions").click();
      await page.getByText("Data Plan").click();
      await page.getByRole("button", { name: "Conditional" }).click();
      await expect(
        await page.getByText("Claimed Commissions").nth(1)
      ).toBeVisible();
      await page.getByLabel("Close").click();
      await page.goto("/settings/custom-terminology", {
        waitUntil: "networkidle",
      });
      await page.getByTestId("pt-Earned").click();
      await page.getByLabel("Input Editor").fill("");
      await page.getByLabel("Input Editor").press("Enter");
      await page.getByRole("button", { name: "Update" }).click();
      // await page.waitForNavigation({ waitUntil: "networkidle" });
      await page.waitForResponse(
        (response) =>
          response.url().includes("spm/localization/custom_terminologies") &&
          response.status() === 200
      );
    });

    test("User should be able to get new terms for 'on target variable pay'", async ({
      adminPage,
    }) => {
      const page = adminPage.page;

      await page.getByTestId("pt-On-Target Variable Pay").click();
      await page.getByLabel("Input Editor").fill("On completion changed pay");
      await page.getByLabel("Input Editor").press("Enter");
      await page.getByRole("button", { name: "Update" }).click();
      // await page.waitForNavigation({ waitUntil: "networkidle" });
      await page.waitForResponse(
        (response) =>
          response.url().includes("spm/localization/custom_terminologies") &&
          response.status() === 200
      );
      await page.goto("/users", { waitUntil: "networkidle" });
      await page.getByRole("button", { name: "Payee test" }).first().click();
      await page.getByRole("button", { name: "Start Date: Jan 01, 2024" }).click();
      await expect(
        await page.getByText("On Completion Changed Pay")
      ).toBeVisible();
      await page.goto("/settings/custom-terminology", {
        waitUntil: "networkidle",
      });
      await page.getByText("On Completion Changed Pay").click();
      await page.getByLabel("Input Editor").fill("");
      await page.getByLabel("Input Editor").press("Enter");
      await page.getByRole("button", { name: "Update" }).click();
      // await page.waitForNavigation({ waitUntil: "networkidle" });
      await page.waitForResponse(
        (response) =>
          response.url().includes("spm/localization/custom_terminologies") &&
          response.status() === 200
      );
    });
  }
);
