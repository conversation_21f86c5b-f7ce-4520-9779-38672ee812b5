import HRISPage from "../../../../../test-objects/hris-objects";
const UserPage = require("../../../../../test-objects/user-objects");

const {
  hrisFixtures: { test, expect },
} = require("../../../../fixtures");

test.beforeEach(async ({ adminPage }, testInfo) => {
  testInfo.setTimeout(testInfo.timeout + 300000);
});

test.describe(
  "HRIS null entry",
  { tag: ["@hris", "@regression", "@adminchamp-2"] },
  () => {
    test("verify the updated data displayed under HRIS", async ({
      adminPage,
      request,
    }, testInfo) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T15335" },
        {
          type: "Description",
          description:
            " Updated Data should be displayed under review updates screen",
        },
        {
          type: "Precondition",
          description: "Data should be impored in HRIS config sheet",
        },
        {
          type: "Expected Behaviour",
          description:
            "User should not able to see the payees under HRIS review screen",
        }
      );
      const hrisPage = new HRISPage(adminPage.page);
      await hrisPage.navigateToHRIS();
      await hrisPage.reviewUpdatesScreen();
      await hrisPage.loader();
      await hrisPage.newUsertab();
      await hrisPage.loader();
      await hrisPage.updatedUser("anthony a");
    });

    test("verify the ignore datas displayed under HRIS", async ({
      adminPage,
      request,
    }, testInfo) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T15347, INTER-T15348" },
        {
          type: "Description",
          description:
            "User should ignore the data displayed under HRIS review updates screen",
        },
        {
          type: "Precondition",
          description: "Data should be imported in HRIS config sheet",
        },
        {
          type: "Expected Behaviour",
          description:
            "User should able to see the payees under the HRIS under processed/ignored screen",
        }
      );
      testInfo.setTimeout(testInfo.timeout + 180000);
      const hrisPage = new HRISPage(adminPage.page);
      await hrisPage.navigateToHRIS();
      await hrisPage.reviewUpdatesScreen();
      await hrisPage.loader();
      await hrisPage.newUsertab();
      await hrisPage.loader();
      await hrisPage.filterUser("First Name", "Contains", "daphnee");
      await hrisPage.loader();
      await hrisPage.checkUser();
      await hrisPage.ignoreRecord(1);
      await hrisPage.reviewIgnoredScreen();
      await hrisPage.newUsertab();
      await hrisPage.loader();
      await hrisPage.navigateToHRIS();
      await hrisPage.reviewIgnoredScreen();
      await hrisPage.loader();
      await hrisPage.newUsertab();
      await hrisPage.loader();
      await hrisPage.verifyIgnoredRecords();
    });

    test("verify the data should be displayed as expected under group view", async ({
      adminPage,
      request,
    }, testInfo) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T15336" },
        {
          type: "Description",
          description: "User should see the expected data under group view",
        },
        {
          type: "Precondition",
          description: "Data should be imported in HRIS config sheet",
        },
        {
          type: "Expected Behaviour",
          description:
            "User should able to see the expected data under group view",
        }
      );
      const hrisPage = new HRISPage(adminPage.page);
      await hrisPage.navigateToHRIS();
      await hrisPage.reviewUpdatesScreen();
      await hrisPage.loader();
      await hrisPage.existingUserUpdate();
      await hrisPage.updatedUser("vennila valluvar");
      await hrisPage.groupViewdropdown("Dec 10");
      await hrisPage.loader();
      var result = [];
      result = await hrisPage.groupDataheaderValue();
      console.log(result);
      expect(result[0]).toBe("User Field");
      expect(result[1]).toBe("Everstage Value");
      expect(result[2]).toBe("HRIS Value");
      const num = await hrisPage.groupRowCount();
      expect(num).toBe(3);
    });

    test("verify the data should be displayed as expected under non-group view", async ({
      adminPage,
      request,
    }, testInfo) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T15336" },
        {
          type: "Description",
          description: "User should see the expected data under non-group view",
        },
        {
          type: "Precondition",
          description: "Data should be imported in HRIS config sheet",
        },
        {
          type: "Expected Behaviour",
          description:
            "User should able to see the expected data under no-group view",
        }
      );
      const hrisPage = new HRISPage(adminPage.page);
      await hrisPage.navigateToHRIS();
      await hrisPage.reviewUpdatesScreen();
      await hrisPage.loader();
      await hrisPage.newUsertab();
      await hrisPage.updatedUser("simon s");
      await hrisPage.groupByUserToggle();
      await hrisPage.nongroupView("<EMAIL>");
      await hrisPage.groupByUserToggle();
    });

    test("verify the user is able to import empty/null data", async ({
      adminPage,
      request,
    }, testInfo) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T15343, INTER-T15344" },
        {
          type: "Description",
          description: "User should see the expected data under mappayee",
        },
        {
          type: "Precondition",
          description: "Data should be imported in HRIS config sheet",
        },
        {
          type: "Expected Behaviour",
          description:
            "User should able to see the expected data under mappayee",
        }
      );
      const hrisPage = new HRISPage(adminPage.page);
      const userPage = new UserPage(adminPage.page);
      const retryIntervals = [2000, 5000]; // in milliseconds
      let success = false;
      let lastError;

      for (const interval of retryIntervals) {
        try {
          // Start fresh or reset state if needed before retrying
          await userPage.navigateToUser();
          await userPage.fillSearch("<EMAIL>");
          await userPage.noUserFound();
          await hrisPage.navigateToHRIS();
          await hrisPage.reviewUpdatesScreen();
          await hrisPage.loader();
          await hrisPage.newUsertab();
          await hrisPage.filterUser("First Name", "Contains", "anthony");
          await hrisPage.checkUser();
          await hrisPage.importeRecord("1");
          await userPage.navigateToUser();
          await userPage.fillSearch("<EMAIL>");
          await userPage.mapPayee("<EMAIL>");
          await userPage.verifyrecentData("Jan 13");
          const email = await userPage.verifyPayrollValues("Employee ID");
          expect(email).toBe("<EMAIL>");
          const result1 = await userPage.verifyPayrollValues("Designation");
          expect(result1).toBe("");
          success = true;
          break; // if all steps pass
        } catch (error) {
          lastError = error;
          console.warn(
            `Retry failed. Waiting ${interval}ms before next attempt...`
          );
          await new Promise((res) => setTimeout(res, interval));
        }
      }
      if (!success) {
        throw lastError || new Error("Retry failed after all intervals");
      }
    });

    test("verify the proceesed data should be displayed as expected under processed category", async ({
      adminPage,
      request,
    }, testInfo) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T15348" },
        {
          type: "Description",
          description:
            "User should see the expected data under processed category",
        },
        {
          type: "Precondition",
          description: "Data should be imported in HRIS config sheet",
        },
        {
          type: "Expected Behaviour",
          description:
            "User should able to see the expected data under processed category",
        }
      );
      const hrisPage = new HRISPage(adminPage.page);
      await hrisPage.navigateToHRIS();
      await hrisPage.reviewIgnoredScreen();
      await hrisPage.loader();
      await hrisPage.filterUser("First Name", "Contains", "vennila");
      await hrisPage.loader();
      await hrisPage.processrecords();
    });

    test("verify the user is able to import override the empty/null data while import", async ({
      adminPage,
      request,
    }, testInfo) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T15344" },
        {
          type: "Description",
          description: "User should see the expected data under mappayee",
        },
        {
          type: "Precondition",
          description: "Data should be imported in HRIS config sheet",
        },
        {
          type: "Expected Behaviour",
          description:
            "User should able to see the expected data under mappayee",
        }
      );
      const hrisPage = new HRISPage(adminPage.page);
      const userPage = new UserPage(adminPage.page);
      await userPage.navigateToUser();
      await userPage.fillSearch("<EMAIL>");
      await userPage.mapPayee("<EMAIL>");
      await userPage.verifyrecentData("Jan 14");
      const email = await userPage.verifyPayrollValues("Employee ID");
      expect(email).toBe("<EMAIL>");
      const result1 = await userPage.verifyPayrollValues("Designation");
      expect(result1).toBe("");
      await hrisPage.navigateToHRIS();
      await hrisPage.reviewUpdatesScreen();
      await hrisPage.loader();
      await hrisPage.groupByUserToggle();
      await hrisPage.loader();
      await hrisPage.filterUser("Designation", "Contains", "Admin");
      await hrisPage.loader();
      await hrisPage.checkNonGroup();
      await hrisPage.existingimporteRecord("1");
      await userPage.navigateToUser();
      await userPage.fillSearch("<EMAIL>");
      await userPage.mapPayee("<EMAIL>");
      await userPage.verifyrecentData("Feb 16");
      const email2 = await userPage.verifyPayrollValues("Employee ID");
      expect(email2).toBe("<EMAIL>");
      const result2 = await userPage.verifyPayrollValues("Designation");
      expect(result2).toBe("Admin");
    });
  }
);
