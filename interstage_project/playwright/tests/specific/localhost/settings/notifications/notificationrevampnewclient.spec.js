import Notification from "../../../../../test-objects/Notifications-object";
const {
  enrichFixtures: { test, expect },
} = require("../../../../fixtures");

test.describe(
  "Notification Revamp for new client",
  { tag: ["@notifications", "@regression", "@repconnect-2"] },
  () => {
    test(
      "UI Validation",
      {
        annotation: [
          {
            type: "Test ID",
            description: "INTER-T16980,INTER-T16995,INTER-T17020",
          },
          {
            type: "Description",
            description:
              "Check whether the 'Enable user notification' Toggle should be OFF for the client and No manager should be displayed in Reporting Manager dropdown",
          },
          {
            type: "Precondition",
            description:
              "User should have Manage account Notification permissionturned on",
          },
          {
            type: "Expected Behaviour",
            description:
              "Enable user notification toglled should be OFF and Reporting Manager dropdown should not display any value",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const notification = new Notification(page);
        await page.goto("/settings/notifications", {
          waitUntil: "networkidle",
        });
        await page
          .locator('button:left-of(:text("Enable user Notifications"))')
          .waitFor({ state: "visible" });
        expect(
          await page
            .locator('button:left-of(:text("Enable user Notifications"))')
            .getAttribute("aria-checked")
        ).toBe("false");
        await notification.openReportingManagerDropdown();
        await expect(page.locator(".ant-select-item-empty")).toBeVisible();
      }
    );

    test(
      "Validating Notification Page when Enable User Notificaitons toggle is off",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T16983" },
          {
            type: "Description",
            description:
              "Validating whether all buttons in Notification pages is disabled when enable User Notification toggle is OFF",
          },
          { type: "Precondition", description: "Users" },
          {
            type: "Expected Behaviour",
            description:
              "All buttons in the notification page should be disabled when Enable User Notification toggle is OFF",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const notification = new Notification(page);
        await page.goto("/settings/notifications", {
          waitUntil: "networkidle",
        });
        const status = await notification.getButtonStatus(false);
        if (status === false) {
          console.log(
            "Buttons are disabled when Enable User Notification toggle is OFF"
          );
        } else {
          console.log(
            "one or More Buttons are not disabled when Enable User Notification toggle is OFF"
          );
        }
        expect(status).toBeFalsy();
      }
    );
  }
);
