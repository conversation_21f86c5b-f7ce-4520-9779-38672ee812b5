const {
  localCloneFixtures: { test, expect },
} = require("../../../../fixtures");

test.beforeEach(async ({ adminPage }, testInfo) => {
  // Test ID: INTER-T12982
  // Description: User should navigate to report enrichment screen
  // Precondition: User should logged in as super admin / power admin
  // Expected behavior: User should navigate to report enrichment screen
  const page = adminPage.page;
  await page.waitForTimeout(1000);
  testInfo.setTimeout(testInfo.timeout + 10000);
  await page.goto("/settings/report-enrichment", { waitUntil: "networkidle" });
  await expect(page.getByText("Settings/Report Enrichment")).toHaveCount(1);
});

test.describe(
  "Report Enrichment - Filter",
  { tag: ["@reportenrichment", "@regression", "@primelogic-5"] },
  () => {
    test("Filter single commission plan @enrichment @filter @smoke", async ({
      adminPage,
    }) => {
      // Test ID: INTER-T12966, INTER-T12966, INTER-T12980
      // Description: User should filter with the single commisison plan
      // Precondition: Commission plan should be already published
      // Expected behavior: User should see 4 rows of data after the single commisison plan has been filtered
      const page = adminPage.page;
      await page.getByRole("button", { name: "Filters" }).click();
      await expect(page.getByLabel("Filters").getByText("Filters")).toHaveCount(
        1
      );
      await selectdata(page, 0, "Tier Plan_Copy");
      await page.getByRole("button", { name: "Apply" }).click();
      // Check whether the user is able to see 4 rows of data
      await expect(
        page.getByTestId("login-indicator").getByText("4").nth(1)
      ).toHaveCount(1);
      // Validate the filtered data
      const plan = await page
        .getByRole("gridcell", { name: "Tier Plan_Copy" })
        .count();
      expect(plan).toBe(4);
    });

    test("Filter multiple commission plan @enrichment @filter @smoke", async ({
      adminPage,
    }) => {
      // Test ID: INTER-T12963, INTER-T12971, INTER-T12964, INTER-T12980
      // Description: User should filter with the multiple commisison plan
      // Precondition: Commission plan should be already published
      // Expected behavior: User should see 6 rows of data for the mutiple plan has been filtered
      const page = adminPage.page;
      await page.getByRole("button", { name: "Filters" }).click();
      await expect(page.getByLabel("Filters").getByText("Filters")).toHaveCount(
        1
      );
      await selectdata(page, 0, "Tier Plan_Copy");
      await selectdata(page, 0, "Quarterly");
      await page.getByRole("button", { name: "Apply" }).click();
      // Check whether the user is able to see 6 rows of data
      await expect(
        page.locator('//div[@role="gridcell" and @col-id="commissionPlan"]')
      ).toHaveCount(6);
      //Validate the filtered data
      const plan1 = await page
        .getByRole("gridcell", { name: "Tier Plan_Copy" })
        .count();
      expect(plan1).toBe(4);
      const plan2 = await page
        .getByRole("gridcell", { name: "Quarterly" })
        .count();
      expect(plan2).toBe(2);
    });

    test("Filter single databook @enrichment @Filter @smoke", async ({
      adminPage,
    }) => {
      // Test ID: INTER-T12969 ,INTER-T12964, INTER-T12968, INTER-T12980
      // Description: User should filter with the single databook used in commisison plan
      // Precondition: Commission plan should be already published
      // Expected behavior: User should see one row of data after applying the filter
      const page = adminPage.page;
      await page.getByRole("button", { name: "Filters" }).click();
      await expect(page.getByLabel("Filters").getByText("Filters")).toHaveCount(
        1
      );
      await selectdata(page, 2, "Enrichment book");
      await page.getByRole("button", { name: "Apply" }).click();

      //Validate the filtered data
      const plan1 = await page
        .getByRole("gridcell", { name: "Enrichment book" })
        .count();
      expect(plan1).toBe(1);
      await expect(page.getByText("1 - 1of1rows")).toHaveCount(1);
    });

    test("Filter multiple databook @enrichment @Filter @smoke", async ({
      adminPage,
    }) => {
      // Test ID: INTER-T12969 ,INTER-T12964, INTER-T12968, INTER-T12973, INTER-T12980
      // Description: User should filter with the multiple databook used in commisison plan
      // Precondition: Commission plan should be already published
      // Expected behavior: User should see one row of data after applying the filter
      const page = adminPage.page;
      await page.getByRole("button", { name: "Filters" }).click();
      await expect(page.getByLabel("Filters").getByText("Filters")).toHaveCount(
        1
      );
      await selectdata(page, 2, "Enrichment book");
      await selectdata(page, 2, "Report New");
      await page.getByRole("button", { name: "Apply" }).click();

      //Validate the filtered data
      const plan1 = await page
        .getByRole("gridcell", { name: "Enrichment book" })
        .count();
      expect(plan1).toBe(1);
      await expect(page.getByText("1 - 1of1rows")).toHaveCount(1);
    });

    test("no rows of data @enrichment @filter @smoke", async ({
      adminPage,
    }) => {
      // Test ID: INTER-T12969 ,INTER-T12964, INTER-T12963, INTER-T13182, INTER-T12975, INTER-T12976
      // Description: User should filter with the single datasheet used in commisison plan by searching through plan dropdown
      // Precondition: Commission plan should be already published
      // Expected behavior: User should see no rows of data after applying the filter and after clearing the filetr user should see all the data
      const page = adminPage.page;
      await page.getByRole("button", { name: "Filters" }).click();
      await expect(page.getByLabel("Filters").getByText("Filters")).toHaveCount(
        1
      );
      await selectdata(page, 3, "test:qa");
      await page.getByRole("button", { name: "Apply" }).click();
      // Check whether the user is able to see 0 rows of data
      await expect(page.getByText("No rows found")).toHaveCount(1);
      await page.getByRole("button", { name: "Filters" }).click();

      // Clear the filter
      await page.getByText("Clear", { exact: true }).click();
      await page.getByRole("button", { name: "Apply" }).click();

      // user should see all the data
      await expect(
        page.getByTestId("login-indicator").getByText("7", { exact: true })
      ).toHaveCount(1);
    });

    test("Filter single datasheet @enrichment @filter @smoke", async ({
      adminPage,
    }) => {
      // Test ID:  INTER-T12964, INTER-T12963, INTER-T12969, INTER-T12980
      // Description: User should filter with single datasheet by searching through the datasheet dropdown
      // Precondition: User should already have the published commission plan
      // Expected behavior: User should see 1 row of data for the single datasheet has been filtered.page;
      const page = adminPage.page;
      await page.getByRole("button", { name: "Filters" }).click();
      await expect(page.getByLabel("Filters").getByText("Filters")).toHaveCount(
        1
      );
      await selectdata(page, 3, "Enrichment book:Enrich-sheet");
      await page.getByRole("button", { name: "Apply" }).click();
      await expect(page.getByText("1 - 1of1rows")).toHaveCount(1);
    });

    test("Filter mutliple datasheet @enrichment @filter @smoke", async ({
      adminPage,
    }) => {
      // Test ID: INTER-T12964, INTER-T12963, INTER-T12974, INTER-T12970, INTER-T12965
      // Description: User should filter with multiple datasheet by searching through the datasheet dropdown
      // Precondition: User should already have the published commission plan
      // Expected behavior: User should see 1 row of data for the multiple  datasheet has been filtered
      const page = adminPage.page;
      await page.getByRole("button", { name: "Filters" }).click();
      await expect(page.getByLabel("Filters").getByText("Filters")).toHaveCount(
        1
      );
      await selectdata(page, 3, "Enrichment book:Enrich-sheet");
      await selectdata(page, 3, "Sales book :Enrich-sheet");
      await page.getByRole("button", { name: "Apply" }).click();
      await expect(page.getByText("1 - 1of1rows")).toHaveCount(1);
      await page.getByRole("button", { name: "Filters" }).click();
      await page.getByText("Cancel", { exact: true }).click();
      await expect(page.getByText("1 - 1of1rows")).toHaveCount(1);
    });

    test("Filter single criteria @enrichment @filter @smoke", async ({
      adminPage,
    }) => {
      // Test ID: INTER-T12969 ,INTER-T12964, INTER-T12963, INTER-T12967, INTER-T12970, INTER-T12967
      // Description: User should filter with single criteria by searching through the criteria dropdown
      // Precondition: User should already have the published commission plan
      // Expected behavior: User should see 1 row of data for the single criteria has been filtered
      const page = adminPage.page;
      await page.getByRole("button", { name: "Filters" }).click();
      await expect(page.getByLabel("Filters").getByText("Filters")).toHaveCount(
        1
      );
      await selectdata(page, 1, "Quarterly:Simple");
      await page.getByRole("button", { name: "Apply" }).click();
      // Check whether the user is able to see 1 row of data
      await expect(
        page.getByTestId("login-indicator").getByText("1").nth(1)
      ).toHaveCount(1);
      //Validate the filtered data
      await page.waitForTimeout(1000);
      const simple_criteria = await page
        .getByRole("gridcell", { name: "Simple" })
        .count();
      expect(simple_criteria).toBe(1);
    });

    test("Filter multiple criteria @enrichment @filter @smoke", async ({
      adminPage,
    }) => {
      // Test ID: INTER-T12964, INTER-T12963, INTER-T12984, INTER-T12972, INTER-T12983, INTER-T12981
      // Description: Verify the user should filter with multiple criteria and select all the rows and delete all the rows
      // Precondition: USer should already have the published commission plan
      // Expected behavior: User should see 2 rows of data for the multiple criteria has been filtered
      const page = adminPage.page;
      await page.getByRole("button", { name: "Filters" }).click();
      await expect(page.getByLabel("Filters").getByText("Filters")).toHaveCount(
        1
      );
      await selectdata(page, 1, "Quarterly:Quota");
      await selectdata(page, 1, "Imelda:Quota (Copy)");
      await page.getByRole("button", { name: "Apply" }).click();
      // Check whether the user is able to see 1 row of data
      await expect(
        page.getByTestId("login-indicator").getByText("2").nth(1)
      ).toHaveCount(1);

      //Check whether the user should see the selected row count
      await page.locator(".ag-header-cell").first().click();
      await expect(
        page.locator("span").filter({ hasText: "2 Rows selected" }).first()
      ).toHaveCount(1);
      // Check whether the user should delete the rows by selecting all the rows
      await page.getByRole("button", { name: "Delete" }).click();
      await page.getByText("Deleted successfully").click();
    });

    test("Apply single filter in all components @enrichment @filter @smoke", async ({
      adminPage,
    }) => {
      // Test ID: INTER-T12964, INTER-T12963, INTER-T12980
      // Description: Verify the user should filter the enrichment rows by selecting one component in all the section and clear all the filter
      // Precondition: User should already have the published commission plan
      // Expected behavior: User should see 1 row of data when one item has been added in filter in all the component
      const page = adminPage.page;
      await page.getByRole("button", { name: "Filters" }).click();
      await expect(page.getByLabel("Filters").getByText("Filters")).toHaveCount(
        1
      );
      await selectdata(page, 0, "Tier Summation");
      await selectdata(page, 1, "Tier Summation:Tier");
      await selectdata(page, 3, "Comm dev datasheet:comm dev sheet");
      await selectdata(page, 2, "Comm dev datasheet");
      await page.getByRole("button", { name: "Apply" }).click();
      // Check whether the user is able to see 1 row of data after filter applied
      await expect(page.getByText("1 - 1of1rows")).toHaveCount(1);

      await page.waitForTimeout(1000);
      const plan_name = await page
        .getByRole("gridcell", { name: "Tier Summation" })
        .count();
      expect(plan_name).toBe(1);

      // ClearAll  filter
      await page.getByRole("button", { name: "Filters" }).click();
      await page.getByText("Clear all", { exact: true }).click();
      await page.getByRole("button", { name: "Apply" }).click();

      // user should see all the data
      await expect(
        page.getByTestId("login-indicator").getByText("7", { exact: true })
      ).toHaveCount(1);
    });
  }
);

async function selectdata(page, n1, planName) {
  await page.waitForTimeout(2000);
  await page
    .locator(`.ant-modal-body [data-testid='ever-select'] input >> nth=${n1}`)
    .click();
  await page
    .locator(`.ant-modal-body [data-testid='ever-select'] input >> nth=${n1}`)
    .type(planName);
  await page.getByTitle(planName, { exact: true }).getByText(planName).click();
  await page.getByLabel("Filters").getByText("Filters").click();
}
