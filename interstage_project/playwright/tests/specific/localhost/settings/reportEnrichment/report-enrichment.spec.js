import { setupGraphQLRouteInterceptor } from "../../../../bearerToken";
const {
  reportEnrichmentFixtures: { test, expect },
  reportEnrichmentLocalGlobalFixtures: { test: test2, expect: expect1 },
  enrichFixtures: { test: test3, expect: expect3 },
} = require("../../../../fixtures");

const csv = require("csv-parser");
const fs = require("fs");
const path = require("path");
var token = "";

test.beforeEach(async ({ adminPage }, testInfo) => {
  testInfo.setTimeout(testInfo.timeout + 900000);
});

test.describe(
  "Report enrichment Testcases",
  { tag: ["@reportenrichment", "@regression", "@primelogic-5"] },
  () => {
    test.describe("Report Enrichment", () => {
      test(" Plan has dependent report enrichments validation", async ({
        adminPage,
      }) => {
        const page = adminPage.page;
        await page.goto("/settings/report-enrichment", {
          waitUntil: "networkidle",
        });
        await expect(page.getByText("demo- monthly").first()).toBeVisible();
        await page.goto("/plans", {
          waitUntil: "networkidle",
        });
        await page.getByPlaceholder("Search by plan name").click();
        await page
          .getByPlaceholder("Search by plan name")
          .fill("demo- monthly");
        await page.locator(".absolute > .ant-btn").first().click();
        await page.getByRole("menuitem", { name: "Delete" }).click();
        await expect(
          page.getByText("This Commission plan can't be deleted.")
        ).toBeVisible();
        await page.getByRole("button", { name: "Close", exact: true }).click();
      });

      test("No row data gif validation", async ({ adminPage }) => {
        const page = adminPage.page;
        await page.goto("/settings/report-enrichment", {
          waitUntil: "networkidle",
        });
        await page.getByRole("button", { name: "Filters" }).click();
        await selectdata(page, 0, "enrichment plan");
        await selectdata(page, 1, "Enrichment_2:simple");
        await page.getByRole("button", { name: "Apply" }).click();
        await expect(page.getByText("No rows found")).toHaveCount(1);
      });

      test("clear button validation", async ({ adminPage }) => {
        const page = adminPage.page;
        await page.goto("/settings/report-enrichment", {
          waitUntil: "networkidle",
        });
        await page.locator(".ag-cell").first().click();
        await page.locator(".ag-row-odd > .ag-cell").first().click();
        await page.locator("div:nth-child(3) > .ag-cell").first().click();
        await page.locator("div:nth-child(4) > .ag-cell").first().click();

        await expect(
          page
            .locator("div")
            .filter({ hasText: /^4 Rows selected$/ })
            .nth(1)
        ).toBeVisible();
        await page.getByRole("button", { name: "Clear" }).click();
        await expect(
          page
            .locator("div")
            .filter({ hasText: /^0 Rows selected$/ })
            .nth(1)
        ).toBeVisible();
      });

      test("Report enrichment page validation", async ({ adminPage }) => {
        const page = adminPage.page;
        await page.goto("/settings/report-enrichment", {
          waitUntil: "networkidle",
        });
        await page
          .locator("div")
          .filter({ hasText: /^Select Commission Plan$/ })
          .nth(2)
          .click();
        await expect(page.getByText("demo- monthly").last()).toBeVisible();
        await expect(page.getByText("Demo-quarterly").last()).toBeVisible();
        await expect(page.getByText("enrichment plan").last()).toBeVisible();
        // await page.locator(".ant-select-item-option-content").first().click();
        await page.locator('div[title="demo- monthly"]').first().click();
        await page
          .locator("div")
          .filter({ hasText: /^Select a Report Type$/ })
          .nth(2)
          .click();
        await expect(page.getByText("Commission Report").last()).toBeVisible();
        await expect(
          page.getByText("Inter Commission Report").last()
        ).toBeVisible();
        await page
          .locator(
            "div:nth-child(6) > div > .ant-select-dropdown > div > .rc-virtual-list > .rc-virtual-list-holder > div > .rc-virtual-list-holder-inner > div > .ant-select-item-option-content"
          )
          .first()
          .click();
        await page
          .locator("div")
          .filter({ hasText: /^Select a Criteria$/ })
          .nth(2)
          .click();
        await page
          .locator("div")
          .filter({ hasText: /^sample-tier$/ })
          .nth(2)
          .click();
        await page
          .locator(
            "div:nth-child(6) > .relative > .ant-select > .ant-select-selector"
          )
          .click();
        await expect(page.getByText("calc_field").first()).toBeVisible();
        await expect(page.getByText("demo_id")).toBeVisible();
        await expect(page.getByText("demo_amount").first()).toBeVisible();
        await expect(page.getByText("demo_bool").first()).toBeVisible();
        await expect(page.getByText("demo_date").first()).toBeVisible();
        await expect(page.getByText("demo_email").first()).toBeVisible();
        await expect(page.getByText("demo_percent").first()).toBeVisible();
        await page
          .locator('input:near(span:text("Variables"))')
          .locator("nth=1")
          .click();
        await page.getByTitle("calc_field").locator("div").first().click();
        await page
          .getByText(
            "Commission Plandemo- monthlyReport TypeCommission ReportCriteriassample-"
          )
          .click();
        await expect(
          page.getByText(
            "Variables that have already been added to a criteria will be skipped during the validation of the enrichment variables."
          )
        ).toBeVisible();
        await page
          .locator("label")
          .filter({ hasText: "Include all plans using this" })
          .locator("span")
          .first()
          .click();
        await page.getByLabel("Include all plans using this").check();
        await page.getByRole("button", { name: "Validate & Add" }).click();
        await page.getByRole("button", { name: "Save" }).click();
        await expect(page.getByText("demo- monthly").first()).toBeVisible();
        await expect(page.getByText("Demo-quarterly").first()).toBeVisible();
      });

      test("clear all and select all filter validation", async ({
        adminPage,
      }) => {
        const page = adminPage.page;
        await page.goto("/settings/report-enrichment", {
          waitUntil: "networkidle",
        });

        await page.getByRole("button", { name: "Filters" }).click();
        await selectdata(page, 0, "enrichment plan");
        await selectdata(page, 1, "Enrichment_2:simple");
        await page.getByRole("button", { name: "Apply" }).click();
        await expect(page.getByText("- 0of0rows")).toHaveCount(1);
        await page.getByRole("button", { name: "Filters" }).click();
        await page.getByRole("button", { name: "Clear all" }).click();
        await page.getByRole("button", { name: "Apply" }).click();
        await expect(page.getByText("- 10of14rows")).toHaveCount(1);
        await page.locator(".ag-header-cell").first().click();
        await page
          .locator("div")
          .filter({ hasText: /^10 Rows selected$/ })
          .nth(1)
          .click();
        await page.getByRole("button", { name: "Clear" }).click();
        await page
          .locator("div")
          .filter({ hasText: /^0 Rows selected$/ })
          .nth(1)
          .click();
        await page.getByRole("button", { name: "Filters" }).click();
        await selectdata(page, 0, "demo- monthly");
        await selectdata(page, 1, "demo- monthly:sample-tier");
        await selectdata(page, 2, "demo-book");
        await selectdata(page, 3, "demo-book:demo-sheet");
        await page.getByRole("button", { name: "Apply" }).click();
      });

      test("Power admin able to access report enrichment and field name edit validation", async ({
        adminPage,
        payeePage,
      }) => {
        const page1 = payeePage.page;
        await page1.goto("/settings/report-enrichment", {
          waitUntil: "networkidle",
        });
        await expect(page1.getByText("Error 403")).toBeVisible();

        const page = adminPage.page;
        await page.goto("/settings/report-enrichment", {
          waitUntil: "networkidle",
        });
        await page
          .getByRole("columnheader", { name: "Report Type" })
          .locator("svg")
          .first()
          .click();
        await page.getByText("Choose Columns", { exact: true }).last().click();
        await page
          .getByLabel("Report Type")
          .getByLabel("Press SPACE to toggle")
          .uncheck();
        await page
          .getByLabel("Commission Plan")
          .getByLabel("Press SPACE to toggle")
          .uncheck();
        await page
          .getByLabel("Criteria")
          .getByLabel("Press SPACE to toggle")
          .uncheck();
        await page
          .getByLabel("Databook")
          .getByLabel("Press SPACE to toggle")
          .uncheck();
        await page.locator("//div[@data-ref='eTitleBarButtons']").click();
        await page
          .getByRole("gridcell", { name: "calc_field" })
          .first()
          .waitFor({ state: "visible", timeout: 5000 });
        await page
          .getByRole("gridcell", { name: "calc_field" })
          .first()
          .dblclick();
        await page
          .getByRole("textbox", { name: "Input Editor" })
          .fill("calc_field_edit");
        await page.getByText("Filters0 Rows selected").click();
        await expect(page.getByText("Name updated successfully")).toBeVisible();
        await page.getByText("Settings/Report Enrichment").click();
        await expect(
          page.getByRole("gridcell", { name: "calc_field_edit" })
        ).toHaveCount(1);
      });

      test("Uncheck enriched field validation", async ({ adminPage }) => {
        const page = adminPage.page;
        await page.goto("/databook", {
          waitUntil: "networkidle",
        });
        await page.getByRole("link", { name: "Demo-book" }).click();
        async function validateAndCheckText() {
          await page.getByText("demo-sheet", { exact: true }).click();
          await page.getByLabel("remove").first().click();
          const editBtn = await page.getByRole("menuitem", { name: "Edit" });
          await editBtn.waitFor({ state: "visible" });
          await editBtn.click();
          await page.getByText("Edit Datasheet").waitFor({ state: "visible" });
          await page
            .locator("//span[text()='demo_email']/ancestor::div[3]/label/span")
            .click({ timeout: 10000 });
          await page.getByRole("button", { name: "Validate" }).click();
          await expect(
            page.getByText("Column's demo_email used in plan - Demo-quarterly")
          ).toBeVisible({ timeout: 15000 });
          await expect(
            page.getByText("Column's demo_email used in plan - demo- monthly", {
              exact: true,
            })
          ).toBeVisible({ timeout: 15000 });
          await expect(
            page.getByText(
              "Error while validating datasheet. Please try again later"
            )
          ).toBeVisible({ timeout: 15000 });
        }
        try {
          await validateAndCheckText();
        } catch {
          await page.reload({ waitFor: "networkidle" });
          await validateAndCheckText();
        }
      });

      test("partial rows in bulk delete mode if any of the rows has dependency", async ({
        adminPage,
      }) => {
        const page = adminPage.page;
        await page.goto("/settings/report-enrichment", {
          waitUntil: "networkidle",
        });
        await page.getByRole("button", { name: "Filters" }).click();
        await selectdata(page, 0, "demo- monthly");
        await selectdata(page, 0, "Demo-quarterly");
        await selectdata(page, 1, "demo- monthly:sample-tier");
        await selectdata(page, 1, "Demo-quarterly:Quarterly-simple");
        await selectdata(page, 2, "demo-book");
        await selectdata(page, 3, "demo-book:demo-sheet");
        await page.getByRole("button", { name: "Apply" }).click();
        await page.locator('input[name="row-0"]').check();
        await page.locator('input[name="row-1"]').check();
        await page.locator('input[name="row-2"]').check();
        await page.getByRole("button", { name: "Delete" }).click();
        await expect(
          page.getByText(
            "Delete partially successful as some records could not be deleted. Please refer to Dependency column"
          )
        ).toBeVisible();
      });

      test("check whether filter is working properly", async ({
        adminPage,
      }) => {
        const page = adminPage.page;
        await page.goto("/settings/report-enrichment", {
          waitUntil: "networkidle",
        });
        await page.getByRole("button", { name: "Filters" }).click();
        await selectdata(page, 0, "enrichment plan");
        await selectdata(page, 1, "Enrichment_2:simple");
        await selectdata(page, 2, "Reports");
        await selectdata(page, 3, "Reports:inter-commission");
        await page.getByRole("button", { name: "Apply" }).click();
        await expect(page.getByText("No rows found")).toBeVisible();
        await page.getByRole("button", { name: "Filters" }).click();
        await page.getByRole("button", { name: "Clear all" }).click();
        await page.getByRole("button", { name: "Apply" }).click();
        await page.getByRole("button", { name: "Filters" }).click();
        await selectdata(page, 0, "enrichment_4");
        await selectdata(page, 1, "enrichment_4:simple");
        await selectdata(page, 2, "report_2");
        await selectdata(page, 3, "report_2:report_2");
        await page.getByRole("button", { name: "Apply" }).click();
        await expect(page.getByText("- 1of1rows")).toHaveCount(1);
      });

      test("check user is able enrich variable in commission plan", async ({
        adminPage,
      }) => {
        const page = adminPage.page;
        const key = "commission-view-period";
        const value = "August-2023";
        await page.evaluate(
          ({ key, value }) => {
            localStorage.setItem(key, value);
          },
          { key, value }
        );
        await page.goto("/commissions", {
          waitUntil: "networkidle",
        });
        await page.getByRole("link", { name: "adam a" }).click();
        await page
          .locator("div")
          .filter({ hasText: /^Commission Summary$/ })
          .first()
          .click();
        await page.getByText("Earned Commissions").click();
        await page.getByText("enrichment_3").click();
        await page.getByRole("button", { name: "simple" }).click();
        await expect(page.getByText("₹20.00").first()).toBeVisible();
      });

      test("overall_criteria", async ({ adminPage }) => {
        const page = adminPage.page;
        await page.goto("/databook", {
          waitUntil: "networkidle",
        });
        await page.getByRole("link", { name: "Reports" }).click();
        await page.getByText("inter-commission").click();
        await page.locator(".grow > div > .ant-btn").click();
        await page.getByRole("list").getByText("adjustment").click();
        await page.getByRole("tab", { name: "Customize Columns" }).click();
        await page
          .getByRole("treeitem", { name: "Period Start Date Column" })
          .getByText("Period Start Date")
          .click();
        await page
          .getByRole("treeitem", { name: "Period End Date Column" })
          .getByText("Period End Date")
          .click();
        await page.getByPlaceholder("Search...").click();
        await page.getByPlaceholder("Search...").fill("commission plan");
        await page
          .getByRole("treeitem", { name: "Commission Plan Column" })
          .getByText("Commission Plan")
          .click();
        await page.getByPlaceholder("Search...").click();
        await page.getByPlaceholder("Search...").fill("Criteria");
        await page
          .getByRole("treeitem", { name: "Criteria Column" })
          .getByText("Criteria")
          .click();
        await page.getByPlaceholder("Search...").click();
        await page.getByPlaceholder("Search...").fill("Line Item Id");
        await page
          .getByRole("treeitem", { name: "Line Item Id Column" })
          .getByText("Line Item Id")
          .click();
        await page.getByPlaceholder("Search...").click();
        await page.getByPlaceholder("Search...").fill("Plan Id");
        await page
          .getByRole("treeitem", { name: "Plan Id Column" })
          .getByText("Plan Id")
          .click();
        await page.getByPlaceholder("Search...").click();
        await page.getByPlaceholder("Search...").fill("Criteria Id");
        await page
          .getByRole("treeitem", { name: "Criteria Id Column" })
          .getByText("Criteria Id")
          .click();
        await page.getByPlaceholder("Search...").click();
        await page.getByPlaceholder("Search...").fill("Plan Id");
        await page.getByPlaceholder("Search...").click();
        await page.getByPlaceholder("Search...").fill("demo_date");
        await page
          .getByRole("treeitem", { name: "demo_date" })
          .getByText("demo_date")
          .click();
        await page.getByRole("tab", { name: "Customize Columns" }).click();
        await expect(page.getByText("445").first()).toBeVisible();
        await page.waitForTimeout(3000);
        await page.locator(".grow > div > .ant-btn").click();
        await page.getByText("overall-criteria").click();
        await expect(page.getByText("No Rows To Show")).toHaveCount(1);
        await page.goto("/databook", {
          waitUntil: "networkidle",
        });
      });

      test("Download csv validation for enrich fields", async ({
        adminPage,
      }) => {
        const page = adminPage.page;
        await page.goto("/databook", {
          waitUntil: "networkidle",
        });
        await page.getByRole("link", { name: "Demo-book" }).click();
        await page.getByText("demo-sheet", { exact: true }).click();
        await page
          .getByRole("button", { name: "Export CSV" })
          .click({ timeout: 6000 });
        await page.getByRole("menuitem", { name: "Pre Adjustment" }).click();
        await page.getByText("Exporting datasheet...").click();
        const downloadPromise = page.waitForEvent("download");
        const download = await downloadPromise;
        const downloadPath = path.join(
          __dirname,
          "downloads",
          download.suggestedFilename()
        );
        await download.saveAs(downloadPath);
        const expectedKeys = [
          "demo_id",
          "demo_amount",
          "demo_bool",
          "demo_date",
          "demo_email",
          "demo_percent",
          "calc_field",
        ];
        fs.createReadStream(downloadPath)
          .pipe(csv())
          .on("headers", (headers) => {
            const trimmedHeaders = headers.map((header) => header.trim());
            expect(trimmedHeaders.length).toBe(7); // Assuming 7 keys are expected
            expectedKeys.forEach((key) => {
              expect(trimmedHeaders).toContain(key);
            });
          })
          .on("error", (err) => {
            throw err;
          });
        await page
          .locator("div")
          .filter({ hasText: "Downloaded Successfully!!" })
          .nth(3)
          .click();
      });

      test("Dependent enriched field deletion validation", async ({
        adminPage,
      }) => {
        const page = adminPage.page;
        await page.goto("/settings/report-enrichment", {
          waitUntil: "networkidle",
        });

        await page.getByRole("button", { name: "Filters" }).click();
        await selectdata(page, 0, "demo- monthly");
        await selectdata(page, 1, "demo- monthly:sample-tier");
        await selectdata(page, 2, "demo-book");
        await selectdata(page, 3, "demo-book:demo-sheet");
        await page.getByRole("button", { name: "Apply" }).click();
        await page.locator('input[name="row-0"]').check();
        await page.getByRole("button", { name: "Delete" }).click();
        await expect(
          page.getByText(
            "Selected enrichments can't be deleted as it has dependencies"
          )
        ).toBeVisible();
      });
    });

    async function selectdata(page, n1, planName) {
      await page.waitForTimeout(2000);
      await page
        .locator(
          `.ant-modal-body [data-testid='ever-select'] input >> nth=${n1}`
        )
        .click();
      await page
        .locator(
          `.ant-modal-body [data-testid='ever-select'] input >> nth=${n1}`
        )
        .type(planName);
      await page
        .getByTitle(planName, { exact: true })
        .getByText(planName)
        .click();
      await page.getByLabel("Filters").getByText("Filters").click();
    }
  }
);

test2.describe(
  "(may timeout fail) Report Enrichment local global adjustment",
  { tag: ["@reportenrichment", "@regression", "@primelogic-5"] },
  () => {
    test2(
      "local/global adjustment when payout is locked",
      async ({ adminPage }, testInfo) => {
        testInfo.setTimeout(testInfo.timeout + 900000);
        const page = adminPage.page;
        await page.goto("/databook", {
          waitUntil: "networkidle",
        });
        await page.getByRole("link", { name: "Data", exact: true }).click();
        await page
          .getByRole("tablist")
          .locator("div")
          .filter({ hasText: "data" })
          .nth(2)
          .click();
        await page.waitForTimeout(2000);
        await page.locator(".text-center").click();
        await page
          .locator("span")
          .filter({ hasText: "Update Record" })
          .first()
          .click();
        await page.getByText("Add new").click();
        const child = await page
          .locator("div")
          .filter({ hasText: /^Column name\*$/ })
          .locator("span")
          .nth(3);
        await child.locator("..").click();
        await page.locator(".ant-select-item-option-content").first().click();
        await page.locator("section").getByRole("textbox").nth(3).click();
        await page
          .locator("section")
          .getByRole("textbox")
          .nth(3)
          .fill("120000");
        await page.locator("section textarea").click();
        await page.locator("section textarea").fill("global");
        await page.waitForTimeout(1000);
        await page.getByTestId("update-button-adjustment-modal").click();
        await page.waitForTimeout(6000);
        // await page.getByText("inter_commission").click();
        await page.getByRole("button", { name: "Update Data" }).click();
        await expect(
          await page.getByText("Datasheet sync request has")
        ).toBeVisible();
        await expect(
          await page.getByText("Datasheet has been generated")
        ).toBeVisible({ timeout: 180000 });
        await page.goto("/databook", {
          waitUntil: "networkidle",
        });
        await page.getByRole("link", { name: "Sheet" }).click();
        await page.goto("/settings/commissions-and-data-sync", {
          waitUntil: "networkidle",
        });
        await page.getByPlaceholder("Select month").click();
        const currentYear = new Date().getFullYear();
        const startYearDiff = currentYear - 2024;
        for (let i = 0; i < startYearDiff; i++) {
          await page.locator("button").first().click();
        }
        await page.getByText("Jun").last().click();
        await page.getByRole("button", { name: "Run", exact: true }).click();
        await page.getByRole("button", { name: "Skip & Run" }).click();
        await expect(
          await page.getByText("Calculating Commissions...")
        ).toBeVisible();
        await expect(
          await page.getByText("Commission Calculations Completed")
        ).toBeVisible({
          timeout: 480000,
        });
        await page.goto("/databook", {
          waitUntil: "networkidle",
        });
        await page.getByRole("link", { name: "Sheet" }).click();
        await page.getByRole("tab", { name: "Customize Columns" }).click();
        // await page
        //   .getByLabel("Period Column")
        //   .getByLabel("Press SPACE to toggle")
        //   .uncheck();
        await page
          .locator('//div[@aria-label="Period Column"]//input')
          .uncheck();
        // await page
        //   .getByLabel("Period Start Date Column")
        //   .getByLabel("Press SPACE to toggle")
        //   .uncheck();
        await page
          .locator('//div[@aria-label="Period Start Date Column"]//input')
          .uncheck();
        // await page
        //   .getByLabel("Period End Date Column")
        //   .getByLabel("Press SPACE to toggle")
        //   .uncheck();
        await page
          .locator('//div[@aria-label="Period End Date Column"]//input')
          .uncheck();
        // await page
        //   .getByLabel("Payee Column")
        //   .getByLabel("Press SPACE to toggle")
        //   .uncheck();
        await page
          .locator('//div[@aria-label="Payee Column"]//input')
          .uncheck();
        // await page
        //   .getByLabel("Payee Email Column")
        //   .getByLabel("Press SPACE to toggle")
        //   .uncheck();
        await page
          .locator('//div[@aria-label="Payee Email Column"]//input')
          .uncheck();
        // await page
        //   .getByLabel("Line Item Id Column")
        //   .getByLabel("Press SPACE to toggle")
        //   .uncheck();
        await page
          .locator('//div[@aria-label="Line Item Id Column"]//input')
          .uncheck();
        await expect1(page.getByText("1,000").first()).toBeVisible();
        await page.getByText("inter_commission").click();
        await page.getByRole("tab", { name: "Customize Columns" }).click();
        await page
          .getByLabel("Period Column")
          .locator("div")
          .filter({ hasText: "Period" })
          .click();
        await page
          .getByLabel("Period Column")
          .getByLabel("Press SPACE to toggle")
          .uncheck();
        await page
          .getByLabel("Period Start Date Column")
          .getByLabel("Press SPACE to toggle")
          .uncheck();
        await page
          .getByLabel("Period End Date Column")
          .getByLabel("Press SPACE to toggle")
          .uncheck();
        await page
          .getByLabel("Payee Column")
          .getByLabel("Press SPACE to toggle")
          .uncheck();
        await page
          .getByLabel("Payee Email Column")
          .getByLabel("Press SPACE to toggle")
          .uncheck();
        await page
          .getByLabel("Line Item Id Column")
          .getByLabel("Press SPACE to toggle")
          .uncheck();
        await page
          .getByLabel("Tier Id Column")
          .getByLabel("Press SPACE to toggle")
          .uncheck();
        await expect1(page.getByText("1,000").first()).toBeVisible();
      }
    );

    test2(
      "plan unlock check in local / global adjustment",
      async ({ adminPage }, testInfo) => {
        testInfo.setTimeout(testInfo.timeout + 600000);
        const page = adminPage.page;
        const currentYear = new Date().getFullYear();
        const startYearDiff = currentYear - 2024;
        const key = "commission-view-period";
        const value = "June-2024";
        await page.evaluate(
          ({ key, value }) => {
            // Set the localStorage value for the current page
            localStorage.setItem(key, value);
          },
          { key, value }
        );
        await page.goto("/commissions", {
          waitUntil: "networkidle",
        });

        await page.locator('input[name="row-0"]').check();
        await page.waitForTimeout(1000);
        await page
          .locator("div")
          .filter({ hasText: /^Unlock$/ })
          .click();
        await page.waitForTimeout(2000);
        await page.getByRole("button", { name: "Unlock" }).click();
        await page.waitForTimeout(2000);

        await page.goto("/settings/commissions-and-data-sync", {
          waitUntil: "networkidle",
        });
        await page.getByPlaceholder("Select month").click();

        for (let i = 0; i < startYearDiff; i++) {
          await page.locator("button").first().click();
        }
        await page.getByText("Jun").last().click();
        await page.getByRole("button", { name: "Run", exact: true }).click();
        await page.getByRole("button", { name: "Skip & Run" }).click();
        await expect(
          await page.getByText("Calculating Commissions...")
        ).toBeVisible();
        await expect(
          await page.getByText("Commission Calculations Completed")
        ).toBeVisible({
          timeout: 300000,
        });
        await page.goto("/databook", {
          waitUntil: "networkidle",
        });
        await page.getByRole("link", { name: "Sheet" }).click();
        // await page.getByRole("button", { name: "Update Data" }).click();
        // await expect(
        //   await page.getByText("Datasheet sync request has")
        // ).toBeVisible();
        // await expect(
        //   await page.getByText("Datasheet has been generated")
        // ).toBeVisible({ timeout: 60000 });
        await page.getByRole("tab", { name: "Customize Columns" }).click();
        await page
          .getByLabel("Period Column")
          .getByLabel("Press SPACE to toggle")
          .uncheck();
        await page
          .getByLabel("Period Start Date Column")
          .getByLabel("Press SPACE to toggle")
          .uncheck();
        await page
          .getByLabel("Period End Date Column")
          .getByLabel("Press SPACE to toggle")
          .uncheck();
        await page
          .getByLabel("Payee Column")
          .getByLabel("Press SPACE to toggle")
          .uncheck();
        await page
          .getByLabel("Payee Email Column")
          .getByLabel("Press SPACE to toggle")
          .uncheck();
        await page
          .getByLabel("Line Item Id Column")
          .getByLabel("Press SPACE to toggle")
          .uncheck();
        await page
          .getByLabel("Tier Id Column")
          .getByLabel("Press SPACE to toggle")
          .uncheck();
        await expect1(page.getByText("120,000").first()).toBeVisible();
        await page
          .locator("div")
          .filter({ hasText: /^commission$/ })
          .first()
          .click();
        // await page.getByRole("button", { name: "Update Data" }).click();
        // await page.waitForTimeout(60000);
        await page.getByRole("tab", { name: "Customize Columns" }).click();
        await page
          .getByLabel("Period Column")
          .getByLabel("Press SPACE to toggle")
          .uncheck();
        await page
          .getByLabel("Period Start Date Column")
          .getByLabel("Press SPACE to toggle")
          .uncheck();
        await page
          .getByLabel("Period End Date Column")
          .getByLabel("Press SPACE to toggle")
          .uncheck();
        await page
          .getByLabel("Payee Column")
          .getByLabel("Press SPACE to toggle")
          .uncheck();
        await page
          .getByLabel("Payee Email Column")
          .getByLabel("Press SPACE to toggle")
          .uncheck();
        await page
          .getByLabel("Line Item Id Column")
          .getByLabel("Press SPACE to toggle")
          .uncheck();
        await page
          .getByLabel("Tier Id Column")
          .getByLabel("Press SPACE to toggle")
          .uncheck();
        await expect1(page.getByText("120,000").first()).toBeVisible();
      }
    );
  }
);

test3.describe(
  "enrichment checks",
  { tag: ["@reportenrichment", "@regression", "@primelogic-5"] },
  () => {
    test3.skip(
      "enrichment_validations",
      async ({ adminPage, request }, testInfo) => {
        testInfo.setTimeout(testInfo.timeout - 60000);
        const page = adminPage.page;
        token = await setupGraphQLRouteInterceptor(page);
        await page.waitForLoadState("networkidle");
        await page.goto("/settings", { waitUntil: "networkidle" });

        // Delete the already enriched commission object variable
        await page
          .getByRole("link", {
            name: "Report Enrichment Enrich the Report data with the power of Report Enrichment",
          })
          .click();
        await page.waitForTimeout(1000);
        await page.locator('input[name="row-0"]').check();
        await page.getByRole("button", { name: "Delete" }).click();
        await expect3(
          await page.getByText(
            "Selected enrichments can't be deleted as it has dependencies"
          )
        ).toHaveCount(1);

        // Delete the already enriched inter commission object variable
        const resp = await request.delete(
          "commission_engine/report_enrichment/bulk_delete",
          {
            data: {
              selectedEnrichments: [
                {
                  commissionPlanId: "640f1807-0cba-4092-9fb5-f276cffbb97f",
                  criteriaId: "198f26f5-e0fe-4ab9-a84d-723bf98e4719",
                  databookId: "9439a0dc-2183-4994-8921-9616edc58438",
                  datasheetId: "4d3d67c4-4d7e-4196-a353-abff352f1d02",
                  systemName: "co_1_demobool",
                  reportSystemName:
                    "co_1_demobool_4d3d67c4_4d7e_4196_a353_abff352f1d02",
                  reportType: "commission",
                  commissionPlanName: "demo- monthly",
                  displayName: "demo_bool",
                },
              ],
              filters: {
                commissionPlanId: [],
                criteriaId: [],
                databookId: [],
                datasheetId: [],
              },
            },
            headers: {
              Authorization: `Bearer ${token}`,
            },
          }
        );
        const response = await resp.json();
        expect3(await response.message).toBe(
          "Selected enrichments can't be deleted as it has dependencies"
        );

        // Delete the inter commission datasheet from which the enrichment varaible used in plan
        await page.goto("/databook", {
          waitUntil: "networkidle",
        });
        await page.getByRole("link", { name: "Reports" }).click();
        await page.getByRole("button", { name: "remove" }).first().click();
        await page.waitForTimeout(2000);
        await page.getByRole("button", { name: "animation" }).isHidden();
        await page.getByText("Delete").click();
        await expect3(page.getByText("Deleting datasheet...")).toHaveCount(1);
        await expect3(
          page.getByText(
            "Error: Cannot delete datasheet as it is used in commission plan or settlement rule"
          )
        ).toHaveCount(1);
        await page.goto("/settings", {
          waitUntil: "networkidle",
        });
      }
    );

    test3("overall_criteria", async ({ adminPage }, testInfo) => {
      const page = adminPage.page;
      await page.goto("/databook", {
        waitUntil: "networkidle",
      });
      await page.getByRole("link", { name: "Reports" }).click();
      await page.getByText("inter-commission").click();

      // Verify the adjusted value in the inter commission report enrichment for row level criteria
      await page.locator(".grow > div > .ant-btn").click();
      await page.getByRole("list").getByText("adjustment").click();
      await page.getByRole("tab", { name: "Customize Columns" }).click();
      await page.getByPlaceholder("Search...").fill("Period Start Date");
      await page
        .getByRole("treeitem", { name: "Period Start Date Column" })
        .getByText("Period Start Date")
        .click();
      await page.getByPlaceholder("Search...").fill("Period End Date");
      await page
        .getByRole("treeitem", { name: "Period End Date Column" })
        .getByText("Period End Date")
        .click();
      await page.getByPlaceholder("Search...").fill("Line Item Id");
      await page
        .getByRole("treeitem", { name: "Line Item Id Column" })
        .getByText("Line Item Id")
        .click();
      await page.getByPlaceholder("Search...").fill("Plan Id");
      await page
        .getByRole("treeitem", { name: "Plan Id Column" })
        .getByText("Plan Id")
        .click();
      await page.getByPlaceholder("Search...").fill("Criteria Id");
      await page
        .getByRole("treeitem", { name: "Criteria Id Column" })
        .getByText("Criteria Id")
        .click();
      await page
        .getByPlaceholder("Search...")
        .fill("Commission Amount (Payout Currency)");
      await page
        .getByLabel("Commission Amount (Payout")
        .getByLabel("Press SPACE to toggle")
        .uncheck();
      await page.getByPlaceholder("Search...").fill("Updated at");
      await page
        .getByLabel("Updated at Column")
        .getByLabel("Press SPACE to toggle")
        .uncheck();
      await page.getByRole("tab", { name: "Customize Columns" }).click();
      await expect3(page.getByRole("gridcell", { name: "445" })).toHaveCount(1);

      // Verify whether no enrichment variable displayed for over all criteria for commission report
      await page.locator(".grow > div > .ant-btn").click();
      await page.getByText("overall-criteria").click();
      await expect3(page.getByText("No Rows To Show")).toHaveCount(1);
      await page.goto("/databook", {
        waitUntil: "networkidle",
      });
    });
  }
);
