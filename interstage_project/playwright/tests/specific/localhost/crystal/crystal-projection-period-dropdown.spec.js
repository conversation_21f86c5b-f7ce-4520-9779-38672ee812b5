import CrystalSelectPage from "../../../../test-objects/crystalselectoppfilters-objects";

const {
  crystalNewFixtures: { test, expect },
} = require("../../../fixtures");

function getCurrentYear() {
  return new Date().getFullYear();
}

function getMonthYearList() {
  const now = new Date();
  const result = [];
  for (let i = 0; i < 13; i++) {
    const date = new Date(now.getFullYear(), now.getMonth() + i, 1);
    result.push(
      `${date.toLocaleString("default", {
        month: "long",
      })} ${date.getFullYear()}`
    );
  }
  return result;
}

function getQuarterList() {
  const now = new Date();
  let year = now.getFullYear();
  let month = now.getMonth();
  let quarter = Math.floor(month / 3) + 1;
  const result = [];
  for (let i = 0; i < 5; i++) {
    // Calculate start and end months for the quarter
    const startMonth = (quarter - 1) * 3;
    const endMonth = startMonth + 2;
    const start = new Date(year, startMonth, 1);
    const end = new Date(year, endMonth + 1, 0); // last day of end month
    result.push(
      `Q${quarter} (${start.toLocaleString("default", {
        month: "short",
      })} ${start.getFullYear()} - ` +
        `${end.toLocaleString("default", {
          month: "short",
        })} ${end.getFullYear()})`
    );
    quarter++;
    if (quarter > 4) {
      quarter = 1;
      year++;
    }
  }
  return result;
}

function getHalfYearList() {
  const now = new Date();
  let year = now.getFullYear();
  let month = now.getMonth();
  let half = month < 6 ? 1 : 2;
  const result = [];
  for (let i = 0; i < 3; i++) {
    let startMonth, endMonth;
    if (half === 1) {
      startMonth = 0; // Jan
      endMonth = 5; // Jun
    } else {
      startMonth = 6; // Jul
      endMonth = 11; // Dec
    }
    const start = new Date(year, startMonth, 1);
    const end = new Date(year, endMonth + 1, 0);
    result.push(
      `H${half} (${start.toLocaleString("default", {
        month: "short",
      })} ${start.getFullYear()} - ` +
        `${end.toLocaleString("default", {
          month: "short",
        })} ${end.getFullYear()})`
    );
    half++;
    if (half > 2) {
      half = 1;
      year++;
    }
  }
  return result;
}

function getWeeklyPeriods(count = 6) {
  const now = new Date();
  // Find the most recent Sunday (could be today)
  const dayOfWeek = now.getDay();
  const daysSinceSunday = dayOfWeek; // 0 if today is Sunday
  let start = new Date(
    now.getFullYear(),
    now.getMonth(),
    now.getDate() - daysSinceSunday
  );

  const result = [];
  for (let i = 0; i < count; i++) {
    const end = new Date(
      start.getFullYear(),
      start.getMonth(),
      start.getDate() + 6
    );
    result.push(
      `${start.toLocaleDateString("en-US", {
        month: "short",
        day: "2-digit",
        year: "numeric",
      })} - ` +
        `${end.toLocaleDateString("en-US", {
          month: "short",
          day: "2-digit",
          year: "numeric",
        })}`
    );
    // Move to next week
    start = new Date(
      start.getFullYear(),
      start.getMonth(),
      start.getDate() + 7
    );
  }
  return result;
}

function getBiWeeklyPeriods(count = 6) {
  const now = new Date();
  // Find the most recent Sunday (could be today)
  const dayOfWeek = now.getDay();
  const daysSinceSunday = dayOfWeek; // 0 if today is Sunday
  let start = new Date(
    now.getFullYear(),
    now.getMonth(),
    now.getDate() - daysSinceSunday
  );

  const result = [];
  for (let i = 0; i < count; i++) {
    const end = new Date(
      start.getFullYear(),
      start.getMonth(),
      start.getDate() + 13
    );
    result.push(
      `${start.toLocaleDateString("en-US", {
        month: "short",
        day: "2-digit",
        year: "numeric",
      })} - ` +
        `${end.toLocaleDateString("en-US", {
          month: "short",
          day: "2-digit",
          year: "numeric",
        })}`
    );
    // Move to next bi-week
    start = new Date(
      start.getFullYear(),
      start.getMonth(),
      start.getDate() + 14
    );
  }
  return result;
}

test.describe("Crystal Projection Period Dropdown Validation", () => {
  test("Validate period dropdown values for different payee types", async ({
    adminPage,
  }) => {
    const page = adminPage.page;
    const crystalSelectPage = new CrystalSelectPage(page);
    await crystalSelectPage.navigate("/crystal");
    await crystalSelectPage.openCrystal("Crystal dropdown validation");
    const payeeCrystalPage = await crystalSelectPage.previewAsPayee();

    // Annual user
    let options = await payeeCrystalPage.getPeriodDropdownValues();
    const year = getCurrentYear();
    expect(options).toEqual([`${year}`, `${year + 1}`]);

    // Monthly user
    await payeeCrystalPage.switchPayee("Monthly user", "₹1,000.00");
    options = await payeeCrystalPage.getPeriodDropdownValues();
    expect(options).toEqual(getMonthYearList());

    // Quarterly user
    await payeeCrystalPage.switchPayee("quarterly user", "₹2,500.00");
    options = await payeeCrystalPage.getPeriodDropdownValues();
    expect(options).toEqual(getQuarterList());

    // Half yearly user
    await payeeCrystalPage.switchPayee("halfyearly user", "₹5,000.00");
    options = await payeeCrystalPage.getPeriodDropdownValues();
    expect(options).toEqual(getHalfYearList());

    // customcalendaruser01 (weekly)
    await payeeCrystalPage.switchPayee(
      "customcalendaruser01 payee",
      "₹12,00,000.00"
    );
    options = await payeeCrystalPage.getPeriodDropdownValues();
    console.log("Expected Weekly Dropdown Values:", getWeeklyPeriods());
    expect(options).toEqual(getWeeklyPeriods());

    // customcalendaruser02 (bi-weekly)
    await payeeCrystalPage.switchPayee(
      "customcalendaruser02 payee",
      "₹1,20,000.00"
    );
    options = await payeeCrystalPage.getPeriodDropdownValues();
    console.log("Expected Bi-Weekly Dropdown Values:", getBiWeeklyPeriods());
    expect(options).toEqual(getBiWeeklyPeriods());
  });
});
