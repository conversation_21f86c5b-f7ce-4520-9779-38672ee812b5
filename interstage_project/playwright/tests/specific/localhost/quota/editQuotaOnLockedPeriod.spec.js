import { expect } from "@playwright/test";
import ManagerEffectiveEndDate from "../../../../test-objects/managerEffEndDate-objects";
import CrystalPage from "../../../../test-objects/crystalselectoppfilters-objects";
import Revertexitpage from "../../../../test-objects/revertexit-objects";
import CommissionsSyncPrevPeriod from "../../../../test-objects/commissionSyncPrevPeriod-objects";
import QuotaAnnualED from "../../../../test-objects/quotaAnnualEd-objects";
import CommonUtils from "../../../../test-objects/common-utils-objects";

const {
  ikUsersFixtures: { test },
} = require("../../../fixtures");

async function setPayoutsDate(adminPage, payoutsdate) {
  const key = "commission-view-period";
  const value = payoutsdate;

  await adminPage.page.evaluate(
    ({ key, value }) => {
      // Set the localStorage value for the current page
      localStorage.clear();
      localStorage.setItem(key, value);
    },
    { key, value }
  );
}

test.beforeEach(async ({ adminPage }, testInfo) => {
  const page = adminPage.page;
  const managerObj = new ManagerEffectiveEndDate(page);
  await managerObj.checkImpersonationandExit();
  testInfo.setTimeout(testInfo.timeout + 2800000);
});

test.describe(
  "Edit Quota on Locked Period ",
  { tag: ["@quota", "@regression", "@primelogic-4"] },
  () => {
    // crystal data needs to be updated
    test.skip("Crystal validation", async ({ adminPage }) => {
      test.info().annotations.push(
        {
          type: "Test ID",
          description: "INTER-T17817,INTER-T17816 ",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description: "User should be able to revert exit",
        }
      );
      const page = adminPage.page;
      const crystalobj = new CrystalPage(page);
      await crystalobj.goToCrystalPage();
      const page1 = await crystalobj.openSimulator("Crystal Annual Draft Last");
      await expect(page1.getByText("1258", { exact: true })).toBeVisible();
      await crystalobj.selectOpportunities(page1);
      await crystalobj.applyProjections(page1);
      await expect(page1.getByText("1258", { exact: true })).toBeVisible();
      await page1.getByText("Quota Attainment").click();
      await expect(
        page1
          .locator("div")
          .filter({ hasText: /^1258%$/ })
          .first()
      ).toBeVisible();
    });

    test("annual quota payee", async ({ adminPage }) => {
      test.info().annotations.push(
        {
          type: "Test ID",
          description:
            "INTER-T17799, INTER-T17797,INTER-T17796, INTER-T17795, INTER-T17794 ",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description: "User should be able to revert exit",
        }
      );
      // Test whether quota calculations are updated when we run the sync for unlocked period for annual quota payee - quotapayee4
      const page = adminPage.page;
      const managerObj = new ManagerEffectiveEndDate(page);
      const quotaED = new QuotaAnnualED(page);
      const commonPage = new CommonUtils(page);
      const csPrevPage = new CommissionsSyncPrevPeriod(page);

      await managerObj.navigate("/quotas");
      await managerObj.clickPayee("quota payee4");
      await managerObj.clickEditQuotabutton();
      await managerObj.editAnnaulQuotavalue("10000");
      await page.getByRole("button", { name: "Save" }).click();
      await expect(page.getByText("Added/Modified Quota")).toBeVisible();
      await managerObj.navigate("/settings/commissions-and-data-sync");
      await csPrevPage.selectPayees("quota payee4");
      await csPrevPage.selectDate("31 Dec 2024");
      await csPrevPage.runCommissions();
      await expect(page.getByText("Notify when complete")).toHaveCount(1);
      await csPrevPage.clickSkipandRun();
      await csPrevPage.waitForCalculationMessage();
      await csPrevPage.waitForCommissionsSuccess();

      await setPayoutsDate(adminPage, "31-December-2024");
      await managerObj.navigate("/commissions");
      await managerObj.searchPayeeinPayouts("quota payee4");
      await managerObj.clickPayee("quota payee4");
      // payouts page
      await expect(page.getByText("125,800.00of20,000.00")).toBeVisible();
      await expect(page.getByText("629.00%")).toBeVisible();
      // Unlock the statement and check
      await managerObj.lockOrUnlockstatement();
      await expect(
        page.getByText("Statement unlocked successfully")
      ).toBeVisible();
      await expect(page.getByText("125,800.00of10,000.00")).toBeVisible();
      await expect(page.getByText("1,258.00%")).toBeVisible();
      await page.getByText("Quota Attainment").click();
      await expect(
        page.getByText(
          "Primary QuotaAnnual125,800.00of10,000.001,258.00%You're on fire!🔥You've"
        )
      ).toBeVisible();
      await managerObj.lockOrUnlockstatement();
      await expect(
        page.getByText("Statement locked successfully")
      ).toBeVisible();

      // Run Report ETL and Update datasheet
      await quotaED.navigate("settings/commissions-and-data-sync");
      await commonPage.expandMenu("Report ETL Run ETL for report", "7");
      await commonPage.runETLReportforAllpayeesAndPeriod(
        "Quota Attainment",
        false
      );
      await csPrevPage.runCommissions();
      await csPrevPage.clickSkipandRun();
      await csPrevPage.waitForReportETLSuccess();

      // Report Object validations
      // Quota Attainment sheet - Check Target value, Attained, Attained percent
      // Annaul quota
      await managerObj.navigate("/databook");
      await managerObj.openDatabook("Report book");
      await managerObj.openDatasheet();
      await page.waitForTimeout(3000);
      await managerObj.updateDatasheet();
      await page.waitForTimeout(3000);
      await managerObj.addFilterinDatasheet(
        "Payee Email Id",
        "Contains",
        "<EMAIL>"
      );
      // Target - quota value
      await expect(page.getByTestId("pt-10000")).toBeVisible();
      // Quota Attained
      await expect(page.getByTestId("pt-125800")).toBeVisible();
      // Quota percentage
      await expect(page.getByRole("gridcell", { name: "1258" })).toBeVisible();
    });

    test("Half yearly quota", async ({ adminPage }) => {
      test.info().annotations.push(
        {
          type: "Test ID",
          description: "INTER-T17592, ",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description: "User should be able to revert exit",
        }
      );

      // Test whether quota calculations are updated when we run the sync for locked period quota edited by enabling "Edit Locked Quota" flag for Half yearly quota - quotapayee 3
      const page = adminPage.page;
      const managerObj = new ManagerEffectiveEndDate(page);
      const quotaED = new QuotaAnnualED(page);
      const commonPage = new CommonUtils(page);
      const csPrevPage = new CommissionsSyncPrevPeriod(page);

      await managerObj.navigate("/quotas");
      await managerObj.clickPayee("quota payee3");
      await managerObj.clickEditQuotabutton();
      await managerObj.editHalfYearlyQuotaValue("25000");
      await page.getByRole("button", { name: "Save" }).click();
      await expect(
        page.getByText("Added/Modified Quota").first()
      ).toBeVisible();
      await managerObj.navigate("/settings/commissions-and-data-sync");
      await csPrevPage.selectPayees("quota payee3");
      await csPrevPage.selectDate("31 Dec 2024");
      await csPrevPage.runCommissions();
      await expect(page.getByText("Notify when complete")).toHaveCount(1);
      await csPrevPage.clickSkipandRun();
      await csPrevPage.waitForCalculationMessage();
      await csPrevPage.waitForCommissionsSuccess();

      await setPayoutsDate(adminPage, "31-December-2024");
      await managerObj.navigate("/commissions");
      await managerObj.searchPayeeinPayouts("quota payee3");
      await managerObj.clickPayee("quota payee3");
      // payouts page
      await expect(page.getByText("85,400.00of50,000.00")).toBeVisible();
      await expect(page.getByText("170.80%")).toBeVisible();
      // Unlock the statement and check
      await managerObj.lockOrUnlockstatement();
      await expect(
        page.getByText("Statement unlocked successfully")
      ).toBeVisible();
      await expect(page.getByText("85,400.00of25,000.00")).toBeVisible();
      await expect(page.getByText("341.60%")).toBeVisible();
      // check in quota attainment
      await page.getByText("Quota Attainment").click();
      await expect(
        page.getByText(
          "Primary QuotaHalf-yearly85,400.00of25,000.00341.60%You're on fire!🔥You've"
        )
      ).toBeVisible();
      await managerObj.lockOrUnlockstatement();
      await expect(
        page.getByText("Statement locked successfully")
      ).toBeVisible();

      // Run Report ETL and Update datasheet
      await quotaED.navigate("settings/commissions-and-data-sync");
      await commonPage.expandMenu("Report ETL Run ETL for report", "7");
      await commonPage.runETLReportforAllpayeesAndPeriod(
        "Quota Attainment",
        false
      );
      await csPrevPage.runCommissions();
      await csPrevPage.clickSkipandRun();
      await csPrevPage.waitForReportETLSuccess();

      // Report Object validations
      // Quota Attainment sheet - Check Target value, Attained, Attained percent
      // Half yearly quota
      await managerObj.navigate("/databook");
      await managerObj.openDatabook("Report book");
      await page.waitForTimeout(3000);
      await managerObj.openDatasheet();
      await managerObj.updateDatasheet();
      await managerObj.addFilterinDatasheet(
        "Payee Email Id",
        "Contains",
        "<EMAIL>"
      );
      await expect(
        page.getByRole("gridcell", { name: "H2 2024 (Jul 2024 - Dec 2024)" })
      ).toBeVisible();
      // Target - quota value
      await expect(page.getByTestId("pt-25000").nth(3)).toBeVisible();
      // Quota Attained
      await expect(page.getByTestId("pt-85400")).toBeVisible();
      // Quota percentage
      await expect(page.getByRole("gridcell", { name: "341.6" })).toBeVisible();
    });

    test("Quarterly quota", async ({ adminPage }) => {
      test.info().annotations.push(
        {
          type: "Test ID",
          description: "INTER-T17592, ",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description: "User should be able to revert exit",
        }
      );
      // Test whether quota calculations are updated when we run the sync for locked period quota edited by enabling "Edit Locked Quota" flag for quarterly quota - quotapayee2
      const page = adminPage.page;
      const managerObj = new ManagerEffectiveEndDate(page);
      const quotaED = new QuotaAnnualED(page);
      const commonPage = new CommonUtils(page);
      const csPrevPage = new CommissionsSyncPrevPeriod(page);

      await managerObj.navigate("/quotas");
      await managerObj.clickExpandbuttoninQuotas("manager2");
      await managerObj.clickPayee("quota payee2");
      await managerObj.clickEditQuotabutton();
      await managerObj.editQuarterlyyQuotavalue("2000", "1");
      await page.getByRole("button", { name: "Save" }).click();
      await expect(page.getByText("Added/Modified Quota")).toBeVisible();
      await managerObj.navigate("/settings/commissions-and-data-sync");
      await csPrevPage.selectPayees("quota payee2");
      await csPrevPage.selectDate("30 Jun 2024");
      await csPrevPage.runCommissions();
      await expect(page.getByText("Notify when complete")).toHaveCount(1);
      await csPrevPage.clickSkipandRun();
      await csPrevPage.waitForCalculationMessage();
      await csPrevPage.waitForCommissionsSuccess();

      await setPayoutsDate(adminPage, "30-June-2024");
      await managerObj.navigate("/commissions");
      await managerObj.searchPayeeinPayouts("quota payee2");
      await managerObj.clickPayee("quota payee2");
      // payouts page
      await expect(page.getByText("20,300.00of1,000.00")).toBeVisible();
      await expect(page.getByText("2,030.00%")).toBeVisible();
      // Unlock the statement and check
      await managerObj.lockOrUnlockstatement();
      await expect(
        page.getByText("Statement unlocked successfully")
      ).toBeVisible();
      await expect(page.getByText("20,300.00of2,000.00")).toBeVisible();
      await expect(page.getByText("1,015.00%")).toBeVisible();
      // check in quota attainment
      await page.getByText("Quota Attainment").click();
      await expect(
        page.getByText(
          "Primary QuotaQuarterly20,300.00of2,000.001,015.00%You're on fire!🔥You've"
        )
      ).toBeVisible();
      await managerObj.lockOrUnlockstatement();
      await expect(
        page.getByText("Statement locked successfully")
      ).toBeVisible();

      // Run Report ETL and Update datasheet
      await quotaED.navigate("settings/commissions-and-data-sync");
      await commonPage.expandMenu("Report ETL Run ETL for report", "7");
      await commonPage.runETLReportforAllpayeesAndPeriod(
        "Quota Attainment",
        false
      );
      await csPrevPage.runCommissions();
      await csPrevPage.clickSkipandRun();
      await csPrevPage.waitForReportETLSuccess();

      // Report Object validations
      // Quota Attainment sheet - Check Target value, Attained, Attained percent
      // Quarterly quota
      await managerObj.navigate("/databook");
      await managerObj.openDatabook("Report book");
      await page.waitForTimeout(3000);
      await managerObj.openDatasheet();
      await managerObj.updateDatasheet();
      await managerObj.addFilterinDatasheet(
        "Payee Email Id",
        "Contains",
        "<EMAIL>"
      );
      await expect(
        page.getByRole("gridcell", { name: "Q2 2024 (Apr 2024 - Jun 2024)" })
      ).toBeVisible();
      // Target - quota value
      await expect(page.getByTestId("pt-2000")).toBeVisible();
      // Quota Attained
      await expect(page.getByTestId("pt-20300")).toBeVisible();
      // Quota percentage
      await expect(page.getByRole("gridcell", { name: "1015" })).toBeVisible();
    });

    test("Monthly quota", async ({ adminPage }) => {
      test.info().annotations.push(
        {
          type: "Test ID",
          description: "INTER-T17814, ",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description: "User should be able to revert exit",
        }
      );
      // Test whether quota calculations are updated when we run the sync for locked period quota edited by enabling "Edit Locked Quota" flag for monthly quota - quota payee1
      const page = adminPage.page;
      const quotaED = new QuotaAnnualED(page);
      const commonPage = new CommonUtils(page);
      const csPrevPage = new CommissionsSyncPrevPeriod(page);
      const managerObj = new ManagerEffectiveEndDate(page);

      await managerObj.navigate("quotas");
      await managerObj.clickPlusbuttoninQuotas();
      await managerObj.clickPayee("quota payee1");
      await managerObj.clickEditQuotabutton();
      await managerObj.editMonthlyQuotavalue("1000", "6");
      await page.getByRole("button", { name: "Save" }).click();
      await expect(page.getByText("Added/Modified Quota")).toBeVisible();
      await managerObj.navigate("/settings/commissions-and-data-sync");
      await csPrevPage.selectPayees("quota payee1");
      await csPrevPage.selectDate("31 May 2024");
      await csPrevPage.runCommissions();
      await expect(page.getByText("Notify when complete")).toHaveCount(1);
      await csPrevPage.clickSkipandRun();
      await csPrevPage.waitForCalculationMessage();
      await csPrevPage.waitForCommissionsSuccess();

      await setPayoutsDate(adminPage, "31-May-2024");
      await managerObj.navigate("/commissions");
      await managerObj.searchPayeeinPayouts("quota payee1");
      await managerObj.clickPayee("quota payee1");
      // payouts page
      await expect(page.getByText("10,100.00of2,000.00")).toBeVisible();
      await expect(page.getByText("505.00%")).toBeVisible();
      await managerObj.lockOrUnlockstatement();
      await expect(
        page.getByText("Statement unlocked successfully")
      ).toBeVisible();
      await expect(page.getByText("10,100.00of1,000.00")).toBeVisible();
      await expect(page.getByText("1,010.00%")).toBeVisible();
      await page.getByText("Quota Attainment").click();
      await expect(
        page.getByText(
          "Primary QuotaMonthly10,100.00of1,000.001,010.00%You're on fire!🔥You've"
        )
      ).toBeVisible();
      await managerObj.lockOrUnlockstatement();
      await expect(
        page.getByText("Statement locked successfully")
      ).toBeVisible();

      // Run Report ETL and Update datasheet
      await quotaED.navigate("settings/commissions-and-data-sync");
      await commonPage.expandMenu("Report ETL Run ETL for report", "7");
      await commonPage.runETLReportforAllpayeesAndPeriod(
        "Quota Attainment",
        false
      );
      await csPrevPage.runCommissions();
      await csPrevPage.clickSkipandRun();
      await csPrevPage.waitForReportETLSuccess();

      // Report Object validations
      // Quota Attainment sheet - Check Target value, Attained, Attained percent
      // Monthly quota
      await managerObj.navigate("/databook");
      await managerObj.openDatabook("Report book");
      await page.waitForTimeout(3000);
      await managerObj.openDatasheet();
      await managerObj.updateDatasheet();
      await managerObj.addFilterinDatasheet(
        "Payee Email Id",
        "Contains",
        "<EMAIL>"
      );
      // Quota Attained
      await expect(page.getByTestId("pt-10100")).toBeVisible();
      // Quota percentage
      await expect(page.getByRole("gridcell", { name: "1010" })).toBeVisible();
    });

    test("payout value when Impersonated ", async ({ adminPage }) => {
      test.info().annotations.push(
        {
          type: "Test ID",
          description: "INTER-T17817,INTER-T17816 ",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description: "User should be able to revert exit",
        }
      );
      const page = adminPage.page;
      const managerObj = new ManagerEffectiveEndDate(page);
      const revertexit = new Revertexitpage(page);
      await revertexit.gotoUsersPage();
      await revertexit.searchUser("<EMAIL>");
      await revertexit.impersonateUser();
      // May 2024 Statement
      await managerObj.navigate(
        "/statements/eyJwYXllZUVtYWlsSWQiOiJxdW90YXBheWVlMUBjcmljay5jb20iLCJwc2QiOiIyMDI0LTA1LTAxIiwicGVkIjoiMjAyNC0wNS0zMSJ9"
      );
      // await managerObj.selectMonthInStatement("May 2024");
      await page.waitForTimeout(3000);
      await expect(page.getByText("10,100.00of1,000.00")).toBeVisible();
      await expect(page.getByText("1,010.00%")).toBeVisible();
      await page.getByText("Quota Attainment").click();
      await expect(
        page.getByText(
          "Primary QuotaMonthly10,100.00of1,000.001,010.00%You're on fire!🔥You've"
        )
      ).toBeVisible();
    });
  }
);
