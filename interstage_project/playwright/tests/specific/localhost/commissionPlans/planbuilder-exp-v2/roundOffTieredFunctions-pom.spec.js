const RoundOffTieredFuncPage = require("../../../../../test-objects/roundOffTieredFunc-objects.js");
const {
  roundOffTierFixtures: { test, expect },
} = require("../../../../fixtures");

test.beforeEach(async ({ adminPage }) => {
  const roundOffTierPage = new RoundOffTieredFuncPage(adminPage.page);
  await roundOffTierPage.beforeEachTests();
});

test.describe(
  "Round Off Tiered Functions",
  { tag: ["@regression", "@statements", "@repconnect-2"] },
  () => {
    test("Not to allow for row level calculations", async ({ adminPage }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-8984" },
        {
          type: "Description",
          description: "Row level calculations are not allowed",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description:
            "Validation should fail and error tooltip should be shown",
        }
      );
      const roundOffTierPage = new RoundOffTieredFuncPage(adminPage.page);
      await roundOffTierPage.goToPlans();
      await roundOffTierPage.selectPlanAndCriteria("Draft plan", "Simple Tier");
      await roundOffTierPage.modifyTierFormula();
      await roundOffTierPage.tooltipValidate(
        1,
        "Rounding off in tier functions is not allowed for row level calculations"
      );
    });

    test("Total Payout - Statements", async ({ adminPage }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-8984" },
        {
          type: "Description",
          description: "Validate the total payout in statements",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description: "Total payout using rounding off should be accurate",
        }
      );
      const roundOffTierPage = new RoundOffTieredFuncPage(adminPage.page);
      await roundOffTierPage.selectPayee("R-off Payee1");
      await roundOffTierPage.validateTotalPayout("AU$41.66");
    });

    test("Simple Tier - Statements", async ({ adminPage }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-8984" },
        {
          type: "Description",
          description:
            "Validate that the payouts in statements generated should be same with rounding off logic for simple tier component",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description:
            "Payout should match the logic used for simple tier component",
        }
      );
      const roundOffTierPage = new RoundOffTieredFuncPage(adminPage.page);
      await roundOffTierPage.selectPayee("R-off Mgr1");
      await roundOffTierPage.validateSimpleTier("CA$11.02");
      await roundOffTierPage.compLogic(3.673256);
      await roundOffTierPage.goToTrace("Simple Tier", 0);
      await roundOffTierPage.getTraceValue("Tier 0", "Simple Tier");
      await roundOffTierPage.verifyValueST();
    });

    test("Cond Tier - Statements", async ({ adminPage }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-8984" },
        {
          type: "Description",
          description:
            "Validate that the payouts in statements generated should be same with rounding off logic for conditional tier component",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description:
            "Payout should match the logic used for conditional tier component",
        }
      );
      const roundOffTierPage = new RoundOffTieredFuncPage(adminPage.page);
      await roundOffTierPage.selectPayee("R-off Mgr1");
      await roundOffTierPage.validateCondTier("CA$11.02");
      await roundOffTierPage.compLogic(3.673256);
      await roundOffTierPage.goToTrace("Cond Tier", 0);
      await roundOffTierPage.getTraceValue("Tier 1", "Cond Tier");
      await roundOffTierPage.verifyValueCT();
    });

    test("Simple Quota - Statements", async ({ adminPage }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-8984" },
        {
          type: "Description",
          description:
            "Validate that the payouts in statements generated should be same with rounding off logic for simple quota component",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description:
            "Payout should match the logic used for simple quota component",
        }
      );
      const roundOffTierPage = new RoundOffTieredFuncPage(adminPage.page);
      await roundOffTierPage.selectPayee("R-off Mgr1");
      await roundOffTierPage.validateSimpleQuota("CA$7.34");
      await roundOffTierPage.goToTrace("Simple Quota", 0);
      await roundOffTierPage.getTraceValue("Tier 0", "Simple Quota");
      await roundOffTierPage.verifyValueSQ();
    });

    test("Cond Quota - Statements", async ({ adminPage }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-8984" },
        {
          type: "Description",
          description:
            "Validate that the payouts in statements generated should be same with rounding off logic for conditional quota component",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description:
            "Payout should match the logic used for conditional quota component",
        }
      );
      const roundOffTierPage = new RoundOffTieredFuncPage(adminPage.page);
      await roundOffTierPage.selectPayee("R-off Mgr1");
      await roundOffTierPage.validateCondQuota("CA$7.34");
      await roundOffTierPage.goToTrace("Cond Quota", 0);
      await roundOffTierPage.getTraceValue("Tier 0", "Cond Quota");
      await roundOffTierPage.verifyValueCQ();
    });

    test("PT Simple Tier - Statements", async ({ adminPage }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-8984" },
        {
          type: "Description",
          description:
            "Validate the payout for team simple tier in statements match with the generated amount",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description: "Payout using rounding off should be accurate",
        }
      );
      const roundOffTierPage = new RoundOffTieredFuncPage(adminPage.page);
      await roundOffTierPage.selectPayee("R-off Mgr1");
      await roundOffTierPage.validatePTSimpleTier("CA$20.04");
    });

    test("PR Cond Tier - Statements", async ({ adminPage }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-8984" },
        {
          type: "Description",
          description:
            "Validate the payout for team simple tier in statements match with the generated amount",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description: "Payout using rounding off should be accurate",
        }
      );
      const roundOffTierPage = new RoundOffTieredFuncPage(adminPage.page);
      await roundOffTierPage.selectPayee("R-off Mgr1");
      await roundOffTierPage.validatePRCondTier("CA$8.02");
    });

    test("PR Simple Quota - Statements", async ({ adminPage }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-8984" },
        {
          type: "Description",
          description:
            "Validate the payout for team simple tier in statements match with the generated amount",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description: "Payout using rounding off should be accurate",
        }
      );
      const roundOffTierPage = new RoundOffTieredFuncPage(adminPage.page);
      await roundOffTierPage.selectPayee("R-off Mgr1");
      await roundOffTierPage.validatePRSimpleQuota("CA$5.74");
    });

    test("PT Cond Quota - Statements", async ({ adminPage }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-8984" },
        {
          type: "Description",
          description:
            "Validate the payout for team simple tier in statements match with the generated amount",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description: "Payout using rounding off should be accurate",
        }
      );
      const roundOffTierPage = new RoundOffTieredFuncPage(adminPage.page);
      await roundOffTierPage.selectPayee("R-off Mgr1");
      await roundOffTierPage.validatePTCondQuota("-CA$12.38");
    });

    test("Simple Tier Trace", async ({ adminPage }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-8984" },
        {
          type: "Description",
          description:
            "Validate that the trace for simple tier in statements should be accurate and visible",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description: "Trace should be displayed for simple tier component",
        }
      );
      const roundOffTierPage = new RoundOffTieredFuncPage(adminPage.page);
      await roundOffTierPage.selectPayee("R-off Mgr1");
      await roundOffTierPage.goToTrace("Simple Tier", 0);
      const traceElement = await roundOffTierPage.tierTraceFormat(
        "Tier 0",
        "4",
        "Tiered value rounded by 0",
        null
      );
      await expect(adminPage.page).toHaveScreenshot("SimpleTierTrace.png", {
        maxDiffPixelRatio: 0.02,
      });
      await expect(traceElement).toHaveText(
        "Tiered value rounded by 0 decimal places"
      );
    });

    test("Cond Tier Trace", async ({ adminPage }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-8984" },
        {
          type: "Description",
          description:
            "Validate that the trace for conditional tier in statements should be accurate and visible",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description:
            "Trace should be displayed for conditional tier component",
        }
      );
      const roundOffTierPage = new RoundOffTieredFuncPage(adminPage.page);
      await roundOffTierPage.selectPayee("R-off Mgr1");
      await roundOffTierPage.goToTrace("Cond Tier", 0);
      const traceElement = await roundOffTierPage.tierTraceFormat(
        "Tier 1",
        "3.60",
        "Tiered value rounded down by 1",
        null
      );
      await expect(adminPage.page).toHaveScreenshot("CondTierTrace.png", {
        maxDiffPixelRatio: 0.02,
      });
      await expect(traceElement).toHaveText(
        "Tiered value rounded down by 1 decimal places"
      );
    });

    test("Simple Quota Trace", async ({ adminPage }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-8984" },
        {
          type: "Description",
          description:
            "Validate that the trace for simple quota in statements should be accurate and visible",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description: "Trace should be displayed for simple quota component",
        }
      );
      const roundOffTierPage = new RoundOffTieredFuncPage(adminPage.page);
      await roundOffTierPage.selectPayee("R-off Mgr1");
      await roundOffTierPage.goToTrace("Simple Quota", 0);
      const traceElement = await roundOffTierPage.tierTraceFormat(
        "Tier 0",
        "100%",
        "Tiered percentage rounded up by 0 decimal places",
        null
      );
      await expect(traceElement).toHaveText(
        "Tiered percentage rounded up by 0 decimal places"
      );
      await roundOffTierPage.tierPercentTraceView("100%Tier percentage");
      await expect(adminPage.page).toHaveScreenshot("SimpleQuotaTrace.png", {
        maxDiffPixelRatio: 0.1,
      });
    });

    test("Cond Quota Trace", async ({ adminPage }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-8984" },
        {
          type: "Description",
          description:
            "Validate that the trace for conditional quota in statements should be accurate and visible",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description:
            "Trace should be displayed for conditional quota component",
        }
      );
      const roundOffTierPage = new RoundOffTieredFuncPage(adminPage.page);
      await roundOffTierPage.selectPayee("R-off Mgr1");
      await roundOffTierPage.goToTrace("Cond Quota", 0);
      const traceElement = await roundOffTierPage.tierTraceFormat(
        "Tier 0",
        "100%",
        "Tiered percentage rounded up by 0 decimal places",
        null
      );
      await expect(traceElement).toHaveText(
        "Tiered percentage rounded up by 0 decimal places"
      );
      await roundOffTierPage.tierPercentTraceView("100%Tier percentage");
      await expect(adminPage.page).toHaveScreenshot("CondQuotaTrace.png", {
        maxDiffPixelRatio: 0.2,
      });
    });
  }
);
