const { test } = require("@playwright/test");
const fs = require("fs");
const { chromium } = require("playwright");
const collectionFilePath =
  "./Postman/Everstage-QA-API-Tests.postman_collection.json";
const cpqCollectionFilePath =
  "./Postman/Everstage-CPQ-API-Tests.postman_collection.json";
let newBrowser, newContext, newPageForTest;

test.beforeEach(async () => {
  newBrowser = await chromium.launch();
  newContext = await newBrowser.newContext();
  const savedCookies = JSON.parse(fs.readFileSync("./cookies.json", "utf8"));
  await newContext.addCookies(savedCookies);
  newPageForTest = await newContext.newPage();
});

test.describe("API TOKEN CHECK", () => {
  test("Everstage - Z Access token", async () => {
    await newPageForTest.route("/spm/session-induction", async (route) => {
      await route.continue();
    });

    await newPageForTest.goto("https://qa.everstage.com/");
    await newPageForTest.getByLabel("Email").click();
    await newPageForTest.getByLabel("Email").fill("<EMAIL>");
    await newPageForTest
      .getByRole("button", { name: "Continue", exact: true })
      .click();
    await newPageForTest.getByLabel("Password").fill("o^/wp6sG(");
    await newPageForTest.getByRole("button", { name: "Continue" }).click();

    const response = await newPageForTest.waitForResponse((response) =>
      response.url().includes("/spm/session-induction")
    );

    const headers = response.request().headers();
    if (headers.authorization) {
      const accessToken = headers.authorization.replace("Bearer ", ""); // Remove "Bearer"
      console.log("Access Token:", accessToken);
      await updatePostmanToken(
        accessToken,
        collectionFilePath,
        "accessToken_Z_Access_APIs"
      );
    } else {
      console.log("No Access Token found in headers");
    }
  });
  test("Everstage - TSAR Access token", async () => {
    await newPageForTest.goto("https://qa.everstage.com/admin-ui/customers", {
      waitUntil: "networkidle",
    });
    await newPageForTest.getByRole("button", { name: "Login" }).click();
    const isLoggedIn = await newPageForTest
      .locator("//h1[text()='Welcome to Everstage']")
      .isVisible();
    if (isLoggedIn) {
      console.log("Found Continue With Google Page...");
      await newPageForTest
        .getByRole("button", { name: "Continue with Google" })
        .click();
      await newPageForTest.waitForLoadState("networkidle");
    }
    await newPageForTest.reload();
    const response = await newPageForTest.waitForResponse((response) =>
      response.url().includes("/everstage_admin/session-induction")
    );
    const headers = response.request().headers();
    if (headers.authorization) {
      const token = headers.authorization.replace("Bearer ", ""); // Remove "Bearer"
      console.log(token);
      await updatePostmanToken(
        token,
        collectionFilePath,
        "accessToken_TSAR_APIs"
      );
    } else {
      console.log("No Access Token found in headers");
    }
  });

  test("Get Access Token for CPQ", async () => {
    await newPageForTest.route("/spm/session-induction", async (route) => {
      await route.continue();
    });

    await newPageForTest.goto("http://localhost:3000/cpq/quotes/");
    await newPageForTest.getByLabel("Email").click();
    await newPageForTest
      .getByLabel("Email")
      .fill("<EMAIL>");
    await newPageForTest
      .getByRole("button", { name: "Continue", exact: true })
      .click();
    await newPageForTest.getByLabel("Password").fill("!h~hc2PO7");
    await newPageForTest.getByRole("button", { name: "Continue" }).click();
    const response = await newPageForTest.waitForResponse((response) =>
      response.url().includes("/spm/session-induction")
    );

    const headers = response.request().headers();
    if (headers.authorization) {
      const accessToken = headers.authorization.replace("Bearer ", ""); // Remove "Bearer"
      console.log("Access Token:", accessToken);
      await updatePostmanToken(
        accessToken,
        cpqCollectionFilePath,
        "Access Token"
      );
    } else {
      console.log("No Access Token found in headers");
    }
  });
});

async function updatePostmanToken(token, collectionFilePath, tokenName) {
  console.log("Current Working Directory:", process.cwd());

  try {
    // Read the Postman collection file
    const collection = JSON.parse(fs.readFileSync(collectionFilePath, "utf8"));

    // Check if the collection has variables
    if (collection.variable) {
      // Find the 'accessToken' variable
      const tokenVariable = collection.variable.find(
        (v) => v.key === tokenName
      );

      if (tokenVariable) {
        // Update the value of 'accessToken' with the new token
        console.log(`Updating existing accessToken with value: ${token}`);
        tokenVariable.value = token;
      } else {
        // Log a warning if 'accessToken' is not found
        console.warn(
          "accessToken variable not found in the Postman collection."
        );
      }
    } else {
      // Log an error if variables are not present in the collection
      console.error("No variables found in the Postman collection.");
    }

    // Write the updated collection back to the file
    fs.writeFileSync(
      collectionFilePath,
      JSON.stringify(collection, null, 2),
      "utf8"
    );
    console.log(
      "Postman collection updated successfully with new accessToken."
    );
  } catch (error) {
    // Handle any errors in reading or writing the file
    console.error("Error updating Postman collection:", error);
  }
}
