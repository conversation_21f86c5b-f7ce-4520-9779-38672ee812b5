import { setupGraphQLRouteInterceptor } from "../../../bearerToken";
import GlobalSearch from "../../../../test-objects/globalSearch-objects";
import UserPage from "../../../../test-objects/user-objects";

const {
  globalSearchRevampFixtures: { test, expect },
} = require("../../../fixtures");

test.describe(
  "Gloabl Search Real time Sync",
  { tag: ["@regression", "@globalsearch", "@launchpad-1"] },
  () => {
    test(
      "UserName validation",
      {
        annotation: [
          {
            type: "TestID",
            description: "INTER-T16521,INTER-T16522 ",
          },
          {
            type: "Description",
            description:
              "Validate whether the edited username appears in the Spotlight Search modal search results",
          },
          { type: "Precondition", description: "Users" },
          {
            type: "Expected Behaviour",
            description:
              "The Search modal should display Edited username - First Name and Last Name",
          },
        ],
      },
      async ({ adminPage }) => {
        const globalSearch = new GlobalSearch(adminPage.page);
        await globalSearch.navigate("/users");
        const editedFirstName = await globalSearch.editUserName(
          "<EMAIL>",
          "firstName"
        );
        await globalSearch.openSearchModalv2();
        await globalSearch.searchUserGlobal(editedFirstName);
        await globalSearch.validateGlobalSearchUsername(editedFirstName);
        await globalSearch.closeSearchModal();
        const editedLastName = await globalSearch.editUserName(
          "<EMAIL>",
          "lastName"
        );
        await globalSearch.openSearchModalv2();
        await globalSearch.searchUserGlobal(editedLastName);
        await globalSearch.validateGlobalSearchUsername(editedLastName);
        await globalSearch.closeSearchModal();
      }
    );

    test(
      "User status - Marked for Exit and Inactive validation",
      {
        annotation: [
          {
            type: "TestID",
            description: "INTER-T16523, INTER-T16524,INTER-T16533 ",
          },
          {
            type: "Description",
            description:
              "Validate whether the User status - Marked for Exit and Inactive appears in the Spotlight Search modal search results",
          },
          { type: "Precondition", description: "Users" },
          {
            type: "Expected Behaviour",
            description:
              "The Search modal should display modified user with their changed user status ",
          },
        ],
      },
      async ({ adminPage }) => {
        const globalSearch = new GlobalSearch(adminPage.page);
        const userEmail = "<EMAIL>";
        try {
          await globalSearch.navigate("/users");
          await globalSearch.initiateExit(userEmail, "Marked for exit");
          await globalSearch.openSearchModalv2();
          await globalSearch.searchUserGlobal(userEmail);
          await globalSearch.validateGlobalSearchStatus("Marked for exit");
          await globalSearch.closeSearchModal();
          await globalSearch.revertExit(userEmail);
          await globalSearch.openSearchModalv2();
          await globalSearch.searchUserGlobal(userEmail);
          await globalSearch.validateGlobalSearchStatus("Added");
          await globalSearch.closeSearchModal();
          await globalSearch.initiateExit(userEmail, "Inactive");
          await globalSearch.openSearchModalv2();
          await globalSearch.searchUserGlobal(userEmail);
          await globalSearch.validateGlobalSearchStatus("Inactive");
          await globalSearch.closeSearchModal();
          await globalSearch.revertExit(userEmail);
          await globalSearch.openSearchModalv2();
          await globalSearch.searchUserGlobal(userEmail);
          await globalSearch.validateGlobalSearchStatus("Added");
          await globalSearch.closeSearchModal();
        } catch (error) {
          await globalSearch.navigate("/users");
          try {
            await globalSearch.revertExit(userEmail);
          } catch (error) {}
          throw new Error("Error Message :" + error);
        }
      }
    );

    test(
      "Power Admin Firstname and Lastname Validation ",
      {
        annotation: [
          {
            type: "TestID",
            description: "INTER-T16534,INTER-T16535 ",
          },
          {
            type: "Description",
            description:
              "Validate whether editing the First name/Last Name of Power Admin from Profile Settings is reflected in the global search results in real-time sync",
          },
          { type: "Precondition", description: "Users" },
          {
            type: "Expected Behaviour",
            description:
              "The Search modal should display modified First name/Last Name of Power Admin",
          },
        ],
      },
      async ({ adminPage }) => {
        const globalSearch = new GlobalSearch(adminPage.page);
        await globalSearch.navigate("/profile-settings");
        const editedFirstName = await globalSearch.editUserNamePowerAdmin(
          "firstname"
        );
        await globalSearch.openSearchModalv2();
        await globalSearch.searchUserGlobal(editedFirstName);
        await globalSearch.validateGlobalSearchUsername(editedFirstName);
        await globalSearch.closeSearchModal();
        await globalSearch.navigate("/profile-settings");
        const editedLastName = await globalSearch.editUserNamePowerAdmin(
          "lastname"
        );
        await globalSearch.openSearchModalv2();
        await globalSearch.searchUserGlobal(editedLastName);
        await globalSearch.validateGlobalSearchUsername(editedLastName);
        await globalSearch.closeSearchModal();
      }
    );

    test(
      "User status - Added and Invited Validation",
      {
        annotation: [
          {
            type: "TestID",
            description: "INTER-T16529,INTER-T16530",
          },
          {
            type: "Description",
            description:
              "Validate whether the User status - Added and Invited appears in the Spotlight Search modal search results",
          },
          { type: "Precondition", description: "Users" },
          {
            type: "Expected Behaviour",
            description:
              "The Search modal should display modified user with their changed user status ",
          },
        ],
      },
      async ({ adminPage, request }) => {
        const page = adminPage.page;
        const globalSearch = new GlobalSearch(page);
        const userPage = new UserPage(page);
        const userEmail = "<EMAIL>";
        let token = await setupGraphQLRouteInterceptor(page);
        try {
          await globalSearch.navigate("/users");
          await globalSearch.closeHRISAlert();
          await userPage.navigateToNewUserForm();
          await userPage.fillEmail(userEmail);
          await userPage.fillFirstName("jeeva new");
          await userPage.fillLastName("new");
          await userPage.selectRole("Payee");
          await userPage.submitNewUser();
          await userPage.verifyToastMessage();
          await globalSearch.openSearchModalv2();
          await globalSearch.searchUserGlobal(userEmail);
          await globalSearch.validateGlobalSearchStatus("Added");
          await globalSearch.closeSearchModal();
          await globalSearch.inviteUser(userEmail);
          await globalSearch.openSearchModalv2();
          await globalSearch.searchUserGlobal(userEmail);
          await globalSearch.validateGlobalSearchStatus("Invited");
          await globalSearch.closeSearchModal();
          let userEmailArray = [];
          userEmailArray.push(userEmail);
          await globalSearch.invalidateUserAPI(request, token, userEmailArray);
        } catch (error) {
          console.log("Running error Block...");
          let userEmailArray = [];
          userEmailArray.push(userEmail);
          await globalSearch.invalidateUserAPI(request, token, userEmailArray);
          throw new Error("Error Message :" + error);
        }
      }
    );

    test(
      "Bulk User Create and Update Validation",
      {
        annotation: [
          {
            type: "TestID",
            description: "INTER-T16531,INTER-T16532",
          },
          {
            type: "Description",
            description:
              "Validate whether the User appears in the Spotlight Search modal search results when created through Bulk",
          },
          { type: "Precondition", description: "Users" },
          {
            type: "Expected Behaviour",
            description: "The Search modal should display Created users",
          },
        ],
      },
      async ({ adminPage, request }, testInfo) => {
        testInfo.setTimeout(testInfo.timeout + 100000);
        const page = adminPage.page;
        const globalSearch = new GlobalSearch(adminPage.page);
        let token = await setupGraphQLRouteInterceptor(page);
        try {
          await page.pause();
          await globalSearch.navigate("/users");
          await globalSearch.closeHRISAlert();
          await globalSearch.createBulkUsers();
          await globalSearch.validateBulkUsers("Bulk User Create.csv");
          await globalSearch.bulkUpdateUsers("Bulk Users Update firstName.csv");
          await globalSearch.validateBulkUsers(
            "Bulk Users Update firstName.csv"
          );
          await globalSearch.bulkUpdateUsers("Bulk Users Update lastName.csv");
          await globalSearch.validateBulkUsers(
            "Bulk Users Update lastName.csv"
          );
          await globalSearch.invalidateBulkUserAPI(
            request,
            token,
            "Bulk User Create.csv"
          );
        } catch (error) {
          console.log("Running error Block...");
          await globalSearch.invalidateBulkUserAPI(
            request,
            token,
            "Bulk User Create.csv"
          );
          throw new Error("Error Message :" + error);
        }
      }
    );

    test.skip(
      "Validate whether the User status - Marked for Deactivation and Pending Exit appears in the Spotlight Search modal search results",
      {
        annotation: [
          {
            type: "TestID",
            description: "INTER-T16525,INTER-T16526 ,INTER-T16537",
          },
          {
            type: "Description",
            description:
              "Validate whether the User status - Marked for Deactivation and Pending Exit appears in the Spotlight Search modal search results",
          },
          { type: "Precondition", description: "Users" },
          {
            type: "Expected Behaviour",
            description:
              "The Search modal should dispay the users with the updated status",
          },
        ],
      },
      async ({ adminPage, request }, testInfo) => {
        testInfo.setTimeout(testInfo.timeout + 600000);
        const page = adminPage.page;
        const globalSearch = new GlobalSearch(page);
        let token = await setupGraphQLRouteInterceptor(page);
        await globalSearch.processAllFiles();
        try {
          await globalSearch.manageData(
            "Create",
            "HRIS Employee Bulk Data without Deactivation Date.csv",
            "employee - obj",
            true
          );
          await globalSearch.manageData(
            "Create",
            "HRIS Payroll Bulk Data.csv",
            "payroll-obj"
          );
          await globalSearch.updateDataSheet();
          await globalSearch.importHRISRecord1(
            "HRIS Employee Bulk Data without Deactivation Date.csv",
            true
          );
          await globalSearch.validateBulkStatus(
            "HRIS Employee Bulk Data without Deactivation Date.csv"
          );
          await globalSearch.manageData(
            "Update",
            "HRIS Employee Bulk Data.csv",
            "employee - obj"
          );
          await globalSearch.updateDataSheet();
          await globalSearch.importHRISRecord1(
            "HRIS Employee Bulk Data without Deactivation Date.csv"
          );
          await globalSearch.validateBulkHRISStatus(
            "HRIS Employee Bulk Data without Deactivation Date.csv"
          );
          await globalSearch.manageData(
            "Delete",
            "HRIS Delete Data Employee object.csv",
            "employee - obj"
          );
          await globalSearch.manageData(
            "Delete",
            "HRIS Delete Data Payroll object.csv",
            "payroll-obj"
          );
          await globalSearch.updateDataSheet();
          await globalSearch.invalidateBulkUserAPI(
            request,
            token,
            "HRIS Employee Bulk Data without Deactivation Date.csv"
          );
        } catch (error) {
          console.log("Running error Block...");
          await globalSearch.manageData(
            "Delete",
            "HRIS Delete Data Employee object.csv",
            "employee - obj"
          );
          await globalSearch.manageData(
            "Delete",
            "HRIS Delete Data Payroll object.csv",
            "payroll-obj"
          );
          await globalSearch.updateDataSheet();
          await globalSearch.invalidateBulkUserAPI(
            request,
            token,
            "HRIS Employee Bulk Data without Deactivation Date.csv"
          );
          throw new Error("Error Message :" + error);
        }
      }
    );
  }
);
