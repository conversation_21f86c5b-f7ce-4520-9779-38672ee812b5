import React, { useState } from "react";
import { observer } from "mobx-react";
import { Button, Modal, showMessage } from "Components";
import { NOTIFICATION_TYPE } from "Enums";

const MessageModal = ({
  isOpen,
  setIsOpen,
  onFinish,
  successMsg,
  failureMsg,
  modalContent,
}) => (
  <Modal open={isOpen} rootClassName="max-w-md w-full">
    <Modal.Title onClose={() => setIsOpen(false)}></Modal.Title>
    <Modal.Content>{modalContent}</Modal.Content>
    <Modal.Footer
      hasCancel
      hasConfirm
      onCancel={() => setIsOpen(false)}
      onConfirm={() => {
        const resp = onFinish();
        showMessage(
          resp ? "Completed" : "Failed",
          resp
            ? {
                description: successMsg,
                type: NOTIFICATION_TYPE.SUCCESS,
              }
            : {
                description: failureMsg,
                type: NOTIFICATION_TYPE.ERROR,
              }
        );
        setIsOpen(false);
      }}
    ></Modal.Footer>
  </Modal>
);

const DeploymentPage = observer(
  ({ onNotifyDeploymentFinish = () => {}, onRevertNotifyDeployment = () => {} }) => {
    const [notifyModalOpen, setNotifyModalOpen] = useState(false);
    const [revertModalOpen, setRevertModalOpen] = useState(false);

    return (
      <>
        <MessageModal
          isOpen={notifyModalOpen}
          setIsOpen={setNotifyModalOpen}
          onFinish={onNotifyDeploymentFinish}
          successMsg="Deployment finish notified successfully"
          failureMsg="Failed to notify deployment finish"
          modalContent={
            "Are you sure you want to notify deployment finish? This will send a broadcast message to all logged in users."
          }
        />

        <MessageModal
          isOpen={revertModalOpen}
          setIsOpen={setRevertModalOpen}
          onFinish={onRevertNotifyDeployment}
          successMsg="Message reverted successfully"
          failureMsg="Failed to revert message"
          modalContent={"Are you sure you want to revert the message?"}
        />

        <div className="flex flex-col h-full w-full justify-center items-center gap-4">
          <div className="w-2/4 bg-ever-base shadow p-4 rounded-lg">
            <p className="text-s font-medium">
              Clicking this button will show a banner to all logged in users of
              our application. This button is meant to be clicked right after
              the deployment of our application to get the latest changes.
            </p>
          </div>
          <Button onClick={() => setNotifyModalOpen(true)}>
            Notify Deployment Finish
          </Button>
          <Button onClick={() => setRevertModalOpen(true)}>
            Revert Notify Deployment
          </Button>
        </div>
      </>
    );
  }
);

export default DeploymentPage;
