import React, {
  useState,
  useEffect,
  useRef,
  useMemo,
  useCallback,
  memo,
} from "react";
import { useQuery, gql, useLazyQuery } from "@apollo/client";
import {
  Select,
  TreeSelect,
  Button,
  message,
  Spin,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Modal,
} from "antd";
import { InfoCircleOutlined } from "@ant-design/icons";
import { useAuthStore } from "GlobalStores/AuthStore";
import { AgGridReact } from "ag-grid-react";
import "ag-grid-community/styles/ag-grid.css";
import "ag-grid-community/styles/ag-theme-alpine.css";

// Constants
const ROLE_TYPES = {
  ALL_ROLES: "all_roles",
  ROLES_WITH_PARENT: "roles_with_parent",
  ALL_SUPER_ADMINS: "all_super_admins",
  ALL_POWER_ADMINS: "all_power_admins",
  ALL_PAYEES: "all_payees",
  ALL_ADMINS: "all_admins",
  ALL_PLAN_DESIGNERS: "all_plan_designers",
};

const CLIENT_TYPES = {
  ALL_CLIENTS: "all_clients",
};

const VALIDATION_TYPES = {
  SUCCESS: "success",
  WARNING: "warning",
  ERROR: "error",
};

const ROLE_TYPE_MAPPING = {
  "super admins": "Super Admin",
  "power admins": "Power Admin",
  admins: "Admin",
  payees: "Payee",
  "plan designers": "Plan Designer",
};

const VALIDATION_COLORS = {
  [VALIDATION_TYPES.SUCCESS]: "#52c41a",
  [VALIDATION_TYPES.WARNING]: "#faad14",
  [VALIDATION_TYPES.ERROR]: "#ff4d4f",
};

const ROLE_DISPLAY_NAMES = {
  [ROLE_TYPES.ALL_ROLES]: "All Roles",
  [ROLE_TYPES.ROLES_WITH_PARENT]: "All Roles with parent permission",
  [ROLE_TYPES.ALL_SUPER_ADMINS]: "All Super Admins",
  [ROLE_TYPES.ALL_POWER_ADMINS]: "All Power Admins",
  [ROLE_TYPES.ALL_PAYEES]: "All Payees",
  [ROLE_TYPES.ALL_ADMINS]: "All Admins",
  [ROLE_TYPES.ALL_PLAN_DESIGNERS]: "All Plan Designers",
};

// UI Constants
const UI_CONSTANTS = {
  TEXT_TRUNCATION: {
    CLIENT_ROLE: 20,
    PERMISSIONS: 60,
    COMMENTS: 50,
  },
  RECENT_WORKFLOWS_LIMIT: 3,
  MIN_SEARCH_CHARACTERS: 3,
  GRID_PAGINATION_SIZE: 20,
  GRID_ROW_HEIGHT: 32,
  GRID_HEADER_HEIGHT: 36,
};

// GraphQL Queries
const GET_CLIENTS = gql`
  query GetAllClients {
    allClients {
      clientId
      name
    }
  }
`;

const GET_ROLES = gql`
  query GetClientRoles($clientIds: [Int]) {
    clientRoles(clientIds: $clientIds) {
      rolePermissionId
      displayName
      clientId
      permissions
      description
    }
  }
`;

const GET_ALL_PERMISSIONS = gql`
  query GetAllPermissions($clientId: Int) {
    allPermissions(clientId: $clientId) {
      id
      name
      description
      parentId
      componentSystemName
      showDataPermissions
    }
  }
`;

const GET_BATCH_CLIENT_PERMISSIONS = gql`
  query GetBatchClientPermissions($clientIds: [Int]!) {
    batchClientPermissions(clientIds: $clientIds) {
      clientId
      permissions {
        id
        name
        description
        parentId
        componentSystemName
        showDataPermissions
      }
    }
  }
`;

// Utility functions
const isStandardRole = (roleValue) => {
  return (
    roleValue === ROLE_TYPES.ALL_ROLES ||
    roleValue === ROLE_TYPES.ROLES_WITH_PARENT
  );
};

const isDefaultRole = (roleName) => {
  return [
    "Payee",
    "Super Admin",
    "Power Admin",
    "Admin",
    "Plan Designer",
  ].includes(roleName);
};

// Memoized cell renderer components
const TruncatedTextCell = memo(
  ({
    value,
    maxLength = UI_CONSTANTS.TEXT_TRUNCATION.CLIENT_ROLE,
    className = "text-xs",
  }) => {
    const truncatedValue =
      value && value.length > maxLength
        ? `${value.substring(0, maxLength)}...`
        : value || "";

    return <span className={className}>{truncatedValue}</span>;
  }
);

const PermissionsCell = memo(({ value }) => {
  let displayValue;
  if (!value || value === "None") {
    displayValue = "None";
  } else {
    displayValue =
      value.length > UI_CONSTANTS.TEXT_TRUNCATION.PERMISSIONS
        ? `${value.substring(0, UI_CONSTANTS.TEXT_TRUNCATION.PERMISSIONS)}...`
        : value;
  }
  return <span className="text-xs">{displayValue}</span>;
});

const CommentsCell = memo(({ value, validationType }) => {
  const color = VALIDATION_COLORS[validationType];
  const truncatedMessage =
    value && value.length > UI_CONSTANTS.TEXT_TRUNCATION.COMMENTS
      ? `${value.substring(0, UI_CONSTANTS.TEXT_TRUNCATION.COMMENTS)}...`
      : value || "";

  return (
    <span className="font-medium text-xs" style={{ color }}>
      {truncatedMessage}
    </span>
  );
});

const BulkPermissionManagement = () => {
  const { accessToken } = useAuthStore();

  // State management
  const [selectedPermission, setSelectedPermission] = useState(null);
  const [selectedClients, setSelectedClients] = useState([]);
  const [selectedRoles, setSelectedRoles] = useState([]);
  const [previewData, setPreviewData] = useState(null);
  const [validRecords, setValidRecords] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [parentPermissionWarning, setParentPermissionWarning] = useState(null);
  const [clientRoles, setClientRoles] = useState({});
  const [rolesLoading, setRolesLoading] = useState(false);
  const [clientPermissions, setClientPermissions] = useState({});

  // Confirmation modal state
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [confirmationSummary, setConfirmationSummary] = useState(null);

  // Refs for cleanup
  const isMountedRef = useRef(true);
  const abortControllerRef = useRef(null);
  const gridApiRef = useRef(null);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      isMountedRef.current = false;
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  // GraphQL queries
  const { data: clientsData, loading: clientsLoading } = useQuery(GET_CLIENTS);
  const { data: permissionsData, loading: permissionsLoading } = useQuery(
    GET_ALL_PERMISSIONS,
    {
      variables: { clientId: 1 },
    }
  );

  const [fetchRoles] = useLazyQuery(GET_ROLES, {
    fetchPolicy: "network-only",
    onCompleted: (data) => {
      if (!isMountedRef.current) return;

      const rolesGrouped = {};
      (data?.clientRoles || []).forEach((role) => {
        const clientId = role.clientId;
        if (!rolesGrouped[clientId]) {
          rolesGrouped[clientId] = [];
        }
        rolesGrouped[clientId].push(role);
      });

      setClientRoles((prevRoles) => ({ ...prevRoles, ...rolesGrouped }));
      setRolesLoading(false);
    },
    onError: (error) => {
      console.error("Error fetching roles:", error);
      if (isMountedRef.current) {
        setRolesLoading(false);
      }
    },
  });

  const [fetchBatchClientPermissions] = useLazyQuery(
    GET_BATCH_CLIENT_PERMISSIONS,
    {
      fetchPolicy: "network-only",
      onCompleted: (data) => {
        if (!isMountedRef.current) return;
        // The data will be handled in the calling function
      },
      onError: (error) => {
        console.error("Error fetching batch client permissions:", error);
      },
    }
  );

  // Transform data with memoization
  const clients = useMemo(() => clientsData?.allClients || [], [clientsData]);
  const permissions = useMemo(
    () => permissionsData?.allPermissions || [],
    [permissionsData]
  );

  // Clear validation results
  const clearValidationResults = useCallback(() => {
    setPreviewData(null);
    setValidRecords(null);
    setParentPermissionWarning(null);
  }, []);

  // Helper function to fetch permissions for multiple clients in batch
  const getBatchClientPermissions = useCallback(
    async (clientIds) => {
      // Filter out client IDs that we already have cached
      const uncachedClientIds = clientIds.filter(
        (clientId) => !clientPermissions[clientId]
      );

      if (uncachedClientIds.length === 0) {
        // All permissions are already cached
        const result = {};
        clientIds.forEach((clientId) => {
          result[clientId] = clientPermissions[clientId];
        });
        return result;
      }

      try {
        const result = await fetchBatchClientPermissions({
          variables: { clientIds: uncachedClientIds.map((id) => parseInt(id)) },
        });

        const batchData = result.data?.batchClientPermissions || [];
        const newPermissions = {};

        // Process batch results
        batchData.forEach((clientData) => {
          newPermissions[clientData.clientId] = clientData.permissions;
        });

        // Update cache
        setClientPermissions((prev) => ({
          ...prev,
          ...newPermissions,
        }));

        // Return combined result (cached + new)
        const combinedResult = {};
        clientIds.forEach((clientId) => {
          combinedResult[clientId] =
            newPermissions[clientId] || clientPermissions[clientId] || [];
        });

        return combinedResult;
      } catch (error) {
        console.error("Error fetching batch client permissions:", error);
        // Fallback to empty permissions for all requested clients
        const fallbackResult = {};
        clientIds.forEach((clientId) => {
          fallbackResult[clientId] = [];
        });
        return fallbackResult;
      }
    },
    [clientPermissions, fetchBatchClientPermissions]
  );

  // Hierarchical change handlers
  const handlePermissionChange = useCallback(
    (permissionId) => {
      const permission = permissions.find((p) => p.id === permissionId);
      setSelectedPermission(permission || null);
      clearValidationResults();

      if (!permissionId || !permission) {
        setSelectedClients([]);
        setSelectedRoles([]);
        setClientRoles({});
      }
    },
    [permissions, clearValidationResults]
  );

  const handleClientsChange = useCallback(
    (newSelectedClients) => {
      let finalSelectedClients = [...newSelectedClients];

      // Handle mutual exclusivity with "All Clients"
      const hasAllClients = finalSelectedClients.includes(
        CLIENT_TYPES.ALL_CLIENTS
      );
      const hasSpecificClients = finalSelectedClients.some(
        (client) => client !== CLIENT_TYPES.ALL_CLIENTS
      );

      if (hasAllClients && hasSpecificClients) {
        const previouslySelected = selectedClients || [];
        const newlyAdded = finalSelectedClients.filter(
          (client) => !previouslySelected.includes(client)
        );

        if (newlyAdded.includes(CLIENT_TYPES.ALL_CLIENTS)) {
          // If "All Clients" was just selected, keep only "All Clients"
          finalSelectedClients = [CLIENT_TYPES.ALL_CLIENTS];
        } else {
          // If specific clients were selected, remove "All Clients"
          finalSelectedClients = finalSelectedClients.filter(
            (client) => client !== CLIENT_TYPES.ALL_CLIENTS
          );
        }
      }

      setSelectedClients(finalSelectedClients);
      clearValidationResults();

      if (!finalSelectedClients || finalSelectedClients.length === 0) {
        setSelectedRoles([]);
      }
    },
    [selectedClients, clearValidationResults]
  );

  const handleRolesChange = useCallback(
    (newSelectedRoles) => {
      let finalSelectedRoles = [...newSelectedRoles];

      // Handle mutual exclusivity
      const hasAllRoles = finalSelectedRoles.includes(ROLE_TYPES.ALL_ROLES);
      const hasRolesWithParent = finalSelectedRoles.includes(
        ROLE_TYPES.ROLES_WITH_PARENT
      );
      const hasOtherSelections = finalSelectedRoles.some(
        (role) => !isStandardRole(role)
      );

      if ((hasAllRoles || hasRolesWithParent) && hasOtherSelections) {
        const previouslySelected = selectedRoles || [];
        const newlyAdded = finalSelectedRoles.filter(
          (role) => !previouslySelected.includes(role)
        );

        if (newlyAdded.includes(ROLE_TYPES.ALL_ROLES)) {
          finalSelectedRoles = [ROLE_TYPES.ALL_ROLES];
        } else if (newlyAdded.includes(ROLE_TYPES.ROLES_WITH_PARENT)) {
          finalSelectedRoles = [ROLE_TYPES.ROLES_WITH_PARENT];
        } else {
          finalSelectedRoles = finalSelectedRoles.filter(
            (role) => !isStandardRole(role)
          );
        }
      }

      if (hasAllRoles && hasRolesWithParent) {
        const previouslySelected = selectedRoles || [];
        const newlyAdded = finalSelectedRoles.filter(
          (role) => !previouslySelected.includes(role)
        );

        if (newlyAdded.includes(ROLE_TYPES.ALL_ROLES)) {
          finalSelectedRoles = [ROLE_TYPES.ALL_ROLES];
        } else if (newlyAdded.includes(ROLE_TYPES.ROLES_WITH_PARENT)) {
          finalSelectedRoles = [ROLE_TYPES.ROLES_WITH_PARENT];
        }
      }

      setSelectedRoles(finalSelectedRoles);
      clearValidationResults();
    },
    [selectedRoles, clearValidationResults]
  );

  // Effect to sync roles with selected clients
  useEffect(() => {
    const syncRoles = async () => {
      if (!isMountedRef.current) return;

      // Handle "All Clients" selection
      if (selectedClients.includes(CLIENT_TYPES.ALL_CLIENTS)) {
        const allClientIds = clients.map((client) => client.clientId);
        const missingClients = allClientIds.filter(
          (clientId) => !(clientId in clientRoles)
        );

        if (missingClients.length > 0 && isMountedRef.current) {
          setRolesLoading(true);
          abortControllerRef.current = new AbortController();

          try {
            await fetchRoles({
              variables: {
                clientIds: missingClients.map((clientId) => parseInt(clientId)),
              },
              context: {
                fetchOptions: { signal: abortControllerRef.current.signal },
              },
            });
          } catch (error) {
            if (error.name !== "AbortError") {
              console.error("Error fetching roles for all clients:", error);
            }
            if (isMountedRef.current) {
              setRolesLoading(false);
            }
          }
        }
        return;
      }

      // Handle specific client selections
      const specificClients = selectedClients.filter(
        (clientId) => clientId !== CLIENT_TYPES.ALL_CLIENTS
      );
      const newClients = specificClients.filter(
        (clientId) => !(clientId in clientRoles)
      );
      const removedClients = Object.keys(clientRoles).filter(
        (clientId) => !specificClients.includes(clientId)
      );

      // Remove roles for deselected clients
      if (removedClients.length > 0 && isMountedRef.current) {
        setClientRoles((prevRoles) => {
          const updatedRoles = { ...prevRoles };
          removedClients.forEach((clientId) => {
            delete updatedRoles[clientId];
          });
          return updatedRoles;
        });
      }

      // Fetch roles for newly selected clients
      if (newClients.length > 0 && isMountedRef.current) {
        setRolesLoading(true);
        abortControllerRef.current = new AbortController();

        try {
          await fetchRoles({
            variables: {
              clientIds: newClients.map((clientId) => parseInt(clientId)),
            },
            context: {
              fetchOptions: { signal: abortControllerRef.current.signal },
            },
          });
        } catch (error) {
          if (error.name !== "AbortError") {
            console.error("Error fetching roles for new clients:", error);
          }
          if (isMountedRef.current) {
            setRolesLoading(false);
          }
        }
      }
    };

    if (selectedClients.length > 0) {
      syncRoles();
    } else if (isMountedRef.current) {
      setClientRoles({});
    }
  }, [selectedClients, fetchRoles]);

  // Helper function to get display name for a selected role value
  const getRoleDisplayName = useCallback(
    (roleValue) => {
      // Handle standard and default role options
      if (ROLE_DISPLAY_NAMES[roleValue]) {
        return ROLE_DISPLAY_NAMES[roleValue];
      }

      // Handle client-specific roles
      if (roleValue.includes(":")) {
        const [clientId, roleId] = roleValue.split(":");
        const client = clients.find((c) => c.clientId == clientId);
        const clientName = client?.name || `Client ${clientId}`;

        const rolesForClient = clientRoles[clientId] || [];
        const role = rolesForClient.find((r) => r.rolePermissionId === roleId);
        const roleName = role?.displayName || `Role ${roleId}`;

        return `${clientName} - ${roleName}`;
      }

      // Handle client nodes
      if (roleValue.startsWith("client_")) {
        const clientId = roleValue.replace("client_", "");
        const client = clients.find((c) => c.clientId == clientId);
        const clientName = client?.name || `Client ${clientId}`;
        return `${clientName} - All roles`;
      }

      return roleValue;
    },
    [clients, clientRoles]
  );

  // Custom tag render function
  const customTagRender = useCallback(
    (props) => {
      const { value, closable, onClose } = props;
      const displayName = getRoleDisplayName(value);

      return (
        <span
          className="inline-block bg-gray-50 border border-gray-300 rounded px-2 py-0.5 m-0.5 text-xs text-gray-600 max-w-[200px] overflow-hidden text-ellipsis whitespace-nowrap"
          title={displayName}
        >
          {displayName}
          {closable && (
            <span
              className="ml-1 cursor-pointer text-gray-500"
              onClick={onClose}
            >
              ×
            </span>
          )}
        </span>
      );
    },
    [getRoleDisplayName]
  );

  // Create nested permission structure
  const permissionTreeData = useMemo(() => {
    if (!permissions || permissions.length === 0) return [];

    const permissionMap = new Map();
    const rootPermissions = [];

    // Create all permission nodes
    permissions.forEach((perm) => {
      const node = {
        title: perm.name,
        value: perm.id,
        key: perm.id,
        description: perm.description,
        children: [],
      };
      permissionMap.set(perm.id, node);
    });

    // Build the tree structure
    permissions.forEach((perm) => {
      const node = permissionMap.get(perm.id);
      if (perm.parentId && permissionMap.has(perm.parentId)) {
        const parent = permissionMap.get(perm.parentId);
        parent.children.push(node);
      } else {
        rootPermissions.push(node);
      }
    });

    return rootPermissions;
  }, [permissions]);

  // Create role options with dynamic structure
  const roleOptions = useMemo(() => {
    const options = [];
    const hasStandardSelection = selectedRoles.some(isStandardRole);
    const hasSpecificSelection = selectedRoles.some(
      (role) => !isStandardRole(role)
    );

    // Standard options
    const standardOptions = [
      {
        title: ROLE_DISPLAY_NAMES[ROLE_TYPES.ALL_ROLES],
        value: ROLE_TYPES.ALL_ROLES,
        key: ROLE_TYPES.ALL_ROLES,
        children: [],
        disabled:
          hasSpecificSelection ||
          selectedRoles.includes(ROLE_TYPES.ROLES_WITH_PARENT),
      },
      {
        title: ROLE_DISPLAY_NAMES[ROLE_TYPES.ROLES_WITH_PARENT],
        value: ROLE_TYPES.ROLES_WITH_PARENT,
        key: ROLE_TYPES.ROLES_WITH_PARENT,
        children: [],
        disabled:
          hasSpecificSelection || selectedRoles.includes(ROLE_TYPES.ALL_ROLES),
      },
    ];

    options.push(...standardOptions);

    // Default role options
    const defaultRoleOptions = Object.entries(ROLE_DISPLAY_NAMES)
      .filter(([key]) => !isStandardRole(key))
      .map(([key, title]) => ({
        title,
        value: key,
        key,
        disabled: hasStandardSelection,
        children: [],
      }));

    options.push(...defaultRoleOptions);

    // Client-specific tree structure
    if (selectedClients.length > 0) {
      // Handle "All Clients" case
      if (selectedClients.includes(CLIENT_TYPES.ALL_CLIENTS)) {
        // Show nested structure for all clients
        clients.forEach((client) => {
          const clientNode = {
            title: client.name,
            key: `client_${client.clientId}`,
            value: `client_${client.clientId}`,
            disabled: hasStandardSelection,
            children: [],
          };

          const rolesForClient = clientRoles[client.clientId] || [];
          const addedDefaultRoles = new Set();

          rolesForClient.forEach((role) => {
            if (role && role.displayName) {
              const roleIsDefault = isDefaultRole(role.displayName);

              if (roleIsDefault) {
                if (!addedDefaultRoles.has(role.displayName)) {
                  clientNode.children.push({
                    title: role.displayName,
                    key: `${client.clientId}:${role.rolePermissionId}`,
                    value: `${client.clientId}:${role.rolePermissionId}`,
                    disabled: hasStandardSelection,
                  });
                  addedDefaultRoles.add(role.displayName);
                }
              } else {
                clientNode.children.push({
                  title: role.displayName,
                  key: `${client.clientId}:${role.rolePermissionId}`,
                  value: `${client.clientId}:${role.rolePermissionId}`,
                  disabled: hasStandardSelection,
                });
              }
            }
          });

          // Only add client node if it has roles
          if (clientNode.children.length > 0) {
            options.push(clientNode);
          }
        });
      } else {
        // Handle specific client selections
        selectedClients.forEach((clientId) => {
          const selectedClient = clients.find(
            (client) => client.clientId == clientId
          );
          const clientName =
            selectedClient?.name || `Unknown Client (${clientId})`;

          const clientNode = {
            title: clientName,
            key: `client_${clientId}`,
            value: `client_${clientId}`,
            disabled: hasStandardSelection,
            children: [],
          };

          const rolesForClient = clientRoles[clientId] || [];
          const addedDefaultRoles = new Set();

          rolesForClient.forEach((role) => {
            if (role && role.displayName) {
              const roleIsDefault = isDefaultRole(role.displayName);

              if (roleIsDefault) {
                if (!addedDefaultRoles.has(role.displayName)) {
                  clientNode.children.push({
                    title: role.displayName,
                    key: `${clientId}:${role.rolePermissionId}`,
                    value: `${clientId}:${role.rolePermissionId}`,
                    disabled: hasStandardSelection,
                  });
                  addedDefaultRoles.add(role.displayName);
                }
              } else {
                clientNode.children.push({
                  title: role.displayName,
                  key: `${clientId}:${role.rolePermissionId}`,
                  value: `${clientId}:${role.rolePermissionId}`,
                  disabled: hasStandardSelection,
                });
              }
            }
          });

          options.push(clientNode);
        });
      }
    }

    return options;
  }, [selectedClients, selectedRoles, clients, clientRoles]);

  // Validation logic with memoization
  const validatePermissionAssignment = useCallback(
    async (
      existingPermissions,
      existingPermissionIds,
      permission,
      clientPermissions = []
    ) => {
      // Check if permission exists for this client
      const permissionExistsForClient = clientPermissions.some(
        (p) => p.id === permission.id
      );
      if (!permissionExistsForClient) {
        return {
          isValid: false,
          message: `Permission "${permission.name}" does not exist for this client. This will be ignored.`,
          type: VALIDATION_TYPES.ERROR,
        };
      }

      const parentExists = existingPermissionIds.includes(permission.parentId);
      const componentName = permission.componentSystemName || "";

      if (existingPermissionIds.includes(permission.id)) {
        return {
          isValid: false,
          message:
            "Permission already exists for this role. This will be ignored.",
          type: VALIDATION_TYPES.ERROR,
        };
      }

      // Check for data_permission requirement
      if (permission.showDataPermissions) {
        const componentPermissions = existingPermissions[componentName] || null;
        if (
          !componentPermissions ||
          !componentPermissions.data_permission ||
          componentPermissions.data_permission === null ||
          !componentPermissions.data_permission.type
        ) {
          return {
            isValid: false,
            message: `This permission requires data_permission to be configured for ${componentName} component. This will be ignored.`,
            type: VALIDATION_TYPES.ERROR,
          };
        }
      }

      if (selectedRoles.includes(ROLE_TYPES.ROLES_WITH_PARENT)) {
        if (!permission.parentId) {
          return {
            isValid: true,
            message:
              'No parent permission for this role. This is same as "All Roles" selection. Permission can be added successfully.',
            type: VALIDATION_TYPES.WARNING,
          };
        }

        if (!parentExists) {
          return {
            isValid: false,
            message:
              "Parent permission does not exist for this role. This will be ignored.",
            type: VALIDATION_TYPES.ERROR,
          };
        }
      } else {
        if (permission.parentId && !parentExists) {
          return {
            isValid: true,
            message:
              "Parent permission does not exist for this role. Parent permission(s) will also be added automatically along with the new permission.",
            type: VALIDATION_TYPES.WARNING,
          };
        }
      }

      return {
        isValid: true,
        message: "Permission can be added successfully",
        type: VALIDATION_TYPES.SUCCESS,
      };
    },
    [selectedRoles]
  );

  // Get all parent permissions for a given permission with memoization
  const getParentPermissions = useCallback(
    (permission) => {
      const parentPermissions = [];
      let currentPermission = permission;

      while (currentPermission.parentId) {
        const parent = permissions.find(
          (p) => p.id === currentPermission.parentId
        );
        if (parent) {
          parentPermissions.push(parent);
          currentPermission = parent;
        } else {
          break;
        }
      }

      return parentPermissions;
    },
    [permissions]
  );

  // Helper function to get roles for a specific client based on selection with memoization
  const getRolesForClient = useCallback(
    (clientId) => {
      const rolesForClient = clientRoles[clientId] || [];

      if (
        selectedRoles.includes(ROLE_TYPES.ALL_ROLES) ||
        selectedRoles.includes(ROLE_TYPES.ROLES_WITH_PARENT) ||
        selectedRoles.includes(`client_${clientId}`)
      ) {
        return rolesForClient;
      } else {
        return rolesForClient.filter((role) => {
          return selectedRoles.some((selectedRole) => {
            if (selectedRole.startsWith("all_")) {
              const roleType = selectedRole
                .replace("all_", "")
                .replace(/_/g, " ");
              const normalizedRoleType =
                ROLE_TYPE_MAPPING[roleType.toLowerCase()] || roleType;
              return role.displayName === normalizedRoleType;
            }

            if (selectedRole.includes(":")) {
              const [, roleId] = selectedRole.split(":");
              return roleId === role.rolePermissionId;
            }

            return selectedRole === role.rolePermissionId;
          });
        });
      }
    },
    [clientRoles, selectedRoles]
  );

  // Create client-role mapping with memoization
  const createClientRoleMapping = useCallback(() => {
    const clientRoleMap = {};

    if (selectedClients.includes(CLIENT_TYPES.ALL_CLIENTS)) {
      // If "All Clients" is selected, include all clients
      clients.forEach((client) => {
        clientRoleMap[client.clientId] = getRolesForClient(client.clientId);
      });
    } else {
      // Otherwise, only include selected clients
      selectedClients.forEach((clientId) => {
        clientRoleMap[clientId] = getRolesForClient(clientId);
      });
    }

    return clientRoleMap;
  }, [selectedClients, clients, getRolesForClient]);

  const handleValidate = async () => {
    if (
      !selectedClients.length ||
      !selectedRoles.length ||
      !selectedPermission
    ) {
      setError("Please select at least one client, role, and permission");
      return;
    }

    setError(null);

    // Check parent permission warning
    if (
      !selectedRoles.includes(ROLE_TYPES.ROLES_WITH_PARENT) &&
      selectedPermission.parentId
    ) {
      const parentPermissions = getParentPermissions(selectedPermission);
      if (parentPermissions.length > 0) {
        const parentNames = parentPermissions.map((p) => p.name).join(", ");
        setParentPermissionWarning(
          `${selectedPermission.name}'s parent permission(s) are: ${parentNames}. If they are not present for any role, they will also be added automatically.`
        );
      } else {
        setParentPermissionWarning(null);
      }
    } else {
      setParentPermissionWarning(null);
    }

    const clientRoleMapping = createClientRoleMapping();
    const previewData = [];
    const validRecordsData = [];

    // Get all client IDs that need permissions
    const allClientIds = Object.keys(clientRoleMapping);

    // Fetch permissions for all clients in batch
    const allClientPermissions = await getBatchClientPermissions(allClientIds);

    for (const [clientId, rolesForClient] of Object.entries(
      clientRoleMapping
    )) {
      const client = clients.find((c) => c.clientId == clientId);

      // Get client-specific permissions from batch result
      const clientSpecificPermissions = allClientPermissions[clientId] || [];

      for (const role of rolesForClient) {
        const existingPermissions = JSON.parse(role.permissions);
        const existingPermissionIds = Object.values(
          existingPermissions
        ).flatMap((item) => item.permissions);
        const existingPermissionNames = existingPermissionIds.map((id) => {
          const found = permissions.find((perm) => perm.id === id);
          return found ? found.name : id;
        });
        const validation = await validatePermissionAssignment(
          existingPermissions,
          existingPermissionIds,
          selectedPermission,
          clientSpecificPermissions
        );
        const parentPermissions = getParentPermissions(selectedPermission);

        const record = {
          client: client ? client.name : `Client ${clientId}`,
          role: role.displayName,
          existingPermissions: existingPermissionNames.join(", ") || "None",
          comments: validation.message,
          validationType: validation.type,
          isValid: validation.isValid,
          roleId: role.rolePermissionId,
          clientId: clientId,
          parentPermissions: parentPermissions,
          roleData: role,
        };

        previewData.push(record);

        if (validation.isValid) {
          validRecordsData.push(record);
        }
      }
    }

    setPreviewData(previewData);
    setValidRecords(validRecordsData);
  };

  // Calculate summary for confirmation modal
  const calculateSummary = useCallback(() => {
    if (!validRecords || validRecords.length === 0) return null;

    // Get unique clients and their counts
    const uniqueClients = [
      ...new Set(validRecords.map((record) => record.clientId)),
    ];
    const clientCounts = uniqueClients.map((clientId) => {
      const clientRecords = validRecords.filter(
        (record) => record.clientId === clientId
      );
      const client = clients.find((c) => c.clientId == clientId);
      return {
        clientName: client?.name || `Client ${clientId}`,
        roleCount: clientRecords.length,
        roles: clientRecords.map((record) => record.role),
      };
    });

    const totalRoles = validRecords.length;
    const totalClients = uniqueClients.length;

    return {
      totalClients,
      totalRoles,
      clientCounts,
      permission: selectedPermission?.name,
      permissionId: selectedPermission?.id,
      parentPermissions: selectedPermission
        ? getParentPermissions(selectedPermission)
        : [],
    };
  }, [validRecords, clients, selectedPermission, getParentPermissions]);

  // Show confirmation modal before applying changes
  const handleSaveChanges = () => {
    if (!validRecords || validRecords.length === 0) {
      message.warning("No valid records to save");
      return;
    }

    const summary = calculateSummary();
    setConfirmationSummary(summary);
    setShowConfirmModal(true);
  };

  // Execute the actual API call after confirmation
  const handleApply = async () => {
    try {
      setLoading(true);
      setShowConfirmModal(false); // Close the modal

      if (!validRecords || validRecords.length === 0) {
        message.warning("No valid records to save");
        return;
      }

      // Helper function to add permissions to the existing role structure
      const addPermissionsToRole = (
        roleData,
        permissionsToAdd,
        newPermissionComponents
      ) => {
        // Create a deep copy of the existing permissions structure
        const finalPermissions = JSON.parse(roleData.permissions);

        // Add new permissions to their respective components
        permissionsToAdd.forEach((permissionId) => {
          const componentName = newPermissionComponents[permissionId];
          if (componentName) {
            // Initialize component if it doesn't exist
            if (!finalPermissions[componentName]) {
              finalPermissions[componentName] = {
                permissions: [],
                dataPermission: null,
              };
            }

            // Add permission if not already present
            if (
              !finalPermissions[componentName].permissions.includes(
                permissionId
              )
            ) {
              finalPermissions[componentName].permissions.push(permissionId);
            }
          }
        });

        return finalPermissions;
      };

      // Create mapping of permissions to their components
      const newPermissionComponents = {};
      const permissionsToAdd = [selectedPermission.id];
      newPermissionComponents[selectedPermission.id] =
        selectedPermission.componentSystemName;

      // Add parent permissions and their components
      const parentPermissions = getParentPermissions(selectedPermission);
      parentPermissions.forEach((parent) => {
        permissionsToAdd.push(parent.id);
        newPermissionComponents[parent.id] = parent.componentSystemName;
      });

      const uniquePermissions = [...new Set(permissionsToAdd)];

      // Transform validRecords into the format expected by backend
      const roleUpdates = validRecords.map((record) => {
        const updatedPermissions = addPermissionsToRole(
          record.roleData,
          uniquePermissions,
          newPermissionComponents
        );

        return {
          client_id: parseInt(record.clientId, 10),
          rolePermissionId: record.roleId,
          displayName: record.roleData.displayName,
          description: record.roleData.description || "",
          permissions: updatedPermissions,
        };
      });

      const response = await fetch("/everstage_admin/bulk_permissions_add", {
        method: "POST",
        headers: {
          Authorization: `Bearer ${accessToken}`,
          Accept: "application/json",
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          role_updates: roleUpdates,
        }),
      });

      const result = await response.json();

      if (response.ok) {
        if (result.failedUpdates > 0) {
          message.warning(
            `Bulk update completed with ${result.failedUpdates} failures out of ${result.totalUpdates} total updates. Check console for details.`
          );
          console.warn("Failed updates:", result.errors);
        } else {
          message.success(
            `All ${result.successfulUpdates} permission updates completed successfully!`
          );
        }
      } else {
        throw new Error("Failed to update permissions");
      }

      setSelectedClients([]);
      setSelectedRoles([]);
      setSelectedPermission(null);
      setPreviewData(null);
      setValidRecords(null);
      setParentPermissionWarning(null);
    } catch (error) {
      console.error("Error updating permissions:", error);
      message.error("Failed to update permissions");
    } finally {
      setLoading(false);
    }
  };

  // AG-Grid column definitions with memoization
  const columnDefs = useMemo(
    () => [
      {
        field: "client",
        headerName: "Client",
        sortable: true,
        filter: true,
        flex: 1,
        minWidth: 120,
        cellRenderer: (params) => (
          <TruncatedTextCell
            value={params.value}
            maxLength={UI_CONSTANTS.TEXT_TRUNCATION.CLIENT_ROLE}
          />
        ),
        tooltipField: "client",
      },
      {
        field: "role",
        headerName: "Role",
        sortable: true,
        filter: true,
        flex: 1,
        minWidth: 120,
        cellRenderer: (params) => (
          <TruncatedTextCell
            value={params.value}
            maxLength={UI_CONSTANTS.TEXT_TRUNCATION.CLIENT_ROLE}
          />
        ),
        tooltipField: "role",
      },
      {
        field: "existingPermissions",
        headerName: "Existing Permissions",
        sortable: true,
        filter: true,
        flex: 2.5,
        minWidth: 200,
        cellRenderer: (params) => <PermissionsCell value={params.value} />,
        tooltipField: "existingPermissions",
      },
      {
        field: "comments",
        headerName: "Comments",
        sortable: true,
        filter: true,
        flex: 2,
        minWidth: 180,
        cellRenderer: (params) => (
          <CommentsCell
            value={params.value}
            validationType={params.data.validationType}
          />
        ),
        tooltipField: "comments",
      },
    ],
    []
  );

  if (clientsLoading || permissionsLoading) {
    return (
      <div className="p-6 bg-white min-h-screen font-sans flex justify-center items-center">
        <Spin size="large" />
      </div>
    );
  }

  return (
    <div className="p-6 bg-white min-h-screen font-sans">
      <div className="flex items-center gap-2 mb-4">
        <h1 className="text-2xl font-semibold mb-0 mt-0 text-gray-800">
          Bulk Permission Addition
        </h1>
        <Tooltip
          title={
            <div className="text-xs leading-relaxed max-w-sm">
              <div className="mb-1">
                • Expects the permission to be present for the selected clients
                in the 'permissions' table
              </div>
              <div className="mb-1">
                • Adds the permission to the 'role_permissions' table for
                selected clients and roles
              </div>
              <div className="mb-1">
                • Does not support permissions from 'Commission Plans' section
                due to extra dependencies
              </div>
              <div>
                • Does not support 'Can login as others' from 'Teams, Groups &
                Users' section due to extra dependencies
              </div>
            </div>
          }
          placement="bottom"
          overlayStyle={{ maxWidth: "400px" }}
        >
          <InfoCircleOutlined className="text-blue-500 text-xs" />
        </Tooltip>
      </div>

      {error && (
        <Alert
          message="Error"
          description={error}
          type="error"
          showIcon
          className="mb-4"
        />
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-2">
        {/* Permission Selection */}
        <div>
          <h2 className="text-base font-semibold mb-1.5 text-gray-700">
            Select Permission <span className="text-red-600">*</span>
          </h2>
          <TreeSelect
            placeholder="Select permission"
            value={selectedPermission?.id || undefined}
            onChange={handlePermissionChange}
            treeData={permissionTreeData}
            showSearch
            treeNodeFilterProp="title"
            className="w-full"
            loading={permissionsLoading}
            treeCheckable={false}
            showCheckedStrategy={TreeSelect.SHOW_CHILD}
            dropdownStyle={{ maxHeight: 400, overflow: "auto" }}
            virtual={false}
          />
        </div>

        {/* Empty div to maintain grid layout */}
        <div></div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-4">
        {/* Client Selection */}
        <div>
          <h2 className="text-base font-semibold mb-1.5 text-gray-700">
            Clients <span className="text-red-600">*</span>
          </h2>
          <Select
            mode="multiple"
            placeholder="Select clients"
            value={selectedClients}
            onChange={handleClientsChange}
            loading={clientsLoading}
            className="w-full"
            disabled={!selectedPermission}
            showSearch
            filterOption={(input, option) =>
              (option?.label ?? "").toLowerCase().includes(input.toLowerCase())
            }
            options={[
              {
                label: "All Clients",
                value: CLIENT_TYPES.ALL_CLIENTS,
              },
              ...clients.map((client) => ({
                label: client.name,
                value: client.clientId,
              })),
            ]}
          />
        </div>

        {/* Role Selection */}
        <div>
          <h2 className="text-base font-semibold mb-1.5 text-gray-700">
            Roles <span className="text-red-600">*</span>
          </h2>
          <TreeSelect
            treeCheckable
            showCheckedStrategy={TreeSelect.SHOW_PARENT}
            placeholder="Select roles"
            value={selectedRoles}
            onChange={handleRolesChange}
            treeData={roleOptions}
            showSearch
            treeNodeFilterProp="title"
            multiple
            className="w-full"
            loading={rolesLoading}
            disabled={!selectedPermission || selectedClients.length === 0}
            tagRender={customTagRender}
            dropdownStyle={{ maxHeight: 400, overflow: "auto" }}
            virtual={false}
          />
        </div>
      </div>

      {/* Parent Permission Warning */}
      {parentPermissionWarning && (
        <Alert
          description={parentPermissionWarning}
          type="info"
          showIcon
          size="small"
          className="mb-4 text-sm px-4 py-2"
        />
      )}

      {/* Validation Results Section */}
      {previewData && (
        <div className="bg-slate-50 p-2.5 rounded-lg mb-4 border border-slate-200">
          <div className="flex justify-between items-center mb-2">
            <h2 className="text-base font-semibold mt-0 text-gray-700">
              Validation Results
            </h2>
            <div className="flex gap-2">
              <Button
                size="small"
                icon={<span>📊</span>}
                onClick={() => {
                  if (gridApiRef.current) {
                    gridApiRef.current.exportDataAsCsv({
                      fileName: `bulk_permission_validation_${
                        new Date().toISOString().split("T")[0]
                      }.csv`,
                    });
                  }
                }}
              >
                Export CSV
              </Button>
              <Button
                size="small"
                icon={<span>📋</span>}
                onClick={() => {
                  if (gridApiRef.current) {
                    gridApiRef.current.exportDataAsExcel({
                      fileName: `bulk_permission_validation_${
                        new Date().toISOString().split("T")[0]
                      }.xlsx`,
                    });
                  }
                }}
              >
                Export Excel
              </Button>
            </div>
          </div>
          <div
            className="ag-theme-alpine w-full"
            style={{
              height:
                previewData.length <= 10
                  ? "auto"
                  : Math.min(
                      400,
                      Math.max(200, previewData.length * 32 + 120)
                    ) + "px",
            }}
          >
            <AgGridReact
              rowData={previewData}
              columnDefs={columnDefs}
              pagination={previewData.length > 10}
              paginationPageSize={20}
              defaultColDef={{
                resizable: true,
                sortable: true,
                filter: true,
                wrapText: false,
                autoHeight: false,
              }}
              rowHeight={32}
              headerHeight={36}
              suppressHorizontalScroll={false}
              enableCellTextSelection={true}
              ensureDomOrder={true}
              domLayout={previewData.length <= 10 ? "autoHeight" : "normal"}
              // Export options
              enableRangeSelection={true}
              suppressCsvExport={false}
              suppressExcelExport={false}
              onGridReady={(params) => {
                params.api.sizeColumnsToFit();
                // Store grid API reference for export functionality
                gridApiRef.current = params.api;
              }}
              onGridSizeChanged={(params) => {
                params.api.sizeColumnsToFit();
              }}
            />
          </div>
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex gap-3">
        <Button
          type="primary"
          onClick={handleValidate}
          loading={loading}
          disabled={
            !selectedClients.length ||
            !selectedRoles.length ||
            !selectedPermission
          }
          size="middle"
        >
          Validate Changes
        </Button>
        <Button
          type="default"
          onClick={handleSaveChanges}
          loading={loading}
          disabled={
            !validRecords ||
            validRecords.length === 0 ||
            !selectedClients.length ||
            !selectedRoles.length ||
            !selectedPermission
          }
          size="middle"
        >
          Save Changes{" "}
          {validRecords && validRecords.length > 0
            ? `(${validRecords.length} valid)`
            : ""}
        </Button>
      </div>

      {/* Confirmation Modal */}
      <Modal
        title="Confirm Bulk Permission Changes"
        open={showConfirmModal}
        onOk={handleApply}
        onCancel={() => setShowConfirmModal(false)}
        okText="Yes, Apply Changes"
        cancelText="Cancel"
        confirmLoading={loading}
        width={600}
        maskClosable={false}
      >
        {confirmationSummary && (
          <div className="space-y-4">
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="font-medium text-gray-700">Permission:</span>
                  <p className="text-blue-700 font-medium mt-2 mb-0">
                    {confirmationSummary.permission}
                  </p>
                </div>
                <div>
                  <span className="font-medium text-gray-700">
                    Total Records:
                  </span>
                  <p className="text-blue-700 font-medium mt-2 mb-0">
                    {confirmationSummary.totalRoles} role(s) across{" "}
                    {confirmationSummary.totalClients} client(s)
                  </p>
                </div>
              </div>
            </div>

            {/* Parent Permissions */}
            {confirmationSummary.parentPermissions.length > 0 && (
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                <h4 className="font-medium text-yellow-800 m-0">
                  Parent permission(s) will also be added:
                </h4>
                <ul className="list-disc list-inside text-sm text-yellow-700 mt-2 mb-0">
                  {confirmationSummary.parentPermissions.map(
                    (parent, index) => (
                      <li key={index}>{parent.name}</li>
                    )
                  )}
                </ul>
              </div>
            )}

            {/* Client Breakdown */}
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-3">
              <div
                className="ag-theme-alpine w-full"
                style={{ height: "200px" }}
              >
                <AgGridReact
                  rowData={confirmationSummary.clientCounts}
                  columnDefs={[
                    {
                      field: "clientName",
                      headerName: "Client",
                      flex: 1,
                      minWidth: 150,
                      filter: true,
                      cellRenderer: (params) => {
                        const clientInfo = params.data;
                        return `${clientInfo.clientName} (${
                          clientInfo.roleCount
                        } role${clientInfo.roleCount !== 1 ? "s" : ""})`;
                      },
                    },
                    {
                      field: "roles",
                      headerName: "Roles",
                      flex: 2,
                      minWidth: 200,
                      filter: true,
                      cellRenderer: (params) => {
                        const roles = params.value;
                        if (roles.length <= 3) {
                          return roles.join(", ");
                        } else {
                          return `${roles.slice(0, 3).join(", ")} and ${
                            roles.length - 3
                          } more...`;
                        }
                      },
                      tooltipValueGetter: (params) => {
                        return params.value.join(", ");
                      },
                      filterValueGetter: (params) => {
                        return params.value.join(", ");
                      },
                    },
                  ]}
                  defaultColDef={{
                    resizable: true,
                    sortable: true,
                    wrapText: false,
                    autoHeight: false,
                  }}
                  rowHeight={32}
                  headerHeight={36}
                  suppressHorizontalScroll={false}
                  enableCellTextSelection={true}
                  ensureDomOrder={true}
                  domLayout="normal"
                  onGridReady={(params) => {
                    params.api.sizeColumnsToFit();
                  }}
                  onGridSizeChanged={(params) => {
                    params.api.sizeColumnsToFit();
                  }}
                />
              </div>
            </div>

            <div className="bg-red-50 border border-red-200 rounded-lg p-3">
              <p className="text-sm text-red-800 m-0">
                <strong>⚠️ Warning:</strong> This action will modify role
                permissions in the database. Please ensure you have reviewed the
                validation results above before proceeding.
              </p>
            </div>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default memo(BulkPermissionManagement);
