import { useQuery } from "@apollo/client";
import { faSpinner as saveIcon } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { Switch as HeadlessSwitch } from "@headlessui/react";
import {
  updateCustomer,
  validateExposeCommFlag,
} from "Api/CustomerUpdationService";
import {
  Drawer,
  Multiselect,
  Select,
  showMessage,
  Switch,
  Modal,
  Info,
  Button,
} from "Components";
import { NOTIFICATION_TYPE, SWITCH_STATUS } from "Enums";
import { useAuthStore } from "GlobalStores/AuthStore";
import { AddImageIcon } from "icons";
import { default as React, useEffect, useState } from "react";
import {
  ALL_QUOTA_CATERGORIES_FOR_CLIENT,
  connectionOptions,
  planOptions,
  getConnectionTypeOptions,
  GET_OPTIONS,
  startMonthOptions,
  statusOptions,
  typeOptions,
  planSummaryModelOptions,
  profilePermissionOptions,
  upstreamEtlVersionOptions,
  crystalCustomPeriodOptions,
  runSettlementReportOptions,
  defaultDashboardStatusValues,
  g2ReviewFormOptions,
  warnOnUnlockOptions,
  hardDeleteFrequency,
  weeklyFrequencyOptions,
  monthlyFrequencyOptions,
  getFilteredPlanOptions,
} from "../";
import { getAllInviteTemplates } from "Api/EmailTemplateService";
import { groupBy, isEmpty, mapValues, omit, uniqBy, sortBy } from "lodash";
import { FEATURE_CATEGORIES, CATEGORY_ORDER, MODULES, isProdEnv } from "Utils";
import { getConfirmationFromUserToReplaceFile } from "~/AdminConsole/Genie/ManageDatasource/ConfirmationModals";
import { DrawerHeader } from "~/Components/DrawerHeader";
import { EditPencilAltIcon } from "@everstage/evericons/outlined";
import { EverButton } from "~/Components/ever-button/EverButton";
import { setupDefaultDashbaord } from "~/Api/DefaultDashboardService";
import { AnalyticsDefaultDashboardLink } from "./DashboardLink";
import { EverAvatar } from "~/Components/EverAvatar";

import { getAllAdditionalDeleteSyncStatusForClient } from "Api/AdditionalDeleteSyncService";

export default function CustomerDetails(props) {
  const { open, onClose, customersStore, userRoles } = props;
  const {
    clientId,
    crmCompanyId,
    customerId,
    name,
    domain,
    logoUrl,
    statementLogoUrl,
    connectionType,
    authConnectionName,
    baseCurrency,
    fiscalStartMonth,
    metaInfo,
    timeZone,
    clientFeatures,
  } = props.customer || {};
  const { isLive, isTest, createdAt, supportEmail, password } = metaInfo || {};
  const {
    hideCategories,
    showCommissionPercent,
    showCommissionBuddy,
    showTerritoryPlan,
    payoutSnapshotEtl,
    settlementV2,
    allowOnlyAdminsToModifyUserName,
    editLockedQuota,
    runSettlementReport,
    showSupersetDashboard,
    showReturnV1Button,
    enableEverComparison,
    managerRollupEd,
    showApprovalFeature,
    exposeCommReportsInPlan,
    enableConcurrentSessions,
    enableSupportUserAccess,
    enableTsarWebappCustomRoles,
    isSecureAdminUiAuth0UserMgmt,
    allowAdjustmentsToFrozenCommission,
    subscriptionPlan,
    showChatgpt,
    showPayoutTableBreakdown,
    isolatedSnowflakeDatabase,
    chromeExtensionEnabled,
    enableEverai,
    planSummaryModel,
    profilePicturePermission,
    warnOnUnlock,
    upstreamEtlVersion,
    customCalendar,
    quotaEffectiveDated,
    allowQuotaSettingsOverride,
    allowAnnualQuotaEffectiveDated,
    helpDocUserRole,
    documentationUrl,
    splitSummationToLi,
    showMetrics,
    avoidIframeInContracts,
    insertMetaDataToVecDb,
    enableHrisIntegration,
    useAggridForPdfExport,
    useMultiEngineStormbreaker,
    showForecast,
    enableCustomWorkflows,
    crystalCustomCalendarFuturePeriods,
    enableRoundingInTierFunctions,
    analyticsDefaultDashboardStatus,
    enableCustomTheme,
    enableSidebarV3,
    enableContractPermissions,
    enableMultiLanguageSupport,
    showG2ReviewForm,
    datasheetV2,
    modules,
    runSyncForMultiplePeriod,
    isAutoEnrichReport,
    asyncExportDatasheet,
    uploadExcelFilesInCustomObject,
    allowCsvUploadBulkPaymentRegister,
  } = clientFeatures || {};

  const [isEdit, setIsEdit] = useState(false);
  const { accessToken } = useAuthStore();
  const [loading, setLoading] = useState(false);

  const [newName, setNewName] = useState(null);
  const [newHubspotCompanyId, setNewHubspotCompanyId] = useState(null);
  const [newLogo, setNewLogo] = useState(null);
  const [newStatementLogo, setNewStatementLogo] = useState(null);
  const [previewSrc, setPreviewSrc] = useState(null);
  const [statementPreviewSrc, setStatementPreviewSrc] = useState(null);
  const [newConnectionName, setNewConnectionName] = useState(null);
  const [newSubscriptionPlan, setNewSubscriptionPlan] = useState(null);
  const [newPlanSummaryModel, setNewPlanSummaryModel] = useState(null);
  const [newProfilePicturePermission, setNewProfilePicturePermission] =
    useState(null);
  const [newWarnOnUnlock, setNewWarnOnUnlock] = useState(null);
  const [newConnectionType, setNewConnectionType] = useState(null);
  const [currencyOptions, setCurrencyOptions] = useState([]);
  const [newCurrency, setNewCurrency] = useState(null);
  const [timezoneOptions, setTimezoneOptions] = useState([]);
  const [newTimezone, setNewTimezone] = useState(null);
  const [newFiscalStartMonth, setNewFiscalStartMonth] = useState(null);
  const [newDatasheetV2, setNewDatasheetV2] = useState(false);
  const [newShowCommissionPercent, setNewShowCommissionPercent] =
    useState(false);
  const [newShowCommissionBuddy, setNewShowCommissionBuddy] = useState(true);
  const [newShowTerritoryPlan, setnewShowTerritoryPlan] = useState(false);
  const [newSettlementV2, setNewSettlementV2] = useState(false);
  const [
    newAllowOnlyAdminsToModifyUserName,
    setNewAllowOnlyAdminsToModifyUserName,
  ] = useState();
  const [newEditLockedQuota, setNewEditLockedQuota] = useState(false);
  const [newIsAutoEnrichReport, setNewIsAutoEnrichReport] = useState(false);
  const [newRunSettlementReport, setNewRunSettlementReport] = useState(null);
  const [newAsyncExportDatasheet, setNewAsyncExportDatasheet] = useState(false);
  const [selectedStatus, setSelectedStatus] = useState(null);
  const [newStatus, setNewStatus] = useState(isLive);
  const [selectedType, setSelectedType] = useState(null);
  const [newType, setNewType] = useState(isTest);
  const [hideCategoryOptions, setHideCategoryOptions] = useState(["Primary"]);
  const [hideCategoryMap, setHideCategoryMap] = useState({
    Primary: "Primary", // systemName : displayName
  });
  const [userRoleOption, setUserRoleOption] = useState([]);
  const [newUserRoles, setNewUserRoles] = useState();
  const [newHideCategories, setNewHideCategories] = useState([]);
  const [updatedHideCategories, setUpdatedHideCategories] = useState([]);
  const [error, setError] = useState({});
  const [newShowSupersetDashboard, setNewShowSupersetDashboard] =
    useState(false);
  const [newShowReturnV1Button, setNewShowReturnV1Button] = useState(false);
  const [newEnableEverComparison, setNewEnableEverComparison] = useState(true);
  const [newShowApprovalFeature, setNewShowApprovalFeature] = useState(false);
  const [newExposeCommReportsInPlan, setNewExposeCommReportsInPlan] =
    useState(false);

  const [newEnableConcurrentSessions, setNewEnableConcurrentSessions] =
    useState(false);
  const [newEnableSupportUserAccess, setNewEnableSupportUserAccess] =
    useState(isProdEnv);
  const [newEnableTsarWebappCustomRoles, setNewEnableTsarWebappCustomRoles] =
    useState(false);
  const [newIsSecureAdminUiAuth0UserMgmt, setNewIsSecureAdminUiAuth0UserMgmt] =
    useState(false);
  const [
    newAllowAdjustmentsToFrozenCommission,
    setNewAllowAdjustmentsToFrozenCommission,
  ] = useState(false);
  const [newSplitSummationToLi, setnewSplitSummationToLi] = useState(false);
  const [showChatGPT, setShowChatGPT] = useState(false);
  const [showPayoutTablebreakdown, setShowPayoutTablebreakdown] =
    useState(false);
  const [newIsolatedSnowflakeDatabase, setNewIsolatedSnowflakeDatabase] =
    useState(false);
  const [newShowMetrics, setNewShowMetrics] = useState(false);
  const [newAvoidIframeInContracts, setNewAvoidIframeInContracts] =
    useState(false);
  const [newInsertMetaDataToVecDb, setNewInsertMetaDataToVecDb] =
    useState(false);

  const [modalInfo, setModalInfo] = useState({});
  const [newChromeExtensionEnabled, setNewChromeExtensionEnabled] =
    useState(false);
  const [newEnableEverai, setNewEnableEverai] = useState(false);
  const [emailInviteTemplateOptions, setEmailInviteTemplateOptions] = useState(
    []
  );
  const [defaultEmailInviteTemplate, setDefaultEmailInviteTemplate] =
    useState("");
  const [selectedEmailInviteTemplate, setSelectedEmailInviteTemplate] =
    useState("");
  const [newUpstreamEtlVersion, setNewUpstreamEtlVersion] = useState(
    upstreamEtlVersionOptions[0].name
  );
  const [newCustomCalendar, setNewCustomCalendar] = useState(false);

  const [newDocumentationUrl, setNewDocumentationUrl] = useState();
  const [newQuotaEffectiveDated, setNewQuotaEffectiveDated] = useState();
  const [newAllowQuotaSettingsOverride, setNewAllowQuotaSettingsOverride] =
    useState();
  const [
    newAllowAnnualQuotaEffectiveDated,
    setNewAllowAnnualQuotaEffectiveDated,
  ] = useState();
  const [newEnableHrisIntegration, setNewEnableHrisIntegration] =
    useState(false);
  const [newUseAgGrid, setNewUseAgGrid] = useState(false);
  const [newUseMultiEngineStormbreaker, setNewUseMultiEngineStormbreaker] =
    useState(false);
  const [newShowForecast, setNewShowForecast] = useState(true);
  const [newEnableCustomWorkflows, setNewEnableCustomWorkflows] =
    useState(false);
  const [
    newCrystalCustomCalendarFuturePeriods,
    setNewCrystalCustomCalendarFuturePeriods,
  ] = useState(crystalCustomPeriodOptions[0]);
  const [newEnableCustomTheme, setNewEnableCustomTheme] = useState(false);
  const [newEnableSidebarV3, setNewEnableSidebarV3] = useState(false);
  const [newEnableContractPermissions, setNewEnableContractPermissions] =
    useState(false);
  const [newEnableMultiLanguageSupport, setNewEnableMultiLanguageSupport] =
    useState(false);
  const [
    newAnalyticsDefaultDashboardStatus,
    setNewAnalyticsDefaultDashboardStatus,
  ] = useState(defaultDashboardStatusValues.NONE);
  const [newShowG2ReviewForm, setNewShowG2ReviewForm] = useState(
    g2ReviewFormOptions[2]
  );
  const [newModules, setNewModules] = useState([]);
  const [newrunSyncMultiplePeriod, setNewRunSyncMultiplePeriod] =
    useState(false);
  const [
    newAllowCsvUploadBulkPaymentRegister,
    setNewAllowCsvUploadBulkPaymentRegister,
  ] = useState(false);

  const [hardDeleteSyncFrequency, setHardDeleteSyncFrequency] = useState(
    hardDeleteFrequency[0]
  );
  const [dayOfWeek, setDayOfWeek] = useState("*");
  const [dayOfMonth, setDayOfMonth] = useState("*");

  const [newDayOfWeek, setNewDayOfWeek] = useState("*");
  const [newDayOfMonth, setNewDayOfMonth] = useState("*");

  const hard_delete_sync_details = (clientId) => {
    getAllAdditionalDeleteSyncStatusForClient(clientId, accessToken)
      .then((response) => response.json())
      .then((jsonData) => {
        const deleteSyncs = jsonData["additionalDeleteSyncs"];
        if (Array.isArray(deleteSyncs) && deleteSyncs.length > 0) {
          const { cronExpression } = deleteSyncs[0];
          if (
            cronExpression.dayOfWeek === "*" &&
            cronExpression.dayOfMonth === "*"
          ) {
            setHardDeleteSyncFrequency(hardDeleteFrequency[1]);
          } else if (cronExpression.dayOfWeek !== "*") {
            setHardDeleteSyncFrequency(hardDeleteFrequency[2]);
            setDayOfWeek(
              weeklyFrequencyOptions[Number(cronExpression.dayOfWeek)]?.name
            );
          } else {
            setHardDeleteSyncFrequency(hardDeleteFrequency[3]);
            setDayOfMonth(
              monthlyFrequencyOptions[Number(cronExpression.dayOfMonth) - 1]
                ?.name
            );
          }
        } else {
          setHardDeleteSyncFrequency(hardDeleteFrequency[0]);
        }
      })
      .catch((err) =>
        console.error("Error fetching delete sync details:", err)
      );
  };

  useEffect(() => {
    hard_delete_sync_details(clientId);
  }, [clientId]);

  const [newEnableExcleUpload, setNewEnableExcelUpload] = useState(false);

  const getInviteTemplates = async () => {
    const response = await getAllInviteTemplates(accessToken);
    if (response.ok) {
      const data = await response.json();
      const inviteTemplates = [];
      data.emailInviteTemplates.forEach((item) => {
        inviteTemplates.push({
          id: item.templateId,
          name: item.templateName,
        });
        if (item.clientIds.includes(Number(clientId))) {
          setDefaultEmailInviteTemplate({
            id: item.templateId,
            name: item.templateName,
          });
          setSelectedEmailInviteTemplate({
            id: item.templateId,
            name: item.templateName,
          });
        }
      });
      setEmailInviteTemplateOptions(inviteTemplates);
    }
  };

  const setSelectState = (value, options, updateState, key = "name") => {
    const selectedValue = options.length
      ? options.find(
          (option) =>
            String(option[key])?.toLowerCase() === String(value)?.toLowerCase()
        )
      : null;
    if (selectedValue) {
      updateState(selectedValue);
    }
  };

  const setOptionState = (value, options, updateState) => {
    updateState(value ? options[0] : options[1]);
  };

  const setDatasheetParallelProcessingOptionState = (
    value,
    options,
    updateState
  ) => {
    const selectedOption = options.find(
      (option) => option.key === String(value)
    );
    selectedOption ? updateState(selectedOption) : updateState(options[1]);
  };

  const { data: optionsData } = useQuery(GET_OPTIONS, {
    fetchPolicy: "no-cache",
    variables: { isClientSpecific: false },
  });

  const { loading: qcLoading } = useQuery(ALL_QUOTA_CATERGORIES_FOR_CLIENT, {
    variables: { clientId: clientId },
    onCompleted: (data) => {
      if (data && data.allQuotaCategoriesForClient) {
        const quotas = [];
        const categoryMap = {};
        data.allQuotaCategoriesForClient.forEach((quotaCategory) => {
          quotas.push(quotaCategory["displayName"]);
          categoryMap[quotaCategory["quotaCategoryName"]] =
            quotaCategory["displayName"];
        });
        categoryMap["Primary"] = "Primary";
        // update hidecategories with displayName
        const tempHideCategories = hideCategories.map(
          (category) => categoryMap[category]
        );
        setUpdatedHideCategories(tempHideCategories);
        setHideCategoryOptions((el) => el.concat(quotas));
        setHideCategoryMap(categoryMap);
      }
    },
  });

  // Convert timezone label from timezone value
  const getTimezoneLabel = (timezoneValue) => {
    if (!optionsData || !optionsData.allNotificationTimezones) {
      return "";
    }
    const timezoneItems = optionsData.allNotificationTimezones.map((item) =>
      JSON.parse(item)
    );
    return timezoneItems.find((tz) => tz.value === timezoneValue)?.label || "";
  };

  // Convert timezone value from timezone label
  const getTimezoneValue = (timezoneLabel) => {
    if (!optionsData || !optionsData.allNotificationTimezones) {
      return "";
    }
    const timezoneItems = optionsData.allNotificationTimezones.map((item) =>
      JSON.parse(item)
    );
    return timezoneItems.find((tz) => tz.label === timezoneLabel).value;
  };

  const initializeState = () => {
    setNewName(name);
    setNewHubspotCompanyId(crmCompanyId);
    setNewDatasheetV2(datasheetV2 ?? false);
    setNewShowCommissionPercent(showCommissionPercent ?? false);
    setNewShowCommissionBuddy(showCommissionBuddy ?? true);
    setnewShowTerritoryPlan(showTerritoryPlan ?? false);
    setNewSettlementV2(settlementV2 ?? false);
    setNewAllowOnlyAdminsToModifyUserName(
      allowOnlyAdminsToModifyUserName ?? false
    );
    setNewEditLockedQuota(editLockedQuota ?? false);
    setNewIsAutoEnrichReport(isAutoEnrichReport ?? false);
    setNewAsyncExportDatasheet(asyncExportDatasheet ?? false);
    setNewShowSupersetDashboard(showSupersetDashboard ?? false);
    setNewShowReturnV1Button(showReturnV1Button ?? false);
    setNewUserRoles(displayNames ?? []);
    setNewHideCategories(updatedHideCategories ?? hideCategories ?? []);
    setSelectState(authConnectionName, connectionOptions, setNewConnectionName);
    setSelectState(
      subscriptionPlan,
      getFilteredPlanOptions(planOptions, newStatus),
      setNewSubscriptionPlan
    );
    setSelectState(
      planSummaryModel,
      planSummaryModelOptions,
      setNewPlanSummaryModel
    );
    setSelectState(
      connectionType,
      getConnectionTypeOptions(authConnectionName),
      setNewConnectionType
    );
    setSelectState(
      fiscalStartMonth,
      startMonthOptions,
      setNewFiscalStartMonth,
      "id"
    );
    setSelectState(getTimezoneLabel(timeZone), timezoneOptions, setNewTimezone);
    setOptionState(isLive, statusOptions, setSelectedStatus);
    setOptionState(isTest, typeOptions, setSelectedType);
    setNewShowApprovalFeature(showApprovalFeature ?? false);
    setNewEnableEverComparison(enableEverComparison ?? true);
    setNewExposeCommReportsInPlan(exposeCommReportsInPlan ?? false);
    setNewEnableConcurrentSessions(enableConcurrentSessions ?? false);
    setNewEnableSupportUserAccess(enableSupportUserAccess ?? isProdEnv);
    setNewEnableTsarWebappCustomRoles(enableTsarWebappCustomRoles ?? false);

    setNewIsSecureAdminUiAuth0UserMgmt(isSecureAdminUiAuth0UserMgmt ?? false);
    setNewAllowAdjustmentsToFrozenCommission(
      allowAdjustmentsToFrozenCommission ?? false
    );
    setnewSplitSummationToLi(splitSummationToLi ?? false);
    setNewShowMetrics(showMetrics ?? false);
    setNewAvoidIframeInContracts(avoidIframeInContracts ?? false);
    setNewInsertMetaDataToVecDb(insertMetaDataToVecDb ?? false);
    setShowChatGPT(showChatgpt ?? false);
    setShowPayoutTablebreakdown(showPayoutTableBreakdown ?? false);
    setNewIsolatedSnowflakeDatabase(isolatedSnowflakeDatabase ?? false);
    setNewChromeExtensionEnabled(chromeExtensionEnabled ?? false);
    setNewEnableEverai(enableEverai ?? false);
    setSelectedEmailInviteTemplate(defaultEmailInviteTemplate);
    setSelectState(
      profilePicturePermission,
      profilePermissionOptions,
      setNewProfilePicturePermission
    );
    setSelectState(warnOnUnlock, warnOnUnlockOptions, setNewWarnOnUnlock);
    setSelectState(
      runSettlementReport,
      runSettlementReportOptions,
      setNewRunSettlementReport,
      "key"
    );
    setNewUpstreamEtlVersion(
      upstreamEtlVersion && upstreamEtlVersion === "v1"
        ? upstreamEtlVersionOptions[0]
        : upstreamEtlVersionOptions[1]
    );
    setNewCustomCalendar(customCalendar ?? false);
    setNewShowForecast(showForecast ?? true);
    setNewQuotaEffectiveDated(quotaEffectiveDated ?? false);
    setNewAllowQuotaSettingsOverride(allowQuotaSettingsOverride ?? false);
    setNewAllowAnnualQuotaEffectiveDated(
      allowAnnualQuotaEffectiveDated ?? false
    );
    setNewDocumentationUrl(documentationUrl);

    setNewEnableHrisIntegration(enableHrisIntegration ?? false);
    setNewUseAgGrid(false);
    setNewUseMultiEngineStormbreaker(useMultiEngineStormbreaker ?? false);
    setNewEnableCustomWorkflows(enableCustomWorkflows ?? false);
    setSelectState(
      crystalCustomCalendarFuturePeriods,
      crystalCustomPeriodOptions,
      setNewCrystalCustomCalendarFuturePeriods,
      "key"
    );
    setNewAnalyticsDefaultDashboardStatus(
      analyticsDefaultDashboardStatus ?? defaultDashboardStatusValues.NONE
    );
    setNewEnableCustomTheme(enableCustomTheme ?? false);
    setNewEnableSidebarV3(enableSidebarV3 ?? false);
    setNewEnableContractPermissions(enableContractPermissions ?? false);
    setNewEnableMultiLanguageSupport(enableMultiLanguageSupport ?? false);
    setSelectState(
      showG2ReviewForm,
      g2ReviewFormOptions,
      setNewShowG2ReviewForm
    );
    setNewModules(modules ?? []);
    setNewRunSyncMultiplePeriod(runSyncForMultiplePeriod ?? false);
    setHardDeleteSyncFrequency(
      hardDeleteSyncFrequency ?? hardDeleteFrequency[0]
    );
    setNewAllowCsvUploadBulkPaymentRegister(
      allowCsvUploadBulkPaymentRegister ?? false
    );
    setNewDayOfWeek(dayOfWeek ?? "*");
    setNewDayOfMonth(dayOfMonth ?? "*");
    setNewEnableExcelUpload(uploadExcelFilesInCustomObject ?? false);
  };
  useEffect(() => {
    const displayNamesArray = Object.keys(userRoles).map(
      (role) => userRoles[role].displayName
    );
    setUserRoleOption(displayNamesArray);
  }, [userRoles]);

  useEffect(() => {
    if (optionsData) {
      const currencyCodes = [];
      const timezones = [];
      optionsData.allActiveCountries.forEach((country, index) => {
        currencyCodes.push({
          id: index + 1,
          name: country["currencyCode"],
        });
      });
      const timezoneItems = optionsData.allNotificationTimezones.map((item) =>
        JSON.parse(item)
      );
      timezoneItems.forEach((timezone, index) => {
        if (timezones.indexOf(timezone["label"]) === -1) {
          timezones.push({ id: index + 1, name: timezone["label"] });
        }
      });

      const sortedTimezoneList = timezones.sort((a, b) => {
        const x = a.name.toUpperCase(),
          y = b.name.toUpperCase();
        return x === y ? 0 : x > y ? 1 : -1;
      });

      setTimezoneOptions(sortedTimezoneList);

      let uniqueOpt = uniqBy(currencyCodes, "name");
      setCurrencyOptions(sortBy(uniqueOpt, ["name"]));
      setSelectState(baseCurrency, currencyCodes, setNewCurrency);
      setSelectState(getTimezoneLabel(timeZone), timezones, setNewTimezone);
    }
  }, [optionsData, baseCurrency, timeZone]);

  useEffect(() => {
    if (newLogo) {
      let reader = new FileReader();
      reader.onloadend = () => {
        setPreviewSrc(reader.result);
      };
      reader.readAsDataURL(newLogo);
    }
  }, [newLogo]);

  useEffect(() => {
    if (newStatementLogo) {
      let reader = new FileReader();
      reader.onloadend = () => {
        setStatementPreviewSrc(reader.result);
      };
      reader.readAsDataURL(newStatementLogo);
    }
  }, [newStatementLogo]);

  useEffect(() => {
    initializeState();
  }, [
    name,
    logoUrl,
    statementLogoUrl,
    connectionType,
    authConnectionName,
    fiscalStartMonth,
    datasheetV2,
    showCommissionPercent,
    showCommissionBuddy,
    showTerritoryPlan,
    payoutSnapshotEtl,
    isLive,
    isTest,
    hideCategories,
    showSupersetDashboard,
    showReturnV1Button,
    managerRollupEd,
    showApprovalFeature,
    exposeCommReportsInPlan,
    chromeExtensionEnabled,
    enableEverai,
    enableConcurrentSessions,
    enableSupportUserAccess,
    enableTsarWebappCustomRoles,
    isSecureAdminUiAuth0UserMgmt,
    allowAdjustmentsToFrozenCommission,
    subscriptionPlan,
    customCalendar,
    helpDocUserRole,
    documentationUrl,
    updatedHideCategories,
    showForecast,
    quotaEffectiveDated,
    allowQuotaSettingsOverride,
    allowAnnualQuotaEffectiveDated,
    allowCsvUploadBulkPaymentRegister,
    upstreamEtlVersion,
    enableHrisIntegration,
    useAggridForPdfExport,
    enableCustomWorkflows,
    useMultiEngineStormbreaker,
    crystalCustomCalendarFuturePeriods,
    enableRoundingInTierFunctions,
    showPayoutTableBreakdown,
    analyticsDefaultDashboardStatus,
    enableCustomTheme,
    enableSidebarV3,
    enableContractPermissions,
    enableMultiLanguageSupport,
    showG2ReviewForm,
    allowOnlyAdminsToModifyUserName,
    modules,
  ]);

  useEffect(() => {
    if (isEdit) {
      setNewDayOfWeek(
        weeklyFrequencyOptions.find((item) => item.name === dayOfWeek)
      );
      setNewDayOfMonth(monthlyFrequencyOptions[dayOfMonth - 1]);
    }
  }, [isEdit]);

  useEffect(() => {
    if (hardDeleteSyncFrequency.name === "Weekly") {
      setNewDayOfMonth("*");
    } else if (hardDeleteSyncFrequency.name === "Monthly") {
      setNewDayOfWeek("*");
    }
  }, [hardDeleteSyncFrequency]);

  useEffect(() => {
    const newStatus =
      selectedStatus?.["name"]?.toLowerCase() ===
      statusOptions[0].name.toLowerCase();
    setNewStatus(newStatus);
    setNewSubscriptionPlan(getFilteredPlanOptions(planOptions, newStatus)[0]);
    setNewType(
      selectedType?.["name"]?.toLowerCase() ===
        typeOptions[0].name.toLowerCase()
    );
  }, [selectedStatus, selectedType]);

  useEffect(() => {
    getInviteTemplates();
  }, []);

  const resetStateValues = () => {
    setIsEdit(false);
    setError({});
    initializeState();
    setPreviewSrc(null);
    setHideCategoryOptions(["Primary"]);
    setUserRoleOption([""]);
    setHideCategoryMap({ Primary: "Primary" });
    setNewUserRoles(displayNames);
  };

  const displayNames = helpDocUserRole
    .map((roleId) => userRoles.find((obj) => obj.rolePermissionId === roleId))
    .filter((role) => role)
    .map((role) => role.displayName);

  const onCloseWrapper = () => {
    resetStateValues();
    onClose();
  };

  const handleCloseModal = () => {
    if (!isEmpty(modalInfo)) {
      modalInfo.onCancel?.();
    }
    setModalInfo({});
  };

  const closeActions = ({ type, message, description }) => {
    setIsEdit(false);
    setPreviewSrc();
    onCloseWrapper();
    showMessage(message, {
      type,
      description,
    });
    customersStore.customersRefetch();
  };

  const handleDisableCommReportsInPlan = async (value) => {
    if (!value) {
      try {
        let data = new FormData();
        data.append("clientId", clientId);
        const response = await validateExposeCommFlag(data, accessToken);
        if (response.status !== 200) {
          const res = await response.json();
          const dsNames = res?.dsNames;
          if (dsNames?.length > 0) {
            const errorMessage = `Please delete the following inter_object datasheets to disable this feature: ${dsNames.join(
              ", "
            )}`;
            const e = { name: { msg: errorMessage } };
            setError(e);
            return;
          }
        }
      } catch (err) {
        console.log(err);
        const errorMessage = "Something went wrong";
        const e = { name: { msg: errorMessage } };
        setError(e);
        return;
      }
    }

    if (error?.name?.msg) setError({});
    setNewExposeCommReportsInPlan(value);
  };

  const validate = () => {
    let pass = true;
    let e = {};
    if (
      hardDeleteSyncFrequency?.name === "Weekly" &&
      newDayOfWeek === undefined
    ) {
      e.frequency = {};
      e.frequency.msg = "Please choose the day of the week";
      pass = false;
    }
    if (
      hardDeleteSyncFrequency?.name === "Monthly" &&
      newDayOfMonth === undefined
    ) {
      e.frequency = {};
      e.frequency.msg = "Please choose the day of the month";
      pass = false;
    }
    setError(e);
    return pass;
  };

  const onSubmit = () => {
    setLoading(true);
    if (!validate()) {
      setLoading(false);
      return;
    }
    let data = new FormData();
    data.append("clientId", clientId);
    data.append("name", newName || name);
    data.append("hubspotCompanyId", newHubspotCompanyId);
    data.append("domain", domain);
    data.append("file", newLogo);
    data.append("statementLogo", newStatementLogo);
    data.append("connectionType", newConnectionType?.name ?? "");
    data.append(
      "subscriptionPlan",
      newSubscriptionPlan?.name ??
        getFilteredPlanOptions(planOptions, newStatus)[0].name
    );
    data.append("connectionName", newConnectionName?.name ?? "");
    data.append("baseCurrency", newCurrency?.name ?? "");
    data.append("fiscalStartMonth", newFiscalStartMonth?.name ?? "");
    data.append(
      "timezone",
      newTimezone?.name ? getTimezoneValue(newTimezone.name) : ""
    );
    data.append("datasheetV2", newDatasheetV2);
    data.append("showCommissionPercent", newShowCommissionPercent);
    data.append("showCommissionBuddy", newShowCommissionBuddy);
    data.append("showTerritoryPlan", newShowTerritoryPlan);
    data.append("settlementV2", newSettlementV2);
    data.append(
      "allowOnlyAdminsToModifyUserName",
      newAllowOnlyAdminsToModifyUserName
    );
    data.append("editLockedQuota", newEditLockedQuota);
    data.append("isAutoEnrichReport", newIsAutoEnrichReport);
    data.append("asyncExportDatasheet", newAsyncExportDatasheet);
    data.append(
      "runSettlementReport",
      newRunSettlementReport?.key ?? runSettlementReportOptions[0].key
    );
    // Update selected Hidden Category display Name with system Name
    data.append(
      "hideCategories",
      newHideCategories.map((displayName) => {
        const systemName = Object.keys(hideCategoryMap).find(
          (key) => hideCategoryMap[key] === displayName
        );
        return systemName;
      })
    );
    data.append("showSupersetDashboard", newShowSupersetDashboard);
    data.append("showReturnV1Button", newShowReturnV1Button);
    data.append("status", newStatus);
    data.append("type", newType);
    data.append("enableEverComparison", newEnableEverComparison);
    data.append("showApprovalFeature", newShowApprovalFeature);
    data.append("exposeCommReportsInPlan", newExposeCommReportsInPlan);
    data.append("enableConcurrentSessions", newEnableConcurrentSessions);
    data.append("enableSupportUserAccess", newEnableSupportUserAccess);
    data.append("enableTsarWebappCustomRoles", newEnableTsarWebappCustomRoles);
    data.append(
      "isSecureAdminUiAuth0UserMgmt",
      newIsSecureAdminUiAuth0UserMgmt
    );
    data.append(
      "allowAdjustmentsToFrozenCommission",
      newAllowAdjustmentsToFrozenCommission
    );
    data.append(
      "allowCsvUploadBulkPaymentRegister",
      newAllowCsvUploadBulkPaymentRegister
    );
    data.append("splitSummationToLi", newSplitSummationToLi);
    data.append("showMetrics", newShowMetrics);
    data.append("avoidIframeInContracts", newAvoidIframeInContracts);
    data.append("insertMetaDataToVecDb", newInsertMetaDataToVecDb);
    data.append("useMultiEngineStormbreaker", newUseMultiEngineStormbreaker);
    data.append("enableHrisIntegration", newEnableHrisIntegration);
    data.append("useAggridForPdfExport", newUseAgGrid);

    if (newUserRoles && newUserRoles.length > 0) {
      data.append(
        "helpDocUserRole",
        newUserRoles.map((role) => {
          const matchingRole = userRoles.find(
            (obj) => obj.displayName === role
          );
          const rolePermissionId = matchingRole
            ? matchingRole.rolePermissionId
            : null;
          return rolePermissionId;
        })
      );
    } else {
      data.append(
        "helpDocUserRole",
        helpDocUserRole.filter(
          (roleId) =>
            roleId &&
            userRoles.some((userRole) => userRole.rolePermissionId === roleId)
        )
      );
    }
    data.append("showChatgpt", showChatGPT);
    data.append("showPayoutTableBreakdown", showPayoutTablebreakdown);
    data.append("isolatedSnowflakeDatabase", newIsolatedSnowflakeDatabase);
    data.append("chromeExtensionEnabled", newChromeExtensionEnabled);
    data.append("enableEverai", newEnableEverai);
    data.append(
      "planSummaryModel",
      newPlanSummaryModel?.name ?? planSummaryModelOptions[2].name
    );
    data.append("emailInviteTemplateId", selectedEmailInviteTemplate.id);
    data.append("documentationUrl", newDocumentationUrl);
    data.append(
      "profilePicturePermission",
      newProfilePicturePermission?.name ?? profilePermissionOptions[3].name
    );
    data.append(
      "warnOnUnlock",
      newWarnOnUnlock?.name ?? warnOnUnlockOptions[0].name
    );
    data.append("upstreamEtlVersion", newUpstreamEtlVersion.name);
    data.append("customCalendar", newCustomCalendar);
    data.append("showForecast", newShowForecast);
    data.append("quotaEffectiveDated", newQuotaEffectiveDated);
    data.append("allowQuotaSettingsOverride", newAllowQuotaSettingsOverride);
    data.append(
      "allowAnnualQuotaEffectiveDated",
      newAllowAnnualQuotaEffectiveDated
    );
    data.append("enableCustomWorkflows", newEnableCustomWorkflows);
    data.append(
      "crystalCustomCalendarFuturePeriods",
      newCrystalCustomCalendarFuturePeriods?.key ??
        crystalCustomPeriodOptions[0].key
    );
    data.append("enableCustomTheme", newEnableCustomTheme);
    data.append("enableSidebarV3", newEnableSidebarV3);
    data.append("enableContractPermissions", newEnableContractPermissions);
    data.append("enableMultiLanguageSupport", newEnableMultiLanguageSupport);
    data.append(
      "showG2ReviewForm",
      newShowG2ReviewForm?.name ?? g2ReviewFormOptions[2].name
    );
    data.append("modules", newModules);
    data.append("runSyncForMultiplePeriod", newrunSyncMultiplePeriod);
    data.append(
      "hardDeleteSyncFrequency",
      hardDeleteSyncFrequency?.name ?? hardDeleteFrequency[0]
    );
    data.append("dayOfWeek", newDayOfWeek?.id ?? "*");
    data.append("dayOfMonth", newDayOfMonth?.id ?? "*");
    data.append("uploadExcelFilesInCustomObject", newEnableExcleUpload);

    updateCustomer(data, accessToken)
      .then((res) => {
        if (res.ok) {
          // Check if toggle has been changed
          if (
            (analyticsDefaultDashboardStatus ===
              defaultDashboardStatusValues.NONE ||
              analyticsDefaultDashboardStatus ===
                defaultDashboardStatusValues.ERROR) &&
            newAnalyticsDefaultDashboardStatus ===
              defaultDashboardStatusValues.PROCESSING
          ) {
            setupDefaultDashbaord(clientId, accessToken)
              .then((dashbaordSetupResult) => {
                let message = null;
                if (dashbaordSetupResult.status === 201) {
                  message = {
                    type: NOTIFICATION_TYPE.SUCCESS,
                    message: "Completed",
                    description:
                      "Customer updated successfully, analytics default dashboard setup triggered succesfully",
                  };
                } else if (dashbaordSetupResult.status === 200) {
                  message = {
                    type: NOTIFICATION_TYPE.SUCCESS,
                    message: "Completed",
                    description:
                      "Customer updated successfully, analytics default dashboard setup already in progress",
                  };
                } else {
                  message = {
                    type: NOTIFICATION_TYPE.ERROR,
                    message: "Partial Error",
                    description:
                      "Customer updated successfully, error while triggering default dashboard setup",
                  };
                }
                closeActions(message);
                setLoading(false);
              })
              .catch((err) => {
                const message = {
                  type: NOTIFICATION_TYPE.ERROR,
                  message: "Partial Error",
                  description:
                    "Customer updated successfully, error while triggering default dashboard setup",
                };
                console.log(err);
                closeActions(message);
                setLoading(false);
              });
          } else {
            const message = {
              type: NOTIFICATION_TYPE.SUCCESS,
              message: "Completed",
              description: "Customer updated successfully",
            };
            closeActions(message);
            setLoading(false);
          }
        } else {
          let e = {};
          res.json().then((ress) => {
            let errorMessage = ress?.message || ress?.errors?.[0];
            if (errorMessage?.field === "name") {
              errorMessage = "Customer name already taken";
              e.name = {};
              e.name.msg = errorMessage;
              setError(e);
            }
            showMessage(errorMessage, {
              type: NOTIFICATION_TYPE.ERROR,
            });
          });
          setLoading(false);
        }
      })
      .catch((err) => {
        console.log(err);
        setLoading(false);
      });
  };

  if (newUserRoles && newUserRoles.every((role) => role === "")) {
    setNewUserRoles(null);
  }

  const weeklyItem = {
    key: "day_of_week",
    type: "select",
    title: "Day Of The Week",
    options: weeklyFrequencyOptions,
    value: newDayOfWeek,
    defaultValue: dayOfWeek || newDayOfWeek,
    onChange: (v) => {
      setNewDayOfWeek(v);
    },
    error: error?.frequency?.msg,
  };

  const monthlyItem = {
    key: "day_of_month",
    type: "select",
    title: "Day Of The Month",
    options: monthlyFrequencyOptions,
    value: newDayOfMonth,
    defaultValue: dayOfMonth || newDayOfMonth,
    onChange: (v) => {
      setNewDayOfMonth(v);
    },
    error: error?.frequency?.msg,
  };

  const inputFieldObjects = [
    {
      key: "customer_name",
      type: "input",
      title: "Customer Name",
      value: name,
      error: error?.name?.msg,
      onChange: setNewName,
      category: FEATURE_CATEGORIES.BASIC_DETAILS,
    },
    {
      key: "hubspot_company_id",
      type: "input",
      title: "Hubspot Company Id",
      value: crmCompanyId,
      onChange: setNewHubspotCompanyId,
      category: FEATURE_CATEGORIES.BASIC_DETAILS,
    },
    {
      key: "logo",
      type: "image",
      title: "Logo",
      value: previewSrc || logoUrl,
      alt: name,
      onChange: setNewLogo,
      category: FEATURE_CATEGORIES.BASIC_DETAILS,
    },
    {
      key: "modules",
      type: "multiSelect",
      title: "Modules",
      value: newModules,
      defaultValue: String(newModules),
      options: Object.values(MODULES),
      onChange: setNewModules,
      category: FEATURE_CATEGORIES.NEW_FEATURE,
    },
    {
      key: "profile_picture_permission",
      type: "select",
      title: "Profile Picture Permission",
      value: newProfilePicturePermission,
      defaultValue:
        profilePicturePermission || newProfilePicturePermission?.name,
      options: profilePermissionOptions,
      onChange: (v) => {
        setNewProfilePicturePermission(v);
      },
      category: FEATURE_CATEGORIES.NEW_FEATURE,
    },
    {
      key: "warn_on_unlock",
      type: "select",
      title: "Warning on Unlock",
      value: newWarnOnUnlock,
      defaultValue: warnOnUnlock || newWarnOnUnlock?.name,
      options: warnOnUnlockOptions,
      onChange: (v) => {
        setNewWarnOnUnlock(v);
      },
      category: FEATURE_CATEGORIES.COMMISSIONS,
    },
    {
      key: "domain",
      title: "Domain",
      value: domain,
      category: FEATURE_CATEGORIES.BASIC_DETAILS,
    },
    {
      key: "client_id",
      title: "Client Id",
      value: clientId,
      category: FEATURE_CATEGORIES.BASIC_DETAILS,
    },
    {
      key: "customer_id",
      title: "Customer Id",
      value: customerId,
      category: FEATURE_CATEGORIES.BASIC_DETAILS,
    },
    {
      key: "support_email",
      title: "Support Email address",
      value: supportEmail,
      category: FEATURE_CATEGORIES.BASIC_DETAILS,
    },
    {
      key: "password",
      title: "Password",
      value: password,
      category: FEATURE_CATEGORIES.BASIC_DETAILS,
    },
    {
      key: "auth_connection_name",
      type: "select",
      title: "Auth Connection Name",
      value: newConnectionName,
      defaultValue: authConnectionName || newConnectionName?.name,
      options: connectionOptions,
      onChange: (value) => {
        setNewConnectionName(value);
        setSelectedEmailInviteTemplate(
          emailInviteTemplateOptions.find(
            (temp) => temp.name === value.name + "-invite"
          )
        );
        if (newConnectionName !== value) {
          setNewConnectionType(getConnectionTypeOptions(value.name)[0]);
        }
      },
      category: FEATURE_CATEGORIES.BASIC_DETAILS,
    },
    {
      key: "auth_connection_type",
      type: "select",
      title: "Auth Connection Type",
      value: newConnectionType,
      defaultValue: connectionType || newConnectionType?.name,
      options: getConnectionTypeOptions(
        newConnectionName?.name || authConnectionName
      ),
      onChange: setNewConnectionType,
      category: FEATURE_CATEGORIES.BASIC_DETAILS,
    },
    {
      key: "subscription_plan",
      type: "select",
      title: "Subscription Plan",
      value: newSubscriptionPlan,
      defaultValue: subscriptionPlan || newSubscriptionPlan?.name,
      options: getFilteredPlanOptions(planOptions, newStatus),
      onChange: setNewSubscriptionPlan,
      category: FEATURE_CATEGORIES.BASIC_DETAILS,
    },
    {
      key: "email_invite_template",
      type: "select",
      title: "Email Invite Template",
      value: selectedEmailInviteTemplate,
      defaultValue: selectedEmailInviteTemplate?.name || "",
      options: emailInviteTemplateOptions,
      onChange: setSelectedEmailInviteTemplate,
      category: FEATURE_CATEGORIES.BASIC_DETAILS,
    },
    {
      key: "plan_summary_model",
      type: "select",
      title: "Plan Summary Model",
      value: newPlanSummaryModel,
      defaultValue: planSummaryModel || newPlanSummaryModel?.name,
      options: planSummaryModelOptions,
      onChange: (v) => {
        setNewPlanSummaryModel(v);
      },
      category: FEATURE_CATEGORIES.BASIC_DETAILS,
    },
    {
      key: "base_currency",
      type: "select",
      title: "Base Currency",
      value: newCurrency,
      defaultValue: baseCurrency || newCurrency?.name,
      options: currencyOptions,
      onChange: setNewCurrency,
      category: FEATURE_CATEGORIES.BASIC_DETAILS,
    },
    {
      key: "timezone",
      type: "select",
      title: "Timezone",
      value: newTimezone,
      defaultValue: getTimezoneLabel(timeZone) || newTimezone?.name,
      options: timezoneOptions,
      onChange: setNewTimezone,
      category: FEATURE_CATEGORIES.BASIC_DETAILS,
    },
    {
      key: "fiscal_start_month",
      type: "select",
      title: "Fiscal Start Month",
      value: newFiscalStartMonth,
      defaultValue: fiscalStartMonth
        ? startMonthOptions[fiscalStartMonth - 1]["name"]
        : newFiscalStartMonth?.name,
      options: startMonthOptions,
      onChange: setNewFiscalStartMonth,
      category: FEATURE_CATEGORIES.BASIC_DETAILS,
    },
    {
      key: "hide_categories",
      type: "multiSelect",
      title: "Hide Categories",
      value: newHideCategories,
      defaultValue:
        String(updatedHideCategories) ||
        String(hideCategories) ||
        String(newHideCategories),
      options: hideCategoryOptions,
      onChange: setNewHideCategories,
      category: FEATURE_CATEGORIES.COMMISSIONS,
    },
    // {
    //   key: "datasheet_v2",
    //   type: "switch",
    //   title: "Datasheet V2",
    //   description: "To enable datasheet v2 experience for an account.",
    //   value: newDatasheetV2,
    //   defaultValue: newDatasheetV2 ? SWITCH_STATUS.ON : SWITCH_STATUS.OFF,
    //   onChange: setNewDatasheetV2,
    //   category: FEATURE_CATEGORIES.NOTIFICATIONS,
    // },
    {
      key: "show_commission_percent",
      type: "switch",
      title: "Show Commission Percent",
      description: "Shows commission percent in statements",
      value: newShowCommissionPercent,
      defaultValue: newShowCommissionPercent
        ? SWITCH_STATUS.ON
        : SWITCH_STATUS.OFF,
      onChange: setNewShowCommissionPercent,
      category: FEATURE_CATEGORIES.COMMISSIONS,
    },
    {
      key: "show_commission_buddy",
      type: "switch",
      title: "Show Commission Buddy",
      description: "Show Calculated widget in Payee Dashobard",
      value: newShowCommissionBuddy,
      defaultValue: newShowCommissionBuddy
        ? SWITCH_STATUS.ON
        : SWITCH_STATUS.OFF,
      onChange: setNewShowCommissionBuddy,
      category: FEATURE_CATEGORIES.COMMISSIONS,
    },
    {
      key: "show_territory_plan",
      type: "switch",
      title: "Show Territory Plan",
      description: "Show TQM in Payee Dashobard",
      value: newShowTerritoryPlan,
      defaultValue: newShowTerritoryPlan ? SWITCH_STATUS.ON : SWITCH_STATUS.OFF,
      onChange: setnewShowTerritoryPlan,
      category: FEATURE_CATEGORIES.NEW_FEATURE,
    },
    {
      key: "settlement_v2",
      type: "switch",
      title: "Settlement using payout snapshot",
      description:
        "To use payout snapshot data to calculate settlements for the client.",
      value: newSettlementV2,
      defaultValue: newSettlementV2 ? SWITCH_STATUS.ON : SWITCH_STATUS.OFF,
      onChange: setNewSettlementV2,
      category: FEATURE_CATEGORIES.INTERNAL,
    },
    {
      key: "run_settlement_report",
      type: "select",
      title: "Run settlement report",
      description:
        "This option determines if the settlement report should be run automatically.",
      value: newRunSettlementReport,
      defaultValue: newRunSettlementReport?.name,
      onChange: setNewRunSettlementReport,
      options: runSettlementReportOptions,
      category: FEATURE_CATEGORIES.INTERNAL,
    },
    {
      key: "status",
      type: "select",
      title: "Status",
      value: selectedStatus,
      options: statusOptions,
      defaultValue: isLive ? statusOptions[0].name : statusOptions[1].name,
      onChange: setSelectedStatus,
      category: FEATURE_CATEGORIES.BASIC_DETAILS,
    },
    {
      key: "type",
      type: "select",
      title: "Type",
      value: selectedType,
      defaultValue: isTest ? typeOptions[0].name : typeOptions[1].name,
      options: typeOptions,
      onChange: setSelectedType,
      category: FEATURE_CATEGORIES.BASIC_DETAILS,
    },
    {
      key: "show_approval_feature",
      type: "switch",
      title: "Show Approval Feature",
      description: "To enable approvals feature for an account.",
      value: newShowApprovalFeature,
      defaultValue: newShowApprovalFeature
        ? SWITCH_STATUS.ON
        : SWITCH_STATUS.OFF,
      onChange: setNewShowApprovalFeature,
      category: FEATURE_CATEGORIES.NEW_FEATURE,
    },
    {
      key: "allow_only_admins_to_modify_user_name",
      type: "switch",
      title: "Allow only admins to modify user name",
      value: newAllowOnlyAdminsToModifyUserName,
      defaultValue: newAllowOnlyAdminsToModifyUserName
        ? SWITCH_STATUS.ON
        : SWITCH_STATUS.OFF,
      onChange: setNewAllowOnlyAdminsToModifyUserName,
      category: FEATURE_CATEGORIES.NEW_FEATURE,
    },
    {
      key: "edit_locked_quota",
      type: "switch",
      title: "Edit Locked Quota",
      description: "To allow editing of quota in locked periods.",
      value: newEditLockedQuota,
      defaultValue: newEditLockedQuota ? SWITCH_STATUS.ON : SWITCH_STATUS.OFF,
      onChange: setNewEditLockedQuota,
      category: FEATURE_CATEGORIES.COMMISSIONS,
    },
    {
      key: "is_auto_enrich_report",
      type: "switch",
      title: "Auto Enrich Report",
      description: "To allow auto enrichment of commission report",
      value: newIsAutoEnrichReport,
      defaultValue: newIsAutoEnrichReport
        ? SWITCH_STATUS.ON
        : SWITCH_STATUS.OFF,
      onChange: setNewIsAutoEnrichReport,
      category: FEATURE_CATEGORIES.INTERNAL,
    },
    {
      key: "async_export_datasheet",
      type: "switch",
      title: "Async Export Datasheet",
      description: "To enable async export of datasheet",
      value: newAsyncExportDatasheet,
      defaultValue: newAsyncExportDatasheet
        ? SWITCH_STATUS.ON
        : SWITCH_STATUS.OFF,
      onChange: setNewAsyncExportDatasheet,
      category: FEATURE_CATEGORIES.INTERNAL,
    },
    {
      key: "upstream_etl_version",
      type: "select",
      title: "Upstream ETL Version",
      value: newUpstreamEtlVersion,
      defaultValue: newUpstreamEtlVersion.name,
      options: upstreamEtlVersionOptions,
      onChange: setNewUpstreamEtlVersion,
      category: FEATURE_CATEGORIES.INTERNAL,
    },
    {
      key: "expose_comm_reports_in_plan",
      type: "switch",
      title: "Expose commission reports in commission plan",
      description:
        "Option to use Commissions and Quota Attainment data in Commission plan Criteria.",
      value: newExposeCommReportsInPlan,
      error: error?.name?.msg,
      defaultValue: newExposeCommReportsInPlan
        ? SWITCH_STATUS.ON
        : SWITCH_STATUS.OFF,
      onChange: (value) => {
        if (value) {
          setModalInfo({
            modalTitle: "⚠️ Warning",
            modalContent: (
              <>
                <p>
                  Turning this on would increase commission sync time
                  significantly. Exercise caution and enable this only if this
                  functionality is critical.
                </p>
                <br />
                <b>Do you want to proceed?</b>
              </>
            ),
            onConfirm: () => {
              handleDisableCommReportsInPlan(value);
              handleCloseModal();
            },
          });
        } else {
          handleDisableCommReportsInPlan(value);
        }
      },
      category: FEATURE_CATEGORIES.COMMISSIONS,
    },
    {
      key: "allow_adjustments_to_frozen_commission",
      type: "switch",
      title: "Allow Adjustments To Locked Commission",
      description: "Option to add Commission Adjustments to Locked Statements",
      value: newAllowAdjustmentsToFrozenCommission,
      defaultValue: newAllowAdjustmentsToFrozenCommission
        ? SWITCH_STATUS.ON
        : SWITCH_STATUS.OFF,
      onChange: setNewAllowAdjustmentsToFrozenCommission,
      category: FEATURE_CATEGORIES.COMMISSIONS,
    },
    {
      key: "show_chatgpt",
      type: "switch",
      title: "Show ChatGPT",
      description:
        "Summarize Plan Document details and allow users to get specific details through a chat interface",
      value: showChatGPT,
      defaultValue: showChatGPT ? SWITCH_STATUS.ON : SWITCH_STATUS.OFF,
      onChange: setShowChatGPT,
      category: FEATURE_CATEGORIES.NEW_FEATURE,
    },
    {
      key: "isolated_snowflake_database",
      type: "switch",
      title: "Isolated Snowflake Database",
      description:
        "To isolate the client's data in a separate database from other clients",
      value: newIsolatedSnowflakeDatabase,
      defaultValue: newIsolatedSnowflakeDatabase
        ? SWITCH_STATUS.ON
        : SWITCH_STATUS.OFF,
      onChange: setNewIsolatedSnowflakeDatabase,
      category: FEATURE_CATEGORIES.NEW_FEATURE,
      isDisabled: true,
    },
    {
      key: "chrome_extension_enabled",
      type: "switch",
      title: "Enable Chrome extension",
      description: "Enable everstage as a chrome extension for the client",
      value: newChromeExtensionEnabled,
      defaultValue: newChromeExtensionEnabled
        ? SWITCH_STATUS.ON
        : SWITCH_STATUS.OFF,
      onChange: setNewChromeExtensionEnabled,
      category: FEATURE_CATEGORIES.NEW_FEATURE,
    },
    {
      key: "enable_everai",
      type: "switch",
      title: "Enable EverAI",
      description: "Enable everAI RBAC for the client",
      value: newEnableEverai,
      defaultValue: newEnableEverai ? SWITCH_STATUS.ON : SWITCH_STATUS.OFF,
      onChange: setNewEnableEverai,
      category: FEATURE_CATEGORIES.NEW_FEATURE,
    },
    {
      key: "created_on",
      title: "Created On",
      value: createdAt,
      category: FEATURE_CATEGORIES.BASIC_DETAILS,
    },
    {
      key: "show_superset_dashboard",
      type: "switch",
      title: "Show Analytics",
      description: "To enable analytics (superset) feature for an account.",
      value: newShowSupersetDashboard,
      defaultValue: newShowSupersetDashboard
        ? SWITCH_STATUS.ON
        : SWITCH_STATUS.OFF,
      onChange: setNewShowSupersetDashboard,
      category: FEATURE_CATEGORIES.NEW_FEATURE,
    },
    {
      key: "show_return_v1_button",
      type: "switch",
      title: "Allow usage of the copy-paste legacy (v1) data import experience",
      description: "Allow v1 data import experience.",
      value: newShowReturnV1Button,
      defaultValue: newShowReturnV1Button
        ? SWITCH_STATUS.ON
        : SWITCH_STATUS.OFF,
      onChange: setNewShowReturnV1Button,
      category: FEATURE_CATEGORIES.NEW_FEATURE,
    },
    {
      key: "show_payout_table_breakdown",
      type: "switch",
      title: "Show Payout Breakdown Table",
      description:
        "Show payout details when arrears are processed and payments are registered.",
      value: showPayoutTablebreakdown,
      defaultValue: showPayoutTablebreakdown
        ? SWITCH_STATUS.ON
        : SWITCH_STATUS.OFF,
      onChange: setShowPayoutTablebreakdown,
      category: FEATURE_CATEGORIES.INTERNAL,
    },
    {
      key: "custom_calendar",
      type: "switch",
      title: "Show Custom Calendar",
      value: newCustomCalendar,
      defaultValue: newCustomCalendar ? SWITCH_STATUS.ON : SWITCH_STATUS.OFF,
      onChange: setNewCustomCalendar,
      category: FEATURE_CATEGORIES.COMMISSIONS,
    },
    {
      key: "enable_concurrent_sessions",
      type: "switch",
      title: "Enable Concurrent Sessions",
      description:
        "To enable concurrent sessions for all users under the client. This will allow users to login from multiple devices/ browsers at the same time.",
      value: newEnableConcurrentSessions,
      defaultValue: newEnableConcurrentSessions
        ? SWITCH_STATUS.ON
        : SWITCH_STATUS.OFF,
      onChange: setNewEnableConcurrentSessions,
      category: FEATURE_CATEGORIES.NEW_FEATURE,
    },
    {
      key: "statement_logo",
      type: "image",
      title: "Statement Logo",
      description:
        "Uploading a logo will replace the default logo in Statements PDF.",
      value: statementPreviewSrc || statementLogoUrl,
      alt: "statement-logo",
      onChange: setNewStatementLogo,
      category: FEATURE_CATEGORIES.BASIC_DETAILS,
      imageSizeDiscalimer: true,
    },
    {
      key: "documentation_url",
      type: "input",
      title: "Documentation Url",
      description:
        "Hey , can you add the link to the tango docs? It’s the redirect link from the solutions corner! ",
      value: newDocumentationUrl,
      onChange: setNewDocumentationUrl,
      category: FEATURE_CATEGORIES.HELP_CENTER,
    },
    {
      key: "help_doc_user_role",
      type: "multiSelect",
      title: "Help Doc User Role",
      value: displayNames,
      description:
        "You’ll be able to see the Solutions Corner in your profile section for any roles you’ve added.",
      defaultValue: String(displayNames),
      options: userRoleOption,
      onChange: setNewUserRoles,
      category: FEATURE_CATEGORIES.HELP_CENTER,
    },
    {
      key: "show_forecast",
      type: "switch",
      title: "Show Forecast",
      value: newShowForecast,
      defaultValue: newShowForecast ? SWITCH_STATUS.ON : SWITCH_STATUS.OFF,
      onChange: setNewShowForecast,
      category: FEATURE_CATEGORIES.NEW_FEATURE,
    },
    {
      key: "quota_effective_dated",
      type: "switch",
      title: "Quota Effective Dated",
      value: newQuotaEffectiveDated,
      defaultValue: newQuotaEffectiveDated
        ? SWITCH_STATUS.ON
        : SWITCH_STATUS.OFF,
      onChange: setNewQuotaEffectiveDated,
      category: FEATURE_CATEGORIES.NEW_FEATURE,
    },
    {
      key: "allow_quota_settings_override",
      type: "switch",
      title: "Allow Quota Settings Override",
      value: newAllowQuotaSettingsOverride,
      defaultValue: newAllowQuotaSettingsOverride
        ? SWITCH_STATUS.ON
        : SWITCH_STATUS.OFF,
      onChange: setNewAllowQuotaSettingsOverride,
      category: FEATURE_CATEGORIES.NEW_FEATURE,
    },
    {
      key: "allow_annual_quota_effective_dated",
      type: "switch",
      title: "Allow Annual Quota Effective Dated",
      description:
        "To allow setting of effective dated annual quotas with an annual schedule.",
      value: newAllowAnnualQuotaEffectiveDated,
      defaultValue: newAllowAnnualQuotaEffectiveDated
        ? SWITCH_STATUS.ON
        : SWITCH_STATUS.OFF,
      onChange: setNewAllowAnnualQuotaEffectiveDated,
      category: FEATURE_CATEGORIES.COMMISSIONS,
    },
    {
      key: "allow_csv_upload_bulk_payment_register",
      type: "switch",
      title: "Allow CSV Upload for Bulk Payment Register",
      description:
        "To allow CSV upload for bulk payment register in the client.",
      value: newAllowCsvUploadBulkPaymentRegister,
      defaultValue: newAllowCsvUploadBulkPaymentRegister
        ? SWITCH_STATUS.ON
        : SWITCH_STATUS.OFF,
      onChange: setNewAllowCsvUploadBulkPaymentRegister,
      category: FEATURE_CATEGORIES.COMMISSIONS,
    },
    {
      key: "show_g2_review_form",
      type: "select",
      options: g2ReviewFormOptions,
      title: "Enable G2 Review Form",
      value: newShowG2ReviewForm,
      defaultValue: newShowG2ReviewForm?.name,
      onChange: setNewShowG2ReviewForm,
      category: FEATURE_CATEGORIES.NEW_FEATURE,
    },
    {
      key: "enable_custom_workflows",
      type: "switch",
      title: "Enable Custom Workflows",
      value: newEnableCustomWorkflows,
      defaultValue: newEnableCustomWorkflows
        ? SWITCH_STATUS.ON
        : SWITCH_STATUS.OFF,
      onChange: setNewEnableCustomWorkflows,
      category: FEATURE_CATEGORIES.NEW_FEATURE,
    },
    {
      key: "crystal_custom_calendar_future_periods",
      type: "select",
      title:
        "Number of future periods for Crystal projection - Custom Calendar",
      description:
        "Number of upcoming periods displayed in the dropdown menu when projecting commissions in Crystal.",
      value: newCrystalCustomCalendarFuturePeriods,
      defaultValue: newCrystalCustomCalendarFuturePeriods?.key,
      onChange: setNewCrystalCustomCalendarFuturePeriods,
      category: FEATURE_CATEGORIES.COMMISSIONS,
      options: crystalCustomPeriodOptions,
    },
    {
      key: "enable_support_user_access",
      type: "switch",
      title: "Enable TSAR",
      description:
        "If this is turned off, login to client's account is possible by common-support-email. If turned on, only support users with a membership can access the client.",
      value: newEnableSupportUserAccess,
      defaultValue: newEnableSupportUserAccess
        ? SWITCH_STATUS.ON
        : SWITCH_STATUS.OFF,
      onChange: setNewEnableSupportUserAccess,
      category: FEATURE_CATEGORIES.SECURITY,
      isDisabled: isProdEnv && enableSupportUserAccess,
    },
    {
      key: "enable_tsar_webapp_custom_roles",
      type: "switch",
      title: "Enable TSAR Webapp Custom Roles",
      description:
        "If this is turned off, only Power Admin role is available for TSAR Memberships.",
      value: newEnableTsarWebappCustomRoles,
      defaultValue: newEnableTsarWebappCustomRoles
        ? SWITCH_STATUS.ON
        : SWITCH_STATUS.OFF,
      onChange: setNewEnableTsarWebappCustomRoles,
      category: FEATURE_CATEGORIES.SECURITY,
    },
    {
      key: "enable_custom_theme",
      type: "switch",
      title: "Allow Custom Theme",
      description:
        "To enable the feature that allows users to generate custom theme for their own organization.",
      value: newEnableCustomTheme,
      defaultValue: newEnableCustomTheme ? SWITCH_STATUS.ON : SWITCH_STATUS.OFF,
      onChange: setNewEnableCustomTheme,
      category: FEATURE_CATEGORIES.INTERNAL,
    },

    {
      key: "enable_sidebar_v3",
      type: "switch",
      title: "Enable Sidebar v3",
      description: "To enable the new sidebar v3 for the client",
      value: newEnableSidebarV3,
      defaultValue: newEnableSidebarV3 ? SWITCH_STATUS.ON : SWITCH_STATUS.OFF,
      onChange: setNewEnableSidebarV3,
      category: FEATURE_CATEGORIES.INTERNAL,
    },
    {
      key: "enable_contract_permissions",
      type: "switch",
      title: "Enable DocuSign Permission Check for Contracts",
      description:
        "Enforces a permission check before allowing contract creation or updates",
      value: newEnableContractPermissions,
      defaultValue: newEnableContractPermissions
        ? SWITCH_STATUS.ON
        : SWITCH_STATUS.OFF,
      onChange: setNewEnableContractPermissions,
      category: FEATURE_CATEGORIES.NEW_FEATURE,
    },
    {
      key: "enable_multi_language_support",
      type: "switch",
      title: "Enable Multi Language Support",
      description:
        "Enable this toggle to allow admins to add or manage multiple languages in Custom Terminology. Turning it off will prevent further changes but will not impact existing translations already visible to users.",
      value: newEnableMultiLanguageSupport,
      defaultValue: newEnableMultiLanguageSupport
        ? SWITCH_STATUS.ON
        : SWITCH_STATUS.OFF,
      onChange: setNewEnableMultiLanguageSupport,
      category: FEATURE_CATEGORIES.NEW_FEATURE,
    },
    {
      key: "is_secure_admin_ui_auth0_user_mgmt",
      type: "switch",
      title: "Secure Admin UI Auth0 User Management",
      description:
        "This flag is for Admin UI; where the user management is done at the client level. Enabling this will - 1. Disable the ability to create new users from the Admin UI. 2. Prevent passwords exposure. 3. Send password reset links directly to the client's high-profile users.",
      value: newIsSecureAdminUiAuth0UserMgmt,
      defaultValue: newIsSecureAdminUiAuth0UserMgmt
        ? SWITCH_STATUS.ON
        : SWITCH_STATUS.OFF,
      onChange: setNewIsSecureAdminUiAuth0UserMgmt,
      category: FEATURE_CATEGORIES.SECURITY,
    },
  ];

  const inputFieldObjectsForDevs = [
    {
      key: "split_summation_to_li",
      type: "switch",
      title: "Split Summation Commission to Line Items",
      description: "Option to split summation commission to line items",
      value: newSplitSummationToLi,
      defaultValue: newSplitSummationToLi
        ? SWITCH_STATUS.ON
        : SWITCH_STATUS.OFF,
      onChange: setnewSplitSummationToLi,
      category: FEATURE_CATEGORIES.COMMISSIONS,
    },
    {
      key: "show_metrics",
      type: "switch",
      title: "Show Metrics",
      description: "To enable the Metrics feature for an account.",
      value: newShowMetrics,
      defaultValue: newShowMetrics ? SWITCH_STATUS.ON : SWITCH_STATUS.OFF,
      onChange: setNewShowMetrics,
      category: FEATURE_CATEGORIES.INTERNAL,
    },
    // {
    //   key: "run_sync_for_multiple_period",
    //   type: "switch",
    //   title: "Use commission sync for multiple period",
    //   description:
    //     "Enables user to run commission sync for multiple period instead of just one period",
    //   value: newrunSyncMultiplePeriod,
    //   defaultValue: newrunSyncMultiplePeriod
    //     ? SWITCH_STATUS.ON
    //     : SWITCH_STATUS.OFF,
    //   onChange: setNewRunSyncMultiplePeriod,
    //   category: FEATURE_CATEGORIES.COMMISSIONS,
    // },
    {
      key: "upload_excel_files_in_custom_object",
      type: "switch",
      title: "Support .xls/.xlsx data upload",
      description:
        "Enables user to upload .xls/.xlsx file types in custom objects",
      value: newEnableExcleUpload,
      defaultValue: newEnableExcleUpload ? SWITCH_STATUS.ON : SWITCH_STATUS.OFF,
      onChange: setNewEnableExcelUpload,
      category: FEATURE_CATEGORIES.NEW_FEATURE,
    },
    {
      key: "avoid_iframe_in_contracts",
      type: "switch",
      title: "Avoid Iframe in Contracts",
      description: "To avoid using iframe in contracts.",
      value: newAvoidIframeInContracts,
      defaultValue: newAvoidIframeInContracts
        ? SWITCH_STATUS.ON
        : SWITCH_STATUS.OFF,
      onChange: setNewAvoidIframeInContracts,
      category: FEATURE_CATEGORIES.NEW_FEATURE,
    },
    {
      key: "insert_meta_data_to_vec_db",
      type: "switch",
      title: "Insert Meta Data to Vector DB",
      description: "",
      value: newInsertMetaDataToVecDb,
      defaultValue: newInsertMetaDataToVecDb
        ? SWITCH_STATUS.ON
        : SWITCH_STATUS.OFF,
      onChange: setNewInsertMetaDataToVecDb,
      category: FEATURE_CATEGORIES.INTERNAL,
    },
    {
      key: "use_multi_engine_stormbreaker",
      type: "switch",
      title: "Use multi-engine Stormbreaker for datasheet data read",
      description:
        "Allows multi-engine Stormbreaker to be used for datasheet data read. This is a performance optimization.",
      value: newUseMultiEngineStormbreaker,
      defaultValue: newUseMultiEngineStormbreaker
        ? SWITCH_STATUS.ON
        : SWITCH_STATUS.OFF,
      onChange: setNewUseMultiEngineStormbreaker,
      category: FEATURE_CATEGORIES.INTERNAL,
    },
    {
      key: "enable_hris_integration",
      type: "switch",
      title: "Enable HRIS Integration",
      description: "To enable HRIS Integration for the client",
      value: newEnableHrisIntegration,
      defaultValue: newEnableHrisIntegration
        ? SWITCH_STATUS.ON
        : SWITCH_STATUS.OFF,
      onChange: setNewEnableHrisIntegration,
      category: FEATURE_CATEGORIES.NEW_FEATURE,
    },
    // {
    //   key: "use_aggrid_for_pdf_export",
    //   type: "switch",
    //   title: "Use AG Grid in Statements PDF",
    //   description:
    //     "Turning this off will increase the performance of Statements PDF",
    //   value: newUseAgGrid,
    //   defaultValue: newUseAgGrid ? SWITCH_STATUS.ON : SWITCH_STATUS.OFF,
    //   onChange: setNewUseAgGrid,
    //   category: FEATURE_CATEGORIES.PERFORMANCE_SPECIFIC,
    // },
    // Commenting out create_analytics_default_dashboard flag - will be split into two flags
    // {
    //   key: "create_analytics_default_dashboard",
    //   type: "switch",
    //   title: "Create Analytics Default Dashboard",
    //   description: "Setup default dashboard in Superset",
    //   value: getDefaultDashboardToggleValue(newAnalyticsDefaultDashboardStatus),
    //   defaultValue: getDefaultDashboardToggleValue(
    //     newAnalyticsDefaultDashboardStatus
    //   )
    //     ? SWITCH_STATUS.ON
    //     : SWITCH_STATUS.OFF,
    //   onChange: (value) => {
    //     if (value) {
    //       if (showDataSourcesV2) {
    //         showMessage("Error", {
    //           type: NOTIFICATION_TYPE.ERROR,
    //           description:
    //             "Analytics default dashboard cannot be created when Show data sources v2 is enabled",
    //         });
    //         return;
    //       }
    //       setModalInfo({
    //         modalTitle: "⚠️ Warning",
    //         modalContent: (
    //           <>
    //             <p>Once this feature flag is enabled, it cannot be disabled.</p>
    //             <br />
    //             <b>Do you want to proceed?</b>
    //           </>
    //         ),
    //         onConfirm: () => {
    //           setNewAnalyticsDefaultDashboardStatus(
    //             defaultDashboardStatusValues.PROCESSING
    //           );
    //           handleCloseModal();
    //         },
    //       });
    //     }
    //   },
    //   category: FEATURE_CATEGORIES.NEW_FEATURE,
    // },
    {
      key: "hard_delete_sync_frequency",
      type: "select",
      title: "Hard Delete Frequency",
      options: hardDeleteFrequency,
      value: hardDeleteSyncFrequency,
      defaultValue: hardDeleteSyncFrequency?.name,
      onChange: setHardDeleteSyncFrequency,
    },
    hardDeleteSyncFrequency?.name === "Weekly" ? weeklyItem : {},
    hardDeleteSyncFrequency?.name === "Monthly" ? monthlyItem : {},
  ];

  const renderCustomerFields = (props) => {
    const {
      type = "",
      title = "",
      value = "",
      defaultValue = "",
      key,
      onChange = () => {},
      options = [],
      error = "",
      alt = "",
      description = "",
      imageSizeDiscalimer = false,
      ...remainingProps
    } = props;
    let content = "";
    let bodyClassName = "";
    if (isEdit) {
      switch (type) {
        case "input":
          content = (
            <input
              type="text"
              name={key}
              id={key}
              className="block w-full border-ever-base-300 sm:text-sm focus:ring-ever-base-500 focus:border-ever-base-500 rounded-md"
              placeholder={value}
              onChange={(e) => onChange(e.target.value)}
              {...remainingProps}
            />
          );
          break;
        case "select":
          content = (
            <Select
              label=""
              options={options}
              selected={value}
              setSelected={onChange}
            />
          );
          break;
        case "multiSelect":
          content = (
            <Multiselect
              label=""
              listOptions={options}
              onChange={(_selectedOptions) => onChange(_selectedOptions)}
              defaultSelectedOptions={value}
            />
          );
          break;
        case "switch":
          bodyClassName = "h-9 flex items-center";
          content = (
            <HeadlessSwitch.Group
              as="div"
              className="flex items-center justify-between"
            >
              <Switch
                id={key}
                enabled={value}
                toggleEnabled={() => onChange(!value)}
                {...remainingProps}
              />
            </HeadlessSwitch.Group>
          );
          break;
        case "image":
          content = (
            <div className="max-w-lg flex justify-center px-6 py-4 border-2 border-ever-base-300 border-dashed rounded-md">
              {value ? (
                <EverAvatar src={value} name={alt} size="40" />
              ) : (
                <AddImageIcon />
              )}
              <div className="space-y-1 text-center pl-3">
                <div className="flex items-center text-sm text-ever-base-content-low">
                  <label
                    htmlFor={key}
                    className="relative cursor-pointer bg-ever-base rounded-md font-medium text-ever-primary hover:text-ever-primary focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-primary"
                  >
                    <span>Upload a file</span>
                    <input
                      id={key}
                      name="file-upload"
                      type="file"
                      className="sr-only"
                      onChange={(e) => onChange(e.target.files[0])}
                    />
                  </label>
                  <span className="pl-1">or drag and drop</span>
                </div>
                <div className="text-xs text-ever-base-content-mid">
                  PNG, JPG, GIF up to 10MB
                </div>
                {imageSizeDiscalimer ? (
                  <div className="text-xs text-ever-base-content-mid">
                    Recommended logo size - 112 x 32 px
                  </div>
                ) : (
                  ""
                )}
              </div>
            </div>
          );
          break;
        default:
          content = value || "-";
          break;
      }
    } else {
      switch (type) {
        case "image":
          {
            key === "statement_logo" && !value
              ? (content = <p>-</p>)
              : (content = <EverAvatar src={value} name={alt} size="40" />);
          }
          break;

        default: {
          const modifiedValue = Array.isArray(value) ? String(value) : value;
          content = defaultValue || modifiedValue || "-";
          break;
        }
      }
    }
    return (
      <div key={key}>
        <div className="text-lg font-bold text-ever-base-content-mid flex gap-1">
          {title}{" "}
          {description && <Info className="mt-1" description={description} />}
        </div>
        <div
          className={`mt-1 break-all text-sm text-ever-base-content ${bodyClassName}`}
        >
          {content}
        </div>
        {/* {!isEdit && key === "create_analytics_default_dashboard" && (
          <div className={`text-sm text-gray-900 ${bodyClassName}`}>
            <AnalyticsDefaultDashboardLink
              dashboardStatus={newAnalyticsDefaultDashboardStatus}
              clientId={clientId}
              accessToken={accessToken}
            />
          </div>
        )} */}
        {error ? (
          <div
            className={`text-ever-base-content sm:col-span-2 ${bodyClassName}`}
          >
            <p className="mt-2 text-sm text-red-600" id="name-error">
              {error}
            </p>
          </div>
        ) : null}
      </div>
    );
  };

  const renderAllFeatures = () => {
    const allFields = inputFieldObjects.concat(inputFieldObjectsForDevs);
    const featureCategoryMap = mapValues(
      groupBy(allFields, "category"),
      (inputList) => inputList.map((input) => omit(input, "category"))
    );
    let orderedCategories = Object.entries(featureCategoryMap);

    orderedCategories.sort((a, b) => {
      const keyA = a[0];
      const keyB = b[0];
      return CATEGORY_ORDER[keyA] - CATEGORY_ORDER[keyB];
    });

    return orderedCategories.map((entries, idx) => {
      return (
        <div key={idx} className="p-6 flex flex-col gap-3">
          <div key={entries[0]} className="text-xl">
            {entries[0] !== "undefined" ? entries[0] : "Others"}
          </div>

          <div
            className={`border border-solid border-ever-base-400 bg-ever-base shadow-md rounded-xl p-6`}
          >
            <div className="grid lg:grid-cols-3 md:grid-cols-2 sm:grid-cols-1 gap-8">
              {entries[1].map(
                (fields) => fields?.key && renderCustomerFields(fields)
              )}
            </div>
          </div>
        </div>
      );
    });
  };

  return (
    props.customer &&
    !qcLoading && (
      <Drawer open={open} onClose={onCloseWrapper} disableClickOutClose>
        <div className="w-screen max-w-full">
          <form className="h-full divide-y divide-ever-base-200 flex flex-col bg-ever-base overflow-y-auto">
            <div className="flex-1 h-0 overflow-y-auto pt-20">
              <DrawerHeader
                name={props.customer.name}
                onCloseWrapper={onClose}
              />
              {!isEdit ? (
                <div className="flex justify-end m-4">
                  <Button
                    onClick={() => setIsEdit(true)}
                    type="ghost"
                    prependIcon={
                      <EditPencilAltIcon
                        className="w-6 h-6"
                        aria-hidden="true"
                      />
                    }
                  >
                    Edit Customer
                  </Button>
                </div>
              ) : null}
              {renderAllFeatures()}
            </div>
            {isEdit ? (
              <div className="flex-shrink-0 px-4 py-4 flex justify-end gap-3 border-0 border-t border-solid border-ever-base-400 bg-ever-base-100">
                <EverButton
                  type="ghost"
                  color="primary"
                  onClick={() => resetStateValues()}
                >
                  Cancel
                </EverButton>
                <EverButton onClick={onSubmit} disabled={loading}>
                  {loading && (
                    <FontAwesomeIcon
                      className="fa-spin fa-pulse h-5 w-5 mr-3"
                      icon={saveIcon}
                    />
                  )}
                  Save
                </EverButton>
              </div>
            ) : (
              <></>
            )}
          </form>
          <Modal open={!isEmpty(modalInfo)} rootClassName="max-w-md w-full">
            <Modal.Title onClose={handleCloseModal}>
              {modalInfo.modalTitle}
            </Modal.Title>
            <Modal.Content>{modalInfo.modalContent}</Modal.Content>
            <Modal.Footer
              hasCancel
              hasConfirm
              onCancel={handleCloseModal}
              onConfirm={modalInfo.onConfirm}
              className={{
                confirm: "disabled:opacity-50",
                cancel: "order-1",
              }}
            />
          </Modal>
        </div>
      </Drawer>
    )
  );
}
