import { SearchLgIcon } from "@everstage/evericons/duotone";
import { observer } from "mobx-react";
import React from "react";
import { useRecoilValue, useSetRecoilState } from "recoil";

import { RBAC_ROLES } from "~/Enums";
import { globalSearchAtom, myClientAtom } from "~/GlobalStores/atoms";
import { useUserPermissionStore } from "~/GlobalStores/UserPermissionStore";
import { getClientFeatures } from "~/Utils/ClientFeaturesUtils";
import { useModules } from "~/v2/hooks";

import { ClientSwitchModal } from "./ClientSwitchModal";
import { HelpCenter } from "./HelpCenter";
import { UserDetails } from "./UserDetails";
import { EverTg } from "../EverTypography";

export const HeaderBar = observer(() => {
  const myClient = useRecoilValue(myClientAtom);
  const { isCPQ } = useModules();
  const clientFeatures = getClientFeatures(myClient);
  const { hasPermissions } = useUserPermissionStore();
  return (
    <div className="h-11 z-10 relative grid grid-cols-3 items-center gap-4 justify-between pr-1.5 grow-0 shrink-0">
      {/* CLIENT DETAILS */}
      <div className="w-52">
        <ClientSwitchModal location="HeaderBar" />
      </div>

      <div className="flex items-center justify-center">
        {hasPermissions(RBAC_ROLES.VIEW_GLOBAL_SEARCH) &&
          clientFeatures?.globalSearch &&
          !isCPQ && <SearchBar />}
      </div>

      {/* BOTTOM USER/PAYEE DETAILS */}
      <div className="flex gap-3 items-center justify-end">
        <HelpCenter />
        <UserDetails />
      </div>
    </div>
  );
});

function SearchBar() {
  const setGlobalSearch = useSetRecoilState(globalSearchAtom);

  return (
    <div
      className="w-[524px] h-8 py-1.5 px-3 rounded bg-ever-accent-400 hover:bg-ever-accent-300 focus:bg-ever-accent-200 border border-solid border-ever-accent-400 hover:border-ever-accent-200 focus:border-ever-accent-200 flex items-center gap-3 text-ever-accent-content cursor-text"
      onClick={() => {
        const globalSearch = {
          showSearchModal: true,
        };
        setGlobalSearch(globalSearch);
      }}
    >
      <SearchLgIcon className="size-4" />
      <EverTg.Caption>Search Everstage</EverTg.Caption>
      <div className="grow"></div>
      <div className="flex gap-1 items-center">
        <div className="size-4 flex items-center text-ever-base-400 justify-center bg-ever-accent-600 rounded-sm">
          <EverTg.Caption>⌘</EverTg.Caption>
        </div>
        <div className="size-4 flex items-center text-ever-base-400 justify-center bg-ever-accent-600 rounded-sm">
          <EverTg.Caption>K</EverTg.Caption>
        </div>
      </div>
    </div>
  );
}
