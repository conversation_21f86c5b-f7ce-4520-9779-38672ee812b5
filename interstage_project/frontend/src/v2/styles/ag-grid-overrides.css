/**
* The following css overrides the ag-grid styles
*/
.ag-theme-material {
  --ag-font-family: "Inter", sans-serif !important;
  --ag-background-color: theme(colors.ever.base.DEFAULT);
  --ag-row-hover-color: theme(colors.ever.primary.lite.DEFAULT);
  --ag-selected-row-background-color: theme(colors.ever.primary.lite.DEFAULT);
  --ag-material-ever-primary-color: theme(colors.ever.primary.DEFAULT);
  --ag-material-accent-color: theme(colors.ever.primary.DEFAULT);
  --ag-checkbox-checked-color: theme(colors.ever.primary.DEFAULT);
  --ag-font-size: theme(fontSize.base);
  --ag-data-color: theme(colors.ever.base.content.DEFAULT);
  --ag-border-color: theme(colors.ever.base.400);
  --ag-row-border-color: theme(colors.ever.base.400);
  --ag-foreground-color: theme(colors.ever.base.content.DEFAULT);
  --ag-header-foreground-color: theme(colors.ever.base.content.DEFAULT);
  --ag-header-background-color: theme(colors.ever.base.100);
  --ag-header-cell-hover-background-color: theme(colors.ever.base.100);
  --ag-value-change-value-highlight-background-color: theme(
    colors.ever.chartColors.41
  );

  --ag-border-radius: theme(borderRadius.lg);
  --ag-checkbox-unchecked-color: theme(colors.ever.base.content.mid);
  --ag-secondary-foreground-color: theme(colors.ever.base.content.mid);

  font-size: var(--ag-font-size);
}

.ag-theme-material.overlay999 .ag-overlay-loading-wrapper {
  z-index: 999;
}

.ag-theme-material .ag-tool-panel-wrapper {
  background-color: theme("colors.ever.base.DEFAULT");
  padding: 20px;
  min-width: 280px;
}
.ag-theme-material .ag-group-title-bar {
  background-color: theme("colors.ever.base.100");
  border-radius: 8px;
  margin-bottom: 8px;
}
.ag-theme-material .ag-cell {
  display: flex;
  align-items: center;
}

.ag-theme-material .ag-cell.ag-right-aligned-cell {
  @apply justify-end;
}

.ag-theme-material .ag-pinned-left-cols-container,
.ag-theme-material .ag-pinned-left-header {
  box-shadow: 2px 0px 4px rgba(35, 101, 255, 0.1);
  z-index: 2;
}

/**
* Adds z-index to sticky top and bottom so that the sticky top always appears above the child rows columns
*/
.ag-sticky-bottom,
.ag-sticky-top {
  z-index: 3;
}

.ag-theme-material .ag-pinned-right-cols-container,
.ag-theme-material .ag-pinned-right-header {
  box-shadow: -2px 0px 4px rgba(35, 101, 255, 0.1);
}

.ag-full-width-row {
  z-index: 3;
}

.ag-theme-material .ag-root .ag-header-row .ag-header-cell {
  @apply overflow-x-hidden;
}

.ag-theme-material .ag-root-wrapper {
  @apply border border-solid border-ever-base-400 rounded-lg;
}

/** Zebra grid style */
.zebra-grid .ag-row-odd {
  @apply bg-ever-base-50;
  &.adjusted-rows {
    @apply bg-ever-warning-lite;
  }
  &.already-adjusted-rows {
    @apply bg-ever-chartColors-12/10;
  }
  &.ag-cell-under-span {
    @apply !border-0 !bg-transparent;
  }
  &.dangling-row {
    @apply bg-ever-error-lite;
  }
}
.zebra-grid .ag-row-even {
  @apply bg-ever-base;
  &.adjusted-rows {
    @apply bg-ever-warning-lite;
  }
  &.already-adjusted-rows {
    @apply bg-ever-chartColors-12/10;
  }
  &.dangling-row {
    @apply bg-ever-error-lite;
  }
  &.ag-cell-under-span {
    @apply !border-0 !bg-transparent;
  }
}
.zebra-grid .ag-row-even.ag-row-hover {
  background-color: var(--ag-row-hover-color) !important;
  &.dangling-row {
    background-color: theme("colors.ever.error.lite.DEFAULT") !important;
  }
  &.adjusted-rows {
    @apply !bg-ever-warning-lite;
  }
  &.already-adjusted-rows {
    @apply !bg-ever-chartColors-12/10;
  }
}
.zebra-grid .ag-row-odd.ag-row-hover {
  background-color: var(--ag-row-hover-color) !important;
  &.dangling-row {
    background-color: theme("colors.ever.error.lite.DEFAULT") !important;
  }
  &.adjusted-rows {
    @apply !bg-ever-warning-ring;
  }
  &.already-adjusted-rows {
    @apply !bg-ever-chartColors-12/10;
  }
}
.zebra-grid .ag-row-even.ag-row-selected {
  background-color: var(--ag-selected-row-background-color) !important;
}
.zebra-grid .ag-row-odd.ag-row-selected {
  background-color: var(--ag-selected-row-background-color) !important;
}

/** No border grid style */
.no-border .ag-root-wrapper {
  @apply border-0 border-b rounded-none;
}

/**
* The following css overrides the ag-grid styles for Row Grouped tables.
*/
.ag-center-cols-viewport .ag-row.ag-row-level-1 {
  @apply bg-ever-base-50;
}
.ag-center-cols-viewport .ag-row-level-0.ag-row.ag-row-footer {
  @apply bg-ever-base-100;
}

.ag-center-cols-viewport .ag-row-level-0.ag-row.ag-row-footer {
  @apply font-medium;
}

.ag-center-cols-viewport .ag-row-last.ag-row.ag-row-footer {
  @apply font-semibold;
}

.ag-theme-material .ag-ltr .ag-unselectable .ag-cell-range-single-cell {
  border-color: transparent !important;
  background-color: initial !important;
}

.ag-theme-material
  .ag-ltr
  .ag-unselectable
  .ag-cell-range-single-cell.import-error-row {
  @apply !bg-ever-error-lite;
}

.ag-theme-material
  .ag-header-group-cell:not(.ag-column-resizing)
  + .ag-header-group-cell.ag-column-resizing {
  @apply bg-ever-base-50 text-sm !text-center;
}

.ag-header-cell-resize::after {
  content: "";
  @apply border-l border-0 border-solid border-ever-base-200 right-1 top-1/4 h-1/2 absolute;
}
/**********************/

/**
* The following css overrides the ag-grid editted cell styles
*/

.ag-theme-material .ag-cell-inline-editing {
  @apply px-6 py-0 shadow-none border-0 border-t border-b border-solid border-transparent h-full flex items-center;
}

.ag-cell-inline-editing .ag-cell-edit-wrapper {
  @apply flex items-center;
}

.ag-theme-material input[class^="ag-"][type="text"] {
  @apply !pb-0 !px-2 my-1 flex items-center relative overflow-hidden h-9 bg-ever-base-50 border border-solid border-ever-base-200 rounded-lg w-10;
}

.ag-theme-material input[class^="ag-"][type="text"]:focus {
  border: 1px solid theme("colors.ever.primary.DEFAULT") !important;
  box-shadow: 0px 0px 0px 3px theme("colors.ever.primary.ring"),
    0px 1px 2px theme("colors.ever.primary.lite.DEFAULT") !important;
}

/** Sidebar */
.ag-theme-material .ag-side-buttons {
  @apply w-14 bg-ever-base-50 border-t-0 border-r-0 border-b-0 border-l border-solid border-ever-base-400;
}

.ag-side-button {
  @apply flex justify-center;
}

.ag-selected .ag-side-button-button {
  @apply !w-9 rounded-lg !border !border-solid !border-ever-base-400 bg-ever-base-25 ring ring-ever-base-ring;
}
.ag-selected .ag-side-button-button:active {
  @apply ring ring-ever-base-ring;
}

.ag-side-button-button {
  @apply !w-9 rounded-lg !border !border-solid !border-transparent bg-ever-base-25 ring ring-transparent;
}

.ag-cell-wrapper {
  @apply max-w-full h-full;
}
.ag-cell-wrapper .ag-cell-value {
  @apply p-1;
}
.ag-column-select-header {
  @apply !border-0 !px-1;
}
.ag-column-select {
  @apply !border-0;
}
.ag-column-select-header-checkbox {
  @apply !hidden;
}
.ag-column-select-column-label {
  @apply !text-ever-base-content-mid font-normal;
}
.ag-tool-panel-wrapper {
  @apply shadow-lg;
}
.ag-icon-grip:before {
  content: url("${S3_LOCATION}/columnDrag.svg") !important;
}
.ag-icon-columns {
  margin-bottom: 8px;
}
.ag-icon-columns:before {
  content: url("${S3_LOCATION}/customizeColumns.svg") !important;
}
.ag-icon-pivot {
  margin-bottom: 8px;
}
.ag-icon-pivot:before {
  content: url("${S3_LOCATION}/pivotTable.svg") !important;
}
.ag-set-filter-item-checkbox {
  width: 216px;
}
.ag-checkbox-label {
  width: 90%;
  @apply truncate;
}

.ag-theme-material .ag-mini-filter {
  @apply !mt-0;
}
.ag-theme-material .ag-set-filter-list {
  @apply !h-auto !mb-8;
}

/** Show Error cells */
.ag-theme-material .ag-cell.ag-cell-not-inline-editing.error {
  @apply border border-solid border-ever-error bg-ever-error-lite;
}

.ag-theme-material
  .ag-cell.ag-cell-not-inline-editing.error:not(.ag-cell-range-selected) {
  @apply border border-solid border-ever-error bg-ever-error-lite;
}

/* Show Warning cells */
.ag-theme-material .ag-cell.ag-cell-not-inline-editing.warning {
  @apply border border-solid border-ever-warning bg-ever-warning-lite;
}

/** Double scrollbar issue fix: https://github.com/ag-grid/ag-grid/issues/2911#issuecomment-841356297 */
.ag-center-cols-viewport::-webkit-scrollbar {
  height: 0px;
}

.audit-log-table .ag-cell {
  line-height: 20px;
}

.objects-v2-table.ag-theme-material .ag-cell,
.datasheet-table.ag-theme-material .ag-cell {
  border-right: 1px solid theme("colors.ever.base.400") !important;
}

.datasheet-v2-table .ag-header-cell {
  height: 32px;
}

.ag-theme-material .ag-layout-auto-height .ag-center-cols-clipper,
.ag-theme-material .ag-layout-auto-height .ag-center-cols-container,
.ag-theme-material .ag-layout-print .ag-center-cols-clipper,
.ag-theme-material .ag-layout-print .ag-center-cols-container {
  min-height: theme("height[72]");
}
.ag-theme-material .ag-layout-auto-height .ag-center-cols-viewport {
  min-height: 175px;
}

/* tables by default have 175px of minimmum height, no-min-height overrides it and unsets it */
.no-min-height.ag-theme-material
  .ag-layout-auto-height
  .ag-center-cols-viewport {
  min-height: initial;
}

.datasheet-v2-table.ag-theme-material .ag-cell-highlight {
  background-color: theme("colors.ever.primary.ring") !important;
}
.datasheet-v2-table.ag-theme-material
  .ag-ltr
  .ag-unselectable
  .ag-cell-range-single-cell.selected-gray {
  background-color: theme("colors.ever.base.50") !important;
}
.datasheet-v2-table.ag-theme-material
  .ag-ltr
  .ag-unselectable
  .ag-cell-range-single-cell.clubbed-cell {
  background-color: theme("colors.ever.base.DEFAULT") !important;
  border-color: theme("colors.ever.base.400") !important;
}
.datasheet-v2-table.ag-theme-material
  .ag-ltr
  .ag-unselectable
  .ag-cell-range-single-cell {
  border-color: theme("colors.ever.primary.DEFAULT") !important;
}
.datasheet-v2-table.ag-theme-material
  .ag-layout-auto-height
  .ag-center-cols-container,
.datasheet-v2-table.ag-theme-material
  .ag-layout-auto-height
  .ag-center-cols-clipper {
  min-height: 32px !important;
}

.datasheet-v2-table {
  .ag-side-buttons {
    @apply hidden;
  }
  .ag-tool-panel-wrapper {
    @apply p-0 w-full;
    .ag-react-container {
      @apply w-full;
    }
  }
}

.datasheet-v2-table.ag-theme-material
  .ag-ltr
  .ag-unselectable
  .ag-cell-range-single-cell.adjustColumn {
  background-color: theme("colors.ever.base.DEFAULT") !important;
}

.datasheet-v2-table.ag-theme-material
  .ag-cell.ag-cell-range-selected:not(
    .ag-cell-range-single-cell
  ).ag-cell-range-right {
  border-right-color: theme("colors.ever.primary.DEFAULT") !important;
}
.datasheet-v2-table.ag-theme-material .ag-tool-panel-wrapper {
  min-width: 100%;
}
.datasheet-v2-table.ag-theme-material .ag-cell-inline-editing {
  padding: 0 !important;
}
.datasheet-v2-table.ag-theme-material .ag-cell-inline-editing .ant-input,
.datasheet-v2-table.ag-theme-material
  .ag-cell-edit-wrapper
  .ag-input-field-input,
.datasheet-v2-table.ag-theme-material .ag-cell-inline-editing .ant-picker,
.datasheet-v2-table.ag-theme-material
  .ag-cell-inline-editing
  .ant-select-selector {
  height: 50px !important;
  border-radius: 0;
}
.datasheet-v2-table.ag-theme-material
  .ag-cell-inline-editing
  .date-cell-editor-wrapper {
  width: 100% !important;
}

.objects-v2-table .ag-header-cell {
  height: 40px;
}
.datasheet-v2-table.ag-theme-material .ag-overlay {
  position: absolute;
}
.datasheet-table.ag-theme-material .ag-overlay {
  position: absolute;
}

.ag-theme-material {
  .ag-overlay-no-rows-wrapper {
    &.ag-layout-auto-height {
      padding: 30px 0; /* idk by default A-grid is setting padding-top: 60px. */
    }
  }
}
