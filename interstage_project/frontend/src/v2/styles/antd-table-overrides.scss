.ant-table-wrapper {
  &.max-content {
    .ant-table-container {
      width: max-content;
      .ant-table-content {
        width: max-content;
      }
    }
  }
}

@mixin antd-table-head-and-body {
  .ant-table-thead {
    tr {
      th {
        @apply px-4 border-t-0 border-r-0 border-b border-l-0 border-solid border-ever-base-400 bg-ever-base-50 text-ever-base-content;
      }
      th:first-child {
        @apply rounded-tl-lg;
      }
      th:last-child {
        @apply rounded-tr-lg;
      }
    }
    tr:hover {
      th {
        @apply bg-ever-base-100;
      }
    }
  }
  .ant-table-tbody {
    tr {
      td {
        @apply px-4 border-t-0 border-r-0 border-b border-l-0 border-solid border-ever-base-400 text-ever-base-content;
      }
    }
    tr:last-child {
      td {
        @apply border-b-0;
      }
      td:first-child {
        @apply rounded-bl-lg;
      }
      td:last-child {
        @apply rounded-br-lg;
      }
    }
    tr:hover {
      td {
        @apply bg-ever-base-100;
      }
    }
  }
  .ant-table-cell {
    @apply pl-6;
  }
}

.ant-table,
.ant-table.ant-table-small {
  @apply bg-ever-base text-ever-base-content;
  .ant-table-container {
    @apply border border-solid border-ever-base-400 rounded-none border-r-0 border-l-0;
    @include antd-table-head-and-body;
    .ant-table-content {
      @include antd-table-head-and-body;
    }
  }
  &.ant-table-bordered {
    > .ant-table-container {
      // @apply border border-solid border-ever-base-400 rounded-lg;
      @apply border-0;
      > .ant-table-content,
      > .ant-table-header {
        > table {
          > thead {
            > tr {
              > th {
                @apply border-ever-base-400 border-solid;
                &:first-child {
                  @apply rounded-tl-lg;
                }
                &:nth-last-child(1) {
                  @apply rounded-tr-lg border-r;
                }
              }
            }
          }
          > tbody {
            > tr {
              > td {
                @apply border-ever-base-400;
                &:nth-last-child(1) {
                  @apply border-r;
                }
              }
            }
          }
          tr {
            &:first-child {
              th {
                @apply border-t border-l-0;
              }
            }
            &:last-child {
              td {
                @apply border-b;
              }
            }
            td,
            th {
              &:first-child {
                @apply border-l border-r;
              }
            }
          }
        }
      }
      .ant-table-body {
        > table {
          > tbody {
            > tr {
              > td {
                @apply border-ever-base-400;
                &:first-child {
                  @apply border-solid border-l border-t-0;
                }
                &:nth-last-child(1) {
                  @apply border-r-0;
                }
              }
            }
          }
        }
      }
    }
    &.ant-table-fixed-header {
      > .ant-table-container {
        > .ant-table-body {
          @apply border-0 border-solid border-b border-ever-base-400 rounded-b-lg;
          > table {
            > tbody {
              > tr {
                &:nth-last-child(1) {
                  > td {
                    @apply border-b-0;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}

@mixin thead-no-right-border {
  > table {
    > thead {
      > tr {
        > th {
          @apply border-r-0;
          &:first-child {
            @apply border-r-0;
          }
          &:nth-last-child(1) {
            @apply border-r;
          }
        }
      }
    }
  }
}

@mixin tbody-no-right-border {
  > table {
    > tbody {
      > tr {
        > td {
          @apply border-r-0;
          &:nth-last-child(1) {
            @apply border-r;
          }
        }
      }
    }
  }
}

.no-borders {
  .ant-table {
    &.ant-table-bordered {
      > .ant-table-container {
        > .ant-table-header {
          @include thead-no-right-border;
        }
        .ant-table-body {
          @include tbody-no-right-border;
        }
        .ant-table-content {
          @include thead-no-right-border;
          @include tbody-no-right-border;
        }
      }
    }
  }
}

.thresholdTable {
  .ant-table-bordered {
    > .ant-table-container {
      @apply border border-solid border-ever-base-400 rounded-lg;
      > .ant-table-content {
        > table {
          > thead {
            > tr {
              > th {
                @apply border-ever-base-400;
                &:nth-last-child(1) {
                  @apply border-r-0;
                }
              }
            }
          }
          > tbody {
            > tr {
              > td {
                @apply border-ever-base-400;
                &:nth-last-child(1) {
                  @apply border-r-0;
                }
              }
            }
          }
          tr {
            &:first-child {
              th {
                @apply border-t;
              }
            }
            &:last-child {
              td {
                @apply border-b;
              }
            }
            td,
            th {
              &:first-child {
                @apply border-l;
              }
            }
          }
        }
      }
    }
  }
}

.quotasDrawsTable {
  .ant-table {
    .ant-table-container {
      tr {
        height: 48px;
      }
      td {
        width: 108px;
      }
      .ant-table-content {
        .ant-table-thead {
          tr {
            th:first-child {
              box-shadow: 3px 5px 8px 0 rgba(0, 0, 0, 0.15);
              @apply rounded-tl-none;
            }
            th:last-child {
              @apply rounded-tr-none;
            }
          }
        }
        .ant-table-tbody {
          tr {
            td:first-child {
              @apply bg-ever-primary-lite py-4 px-6;
              box-shadow: 3px 5px 8px 0 rgba(0, 0, 0, 0.15);
            }
          }
        }
      }
    }
  }
}

.databook-table {
  &.ant-table-wrapper {
    .ant-spin-container {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      .ant-pagination {
        display: flex;
        justify-content: flex-end;
        .ant-pagination-options-size-changer {
          @apply h-8;
          .ant-select-arrow {
            @apply h-8;
          }
          .ant-select-selector {
            @apply h-8 px-3 flex items-center justify-between;
            input {
              @apply h-8;
            }
          }
        }
      }
    }
    .ant-table-container {
      @apply border-r-0 border-l-0 border-t-0 rounded-none;
      .ant-table-content {
        .ant-table-thead {
          tr {
            th:first-child {
              @apply rounded-none;
            }
            th:last-child {
              @apply rounded-none;
            }
          }
        }
      }
    }
  }
}
.adjustmentsTable {
  .ant-table {
    .ant-table-container {
      .ant-table-content {
        .ant-table-cell {
          text-align: center !important;
          @apply pl-2;
        }
      }
    }
  }
}
