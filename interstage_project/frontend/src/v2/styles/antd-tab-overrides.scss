.ant-tabs {
  @apply h-full;

  &.hideTabList {
    .ant-tabs-nav {
      display: none;
    }
  }

  .ant-tabs-tab-remove {
    display: flex;
    margin-left: 0;
  }

  &.default {
    .ant-tabs-nav {
      .ant-tabs-tab {
        padding: theme("spacing.2") theme("spacing.4");

        .ant-tabs-tab-btn {
          font-size: theme("fontSize.base");
        }
      }
    }
  }

  &.small {
    .ant-tabs-nav {
      .ant-tabs-tab {
        padding: theme("spacing.1") theme("spacing.4");

        .ant-tabs-tab-btn {
          font-size: theme("fontSize.xs");
        }
      }
    }
  }

  .ant-tabs-nav {
    @apply mb-0 grid grid-flow-col;

    &::before {
      border-bottom: 1px solid theme("colors.ever.base.ring");
    }

    .ant-tabs-ink-bar {
      @apply h-0.5 rounded-none bg-ever-primary visible;
    }

    .ant-tabs-tab {
      padding-left: theme("spacing.3");
      padding-right: theme("spacing.4");
      border-radius: theme("borderRadius.md") theme("borderRadius.md") 0 0 !important;
      border-color: theme("colors.transparent");

      &.ant-tabs-tab-with-remove {
        margin-right: theme("spacing.1") !important;
      }

      .ant-tabs-tab-btn {
        font-weight: theme("fontWeight.medium");
        color: theme("colors.ever.base.content.mid");
      }

      &.ant-tabs-tab-active {
        background-color: inherit;

        .ant-tabs-tab-btn {
          color: theme("colors.ever.primary.DEFAULT");
        }
      }
    }
  }

  &.ant-tabs-card {
    .ant-tabs-nav {
      margin-bottom: theme("spacing.0");

      .ant-tabs-tab {
        border-color: theme("colors.ever.base.ring");
        background-color: theme("colors.ever.base.100");

        .ant-tabs-tab-btn {
          font-weight: theme("fontWeight.medium");
          color: theme("colors.ever.base.content.DEFAULT");
        }

        &.ant-tabs-tab-active {
          background-color: theme("colors.ever.base.DEFAULT");
          border-bottom-color: theme("colors.transparent");
          border: 1px solid theme("colors.ever.base.ring");

          .ant-tabs-tab-btn {
            color: theme("colors.ever.primary.DEFAULT");
          }
        }
      }
    }
    .line-style-tabs {
      .ant-tabs-nav {
        .ant-tabs-tab {
          border-color: transparent;
          background-color: transparent;
        }
      }
    }
  }

  .ant-tabs-content {
    @apply h-full;
  }

  .ant-tabs-nav .ant-tabs-nav-add {
    @apply ml-3 px-3 flex items-center justify-center bg-transparent border-0 border-l border-solid border-ever-base-400 h-6 self-center;
  }

  .ant-tabs-nav {
    &::before {
      @apply border-none;
    }
  }
}
.ant-tabs {
  .ant-tabs-nav {
    .ant-tabs-tab {
      &.ant-tabs-tab-with-remove {
        margin-right: 0 !important;
        padding-right: 16px;
      }
      &.ant-tabs-tab-active {
        .ant-tabs-tab-btn {
          color: theme("colors.ever.base.content.DEFAULT") !important;
        }
      }
    }
    .ant-tabs-nav-add {
      padding: 0 !important;
      margin-left: 0 !important;
      width: 24px !important;
    }
  }

  &.ant-tabs-card {
    .ant-tabs-nav {
      .ant-tabs-tab {
        .ant-tabs-tab-btn {
          color: theme("colors.ever.base.content.mid");
        }

        &.ant-tabs-tab-active {
          border: none;
          .ant-tabs-tab-btn {
            color: theme("colors.ever.primary.DEFAULT");
          }
        }
      }
    }
  }

  .ant-tabs-content {
    height: 100%;
  }

  .ant-tabs-nav .ant-tabs-nav-add {
    @apply flex items-center justify-center bg-transparent border-0 border-l border-solid border-ever-base-400 h-6;
  }

  .ant-tabs-nav {
    &::before {
      border: none;
    }
  }
}

.ant-tabs-tab {
  @apply mr-0 hover:border-t-0 hover:border-r-0 hover:border-b-[2px] hover:border-l-0 hover:border-solid;
}
.ant-tabs-tab {
  border-bottom: 2px solid transparent !important;
}
.ant-tabs-tab:hover:not(.ant-tabs-tab-active) {
  border-color: theme("colors.ever.primary.ring") !important;
  .ant-tabs-tab-btn {
    color: theme("colors.ever.base.content.DEFAULT") !important;
  }
}
