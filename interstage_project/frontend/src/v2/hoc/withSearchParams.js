// src/v2/components/hoc/withSearchParams.js
import React from "react";
import { useSearchParams } from "react-router-dom";

/**
 * HOC to inject searchParams (query params) as props into the wrapped component.
 * The wrapped component will receive a `searchParams` prop (URLSearchParams instance)
 * and a `setSearchParams` prop (function to update the params).
 */
const withSearchParams = (WrappedComponent) => {
  return function WithSearchParamsWrapper(props) {
    const [searchParams, setSearchParams] = useSearchParams();

    // Convert searchParams to a plain object for easier usage
    const paramsObject = React.useMemo(() => {
      const obj = {};
      for (const [key, value] of searchParams.entries()) {
        obj[key] = value;
      }
      return obj;
    }, [searchParams]);

    return (
      <WrappedComponent
        {...props}
        searchParams={paramsObject}
        setSearchParams={setSearchParams}
      />
    );
  };
};

export default withSearchParams;
