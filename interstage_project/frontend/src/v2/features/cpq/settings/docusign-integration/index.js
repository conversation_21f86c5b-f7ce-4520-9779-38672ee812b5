import { debounce } from "lodash";
import React, { useMemo, useEffect } from "react";

import { EverButton, EverDivider, EverSwitch, EverTg } from "~/v2/components";

import { IntegrationButton } from "./IntegrationButton";
import { RecipientConfig } from "./RecipientConfig";
import { useESignature } from "./useESignature";

export default function DocusignIntegration() {
  const {
    disabledTooltip,
    currentRecipients,
    isFetching,
    isDocuSignConnected,
    isSaveInProgress,
    allowOfflineSigning,
    mandateOfflineSigning,
    isCallbackLoading,
    isSaveButtonDisabled,
    handleRecipientChange,
    handleAddNewRecipient,
    handleRemoveRecipient,
    handleSaveRecipients,
    connectDocusign,
    sendAuthCode,
    handleDisconnectDocusign,
    handleOfflineSigningChange,
    handleMandateOfflineSigningChange,
  } = useESignature();

  const debouncedSaveChanges = useMemo(
    () => debounce(() => handleSaveRecipients(), 500),
    [handleSaveRecipients]
  );

  useEffect(() => {
    return () => {
      debouncedSaveChanges.cancel();
    };
  }, [debouncedSaveChanges]);

  return (
    <div className="flex flex-col w-full h-full overflow-hidden relative">
      <div className="flex justify-between items-center px-8 py-4 bg-ever-base-50 border-b border-ever-base-400 sticky top-0 z-10">
        <div className="flex flex-col gap-1.5">
          <EverTg.Heading2>eSignature</EverTg.Heading2>
          <EverTg.Text className="text-ever-base-content-mid">
            Configure your eSignature integration to enable seamless document
            signing for quotes.
          </EverTg.Text>
        </div>
      </div>
      <div className="flex flex-col pb-10 pt-6 px-12 gap-8 overflow-y-auto">
        <IntegrationButton
          connectDocusign={connectDocusign}
          sendAuthCode={sendAuthCode}
          isDocuSignConnected={isDocuSignConnected}
          handleDisconnectDocusign={handleDisconnectDocusign}
          isCallbackLoading={isCallbackLoading}
        />
        <EverDivider />
        <RecipientConfig
          recipients={currentRecipients}
          isLoading={isFetching}
          handleRecipientChange={handleRecipientChange}
          handleAddNewRecipient={handleAddNewRecipient}
          handleRemoveRecipient={handleRemoveRecipient}
        />
        <EverDivider />
        <div className="flex gap-14 items-center">
          <div className="flex flex-col gap-2 w-2/5">
            <EverTg.Heading4>Allow offline signing of quotes</EverTg.Heading4>
            <EverTg.Text className="text-ever-base-content-mid">
              Reps can download the quote for offline signing and mark it as Won
              by uploading the signed document.
            </EverTg.Text>
          </div>
          <EverSwitch
            label={
              <EverTg.Heading4 className="text-ever-base-content">
                {allowOfflineSigning ? "ON" : "OFF"}
              </EverTg.Heading4>
            }
            checked={allowOfflineSigning}
            onChange={() => handleOfflineSigningChange(!allowOfflineSigning)}
          />
        </div>
        <EverDivider />
        <div className="flex gap-14 items-center">
          <div className="flex flex-col gap-2 w-2/5">
            <EverTg.Heading4>
              Mandate users to enter reason for offline signing
            </EverTg.Heading4>
            <EverTg.Text className="text-ever-base-content-mid">
              Reps need to enter the reason for why they chose to share the
              quote offline with the buyer.
            </EverTg.Text>
          </div>
          <EverSwitch
            label={
              <EverTg.Heading4 className="text-ever-base-content">
                {mandateOfflineSigning ? "ON" : "OFF"}
              </EverTg.Heading4>
            }
            checked={mandateOfflineSigning}
            onChange={() =>
              handleMandateOfflineSigningChange(!mandateOfflineSigning)
            }
          />
        </div>
      </div>
      <div className="sticky bottom-0 left-0 flex px-6 py-3 justify-end w-full bg-ever-base-50 shadow-md border-t border-ever-base-300 border-solid">
        <EverButton
          color="primary"
          type="filled"
          size="sm"
          disabled={isSaveButtonDisabled() || isSaveInProgress}
          onClick={debouncedSaveChanges}
          tooltipTitle={disabledTooltip}
          loading={isSaveInProgress}
        >
          Save Changes
        </EverButton>
      </div>
    </div>
  );
}
