import { PlusSquareIcon } from "@everstage/evericons/outlined";
import { debounce } from "lodash";
import React, { useEffect, useState, useMemo } from "react";

import { EverButton, EverInput, EverRadio, EverTg } from "~/v2/components";

import { AnimatedWrapper, ANIMATION_TYPES } from "../../../../components";

export function ApprovalStrategy({
  currentStage,
  handleStageUpdate,
  currentStageName,
}) {
  const [showNotes, setShowNotes] = useState(false);
  const [notesValue, setNotesValue] = useState(
    currentStage?.notes === "." ? "" : currentStage?.notes
  );

  const debouncedHandleNotesUpdate = useMemo(
    () =>
      debounce((value) => {
        handleStageUpdate({ notes: value });
      }, 500),
    [handleStageUpdate]
  );

  // Cleanup the debounced function on component unmount
  useEffect(() => {
    return () => {
      debouncedHandleNotesUpdate.cancel();
    };
  }, [debouncedHandleNotesUpdate]);

  // Handle immediate input update and trigger debounced update
  const handleNotesChange = (e) => {
    const value = e.target.value;
    setNotesValue(value);
    debouncedHandleNotesUpdate(value);
  };

  useEffect(() => {
    setNotesValue(currentStage?.notes);
    currentStage?.notes?.length > 0 && currentStage?.notes !== "."
      ? setShowNotes(true)
      : setShowNotes(false);
  }, [currentStageName, currentStage]);

  return (
    <div className="flex flex-col gap-5">
      <EverTg.SubHeading4 className="text-ever-base-content">
        Consider stage approved when
      </EverTg.SubHeading4>
      <EverRadio.Group
        value={currentStage?.approval_strategy}
        onChange={(event) => {
          handleStageUpdate({ approval_strategy: event.target.value });
        }}
        className="flex flex-col"
      >
        <EverRadio value="everyone" label="Everyone approves">
          <EverTg.Description>
            Stage is considered Rejected when anyone rejects.
          </EverTg.Description>
        </EverRadio>
        <EverRadio value="anyone" label="Anyone approves">
          <EverTg.Description>
            Stage is considered Rejected when the first person responding
            rejects.
          </EverTg.Description>
        </EverRadio>
      </EverRadio.Group>
      <EverButton
        color="base"
        prependIcon={<PlusSquareIcon />}
        size="small"
        type="link"
        className="!w-max !pl-0"
        onClick={() => setShowNotes(!showNotes)}
      >
        Include additional notes for the approvers
      </EverButton>
      <AnimatedWrapper
        isVisible={showNotes}
        motionProps={{
          type: ANIMATION_TYPES.EXPAND_COLLAPSE,
        }}
      >
        <EverInput.TextArea
          placeholder="Enter notes"
          value={notesValue}
          onChange={handleNotesChange}
          maxLength={500}
          showCount={true}
        />
      </AnimatedWrapper>
    </div>
  );
}
