import { gql, useQuery } from "@apollo/client";
import React, { useMemo, useCallback } from "react";

import { ClickBoundary, EverLabel } from "~/v2/components";

import { MultiTabSelector } from "./components/MultiTabSelector";
import { StepsHeader } from "./components/StepsHeader";

const GET_GROUPS_LIST = gql`
  query UserGroupsQuery {
    userGroups {
      userGroupId
      userGroupName
      allMembers {
        firstName
      }
    }
  }
`;

export function NotificationsBuilder({ currentRule, handleModifyNotifiers }) {
  // State to track if each selector is open
  const [isApproveOpen, setIsApproveOpen] = React.useState(false);
  const [isRejectOpen, setIsRejectOpen] = React.useState(false);

  // Get groups data
  const { data: fetchedGroupsData } = useQuery(GET_GROUPS_LIST, {
    fetchPolicy: "no-cache",
  });

  const groupsData = useMemo(() => {
    if (fetchedGroupsData?.userGroups) {
      return fetchedGroupsData.userGroups.map((group) => ({
        label: group.userGroupName,
        text: group?.allMembers ? group.allMembers.length + " users" : "0 user",
        value: group.userGroupId,
      }));
    }
    return [];
  }, [fetchedGroupsData]);

  // Dynamic notifier options
  const dynamicNotifierOptions = useMemo(
    () => [
      { label: "Quote owner", value: "quote_owner" },
      { label: "All Approvers", value: "all_approvers" },
    ],
    []
  );

  // Transform dynamic string arrays to objects for MultiTabSelector
  const getDynamicSelectedData = useCallback(
    (dynamicArray = []) => {
      return dynamicArray.map((value) => {
        const option = dynamicNotifierOptions.find(
          (opt) => opt.value === value
        );
        return option || { label: value, value };
      });
    },
    [dynamicNotifierOptions]
  );

  // Get selected data for approve notifications
  const approveSelectedData = useMemo(() => {
    const notifyOnApprove = currentRule?.notify_on_approve || {
      users: [],
      groups: [],
      dynamic: [],
    };
    return {
      dynamic: getDynamicSelectedData(notifyOnApprove.dynamic),
      users: notifyOnApprove.users || [],
      groups: notifyOnApprove.groups || [],
    };
  }, [currentRule?.notify_on_approve, getDynamicSelectedData]);

  // Get selected data for reject notifications
  const rejectSelectedData = useMemo(() => {
    const notifyOnReject = currentRule?.notify_on_reject || {
      users: [],
      groups: [],
      dynamic: [],
    };
    return {
      dynamic: getDynamicSelectedData(notifyOnReject.dynamic),
      users: notifyOnReject.users || [],
      groups: notifyOnReject.groups || [],
    };
  }, [currentRule?.notify_on_reject, getDynamicSelectedData]);

  // Handle selection changes for approve notifications
  const handleApproveSelection = (currentSelection, type, item) => {
    const approversKey = {
      dynamicApprovers: "dynamic",
      userApprovers: "users",
      groupApprovers: "groups",
    }[type];

    const isSelected = currentSelection.some(
      (selected) => selected.value === item.value
    );

    // Create a new selection array (add or remove the item)
    const newSelection = isSelected
      ? currentSelection.filter((i) => i.value !== item.value)
      : [...currentSelection, item];

    // For dynamic selections, we only need to store the value, not the entire object
    const updatedDynamic =
      approversKey === "dynamic"
        ? newSelection.map((item) => item.value)
        : currentRule?.notify_on_approve?.[approversKey] || [];

    // Update the appropriate array in notify_on_approve
    const updatedNotifyOnApprove = {
      ...currentRule?.notify_on_approve,
      [approversKey]:
        approversKey === "dynamic" ? updatedDynamic : newSelection,
    };

    // Call the parent's update function
    handleModifyNotifiers({
      notify_on_approve: updatedNotifyOnApprove,
    });
  };

  // Handle selection changes for reject notifications
  const handleRejectSelection = (currentSelection, type, item) => {
    const approversKey = {
      dynamicApprovers: "dynamic",
      userApprovers: "users",
      groupApprovers: "groups",
    }[type];

    const isSelected = currentSelection.some(
      (selected) => selected.value === item.value
    );

    // Create a new selection array (add or remove the item)
    const newSelection = isSelected
      ? currentSelection.filter((i) => i.value !== item.value)
      : [...currentSelection, item];

    // For dynamic selections, we only need to store the value, not the entire object
    const updatedDynamic =
      approversKey === "dynamic"
        ? newSelection.map((item) => item.value)
        : currentRule?.notify_on_reject?.[approversKey] || [];

    // Update the appropriate array in notify_on_reject
    const updatedNotifyOnReject = {
      ...currentRule?.notify_on_reject,
      [approversKey]:
        approversKey === "dynamic" ? updatedDynamic : newSelection,
    };

    // Call the parent's update function
    handleModifyNotifiers({
      notify_on_reject: updatedNotifyOnReject,
    });
  };

  return (
    <div className="flex flex-col gap-5">
      <StepsHeader
        title="Step 3: Notifications"
        subTitle="Configure who needs to be notified when the request is approved or rejected."
      />
      {/* Approve Notifications */}
      <div className="flex flex-col gap-2">
        <EverLabel required>Notify when Approved</EverLabel>
        <ClickBoundary
          onClickOutside={() => isApproveOpen && setIsApproveOpen(false)}
        >
          <MultiTabSelector
            placeholder="Choose people to notify on approval"
            isSelectorSelected={isApproveOpen}
            isSelectorUnSelected={isApproveOpen}
            setIsSelectorSelectedAction="set_is_approve_selector_open"
            setTemplateData={(bool) => setIsApproveOpen(bool)}
            setSelectedData={handleApproveSelection}
            dynamicSourceData={dynamicNotifierOptions}
            dynamicSelectedData={approveSelectedData.dynamic}
            dynamicType="dynamicApprovers"
            groupSourceData={groupsData}
            groupSelectedData={approveSelectedData.groups}
            groupType="groupApprovers"
            userSelectedData={approveSelectedData.users}
            userType="userApprovers"
            displayMode="tags"
          />
        </ClickBoundary>
      </div>

      {/* Reject Notifications */}
      <div className="flex flex-col gap-2">
        <EverLabel required>Notify when Rejected</EverLabel>
        <ClickBoundary
          onClickOutside={() => isRejectOpen && setIsRejectOpen(false)}
        >
          <MultiTabSelector
            placeholder="Choose people to notify on rejection"
            isSelectorSelected={isRejectOpen}
            isSelectorUnSelected={isRejectOpen}
            setIsSelectorSelectedAction="set_is_reject_selector_open"
            setTemplateData={(bool) => setIsRejectOpen(bool)}
            setSelectedData={handleRejectSelection}
            dynamicSourceData={dynamicNotifierOptions}
            dynamicSelectedData={rejectSelectedData.dynamic}
            dynamicType="dynamicApprovers"
            groupSourceData={groupsData}
            groupSelectedData={rejectSelectedData.groups}
            groupType="groupApprovers"
            userSelectedData={rejectSelectedData.users}
            userType="userApprovers"
            displayMode="tags"
          />
        </ClickBoundary>
      </div>
    </div>
  );
}
