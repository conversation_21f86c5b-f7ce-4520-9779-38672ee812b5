import { useQuery } from "@apollo/client";
import React, { useMemo, useState } from "react";

import { ClickBoundary, EverLabel } from "~/v2/components";

import { RuleListItem } from "./RuleListItem";
import {
  createDefaultRule,
  dynamicApproverOptions,
  GET_GROUPS_LIST,
} from "../../utils";
import { MultiTabSelector } from "../components/MultiTabSelector";

export function SelectApprovers({
  currentStage,
  handleStageUpdate,
  ruleDetails,
}) {
  // State to track if the selector is open
  const [isSelectorOpen, setIsSelectorOpen] = useState(false);

  const { data: fetchedGroupsData } = useQuery(GET_GROUPS_LIST, {
    fetchPolicy: "no-cache",
  });

  const groupsData = useMemo(() => {
    if (fetchedGroupsData?.userGroups) {
      return fetchedGroupsData.userGroups.map((group) => {
        return {
          label: group.userGroupName,
          text: group?.allMembers
            ? group.allMembers.length + " users"
            : "0 user",
          value: group.userGroupId,
        };
      });
    }
    return [];
  }, [fetchedGroupsData]);

  // Collect all approvers from all rules in the current stage
  const allApprovers = useMemo(() => {
    if (!currentStage?.rules) return { users: [], groups: [], dynamic: [] };

    // Initialize with empty arrays
    const consolidated = {
      users: [],
      groups: [],
      dynamic: [],
    };

    // Loop through all rules and collect approvers
    for (const rule of currentStage.rules) {
      if (rule.approvers) {
        // Add users
        if (rule.approvers.users && rule.approvers.users.length > 0) {
          consolidated.users.push(...rule.approvers.users);
        }
        // Add groups
        if (rule.approvers.groups && rule.approvers.groups.length > 0) {
          consolidated.groups.push(...rule.approvers.groups);
        }
        // Add dynamic
        if (rule.approvers.dynamic && rule.approvers.dynamic.length > 0) {
          consolidated.dynamic.push(...rule.approvers.dynamic);
        }
      }
    }

    return consolidated;
  }, [currentStage]);

  // Filter out "Previous Stage Approver Manager" for first stage
  const filteredDynamicOptions =
    currentStage?.stage_order === 1
      ? dynamicApproverOptions.filter(
          (option) => option.value !== "previous_approver_manager"
        )
      : dynamicApproverOptions;

  // Function to handle selection changes
  const handleSelectionUpdate = (currentSelection, type, item) => {
    // Map the type to the corresponding key in approvers object
    const approversKey = {
      dynamicApprovers: "dynamic",
      userApprovers: "users",
      groupApprovers: "groups",
    }[type];

    // Check if this approver already exists in any rule
    const isAlreadySelected = allApprovers[approversKey].some(
      (approver) => approver.value === item.value
    );

    // If already selected, we don't need to add it again
    if (isAlreadySelected) {
      return;
    }

    // Create approvers object with the selected item
    const approvers = {
      users: [],
      groups: [],
      dynamic: [],
    };

    // Add the selected item to the appropriate array
    approvers[approversKey] = [item];

    // Create a new rule with the default structure and selected approver
    const newRule = createDefaultRule(approvers);

    // Add the new rule to the current stage
    const updatedRules = [...(currentStage.rules || []), newRule];

    // Update the stage with the new rule
    handleStageUpdate({ rules: updatedRules });
  };

  function handleRemoveRule(indexToRemove) {
    // Create a copy of the current rules array
    const updatedRules = currentStage.rules.filter(
      (_, index) => index !== indexToRemove
    );

    // Update the stage with the new rules array
    handleStageUpdate({ rules: updatedRules });
  }

  return (
    <div className="flex flex-col gap-2">
      <EverLabel
        required={true}
        className="text-ever-base-content-mid font-medium"
      >
        Approvers
      </EverLabel>
      <ClickBoundary
        onClickOutside={() => {
          if (isSelectorOpen) {
            setIsSelectorOpen(false);
          }
        }}
      >
        <MultiTabSelector
          placeholder="Choose approvers"
          isSelectorSelected={isSelectorOpen}
          isSelectorUnSelected={isSelectorOpen}
          setIsSelectorSelectedAction="set_is_approvers_selector_open"
          setTemplateData={(bool) => setIsSelectorOpen(bool)}
          setSelectedData={handleSelectionUpdate}
          dynamicSourceData={filteredDynamicOptions}
          dynamicSelectedData={allApprovers.dynamic}
          dynamicType="dynamicApprovers"
          groupSourceData={groupsData}
          groupSelectedData={allApprovers.groups}
          groupType="groupApprovers"
          userSelectedData={allApprovers.users}
          userType="userApprovers"
        />
      </ClickBoundary>
      {currentStage?.rules?.map((rule, index) => (
        <div key={index} className="flex flex-col gap-2">
          <RuleListItem
            index={index}
            rule={rule}
            onRemove={() => handleRemoveRule(index)}
            handleStageUpdate={handleStageUpdate}
            currentStage={currentStage}
            ruleDetails={ruleDetails}
          />
        </div>
      ))}
    </div>
  );
}
