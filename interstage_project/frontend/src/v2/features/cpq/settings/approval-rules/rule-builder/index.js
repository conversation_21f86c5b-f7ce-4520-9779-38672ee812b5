import { debounce } from "lodash";
import React, { useMemo } from "react";

import { EverButton, EverDivider } from "~/v2/components";

import { ConditionsBuilder } from "./ConditionsBuilder";
import { NotificationsBuilder } from "./NotificationsBuilder";
import { RuleHeader } from "./RuleHeader";
import { StageBuilder } from "./stage-builder/StageBuilder";
import { useApprovalRule } from "./useApprovalRule";

export default function ApprovalRulesBuilder() {
  const {
    isRuleLoading,
    ruleDetails,
    currentRule,
    currentStage,
    disabledTooltip,
    currentStageName,
    isSaveButtonDisabled,
    handleAddStage,
    handleRemoveStage,
    handleCurrentStageChange,
    handleStageUpdate,
    handleModifyNotifiers,
    refetchRule,
    handleCloneRule,
    handleDeleteRule,
    handleUpdateRule,
    handleSaveChanges,
    handleRuleConditionUpdate,
  } = useApprovalRule();

  // Debounce the save changes function to prevent multiple rapid clicks
  const debouncedSaveChanges = useMemo(
    () =>
      debounce(() => {
        handleSaveChanges();
      }, 300),
    [handleSaveChanges]
  );

  return (
    <div className="w-full h-full flex flex-col relative overflow-hidden">
      <RuleHeader
        ruleDetails={ruleDetails}
        currentRule={currentRule}
        isFetching={isRuleLoading}
        refetchRule={refetchRule}
        handleCloneRule={handleCloneRule}
        handleDeleteRule={handleDeleteRule}
        handleUpdateRule={handleUpdateRule}
      />
      <div className="flex flex-col px-24 py-4 gap-8 overflow-y-auto">
        <ConditionsBuilder
          currentRule={currentRule}
          handleRuleConditionUpdate={handleRuleConditionUpdate}
          ruleDetails={ruleDetails}
        />
        <EverDivider />
        <StageBuilder
          currentRule={currentRule}
          currentStage={currentStage}
          handleAddStage={handleAddStage}
          handleRemoveStage={handleRemoveStage}
          handleCurrentStageChange={handleCurrentStageChange}
          handleStageUpdate={handleStageUpdate}
          currentStageName={currentStageName}
          ruleDetails={ruleDetails}
        />
        <EverDivider />
        <NotificationsBuilder
          currentRule={currentRule}
          handleModifyNotifiers={handleModifyNotifiers}
        />
      </div>
      <div className="sticky bottom-0 right-2 flex px-6 py-3 justify-end bg-ever-base-50 shadow-md border-t border-ever-base-300 border-solid w-full z-100">
        <EverButton
          color="primary"
          type="filled"
          size="small"
          disabled={isSaveButtonDisabled()}
          onClick={debouncedSaveChanges}
          tooltipTitle={disabledTooltip}
        >
          Save Changes
        </EverButton>
      </div>
    </div>
  );
}
