import { gql } from "@apollo/client";
import { UserIcon, CheckIcon, XCloseIcon } from "@everstage/evericons/outlined";
// eslint-disable-next-line no-restricted-imports
import { Avatar, List } from "antd";
import { isNil, isEmpty } from "lodash";
import React, { useRef, useState, useEffect } from "react";

import {
  LazyListContent,
  useAbortiveLazyQuery,
  EverTabs,
  useCurrentTheme,
  EverTg,
  EverInput,
  EverListItem,
  EverGroupAvatar,
} from "~/v2/components";
import { emptyFace } from "~/v2/images";

import { TruncatedText } from "../../../../components";

const EmptyFaceState = (props) => {
  const { description } = props;
  return (
    <span className="flex flex-col items-center mt-10">
      <img src={emptyFace} />
      <EverTg.SubHeading3 className="mt-8">{description}</EverTg.SubHeading3>
    </span>
  );
};

const SelectableCard = ({ data, setData, isSelected, avatar }) => {
  const { chartColors } = useCurrentTheme();
  const prepend = avatar || (
    <Avatar size="medium" style={{ backgroundColor: chartColors[4] + "3A" }}>
      {`${data.label.split(" ")[0].charAt(0).toUpperCase()}${data.label
        .split(" ")[1]
        .charAt(0)
        .toUpperCase()}`}
    </Avatar>
  );
  return (
    <EverListItem
      key={data.value}
      onSelect={setData}
      prepend={prepend}
      title={data.label}
      subtitle={data.text}
      selectable
      append={
        isSelected ? <CheckIcon className="w-5 h-5 text-ever-primary" /> : null
      }
    />
  );
};

const LazyFetchUsers = (props) => {
  const { userSelectedData, setData, userType, listHeightClass } = props;
  const GET_USERS = gql`
    query AllEmployees(
      $userStatus: String
      $searchTerm: String
      $offsetValue: Int
      $limitValue: Int
      $context: String
    ) {
      employeeProfileDetailsPayoutModule(
        userStatus: $userStatus
        searchTerm: $searchTerm
        offsetValue: $offsetValue
        limitValue: $limitValue
        context: $context
      ) {
        employeeEmailId
        fullName
        profilePicture
      }
    }
  `;
  const initialLoadRef = useRef(true);
  const [getUsers, { variables, abort, data }] = useAbortiveLazyQuery(
    GET_USERS,
    {
      fetchPolicy: "network-only",
      onCompleted: (data) => {
        const response = data?.employeeProfileDetailsPayoutModule;
        initialLoadRef.current = false;
        if (isNil(response)) {
          variables.failureCbk();
        } else {
          const formattedResponse = response.map((option) => {
            return {
              label: option.fullName,
              value: option.employeeEmailId,
              text: option.employeeEmailId,
              profilePicture: option.profilePicture,
            };
          });
          variables.successCbk(formattedResponse);
        }
      },
      onError: () => {
        variables.failureCbk();
      },
    }
  );

  const usersLazyLoadProps = {
    abort,
    getOptions: async (params) => {
      const { searchTerm, offset, limit } = params;
      await getUsers({
        ...params,
        userStatus: "Active",
        context: "notify",
        searchTerm,
        offsetValue: offset,
        limitValue: limit,
      });
    },
  };

  return (
    <LazyListContent
      listHeight={340}
      skipSelectAll
      showSearch={
        initialLoadRef.current &&
        isEmpty(data?.employeeProfileDetailsPayoutModule)
          ? false
          : true
      }
      noDataText={<EmptyFaceState description="No users were found!" />}
      listItemRenderer={(option) => (
        <SelectableCard
          isSelected={userSelectedData.some((e) => e.value === option.value)}
          data={option}
          setData={() => setData(userSelectedData, userType, option)}
          avatar={
            <EverGroupAvatar
              avatars={[
                {
                  image: option.profilePicture,
                  firstName: option.label,
                  className: "w-6 h-6",
                },
              ]}
            />
          }
        />
      )}
      listItemHeight={72}
      listHeightClass={listHeightClass}
      {...usersLazyLoadProps}
    />
  );
};

export const MultiTabSelector = (props) => {
  const {
    isSelectorSelected,
    setTemplateData,
    dynamicSourceData,
    dynamicDataOnNotify,
    dynamicDataOnNotifyForRevoked,
    notifyType,
    dynamicSelectedData,
    setSelectedData,
    groupSourceData,
    groupSelectedData,
    userSelectedData,
    userType,
    dynamicType,
    groupType,
    placeholder,
    siderMuliTabSelectorRef,
    listHeightClass,
    displayMode = "default",
  } = props;

  const { chartColors } = useCurrentTheme();
  const [showAbove, setShowAbove] = useState(false);
  const containerRef = useRef(null);

  useEffect(() => {
    if (isSelectorSelected && containerRef.current) {
      const container = containerRef.current;
      const rect = container.getBoundingClientRect();
      const spaceBelow = window.innerHeight - rect.bottom;
      const spaceAbove = rect.top;

      // If there's less than 350px below and more space above, show dropdown above
      const shouldShowAbove = spaceBelow < 350 && spaceAbove > spaceBelow;
      setShowAbove(shouldShowAbove);
    }
  }, [isSelectorSelected]);

  const renderTags = () => {
    const allItems = [
      ...dynamicSelectedData,
      ...groupSelectedData,
      ...userSelectedData,
    ];

    const handleRemoveTag = (item) => {
      // Determine which array the item belongs to
      if (dynamicSelectedData.some((i) => i.value === item.value)) {
        setSelectedData(dynamicSelectedData, dynamicType, item);
      } else if (groupSelectedData.some((i) => i.value === item.value)) {
        setSelectedData(groupSelectedData, groupType, item);
      } else if (userSelectedData.some((i) => i.value === item.value)) {
        setSelectedData(userSelectedData, userType, item);
      }
    };

    return (
      <div className="flex items-start gap-1.5 flex-wrap py-0.5 max-w-full">
        {allItems.map((item, index) => (
          <div
            key={index}
            className="flex items-center gap-1 bg-ever-base-200 text-ever-base-content rounded-sm px-2 py-1 text-xs whitespace-nowrap"
          >
            <EverGroupAvatar
              avatars={[
                {
                  image: item.profilePicture,
                  firstName: item.label,
                  className: "w-5 h-5",
                },
              ]}
            />
            <TruncatedText text={item.label}>
              <EverTg.Caption.Medium>{item.label}</EverTg.Caption.Medium>
            </TruncatedText>
            <div
              className="cursor-pointer text-ever-base-content-mid"
              onClick={(e) => {
                e.stopPropagation();
                handleRemoveTag(item);
              }}
            >
              <XCloseIcon className="w-4 h-4" />
            </div>
          </div>
        ))}
      </div>
    );
  };

  return (
    <div className="relative" ref={siderMuliTabSelectorRef}>
      <div ref={containerRef}>
        <EverInput
          placeholder={displayMode === "tags" ? undefined : placeholder}
          onKeyPress={(e) => (e.preventDefault(), e.stopPropagation())}
          onPaste={(e) => (e.preventDefault(), e.stopPropagation())}
          onClick={() => setTemplateData(!isSelectorSelected)}
          prefix={displayMode === "tags" ? renderTags() : undefined}
        />
      </div>
      {isSelectorSelected ? (
        <div
          className={`absolute ${
            showAbove ? "bottom-11" : "top-11"
          } left-0 bg-ever-base z-20 w-2/3 rounded-lg min-w-fit shadow-lg`}
        >
          <EverTabs className="w-full">
            <EverTabs.TabPane tab="Dynamic" key="dynamic" className="p-2">
              <List
                itemLayout="horizontal"
                dataSource={
                  notifyType === "revoke"
                    ? dynamicDataOnNotifyForRevoked
                    : dynamicDataOnNotify || dynamicSourceData
                }
                renderItem={(data) => (
                  <SelectableCard
                    key={data.value}
                    data={data}
                    isSelected={dynamicSelectedData.some(
                      (e) => e.value === data.value
                    )}
                    setData={() =>
                      setSelectedData(dynamicSelectedData, dynamicType, data)
                    }
                    avatar={
                      <Avatar
                        className="flex items-center justify-center flex-wrap"
                        size="medium"
                        style={{ backgroundColor: chartColors[4] + "3A" }}
                        icon={
                          <UserIcon
                            className="size-2/3"
                            style={{ color: chartColors[4] }}
                          />
                        }
                      />
                    }
                  />
                )}
              />
            </EverTabs.TabPane>
            <EverTabs.TabPane tab="Users" key="users">
              <LazyFetchUsers
                userSelectedData={userSelectedData}
                userType={userType}
                setData={setSelectedData}
                listHeightClass={listHeightClass}
              />
            </EverTabs.TabPane>
            <EverTabs.TabPane tab="Groups" key="groups">
              {groupSourceData ? (
                <List
                  itemLayout="horizontal"
                  dataSource={groupSourceData}
                  renderItem={(data) => (
                    <SelectableCard
                      data={data}
                      isSelected={groupSelectedData.some(
                        (e) => e.value === data.value
                      )}
                      setData={() =>
                        setSelectedData(groupSelectedData, groupType, data)
                      }
                      avatar={
                        <Avatar
                          className="flex items-center justify-center flex-wrap"
                          size="medium"
                          style={{ backgroundColor: chartColors[4] + "3A" }}
                          icon={
                            <UserIcon
                              className="size-2/3"
                              style={{ color: chartColors[4] }}
                            />
                          }
                        />
                      }
                    />
                  )}
                />
              ) : (
                <EmptyFaceState
                  description={"No user groups have been setup!"}
                />
              )}
            </EverTabs.TabPane>
          </EverTabs>
        </div>
      ) : null}
    </div>
  );
};
