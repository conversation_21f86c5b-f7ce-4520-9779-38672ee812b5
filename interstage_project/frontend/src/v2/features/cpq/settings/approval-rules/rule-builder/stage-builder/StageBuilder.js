import {
  ArrowCircleRightIcon,
  PlusCircleIcon,
  TrashIcon,
} from "@everstage/evericons/outlined";
import React, { useState } from "react";
import { twMerge } from "tailwind-merge";

import {
  EverButton,
  EverCard,
  EverDivider,
  EverLoader,
  EverTg,
} from "~/v2/components";

import { ApprovalStrategy } from "./ApprovalStrategy";
import { DueDateSettings } from "./DueDateSettings";
import { SelectApprovers } from "./SelectApprovers";
import { StepsHeader } from "../components/StepsHeader";

export function StageBuilder({
  currentRule,
  handleAddStage,
  handleRemoveStage,
  currentStage,
  handleCurrentStageChange,
  isRuleLoading,
  handleStageUpdate,
  currentStageName,
  ruleDetails,
}) {
  return (
    <div className="flex flex-col">
      <StepsHeader
        title="Step 2: Approval Stages"
        subTitle="Set up the approval process by configuring approvers, approval criteria, and additional stage-specific settings."
        className="mb-7"
      />
      <div className="flex gap-8 h-full w-full">
        <div className="w-60 min-h-[654px] h-auto flex flex-col gap-3 py-8 px-4 bg-ever-base-50 border border-solid border-ever-base-400 rounded-lg">
          {isRuleLoading ? (
            <>
              <EverLoader.Skeleton config={[2]} className="h-10 w-full" />
              <EverLoader.Skeleton config={[2]} className="h-10 w-full" />
            </>
          ) : (
            currentRule?.stages?.map((stage, index) => (
              <StageCard
                key={stage?.stage_name}
                name={stage?.stage_name}
                currentStage={currentStage}
                handleStageChange={handleCurrentStageChange}
                handleRemoveStage={handleRemoveStage}
                index={index}
              />
            ))
          )}
          <EverButton
            color="info"
            prependIcon={<PlusCircleIcon />}
            size="medium"
            type="dashed"
            className="!bg-transparent"
            onClick={handleAddStage}
          >
            Add new stage
          </EverButton>
        </div>
        <div className="flex flex-col gap-6 pt-2 w-full">
          <div className="flex flex-col gap-2">
            {isRuleLoading ? (
              <>
                <EverLoader.Skeleton config={[2]} className="h-7 w-20" />
                <EverLoader.Skeleton config={[2]} className="h-7 w-40" />
              </>
            ) : (
              <>
                <EverTg.Heading4>{currentStage?.stage_name}</EverTg.Heading4>
                <EverTg.Description>
                  Configure approvers, due date, etc. for this stage.
                </EverTg.Description>
              </>
            )}
          </div>
          <SelectApprovers
            currentStage={currentStage}
            handleStageUpdate={handleStageUpdate}
            ruleDetails={ruleDetails}
          />
          <ApprovalStrategy
            currentStage={currentStage}
            handleStageUpdate={handleStageUpdate}
            currentStageName={currentStageName}
          />
          <EverDivider className="w-full" />
          <DueDateSettings
            currentStage={currentStage}
            handleStageUpdate={handleStageUpdate}
            currentStageName={currentStageName}
          />
        </div>
      </div>
    </div>
  );
}

function StageCard({
  name,
  currentStage,
  handleStageChange,
  index,
  handleRemoveStage,
}) {
  const [isHovered, setIsHovered] = useState(false);
  return (
    <EverCard
      interactive
      outlined
      roundedSize="lg"
      className={twMerge(
        "h-10 group flex items-center justify-between border-ever-base-300 bg-ever-base-200 border-b",
        currentStage?.stage_name === name &&
          "ring-4 ring-ever-primary-ring border-ever-primary"
      )}
      onClick={() => handleStageChange(name)}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <EverTg.SubHeading4 className="text-ever-base-content">
        {name}
      </EverTg.SubHeading4>
      <div className="flex items-center gap-2 transition-all duration-200 ease-in-out">
        {isHovered && index > 0 ? (
          <TrashIcon
            className="w-4 h-4 text-ever-error transition-opacity duration-200"
            onClick={(e) => {
              handleRemoveStage(index);
              e.stopPropagation();
            }}
          />
        ) : (
          <ArrowCircleRightIcon
            className={twMerge(
              "w-4 h-4 transition-all duration-200 text-ever-base-content-low group-hover:text-ever-primary",
              currentStage?.stage_name === name && "text-ever-primary"
            )}
          />
        )}
      </div>
    </EverCard>
  );
}
