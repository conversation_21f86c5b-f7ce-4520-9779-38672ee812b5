import { PlusCircleIcon } from "@everstage/evericons/outlined";
import React, { useState } from "react";
import { useQuery } from "react-query";

import {
  EmptyPagePlaceholder,
  EverButton,
  EverLoader,
  EverTg,
} from "~/v2/components";
import { findInRoll } from "~/v2/images";

import { AddRuleModal } from "./AddRuleModal";
import { ApprovalRuleCard } from "./ApprovalRuleCard";
import { useFetchApi } from "../../../hooks";
import { API, API_ENDPOINTS } from "../utils";

export default function ApprovalRules() {
  const { fetchEndpoint } = useFetchApi(API_ENDPOINTS);
  const [createRuleModalVisible, setCreateRuleModalVisible] = useState(false);
  const [approvalRules, setApprovalRules] = useState(null);
  const [isEditingRule, setIsEditingRule] = useState(false);
  const [ruleToEdit, setRuleToEdit] = useState(null);
  const { refetch } = useQuery(
    "approval-rules",
    () => fetchEndpoint(API.LIST_APPROVAL_RULES, {}),
    {
      onSuccess: (data) => {
        setApprovalRules(data?.rule_groups);
      },
    }
  );

  function handleCreateRuleModalOpen(isEditing = false, rule) {
    setCreateRuleModalVisible(true);
    setIsEditingRule(isEditing);
    setRuleToEdit(rule);
  }

  function handleCreateRuleModalClose() {
    setCreateRuleModalVisible(false);
    setIsEditingRule(false);
    setRuleToEdit(null);
  }

  return (
    <div className="flex flex-col w-full h-full">
      <div className="flex justify-between items-center px-8 py-4 bg-ever-base-50 border-b border-ever-base-400 sticky top-0 z-10">
        <div className="flex flex-col gap-1.5">
          <EverTg.Heading2>Approval Rules</EverTg.Heading2>
          <EverTg.Text>
            Configure approval rules to manage exceptions like excessive
            discounts, invalid product configurations, and other special cases.
          </EverTg.Text>
        </div>
        {approvalRules?.length > 0 && (
          <EverButton
            prependIcon={<PlusCircleIcon />}
            type="filled"
            color="primary"
            size="small"
            onClick={() => handleCreateRuleModalOpen(false)}
          >
            Create New Rule
          </EverButton>
        )}
      </div>
      <div className="flex flex-col w-full h-full py-5 px-8">
        {approvalRules === null ? (
          <div className="flex justify-center items-center h-full">
            <EverLoader.SpinnerLottie className="w-20 h-20" />
          </div>
        ) : approvalRules?.length === 0 ? (
          <EmptyPagePlaceholder
            image={findInRoll}
            title="Manage your Approvals"
            description="Build customized workflows to streamline your organization's approval process."
            buttonsList={[
              <EverButton
                size="small"
                type="ghost"
                color="primary"
                prependIcon={<PlusCircleIcon />}
                key="createNewRule"
                onClick={() => handleCreateRuleModalOpen(false)}
              >
                Create New Approval Rule
              </EverButton>,
            ]}
          />
        ) : (
          <div className="flex flex-col w-full h-full">
            <div className="flex flex-col gap-4">
              {approvalRules?.map((rule) => (
                <ApprovalRuleCard
                  key={rule.rule_group_id}
                  rule={rule}
                  handleCreateRuleModalOpen={handleCreateRuleModalOpen}
                  fetchEndpoint={fetchEndpoint}
                  refetch={refetch}
                />
              ))}
            </div>
          </div>
        )}
      </div>
      {createRuleModalVisible && (
        <AddRuleModal
          visible={createRuleModalVisible}
          setVisible={setCreateRuleModalVisible}
          onClose={handleCreateRuleModalClose}
          refetchApprovalRules={refetch}
          isEdit={isEditingRule}
          ruleToEdit={ruleToEdit}
        />
      )}
    </div>
  );
}
