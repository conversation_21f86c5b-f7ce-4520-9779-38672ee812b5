import { ArrowCircleRightIcon } from "@everstage/evericons/outlined";
import { debounce } from "lodash";
import React, { useEffect, useMemo } from "react";
import { useMutation } from "react-query";

import {
  EverButton,
  EverInput,
  EverModal,
  EverSelect,
  EverForm,
  EverTg,
  message,
} from "~/v2/components";

import { useFetchApi } from "../../../hooks";
import { API, API_ENDPOINTS } from "../utils";

const initialState = {
  name: "",
  description: "",
  form_builder_id: null,
};

export function AddRuleModal({
  visible,
  setVisible,
  isEdit = false,
  onClose,
  refetchApprovalRules,
  ruleToEdit,
}) {
  const [form] = EverForm.useForm();
  const { fetchEndpoint } = useFetchApi(API_ENDPOINTS);

  const [options, setOptions] = React.useState([]);
  const [searchTerm, setSearchTerm] = React.useState("");
  const [limitValue, _setLimitValue] = React.useState(25);
  const [pageNumber, setPageNumber] = React.useState(1);
  const [loading, setLoading] = React.useState(false);

  const fetchOptions = useMutation(
    ({ searchTerm, limitValue, pageNumber }) =>
      fetchEndpoint(API.GET_ALL_FORM_BUILDERS, {
        body: {
          searchTerm,
          limit_value: limitValue,
          page_number: pageNumber,
        },
      }),
    {
      onSuccess: (data) => {
        setOptions((prevOptions) => [
          ...prevOptions,
          ...data.form_builders.map((item) => ({
            label: item.form_builder_name,
            value: item.form_builder_id,
          })),
        ]);
        setLoading(false);
      },
    }
  );

  const createRule = useMutation(
    ({ name, description, form_builder_id }) =>
      fetchEndpoint(API.CREATE_APPROVAL_RULE, {
        body: {
          name: name.trim(),
          description: description.trim(),
          form_builder_id,
        },
      }),
    {
      onSuccess: (data) => {
        setLoading(false);
        setVisible(false);
        onClose();
        message.success(data.message || "Approval rule created successfully");
        refetchApprovalRules();
      },
      onError: (error) => {
        console.log(error);
        message.error(error.message || "Failed to create approval rule");
      },
    }
  );
  const updateRule = useMutation(
    ({ form_builder_id, rule_group_id, updated_fields }) => {
      const payload = {
        form_builder_id,
        rule_group_id,
        updated_fields,
      };
      return fetchEndpoint(API.UPDATE_APPROVAL_RULE, {
        body: payload,
      });
    },
    {
      onSuccess: (data) => {
        setLoading(false);
        setVisible(false);
        onClose();
        message.success(data.message || "Approval rule updated successfully");
        refetchApprovalRules();
      },
      onError: (error) => {
        console.log(error);
        message.error("Failed to update approval rule");
      },
    }
  );

  const debouncedHandleSearch = useMemo(() => {
    return debounce((value) => {
      if (value.length > 3) {
        setSearchTerm(value);
        setOptions([]);
        setPageNumber(1);
        setLoading(true);
        fetchOptions.mutate({ searchTerm: value, limitValue, pageNumber: 1 });
      }
    }, 300);
  }, [
    limitValue,
    fetchOptions,
    setSearchTerm,
    setOptions,
    setPageNumber,
    setLoading,
  ]);

  function handleScroll(event) {
    const { target } = event;
    if (
      !loading &&
      target.scrollTop + target.clientHeight >= target.scrollHeight
    ) {
      setLoading(true);
      setPageNumber((prevPageNumber) => prevPageNumber + 1);
      fetchOptions.mutate({
        searchTerm,
        limitValue,
        pageNumber: pageNumber + 1,
      });
    }
  }

  const debouncedHandleFinish = debounce(handleFinish, 300);

  function handleFinish() {
    if (isEdit) {
      const currentValues = form.getFieldsValue(["name", "description"]);
      const initialValues = {
        name: ruleToEdit.name,
        description: ruleToEdit.description,
      };

      // Compare and only include changed fields
      const changedFields = {};
      if (currentValues.name !== initialValues.name) {
        changedFields.name = currentValues.name;
      }
      if (currentValues.description !== initialValues.description) {
        changedFields.description = currentValues.description;
      }

      // Only proceed if there are changes
      if (Object.keys(changedFields).length > 0) {
        updateRule.mutate({
          form_builder_id: form.getFieldValue("form_builder_id"),
          rule_group_id: ruleToEdit.rule_group_id,
          updated_fields: changedFields,
        });
      } else {
        // No changes made
        setVisible(false);
        onClose();
        message.info("No changes detected");
      }
    } else {
      createRule.mutate(form.getFieldsValue());
    }
  }

  function handleClose() {
    setVisible(false);
    onClose();
  }

  useEffect(() => {
    fetchOptions.mutate({
      searchTerm: "",
      limitValue,
      pageNumber,
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (isEdit && ruleToEdit) {
      form.setFieldsValue({
        name: ruleToEdit.name,
        description: ruleToEdit.description,
        form_builder_id: ruleToEdit.form_builder_id,
      });
    }
  }, [isEdit, ruleToEdit, form]);

  return (
    <EverModal
      visible={visible}
      closable={true}
      onCancel={handleClose}
      maskClosable={false}
      destroyOnClose
      centered
      className="!w-[640px]"
      bodyStyle={{ padding: "0" }}
    >
      <div className="flex flex-col items-center justify-center w-full gap-10 py-14">
        <div className="flex pb-2 px-[120px]">
          <EverTg.Heading2 className="text-center">
            {isEdit ? "Edit Approval Rule" : "Create New Approval Rule"}
          </EverTg.Heading2>
        </div>
        <EverForm
          form={form}
          layout="vertical"
          onFinish={debouncedHandleFinish}
          initialValues={initialState}
          className="w-full"
        >
          <EverForm.Item
            label="Name"
            name="name"
            rules={[
              {
                required: true,
                message: "Please enter the approval rule name",
                validator: (_, value) => {
                  if (value.length > 255) {
                    return Promise.reject(
                      new Error("Name must be less than 255 characters")
                    );
                  }
                  if (value.trim() === "") {
                    return Promise.reject(new Error("Name cannot be empty"));
                  }
                  return Promise.resolve();
                },
              },
            ]}
            className="px-[120px]"
          >
            <EverInput maxLength={255} placeholder="Enter approval rule name" />
          </EverForm.Item>
          <EverForm.Item
            label="Description"
            name="description"
            rules={[{ required: false }]}
            className="px-[120px]"
          >
            <EverInput.TextArea
              maxLength={255}
              showCount={true}
              rows={2}
              placeholder="Enter approval rule description"
              className="!min-h-[45px]"
            />
          </EverForm.Item>
          <EverForm.Item
            label="Associated form"
            name="form_builder_id"
            rules={[
              { required: true, message: "Please select the associated form" },
            ]}
            className="px-[120px]"
          >
            <EverSelect
              placeholder="Select the associated form"
              options={options}
              onSearch={debouncedHandleSearch}
              onPopupScroll={handleScroll}
              showSearch
              loading={loading}
              disabled={isEdit}
            />
          </EverForm.Item>
          <EverForm.Item className="px-20 mt-20">
            <EverButton
              appendIcon={<ArrowCircleRightIcon />}
              htmlType="submit"
              color="primary"
              className="w-full"
            >
              {isEdit ? "Save Changes" : "Add Approval Rule"}
            </EverButton>
          </EverForm.Item>
        </EverForm>
      </div>
    </EverModal>
  );
}
