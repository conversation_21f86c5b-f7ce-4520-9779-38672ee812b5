import React, { useState, useEffect, useRef } from "react";

import {
  DATATYPE,
  EXPRESSION_BOX_STATUS,
  EXPRESSION_TOKEN_TYPES,
} from "~/Enums";
import {
  Ever<PERSON>utton,
  EverDivider,
  EverGroupAvatar,
  EverInput,
  EverModal,
  EverRadio,
  EverTg,
} from "~/v2/components";

import { LabeledField } from "../../../../components";
import { TruncatedText } from "../../../../components/TruncatedText";
import { getInitialExpression } from "../../utils";
import { ApprovalExpressionBox } from "../components/ApprovalExpressionBox";

export function ApproverSettingsModal({
  visible,
  onClose,
  rule,
  handleStageUpdate,
  index,
  currentStage,
  ruleDetails,
}) {
  // Local state for rule modifications
  const [localRule, setLocalRule] = useState(rule);
  const [isExpressionValid, setIsExpressionValid] = useState(true);
  const [showConditionBuilder, setShowConditionBuilder] = useState(
    !localRule.always_include
  );
  const approvalExpressionRef = useRef(null);
  const ruleNameInputRef = useRef(null);

  function extractApproverInfo() {
    const approver = [
      ...localRule.approvers.users,
      ...localRule.approvers.groups,
      ...localRule.approvers.dynamic,
    ];

    return approver[0] || {};
  }

  const approver = extractApproverInfo();

  // Handle updating a field in the local rule state
  const handleLocalRuleUpdate = (updatedFields) => {
    setLocalRule((prevRule) => ({
      ...prevRule,
      ...updatedFields,
    }));
  };

  // Save changes to parent component
  const handleSaveChanges = () => {
    // Create a new copy of the rules array from currentStage
    const updatedRules = [...currentStage.rules];

    // Trim the rule name
    localRule.rule_name = localRule.rule_name.trim();

    // If the rule is always included, set the rule name to "Always include"
    // and set the condition to true
    if (localRule.always_include) {
      localRule.rule_name = "Always include";
      localRule.rule.actions[0].source_fields = [];
      localRule.rule.actions[0].condition = [
        {
          token_type: "CONSTANT_VARIABLES",
          token: {
            args: ["Boolean", "True"],
            name: "True",
            key: "True",
            data_type: "Boolean",
          },
        },
      ];
    }

    // Update the specific rule at the given index
    updatedRules[index] = localRule;

    // Update the stage with the entire modified rules array
    handleStageUpdate({
      rules: updatedRules,
    });

    // Close the modal
    onClose();
  };

  const handleExpressionChange = (expression, result) => {
    if (
      result.status === EXPRESSION_BOX_STATUS.VALID &&
      result.data_type === DATATYPE.BOOLEAN
    ) {
      setIsExpressionValid(true);
    } else {
      setIsExpressionValid(false);
    }
    const source_fields = [];
    for (const item of expression) {
      if (
        item.tokenType === EXPRESSION_TOKEN_TYPES.QUOTE_VARIABLES &&
        !source_fields.includes(item.token.systemName)
      ) {
        source_fields.push(item.token.systemName);
      }
    }

    setLocalRule((prevRule) => ({
      ...prevRule,
      rule: {
        ...prevRule.rule,
        actions: [
          {
            ...prevRule.rule.actions[0],
            condition: expression,
          },
        ],
        source_fields: source_fields,
      },
    }));
  };

  const getInitialCondition = () => {
    if (rule.always_include) {
      return [];
    }
    return localRule.rule.actions[0].condition;
  };

  const checkSaveButtonEnabled = () => {
    if (!isExpressionValid) return true;
    if (localRule.rule_name.trim() === "") return true;
    return false;
  };

  // Reset local state when modal opens with new rule
  useEffect(() => {
    if (visible) {
      setLocalRule(rule);
      setShowConditionBuilder(!rule.always_include);
    }
  }, [visible, rule]);

  return (
    <EverModal
      visible={visible}
      closable
      onCancel={onClose}
      title={
        <div className="w-full flex items-center relative">
          <EverTg.Heading3>Approver Settings</EverTg.Heading3>
          <div className="absolute w-full flex justify-center items-end gap-2">
            <EverGroupAvatar
              avatars={[
                {
                  image: approver.profilePicture,
                  firstName: approver.label,
                  className: "w-6 h-6",
                },
              ]}
            />
            <TruncatedText
              text={approver.label}
              className="w-full max-w-[30%] w-max"
            >
              <EverTg.Heading4 className="text-ever-base-content">
                {approver.label}
              </EverTg.Heading4>
            </TruncatedText>
          </div>
        </div>
      }
      destroyOnClose
      centered
      className="!w-[760px]"
      bodyStyle={{ padding: "28px 32px", height: "480px" }}
      footer={
        <div className="flex justify-end w-full">
          <EverButton
            color="primary"
            size="small"
            type="filled"
            onClick={handleSaveChanges}
            disabled={checkSaveButtonEnabled()}
          >
            Save changes
          </EverButton>
        </div>
      }
    >
      <div className="flex flex-col gap-5 w-full">
        <TitleWithLine title="Configure rules to be met for inclusion" />
        <div className="flex flex-col gap-4">
          <EverTg.Heading4>
            When should {approver.label} be an approver?
          </EverTg.Heading4>
          <EverRadio.Group
            value={localRule.always_include ? "default" : "conditional"}
            onChange={(event) => {
              setShowConditionBuilder(event.target.value === "conditional");
              handleLocalRuleUpdate({
                always_include: event.target.value === "default",
                rule_name:
                  event.target.value === "default"
                    ? "Always include"
                    : rule.rule_name,
              });
              if (event.target.value === "conditional") {
                setTimeout(() => {
                  if (ruleNameInputRef.current) {
                    ruleNameInputRef.current.focus();
                    ruleNameInputRef.current.select();
                  }
                }, 150);
              }
            }}
            className="flex gap-6"
          >
            <EverRadio value="default" label="Added as approver by default" />
            <EverRadio
              value="conditional"
              label="Added as approver conditionally"
            />
          </EverRadio.Group>
        </div>
        <div
          className={`transition-all duration-300 ease-in-out ${
            showConditionBuilder
              ? "max-h-[1000px] opacity-100"
              : "max-h-0 opacity-0"
          } overflow-visible`}
        >
          <div className="flex flex-col gap-6">
            <LabeledField label="Describe the scenario" required>
              <EverInput
                value={localRule.rule_name}
                onChange={(e) => {
                  handleLocalRuleUpdate({
                    rule_name: e.target.value,
                  });
                }}
                placeholder="Enter rule name"
                ref={ruleNameInputRef}
              />
            </LabeledField>
            <TitleWithLine title="conditions" />
            <ApprovalExpressionBox
              onChange={handleExpressionChange}
              initialExpression={getInitialExpression(getInitialCondition())}
              resultType={DATATYPE.BOOLEAN}
              className="!min-h-[65px] w-full rounded-md p-3 bg-ever-base-100 !mt-2"
              context={{
                approvalType: "quote",
                variables: [],
              }}
              instance="approverCondition"
              approvalExpressionRef={approvalExpressionRef}
              formBuilderId={ruleDetails?.form_builder_id}
            />
          </div>
        </div>
      </div>
    </EverModal>
  );
}

function TitleWithLine({ title }) {
  return (
    <div className="flex gap-2 items-center w-full">
      <EverTg.Caption.Medium className="text-ever-base-content-mid whitespace-nowrap">
        {title.toUpperCase()}
      </EverTg.Caption.Medium>
      <EverDivider />
    </div>
  );
}
