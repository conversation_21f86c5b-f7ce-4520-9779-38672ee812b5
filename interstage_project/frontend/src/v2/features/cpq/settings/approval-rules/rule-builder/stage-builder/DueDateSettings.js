import React, { useEffect, useState } from "react";

import {
  EverCheckbox,
  EverInput,
  EverLabel,
  EverSwitch,
} from "~/v2/components";

import { AnimatedWrapper, ANIMATION_TYPES } from "../../../../components";

export function DueDateSettings({
  currentStage,
  handleStageUpdate,
  currentStageName,
}) {
  const [showDueDate, setShowDueDate] = useState(
    currentStage?.due_period > 1 || currentStage?.is_auto_approve
  );

  function handleDaysChange(days) {
    handleStageUpdate({ due_period: days });
  }

  function handleAutoApproveChange(e) {
    const value = e.target.checked;
    handleStageUpdate({ is_auto_approve: value });
  }

  function handleDueDateSwitchChange(checked) {
    setShowDueDate(checked);
    if (!checked) {
      handleStageUpdate({ due_period: 1, is_auto_approve: false });
    }
  }

  useEffect(() => {
    if (currentStage) {
      if (currentStage.is_auto_approve) {
        setShowDueDate(true);
      } else if (currentStage.due_period > 1) {
        setShowDueDate(true);
      } else {
        setShowDueDate(false);
      }
    }
  }, [currentStageName, currentStage]);

  return (
    <div>
      <EverSwitch
        label="Set a due date for this stage"
        checked={showDueDate}
        onChange={handleDueDateSwitchChange}
      />
      <AnimatedWrapper
        isVisible={showDueDate}
        motionProps={{
          type: ANIMATION_TYPES.EXPAND_COLLAPSE,
        }}
      >
        <div className="flex flex-col gap-4 mt-4">
          <EverLabel required>
            Select no of days from when this stage is triggered.
          </EverLabel>
          <EverInput.Number
            placeholder="Enter due date"
            value={currentStage?.due_period}
            onChange={handleDaysChange}
            min={1}
            max={30}
            className="w-1/5"
          />
          <EverCheckbox
            checked={currentStage?.is_auto_approve}
            onChange={handleAutoApproveChange}
            label="Automatically approve stage if no response beyond the due date"
          />
        </div>
      </AnimatedWrapper>
    </div>
  );
}
