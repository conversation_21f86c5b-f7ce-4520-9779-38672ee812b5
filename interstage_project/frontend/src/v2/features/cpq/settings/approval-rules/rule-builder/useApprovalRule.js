import { cloneDeep } from "lodash";
import { useState, useMemo, useEffect } from "react";
import { useMutation, useQuery } from "react-query";
import { useLocation, useParams } from "react-router-dom";
import { useSetRecoilState } from "recoil";

import {
  DATATYPE,
  EXPRESSION_BOX_STATUS,
  EXPRESSION_TOKEN_TYPES,
} from "~/Enums";
import { breadcrumbAtom } from "~/GlobalStores/atoms";
import { message } from "~/v2/components";

import { useFetchApi } from "../../../hooks";
import {
  API,
  API_ENDPOINTS,
  convertKeysToPascalCase,
  dynamicApproverMap,
} from "../utils";

function getEmptyStage(n) {
  return {
    stage_name: `Stage #${n}`,
    stage_order: n,
    approval_strategy: "everyone",
    rules: [],
    due_period: 1,
    notes: ".",
    approval_trigger: "approved",
    cool_off_period: null,
  };
}

function populateDynamicData(data) {
  data.stages = data.stages.map((stage) => {
    stage.rules = stage.rules.map((rule) => {
      if (rule.approvers.dynamic.length > 0) {
        rule.approvers.dynamic = rule.approvers.dynamic.map((dynamic) => {
          return {
            label: dynamicApproverMap[dynamic],
            value: dynamic,
          };
        });
      }
      return rule;
    });
    return stage;
  });

  return data;
}

export function useApprovalRule() {
  const { fetchEndpoint } = useFetchApi(API_ENDPOINTS);
  const location = useLocation();
  const params = useParams();
  const setBreadcrumbName = useSetRecoilState(breadcrumbAtom);

  const [_savedRule, setSavedRule] = useState({
    stages: [],
    notify_on_reject: {},
    notify_on_approve: {},
    condition: [],
  });

  const [currentRule, setCurrentRule] = useState({
    stages: [],
    notify_on_reject: {},
    notify_on_approve: {},
    condition: [],
  });

  const [ruleDetails, setRuleDetails] = useState({
    rule_group_id: "",
    name: "",
    description: "",
    status: "",
    form_builder_name: "",
    form_builder_id: "",
    created_by: "",
  });

  const [currentStage, setCurrentStage] = useState(null);
  const [isExpressionValid, setIsExpressionValid] = useState(true);
  const [currentStageName, setCurrentStageName] = useState(null);
  const [disabledTooltip, setDisabledTooltip] = useState("");

  const ruleId = params.ruleId || location.pathname.split("/").pop();

  const {
    isFetching: isRuleLoading,
    error: ruleError,
    refetch: refetchRule,
  } = useQuery(
    ["approvalRule", ruleId],
    () => fetchEndpoint(API.GET_APPROVAL_RULE, {}, { ruleId }),
    {
      enabled: !!ruleId,
      onSuccess: (data) => {
        data = populateDynamicData(data);
        const sortedStages = [...data.stages].sort(
          (a, b) => a.stage_order - b.stage_order
        );
        sortedStages.length === 0 && sortedStages.push(getEmptyStage(1));
        if (sortedStages.length === 0) {
          sortedStages.push(getEmptyStage(1));
        }
        setCurrentStage(sortedStages[0]);
        setCurrentStageName(sortedStages[0].stage_name);
        setCurrentRule({
          stages: sortedStages,
          notify_on_reject: data.notify_on_reject,
          notify_on_approve: data.notify_on_approve,
          condition: data.condition,
        });
        setSavedRule({
          stages: data.stages,
          notify_on_reject: data.notify_on_reject,
          notify_on_approve: data.notify_on_approve,
          condition: data.condition,
        });

        setRuleDetails({
          rule_group_id: data.rule_group_id,
          name: data.name,
          description: data.description,
          status: data.status,
          form_builder_name: data.form_builder_name,
          form_builder_id: data.form_builder_id,
          created_by: data.created_by,
        });
        setBreadcrumbName([
          {
            index: 2,
            title: data.name,
            name:
              data.name && data.name.length > 200
                ? data.name.slice(0, 100) + "..."
                : data.name,
          },
        ]);
      },
    }
  );

  const updateRule = useMutation(
    ({ form_builder_id, rule_group_id, updated_fields }) => {
      const payload = {
        form_builder_id,
        rule_group_id,
        updated_fields,
      };
      return fetchEndpoint(API.UPDATE_APPROVAL_RULE, { body: payload });
    },
    {
      onSuccess: (data, { refetch }) => {
        message.success(data.message || "Approval rule updated successfully");
        refetch();
      },
      onError: (error) => {
        console.log(error);
        message.error("Failed to update approval rule");
      },
    }
  );

  const cloneRule = useMutation(
    ({ ruleId }) =>
      fetchEndpoint(API.CREATE_APPROVAL_RULE, {
        body: { rule_group_id: ruleId },
      }),
    {
      onSuccess: (data) => {
        message.success(data.message || "Approval rule created successfully");
      },
      onError: (error) => {
        message.error(error.message || "Failed to create approval rule");
      },
    }
  );

  const deleteRule = useMutation(
    ({ ruleId }) =>
      fetchEndpoint(API.DELETE_APPROVAL_RULE, {
        body: { rule_group_id: ruleId },
      }),
    {
      onSuccess: (data, { afterDelete }) => {
        message.success(data.message || "Approval rule deleted successfully");
        afterDelete && afterDelete();
      },
      onError: (error) => {
        message.error(error.message || "Failed to delete approval rule");
      },
    }
  );

  function handleCloneRule(ruleId) {
    cloneRule.mutate({ ruleId });
  }

  function handleDeleteRule(ruleId, afterDelete) {
    deleteRule.mutate({ ruleId, afterDelete });
  }

  function handleUpdateRule(
    form_builder_id,
    rule_group_id,
    updated_fields,
    refetch
  ) {
    updateRule.mutate({
      form_builder_id,
      rule_group_id,
      updated_fields,
      refetch,
    });
  }

  // New function to get updated fields by comparing saved and current rule
  function getUpdatedFields() {
    const updatedFields = {};

    // check if the condition is valid
    if (
      JSON.stringify(_savedRule.condition) !==
      JSON.stringify(currentRule.condition)
    ) {
      updatedFields.condition = currentRule.condition;
    }

    // Check stages
    if (
      JSON.stringify(_savedRule.stages) !== JSON.stringify(currentRule.stages)
    ) {
      updatedFields.stages = currentRule.stages;
    }

    // Check notify_on_reject
    if (
      JSON.stringify(_savedRule.notify_on_reject) !==
      JSON.stringify(currentRule.notify_on_reject)
    ) {
      updatedFields.notify_on_reject = currentRule.notify_on_reject;
    }

    // Check notify_on_approve
    if (
      JSON.stringify(_savedRule.notify_on_approve) !==
      JSON.stringify(currentRule.notify_on_approve)
    ) {
      updatedFields.notify_on_approve = currentRule.notify_on_approve;
    }

    return updatedFields;
  }

  // New helper function to save changes
  function handleSaveChanges() {
    if (!isExpressionValid) return;

    const updatedFields = getUpdatedFields();

    if (updatedFields.stages) {
      updatedFields.stages = cloneDeep(updatedFields.stages).map((stage) => {
        stage.rules = stage.rules.map((rule) => {
          return {
            ...rule,
            approvers: {
              users: rule.approvers.users?.map((user) => user.value) || [],
              groups: rule.approvers.groups?.map((group) => group.value) || [],
              dynamic:
                rule.approvers.dynamic?.map((dynamic) => dynamic.value) || [],
            },
          };
        });
        return stage;
      });
    }

    if (updatedFields.notify_on_reject) {
      updatedFields.notify_on_reject = {
        users:
          updatedFields.notify_on_reject.users?.map((user) => user.value) || [],
        groups:
          updatedFields.notify_on_reject.groups?.map((group) => group.value) ||
          [],
        dynamic: updatedFields.notify_on_reject.dynamic || [],
      };
    }
    if (updatedFields.notify_on_approve) {
      updatedFields.notify_on_approve = {
        users:
          updatedFields.notify_on_approve.users?.map((user) => user.value) ||
          [],
        groups:
          updatedFields.notify_on_approve.groups?.map((group) => group.value) ||
          [],
        dynamic: updatedFields.notify_on_approve.dynamic || [],
      };
    }

    if (Object.keys(updatedFields).length > 0) {
      handleUpdateRule(
        ruleDetails.form_builder_id,
        ruleDetails.rule_group_id,
        updatedFields,
        refetchRule
      );
    }
  }

  function handleAddStage() {
    const newStageOrder = currentRule.stages.length + 1;
    setCurrentRule({
      ...currentRule,
      stages: [...currentRule.stages, getEmptyStage(newStageOrder)],
    });
  }

  function handleRemoveStage(removeIndex) {
    // Create a new array with the stage removed
    const filteredStages = currentRule.stages.filter(
      (_, index) => index !== removeIndex
    );

    // Create new stage objects with updated properties
    const updatedStages = filteredStages.map((stage, index) => ({
      ...cloneDeep(stage),
      stage_order: index + 1,
      stage_name: `Stage #${index + 1}`,
    }));

    setCurrentRule({
      ...currentRule,
      stages: updatedStages,
    });

    // Set current stage to the first stage in the updated array
    const firstStage = updatedStages[0];
    setCurrentStage(firstStage);
    setCurrentStageName(firstStage.stage_name);
  }

  function handleCurrentStageChange(stageName) {
    const stageObj = currentRule.stages.find(
      (stage) => stage.stage_name === stageName
    );
    setCurrentStage({ ...stageObj });
    setCurrentStageName(stageName);
  }

  function handleStageUpdate(updatedValues) {
    let updatedStage = cloneDeep(currentStage);
    updatedStage = { ...updatedStage, ...updatedValues };
    setCurrentStage(updatedStage);
    setCurrentRule({
      ...currentRule,
      stages: currentRule.stages.map((s) =>
        s.stage_name === updatedStage.stage_name ? updatedStage : s
      ),
    });
  }

  function handleModifyNotifiers(updatedValues) {
    setCurrentRule({
      ...currentRule,
      ...updatedValues,
    });
  }

  function handleRuleConditionUpdate(expression, result) {
    if (
      result.status === EXPRESSION_BOX_STATUS.VALID &&
      result.data_type === DATATYPE.BOOLEAN.toUpperCase()
    ) {
      setIsExpressionValid(true);
      const sourceFields = [];
      for (const item of expression) {
        if (item.tokenType === EXPRESSION_TOKEN_TYPES.QUOTE_VARIABLES) {
          sourceFields.push(item.token.systemName);
        }
      }
      setCurrentRule({
        ...currentRule,
        condition: { rule: expression, source_fields: sourceFields },
      });
    } else {
      setIsExpressionValid(false);
    }
  }

  // Calculate save button state and tooltip with useMemo
  const saveButtonState = useMemo(() => {
    // Check if expression is valid
    if (!isExpressionValid) {
      return { disabled: true, tooltip: "Condition is not valid" };
    }

    if (
      JSON.stringify(
        convertKeysToPascalCase(cloneDeep(_savedRule?.condition?.rule))
      ) === JSON.stringify(currentRule?.condition?.rule) &&
      JSON.stringify(_savedRule.stages) ===
        JSON.stringify(currentRule.stages) &&
      JSON.stringify(_savedRule.notify_on_approve) ===
        JSON.stringify(currentRule.notify_on_approve) &&
      JSON.stringify(_savedRule.notify_on_reject) ===
        JSON.stringify(currentRule.notify_on_reject)
    ) {
      return { disabled: true, tooltip: "No changes detected" };
    }

    // Check if there are any changes

    // Check if all stages have at least one rule/approver
    for (const stage of currentRule.stages) {
      if (!stage.rules || stage.rules.length === 0) {
        return {
          disabled: true,
          tooltip: `${stage.stage_name} has no approvers`,
        };
      }
    }

    // Check if notify_on_approve has at least one notifier
    const approveNotifiers = [
      ...(currentRule.notify_on_approve.users || []),
      ...(currentRule.notify_on_approve.groups || []),
      ...(currentRule.notify_on_approve.dynamic || []),
    ];
    if (approveNotifiers.length === 0) {
      return {
        disabled: true,
        tooltip: "Notify when Approved should have at least one value",
      };
    }

    // Check if notify_on_reject has at least one notifier
    const rejectNotifiers = [
      ...(currentRule.notify_on_reject.users || []),
      ...(currentRule.notify_on_reject.groups || []),
      ...(currentRule.notify_on_reject.dynamic || []),
    ];
    if (rejectNotifiers.length === 0) {
      return {
        disabled: true,
        tooltip: "Notify when Rejected should have at least one value",
      };
    }

    // If all checks pass
    return { disabled: false, tooltip: "" };
  }, [isExpressionValid, _savedRule, currentRule]);

  // Update tooltip when saveButtonState changes
  useEffect(() => {
    setDisabledTooltip(saveButtonState.tooltip);
  }, [saveButtonState]);

  // Function that just returns the calculated value
  function isSaveButtonDisabled() {
    return saveButtonState.disabled;
  }

  return {
    currentRule,
    currentStage,
    ruleDetails,
    isRuleLoading,
    ruleError,
    isExpressionValid,
    currentStageName,
    disabledTooltip,
    isSaveButtonDisabled,
    refetchRule,
    handleAddStage,
    handleRemoveStage,
    handleCurrentStageChange,
    handleStageUpdate,
    handleModifyNotifiers,
    handleCloneRule,
    handleDeleteRule,
    handleUpdateRule,
    handleSaveChanges,
    handleRuleConditionUpdate,
  };
}
