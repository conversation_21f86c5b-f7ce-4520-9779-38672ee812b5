import {
  ArrowCircleRightIcon,
  File07Icon,
} from "@everstage/evericons/outlined";
import { motion } from "framer-motion";
import React, { useState, useRef, useMemo } from "react";

import { useAuthStore } from "~/GlobalStores/AuthStore";
import {
  <PERSON>Button,
  EverCard,
  EverDivider,
  EverLabel,
  EverModal,
  EverSelect,
  EverTg,
  LazySelect,
} from "~/v2/components";
import { useFetchApiWithAuth } from "~/v2/hooks";

import { TruncatedText } from "../../../components";
import { snakeToCamel } from "../../../price-book/utils";
import { quoteDetails } from "../services/restApi";
import { CREATE_QUOTE_TYPES } from "../utils";

const { Option } = EverSelect;

const QuoteCard = ({ icon, title, subTitle, isActive, onClick }) => (
  <div onClick={onClick}>
    <EverCard
      className={`min-w-72 hover:cursor-pointer ${
        isActive
          ? "ring-4 ring-ever-primary-ring border border-solid border-ever-primary"
          : ""
      }`}
      interactive
    >
      <div className="flex flex-col items-center justify-center px-6 py-2 gap-2">
        {React.cloneElement(icon, {
          className: `w-7 h-7 mb-2 ${
            isActive ? "text-ever-primary" : "text-ever-base-content-mid"
          }`,
        })}
        <EverTg.Heading3
          className={isActive ? "text-ever-primary" : "text-ever-base-content"}
        >
          {title}
        </EverTg.Heading3>
        <EverTg.Text className="text-ever-base-content-mid">
          {subTitle}
        </EverTg.Text>
      </div>
    </EverCard>
  </div>
);

const CreateQuoteModal = ({
  visible,
  onClose,
  handleCreateQuote,
  isLoading,
  associatedForm,
  setAssociatedForm,
}) => {
  const [selectedQuoteType, setSelectedQuoteType] = useState("");
  const [selectedQuoteId, setSelectedQuoteId] = useState("");
  const [showAssociatedForm, setShowAssociatedForm] = useState(true);

  const abortController = useRef();
  const quoteFormAbortController = useRef();
  const { accessToken } = useAuthStore();
  const { fetchData } = useFetchApiWithAuth();

  const fetchQuoteForms = async (params) => {
    const { searchTerm = null, page, limit, successCbk, failureCbk } = params;

    quoteFormAbortController.current = new AbortController();
    const { signal } = quoteFormAbortController.current;
    const body = {
      limit_value: limit,
      search_term: searchTerm || "",
      page_number: page + 1,
      status: "active",
    };

    try {
      const res = await fetchData(
        `/ninja/cpq/forms/all_form_builders`,
        "POST",
        body,
        { signal }
      );
      const data = res.form_builders.map((item) => ({
        label: item.form_builder_name,
        value: item.form_builder_id,
        key: `${item.form_builder_name}##::##${item.form_builder_id}`,
      }));
      if (data.length === 1) {
        setAssociatedForm(data[0].value);
        setShowAssociatedForm(false);
      }
      successCbk(data);
    } catch (e) {
      failureCbk();
    }
  };

  const quoteFormLazyLoadProps = {
    abort: () => {
      quoteFormAbortController?.current?.abort();
    },
    getOptions: async (params) => {
      await fetchQuoteForms(params);
    },
  };

  const fetchQuoteData = async (params) => {
    const { searchTerm = null, offset, limit, successCbk, failureCbk } = params;

    abortController.current = new AbortController();
    const { signal } = abortController.current;
    const options = {
      limit_value: limit,
      offset_value: offset,
      search_term: searchTerm || "",
    };

    try {
      const resp = await quoteDetails(options, accessToken, { signal });
      const data = snakeToCamel(resp.quotes).map((quote) => ({
        label: quote.quoteName,
        value: quote.quoteId,
        key: `${quote.quoteName}##::##${quote.quoteId}`,
        ...quote,
      }));
      successCbk(data);
    } catch (e) {
      failureCbk();
    }
  };

  const lazyLoadProps = {
    abort: () => {
      abortController?.current?.abort();
    },
    getOptions: async (params) => {
      await fetchQuoteData(params);
    },
  };

  const executeHandleCreateQuote = (quoteId) => {
    const payload = {
      quote_id: quoteId || null,
      request_type: selectedQuoteType,
    };
    handleCreateQuote(payload);
  };

  const handleClose = () => {
    setSelectedQuoteType("");
    setSelectedQuoteId("");
    onClose();
  };

  const handleDisabled = () => {
    if (selectedQuoteType === "clone" && !selectedQuoteId) {
      return true;
    } else if (selectedQuoteType === "new" && !associatedForm) {
      return true;
    } else {
      return !selectedQuoteType;
    }
  };

  const showQuoteFormSelector = useMemo(() => {
    return selectedQuoteType === "clone" || showAssociatedForm;
  }, [selectedQuoteType, showAssociatedForm]);

  return (
    <div>
      <EverModal
        visible={visible}
        onCancel={handleClose}
        destroyOnClose
        width={768}
      >
        <div className="flex flex-col items-center justify-between py-20 px-16 gap-10 w-full">
          <div className="flex flex-col w-full gap-3 text-center">
            <EverTg.Heading1>Create Quote</EverTg.Heading1>
            <EverTg.Text className="text-ever-base-content-mid">
              How do you want to get started?
            </EverTg.Text>
          </div>
          <div className="flex gap-4 w-full">
            {CREATE_QUOTE_TYPES.map((quoteType, index) => (
              <QuoteCard
                key={index}
                icon={quoteType.icon}
                title={quoteType.title}
                subTitle={quoteType.subTitle}
                isActive={selectedQuoteType === quoteType.type}
                onClick={() => setSelectedQuoteType(quoteType.type)}
              />
            ))}
          </div>
          <div className="flex flex-col justify-center items-center gap-10 w-full">
            <motion.div
              initial={{ opacity: 0, maxHeight: 0 }}
              animate={{
                opacity: ["clone", "new"].includes(selectedQuoteType) ? 1 : 0,
                maxHeight: ["clone", "new"].includes(selectedQuoteType)
                  ? 80
                  : 0,
              }}
              transition={{ duration: 0.25, ease: "easeInOut" }}
              className="flex flex-col gap-2 w-full overflow-hidden"
            >
              <EverLabel required={showQuoteFormSelector}>
                <EverTg.Caption className="text-ever-base-content-mid">
                  {selectedQuoteType === "clone"
                    ? "Choose a Quote"
                    : showAssociatedForm
                    ? "Quote form"
                    : null}
                </EverTg.Caption>
              </EverLabel>
              {selectedQuoteType === "clone" ? (
                <LazySelect
                  key="quotes"
                  showSearch
                  limit={50}
                  size="medium"
                  className="w-full"
                  placeholder="Search by Quote Name or ID"
                  value={selectedQuoteId}
                  onChange={(value) => setSelectedQuoteId(value)}
                  customDropdownOptionsRender={(options) => {
                    return options.map((quote, index) => {
                      if (quote.value === "spin-1") {
                        return (
                          <Option key={index} {...quote}>
                            {quote.label}
                          </Option>
                        );
                      }
                      return (
                        <Option
                          key={index}
                          value={quote.quoteId}
                          title={`${quote.quoteName} | ${quote.quoteDisplayId}`}
                        >
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                              <div className="flex items-center gap-2">
                                <File07Icon className="w-4 h-4 text-ever-base-content-low stroke-[1.2]" />
                                <EverTg.Text className="text-ever-base-content  max-w-80 truncate">
                                  <TruncatedText text={quote.quoteName}>
                                    {quote.quoteName}
                                  </TruncatedText>
                                </EverTg.Text>
                              </div>
                              <EverDivider type="vertical" className="h-4" />
                              <EverTg.Text className="text-uppercase text-ever-base-content-mid">
                                {quote.quoteDisplayId}
                              </EverTg.Text>
                            </div>
                            <EverTg.Caption className="text-ever-base-content-low font-medium">
                              {quote.status}
                            </EverTg.Caption>
                          </div>
                        </Option>
                      );
                    });
                  }}
                  {...lazyLoadProps}
                />
              ) : showAssociatedForm ? (
                <LazySelect
                  key="quote form"
                  showSearch
                  limit={25}
                  size="medium"
                  className="w-full"
                  placeholder="Select a quote form"
                  value={associatedForm}
                  onChange={(value) => setAssociatedForm(value)}
                  {...quoteFormLazyLoadProps}
                />
              ) : null}
            </motion.div>
            <EverButton
              appendIcon={<ArrowCircleRightIcon className="w-5 h-5" />}
              className="w-80 !h-12"
              onClick={() => executeHandleCreateQuote(selectedQuoteId)}
              disabled={handleDisabled()}
              loading={isLoading}
            >
              <EverTg.Text className="text-lg">Continue</EverTg.Text>
            </EverButton>
          </div>
        </div>
      </EverModal>
    </div>
  );
};

export default CreateQuoteModal;
