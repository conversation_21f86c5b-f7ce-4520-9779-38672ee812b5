import { PlusCircleIcon } from "@everstage/evericons/outlined";

import { EverTg, EverButton } from "~/v2/components";
import { createQuotes, emptyBg } from "~/v2/images";

const CreateQuote = ({ setIsCreateQuoteModalOpen, isLoading }) => {
  return (
    <div className="relative h-[calc(100vh-140px)] flex flex-col justify-center items-center">
      <img src={emptyBg} className="w-full absolute top-0 left-0" />
      <div className="flex flex-col gap-2 mb-5 items-center mt-8 z-2">
        <img src={createQuotes} className="mb-8" />
        <EverTg.Heading2 className="text-center">
          Creating a quote is like casting a spell - enchant clients, conjure up
          discounts and make that deal a reality!
        </EverTg.Heading2>
        <EverTg.Text>Ready for some magic?</EverTg.Text>
        <EverButton
          size="small"
          prependIcon={<PlusCircleIcon className="w-4 h-4" />}
          className="mt-4"
          onClick={() => setIsCreateQuoteModalOpen(true)}
          loading={isLoading}
        >
          Create Quote
        </EverButton>
      </div>
    </div>
  );
};

export default CreateQuote;
