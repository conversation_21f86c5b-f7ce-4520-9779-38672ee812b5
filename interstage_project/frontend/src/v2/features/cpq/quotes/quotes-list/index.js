import {
  Server01Icon,
  Building07Icon,
  Users02Icon,
} from "@everstage/evericons/duotone";
import {
  ChevronDownIcon,
  PlusCircleIcon,
  ClockFastForwardIcon,
} from "@everstage/evericons/outlined";
import { CircleIcon } from "@everstage/evericons/solid";
import { AgGridReact } from "ag-grid-react";
import { debounce, isEmpty, get, isNil } from "lodash";
import {
  useMemo,
  useEffect,
  useState,
  useRef,
  useLayoutEffect,
  useCallback,
} from "react";
import { useMutation, useQuery } from "react-query";
import { useNavigate } from "react-router-dom";
import { twMerge } from "tailwind-merge";

import { useAuthStore } from "~/GlobalStores/AuthStore";
import { getLocalizedCurrencyValue } from "~/Utils/CurrencyUtils";
import {
  EverButton,
  EverInput,
  EverInteractiveChip,
  EverLoader,
  EverTg,
  showToastMessage,
} from "~/v2/components";
import {
  everAgGridOptions,
  everAgGridCallbacks,
} from "~/v2/components/ag-grid";
import { useViewport } from "~/v2/components/custom-hooks";
import { StatusBadge } from "~/v2/features/cpq/components";

import CreateQuote from "./components/CreateQuote";
import CreateQuoteModal from "./components/CreateQuoteModal";
import NoQuotesFound from "./components/NoQuoteFound";
import ExpiresOnCellRenderer from "./components/table-cell/ExpiresOnCellRenderer";
import MenuButton from "./components/table-cell/MenuButton";
import OpportunityCellRenderer from "./components/table-cell/OpportunityCellRenderer";
import QuoteNameCellRenderer from "./components/table-cell/QuoteNameCellRenderer";
import QuoteValueCellRenderer from "./components/table-cell/QuoteValueCellRenderer";
import {
  getQuoteList,
  getQuoteListCount,
  createQuote,
  markQuoteAsPrimary,
  deleteQuote,
  getAccountDetails,
} from "./services/restApi";
import { QUOTE_LIST_TOAST_MESSAGES, STATUS_OPTIONS } from "./utils";
import {
  AgGridLoaderTable,
  SkeletonCellHeader,
  DoubleSkeletonCell,
  SkeletonWithAvatar,
  SingleSkeletonCell,
} from "../../components/ag-grid/AgGridLoaderTable";
import OwnerCellRenderer from "../../components/ag-grid/OwnerCellRenderer";
import CustomDropDown from "../../components/CustomDropDown";
import { snakeToCamel } from "../../price-book/utils";
import "./styles.scss";

const PAGE_SIZE = 20;
const QuotesList = () => {
  const navigate = useNavigate();
  const { accessToken, userMetadata } = useAuthStore();
  const tableRef = useRef(null);
  const { width } = useViewport(0);
  const IS_IMPERSONATED = !isEmpty(userMetadata);
  const SCREEN_SIZE_THRESHOLD = 1280;
  const isLargeScreen = width > 1440; // greater than 1440px is considered large screen

  const [searchQuery, setSearchQuery] = useState("");
  const [searchInput, setSearchInput] = useState("");
  const [statusFilter, setStatusFilter] = useState([]);
  const [ownerFilter, setOwnerFilter] = useState([]);
  const [accountFilter, setAccountFilter] = useState([]);
  const [statusOptions, setStatusOptions] = useState([]);
  const [ownerOptions, setOwnerOptions] = useState([]);
  const [accountOptions, setAccountOptions] = useState([]);
  const [isCreateQuoteModalOpen, setIsCreateQuoteModalOpen] = useState(false);
  const [gridApi, setGridApi] = useState(null);
  const [isApprovalsWaitingOnYouActive, setIsApprovalsWaitingOnYouActive] =
    useState(false);
  const [pendingApprovalsCount, setPendingApprovalsCount] = useState(0);
  const [isQuotesDataEmpty, setIsQuotesDataEmpty] = useState(false);
  const [isDataSourceLoading, setIsDataSourceLoading] = useState(false);
  const [associatedForm, setAssociatedForm] = useState(null);

  const { isFetching } = useQuery(
    ["getAccountDetails"],
    () => getAccountDetails(accessToken),
    {
      retry: false,
      cacheTime: 0,
      refetchOnWindowFocus: false,
      onSuccess: (data) => {
        const ownerDetailsOption = Object.entries(data.employee_details).map(
          ([key, value]) => ({
            label: value.full_name,
            value: key,
          })
        );

        const accountDetailsOption = data.account_details.map((account) => ({
          label: account.account_name,
          value: account.account_id,
        }));

        const statusCount = {};
        data.quote_status.forEach((quoteStatus) => {
          statusCount[quoteStatus.status] = quoteStatus.count;
        });
        const newStatusOptions = STATUS_OPTIONS.map((option) => ({
          ...option,
          count: statusCount[option.value] || 0,
        }));

        setOwnerOptions(ownerDetailsOption);
        setAccountOptions(accountDetailsOption);
        setStatusOptions(newStatusOptions);
      },
    }
  );

  const { isFetching: isFetchingCount } = useQuery(
    ["getQuoteListCount"],
    () => getQuoteListCount(accessToken),
    {
      retry: false,
      cacheTime: 0,
      refetchOnWindowFocus: false,
      onSuccess: (data) => {
        setPendingApprovalsCount(data.count);
      },
    }
  );

  const updateOverlayState = useCallback(
    (state) => {
      if (!gridApi || !gridApi.api) return;

      const loading = state === "SHOW_LOADING_OVERLAY";

      gridApi.api.setGridOption("loading", loading);

      switch (state) {
        case "SHOW_LOADING_OVERLAY": {
          gridApi.api.hideOverlay();
          break;
        }

        case "SHOW_NO_ROWS_OVERLAY": {
          gridApi.api.showNoRowsOverlay();
          break;
        }

        case "HIDE_OVERLAY": {
          gridApi.api.hideOverlay();
          break;
        }

        default: {
          break;
        }
      }
    },
    [gridApi]
  );

  const onFetchSettled = useCallback(() => {
    setIsDataSourceLoading(false);
    gridApi.api.sizeColumnsToFit();
  }, [gridApi]);

  const getDatasource = useCallback(
    (payload = {}) => ({
      rowCount: undefined,
      getRows: async (params) => {
        const {
          search_term,
          owner_id,
          account_id,
          status,
          approvals_waiting_on_you,
        } = payload;

        setIsDataSourceLoading(true);
        updateOverlayState("SHOW_LOADING_OVERLAY");
        try {
          const fetchResponse = await getQuoteList(accessToken, {
            limit_value: PAGE_SIZE,
            offset_value: params.startRow,
            search_term,
            owner_id,
            account_id,
            status,
            approvals_waiting_on_you,
          });

          const dataCount = get(fetchResponse, "quotes", []).length;
          if (dataCount === 0 && params.startRow === 0) {
            updateOverlayState("SHOW_NO_ROWS_OVERLAY");
          } else {
            updateOverlayState("HIDE_OVERLAY");
            setIsDataSourceLoading(false);
          }

          let lastRow = undefined;
          if (dataCount < PAGE_SIZE) {
            lastRow = dataCount + params.startRow;
          }

          setIsQuotesDataEmpty(dataCount === 0);
          params.successCallback(
            snakeToCamel(fetchResponse.quotes || []),
            lastRow
          );
        } catch (error) {
          console.error(error);
          params.failCallback();
          updateOverlayState("HIDE_OVERLAY");
          setIsDataSourceLoading(false);
        } finally {
          onFetchSettled();
        }
      },
    }),
    [
      PAGE_SIZE,
      accessToken,
      updateOverlayState,
      onFetchSettled,
      setIsQuotesDataEmpty,
    ]
  );

  const fetchQuoteListDatasource = useCallback(() => {
    gridApi?.api?.setGridOption(
      "datasource",
      getDatasource({
        search_term: searchQuery.length > 2 ? searchQuery : "",
        owner_id: ownerFilter,
        account_id: accountFilter,
        status: statusFilter,
        approvals_waiting_on_you: isApprovalsWaitingOnYouActive,
      })
    );
  }, [
    gridApi,
    getDatasource,
    ownerFilter,
    accountFilter,
    statusFilter,
    searchQuery,
    isApprovalsWaitingOnYouActive,
  ]);

  const executeMarkQuoteAsPrimary = useMutation(
    (payload) => markQuoteAsPrimary(accessToken, payload),
    {
      onSuccess: () => {
        fetchQuoteListDatasource();
      },
    }
  );

  const handleMarkQuoteAsPrimary = (quoteId) => {
    executeMarkQuoteAsPrimary.mutate({ quote_id: quoteId });
  };

  const executeDeleteQuote = useMutation(
    (payload) => deleteQuote(accessToken, payload),
    {
      onSuccess: () => {
        fetchQuoteListDatasource();
      },
    }
  );

  const handleDeleteQuote = (quoteId) => {
    const deleteQuotePromise = executeDeleteQuote.mutateAsync({
      quote_id: quoteId,
    });
    showToastMessage(deleteQuotePromise, {
      messages: QUOTE_LIST_TOAST_MESSAGES.DELETE,
    });
  };

  const executeCreateQuote = useMutation(
    (payload) => createQuote(accessToken, payload),
    {
      onSuccess: (data) => {
        navigate(data.form_spec.sections.section1.fields.field1.value);
      },
    }
  );

  const handleCreateQuote = (payload) => {
    const fullPayload = {
      quote_id: payload.quote_id,
      request_type: payload.request_type,
      form_builder_id: associatedForm, // will be needed when we have multiple form builders
    };
    const createQuotePromise = executeCreateQuote.mutateAsync(fullPayload);
    showToastMessage(createQuotePromise, {
      messages: QUOTE_LIST_TOAST_MESSAGES[payload.request_type.toUpperCase()],
    });
  };

  const debouncedSearch = useMemo(
    () =>
      debounce((query) => {
        if (
          query.length >= 3 ||
          (searchQuery.length >= 3 && query.length < 3)
        ) {
          setSearchQuery(query);
        }
      }, 500),
    [searchQuery]
  );

  const handleSearchChange = useCallback(
    (event) => {
      const query = event.target.value;
      const trimmedQuery = query.trim();

      setSearchInput(query);
      debouncedSearch(trimmedQuery);
    },
    [debouncedSearch]
  );

  useEffect(() => {
    if (gridApi) {
      fetchQuoteListDatasource();
    }
  }, [fetchQuoteListDatasource, gridApi]);

  const rowClassRules = useMemo(
    () => ({
      "more-menu-button-active": (params) => params.node?.data?.isHovered,
    }),
    []
  );

  const adjustDomLayout = useCallback(() => {
    setTimeout(() => {
      if (gridApi?.api && tableRef.current) {
        const DEFAULT_HEADER_HEIGHT = 48;
        const DEFAULT_ROW_HEIGHT = 64;
        const VIEWPORT_HEIGHT = window.innerHeight;
        const TABLE_OFFSET_HEIGHT = IS_IMPERSONATED ? 172 : 112;

        const bodyHeight =
          VIEWPORT_HEIGHT - (TABLE_OFFSET_HEIGHT + DEFAULT_HEADER_HEIGHT);
        const displayedRowCount = gridApi.api.getDisplayedRowCount();
        const contentHeight = displayedRowCount * DEFAULT_ROW_HEIGHT;
        const domLayout =
          contentHeight / bodyHeight >= 1 || displayedRowCount === 0
            ? "normal"
            : "autoHeight";
        gridApi.api.setGridOption("domLayout", domLayout);
      }
    }, 100);
  }, [gridApi]);

  const adjustGridColumnType = useCallback(
    (gridApi) => {
      if (window.innerWidth > SCREEN_SIZE_THRESHOLD) {
        gridApi.sizeColumnsToFit();
      } else {
        gridApi.autoSizeAllColumns();
      }
    },
    [SCREEN_SIZE_THRESHOLD]
  );

  useLayoutEffect(() => {
    adjustDomLayout();
    const handleResize = () => {
      adjustDomLayout();
      if (gridApi?.api) {
        adjustGridColumnType(gridApi.api);
      }
    };
    window.addEventListener("resize", handleResize);
    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, [adjustDomLayout, gridApi, adjustGridColumnType]);

  const onGridReady = useCallback(
    (params) => {
      setGridApi(params);

      setTimeout(() => {
        adjustGridColumnType(params.api);
      }, 100);

      const resizeHandler = () => adjustGridColumnType(params.api);
      window.addEventListener("resize", resizeHandler);

      return () => {
        window.removeEventListener("resize", resizeHandler);
      };
    },
    [adjustGridColumnType]
  );

  const isFilterApplied = () =>
    statusFilter.length > 0 ||
    ownerFilter.length > 0 ||
    accountFilter.length > 0 ||
    searchQuery.length > 0;

  const handleCreateQuoteCTA = () => {
    if (isQuotesDataEmpty) {
      navigate("new");
    } else {
      setIsCreateQuoteModalOpen(true);
    }
  };

  const defaultColumnDefs = {
    flex: 1,
    suppressHeaderMenuButton: true,
    sortable: true,
    headerClass: "!text-ever-base-content-mid !uppercase !text-xs",
  };

  const skeletonColumnDefs = [
    {
      field: "0",
      minWidth: 340,
      headerComponent: SkeletonCellHeader,
      cellRenderer: () => {
        return (
          <div className="flex flex-col gap-2">
            <EverLoader.Skeleton
              config={[2]}
              className="h-3.5 !p-0 !pl-1 !w-64"
            />
            <div className="flex flex-row items-center gap-2.5">
              <EverLoader.Skeleton
                config={[2]}
                className="h-3.5 !p-0 !pl-1 !w-16"
              />
              <CircleIcon className="w-1.5 h-1.5 text-ever-base-200" />
              <EverLoader.Skeleton
                config={[2]}
                className="h-3.5 !p-0 !pl-1 !w-16"
              />
            </div>
          </div>
        );
      },
    },
    {
      field: "1",
      headerComponent: SkeletonCellHeader,
      minWidth: 216,
      cellRenderer: DoubleSkeletonCell,
    },
    {
      field: "2",
      headerComponent: SkeletonCellHeader,
      minWidth: 216,
      type: "rightAligned",
      cellRenderer: DoubleSkeletonCell,
      cellRendererParams: { rightAligned: true },
    },
    {
      field: "3",
      headerComponent: SkeletonCellHeader,
      cellRenderer: SingleSkeletonCell,
    },
    {
      field: "4",
      headerComponent: SkeletonCellHeader,
      cellRenderer: DoubleSkeletonCell,
    },
    {
      field: "5",
      headerComponent: SkeletonCellHeader,
      cellRenderer: SkeletonWithAvatar,
    },
  ];

  const columnDefs = [
    {
      field: "quoteName",
      headerName: "Name",
      minWidth: 300,
      maxWidth: isLargeScreen ? 600 : 300,
      flex: 2,
      cellRenderer: QuoteNameCellRenderer,
      cellRendererParams: (params) => ({
        quoteName: params?.data?.quoteName,
        quoteId: params?.data?.quoteId,
        quoteDisplayId: params?.data?.quoteDisplayId,
        productCount: params?.data?.productCount,
        quotePrimary: params?.data?.isPrimary,
      }),
    },
    {
      field: "netQuoteTotal",
      headerName: "Value",
      minWidth: 120,
      maxWidth: 120,
      type: "rightAligned",
      headerClass:
        "!text-ever-base-content-mid !uppercase !text-xs !text-right",
      cellStyle: () => {
        return { display: "flex", justifyContent: "right" };
      },
      cellRenderer: QuoteValueCellRenderer,
      cellRendererParams: (params) => ({
        quoteTotal: isNil(params?.data?.netQuoteTotal)
          ? null
          : getLocalizedCurrencyValue(
              params?.data?.netQuoteTotal,
              "$",
              "en-US",
              2
            ),
        quoteCurrency: params?.data?.quoteCurrency,
        quoteDurationValue: params?.data?.durationValue,
        quoteDurationType: params?.data?.durationType,
      }),
    },
    {
      field: "opportunityName",
      headerName: "Opportunity",
      minWidth: 250,
      maxWidth: isLargeScreen ? 500 : 250,
      flex: 2,
      cellRenderer: OpportunityCellRenderer,
      cellRendererParams: (params) => ({
        opportunityName: params?.data?.opportunityName,
        opportunityLink: params?.data?.opportunityLink,
        accountName: params?.data?.accountName,
      }),
    },
    {
      field: "status",
      headerName: "Status",
      cellRenderer: StatusBadge,
      cellRendererParams: (params) => ({
        status: params?.data?.status,
        statusInstanceData: params?.data?.statusInstanceData,
      }),
      minWidth: 250,
    },
    {
      field: "validTill",
      headerName: "Expires on",
      cellRenderer: ExpiresOnCellRenderer,
      cellRendererParams: (params) => ({
        validTill: params?.data?.validTill,
      }),
    },
    {
      field: "ownerId",
      headerName: "Owner",
      cellRenderer: OwnerCellRenderer,
      cellRendererParams: (params) => ({
        ownerId: params?.data?.ownerId,
      }),
    },
    {
      field: "moreButton",
      headerName: "",
      minWidth: 80,
      maxWidth: 80,
      cellClass: "more-menu-button invisible opacity-0",
      cellRenderer: MenuButton,
      cellRendererParams: (params) => ({
        quoteId: params?.data?.quoteId,
        quotePrimary: params?.data?.isPrimary,
        formBuilderId: params?.data?.formBuilderId,
        opportunityId: params?.data?.opportunityId,
        createQuote: handleCreateQuote,
        markQuoteAsPrimary: handleMarkQuoteAsPrimary,
        deleteQuote: handleDeleteQuote,
        setIsHovered: (visible) => {
          params.api.applyTransaction({
            update: [{ ...params.data, isHovered: visible }],
          });
        },
      }),
    },
  ];

  return (
    <div className="flex flex-col h-full w-full">
      <EverLoader
        spinning={
          executeDeleteQuote.isLoading || executeMarkQuoteAsPrimary.isLoading
        }
        className="flex flex-col"
      >
        <div className="flex py-2 px-6 items-center justify-between">
          <div>
            <EverInput.Search
              className="w-80"
              placeholder="Search"
              value={searchInput}
              onChange={handleSearchChange}
              size="small"
            />
          </div>
          <div className="flex gap-3">
            {!isFetchingCount &&
            pendingApprovalsCount > 0 &&
            !isQuotesDataEmpty ? (
              <EverInteractiveChip
                isSelected={isApprovalsWaitingOnYouActive}
                size="small"
                className="h-8"
                title={"Approvals waiting on you"}
                disabled={isQuotesDataEmpty}
                append={
                  <EverTg.SubHeading4 className="text-ever-base-content">
                    {pendingApprovalsCount}
                  </EverTg.SubHeading4>
                }
                prepend={
                  <ClockFastForwardIcon className="!w-[18px] !h-[18px] text-ever-error shrink-0" />
                }
                onClick={() => {
                  setIsApprovalsWaitingOnYouActive(
                    !isApprovalsWaitingOnYouActive
                  );
                  setOwnerFilter([]);
                  setAccountFilter([]);
                  setStatusFilter([]);
                }}
              />
            ) : null}
            <CustomDropDown
              multi
              statusCount
              setHandlerState={setStatusFilter}
              options={statusOptions}
              onItemClick={() => {
                setIsApprovalsWaitingOnYouActive(false);
              }}
            >
              <EverButton
                size="small"
                type="ghost"
                color="base"
                disabled={isQuotesDataEmpty}
                prependIcon={
                  <Server01Icon className="w-4 h-4 text-ever-base-content-mid shrink-0" />
                }
                appendIcon={
                  <ChevronDownIcon className="w-4 h-4 text-ever-base-content-mid" />
                }
              >
                <EverTg.Text className="text-base-content font-normal">
                  Status
                </EverTg.Text>
              </EverButton>
            </CustomDropDown>
            <CustomDropDown
              search
              multi
              avatar
              setHandlerState={setOwnerFilter}
              searchPlaceholder="Search Owners"
              options={ownerOptions}
              selectedState={ownerFilter}
              onItemClick={() => {
                setIsApprovalsWaitingOnYouActive(false);
              }}
            >
              <EverButton
                size="small"
                type="ghost"
                color="base"
                disabled={isQuotesDataEmpty}
                prependIcon={
                  <Users02Icon className="!w-[18px] !h-[18px] text-ever-base-content-mid shrink-0" />
                }
                appendIcon={
                  <ChevronDownIcon className="w-4 h-4 text-ever-base-content-mid" />
                }
              >
                <EverTg.Text className="text-base-content font-normal">
                  Owners
                </EverTg.Text>
              </EverButton>
            </CustomDropDown>
            <CustomDropDown
              search
              multi
              setHandlerState={setAccountFilter}
              searchPlaceholder="Search Accounts"
              options={accountOptions}
              onItemClick={() => {
                setIsApprovalsWaitingOnYouActive(false);
              }}
            >
              <EverButton
                size="small"
                type="ghost"
                color="base"
                disabled={isQuotesDataEmpty}
                prependIcon={
                  <Building07Icon className="!w-[18px] !h-[18px] text-ever-base-content-mid shrink-0" />
                }
                appendIcon={
                  <ChevronDownIcon className="w-4 h-4 text-ever-base-content-mid" />
                }
              >
                <EverTg.Text className="text-base-content font-normal">
                  Account
                </EverTg.Text>
              </EverButton>
            </CustomDropDown>
            {!isQuotesDataEmpty && (
              <div>
                <EverButton
                  size="small"
                  prependIcon={<PlusCircleIcon className="w-4 h-4" />}
                  onClick={handleCreateQuoteCTA}
                  loading={executeCreateQuote.isLoading}
                >
                  Create
                </EverButton>
                <CreateQuoteModal
                  visible={isCreateQuoteModalOpen}
                  onClose={() => setIsCreateQuoteModalOpen(false)}
                  handleCreateQuote={handleCreateQuote}
                  isLoading={executeCreateQuote.isLoading}
                  associatedForm={associatedForm}
                  setAssociatedForm={setAssociatedForm}
                />
              </div>
            )}
          </div>
        </div>
        <div
          ref={tableRef}
          className="w-full h-full pt-4 pb-3.5 px-6 overflow-hidden"
        >
          {isDataSourceLoading || isFetching ? (
            <AgGridLoaderTable
              className="quote-list-table"
              columnDefs={skeletonColumnDefs}
              rowCount={4}
            />
          ) : isQuotesDataEmpty && isFilterApplied() ? (
            <NoQuotesFound />
          ) : isQuotesDataEmpty ? (
            <div>
              <CreateQuote
                setIsCreateQuoteModalOpen={setIsCreateQuoteModalOpen}
                isLoading={executeCreateQuote.isLoading}
              />
              <CreateQuoteModal
                visible={isCreateQuoteModalOpen}
                onClose={() => setIsCreateQuoteModalOpen(false)}
                handleCreateQuote={handleCreateQuote}
                isLoading={executeCreateQuote.isLoading}
                associatedForm={associatedForm}
                setAssociatedForm={setAssociatedForm}
              />
            </div>
          ) : null}
          <div
            className={twMerge(
              "ag-theme-material w-full h-full overflow-y-auto quote-list-table",
              isQuotesDataEmpty && "hidden"
            )}
          >
            <AgGridReact
              {...everAgGridOptions.getDefaultOptions()}
              domLayout="normal"
              getRowId={(params) => params.data.quoteId}
              defaultColDef={defaultColumnDefs}
              columnDefs={columnDefs}
              rowClassRules={rowClassRules}
              suppressContextMenu={true}
              rowModelType="infinite"
              maxConcurrentDatasourceRequests={1}
              debounceVerticalScrollbar={false}
              enableRangeSelection={false}
              suppressCellFocus={true}
              suppressColumnVirtualisation={true}
              suppressRowClickSelection={true}
              rowBuffer={20}
              onCellValueChanged={everAgGridCallbacks.onCellValueChangedStyled}
              onGridReady={onGridReady}
              onFirstDataRendered={(params) => {
                adjustDomLayout();
                setTimeout(() => adjustGridColumnType(params.api), 100);
              }}
              onModelUpdated={adjustDomLayout}
              onGridSizeChanged={(params) => {
                setTimeout(() => adjustGridColumnType(params.api), 100);
              }}
              onColumnResized={(params) => adjustGridColumnType(params.api)}
              suppressBrowserResizeObserver={true}
              suppressMovableColumns={true}
              suppressRowDrag={true}
              pagination={false}
            />
          </div>
        </div>
      </EverLoader>
    </div>
  );
};

export default QuotesList;
