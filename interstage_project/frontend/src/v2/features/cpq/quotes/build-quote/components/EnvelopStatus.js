import { ClockIcon } from "@everstage/evericons/outlined";
import { isNil } from "lodash";
import React from "react";
import { twMerge } from "tailwind-merge";

import { EverDivider, EverTg } from "~/v2/components";

import { TruncatedText } from "../../../components";

export function EnvelopStatus({ title, pendingDays, actionButton, timeline }) {
  return (
    <div className="flex flex-col w-[432px] rounded-lg gap-6 bg-ever-base-25 pb-2">
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <EverTg.Heading4 className="whitespace-nowrap">
            {title}
          </EverTg.Heading4>
          {!isNil(pendingDays) && (
            <div className="flex items-center">
              <EverDivider type="vertical" className="mx-2" />
              <ClockIcon className="text-ever-base-content-low mr-1.5 w-4 h-4" />
              <EverTg.Text className="text-ever-base-content-mid">
                {pendingDays} {pendingDays === 1 ? "day" : "days"} pending
              </EverTg.Text>
            </div>
          )}
        </div>
        <div className="flex justify-end">{actionButton}</div>
      </div>
      {timeline && (
        <div className="flex flex-col pl-2 w-full">
          {timeline.map((item, index) => (
            <div key={item.id} className="flex gap-3 w-full">
              <div className="flex flex-col items-center mt-[2px]">
                <div className="rounded-full bg-ever-primary border border-solid border-ever-base-25 w-4 h-4" />
                {index !== timeline.length - 1 && (
                  <div className="w-0.5 flex-1 bg-ever-primary" />
                )}
              </div>
              <div
                className={twMerge(
                  "flex flex-col gap-1 w-full",
                  index !== timeline.length - 1 && "mb-6"
                )}
              >
                <div className="flex gap-2 justify-between w-full">
                  <div className="flex-1 max-w-64">
                    <TruncatedText text={item.name}>
                      <EverTg.SubHeading4 className="text-ever-base-content">
                        {item.name}
                      </EverTg.SubHeading4>
                    </TruncatedText>
                  </div>
                  <div className="px-1.5 bg-ever-info-lite shadow-sm rounded-sm">
                    <EverTg.Caption className="text-ever-info whitespace-nowrap">
                      {item.signingPreference}
                    </EverTg.Caption>
                  </div>
                </div>
                <div>
                  <TruncatedText text={item.email} className="w-[300px]">
                    <EverTg.Text className="text-ever-base-content-mid">
                      {item.email}
                    </EverTg.Text>
                  </TruncatedText>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
