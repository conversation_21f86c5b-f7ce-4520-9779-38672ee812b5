/* eslint-disable no-restricted-imports */
import { LoadingOutlined } from "@ant-design/icons";
import { Progress } from "antd";
import {
  intersection,
  cloneDeep,
  flatten,
  isEqual,
  debounce,
  isNil,
} from "lodash";
import { useEffect, useMemo, useState, useRef, useCallback } from "react";
import { useFormContext } from "react-hook-form";
import { twMerge } from "tailwind-merge";

import { usePrevious } from "~/Utils/DevTools";
import { EverCard, EverGroupAvatar, EverTg } from "~/v2/components";
import { PROGRESS_BG } from "~/v2/features/cpq/quotes/build-quote/constants";
import {
  useBuildQuote,
  useFormValues,
} from "~/v2/features/cpq/quotes/build-quote/hooks";
import { getPercent } from "~/v2/features/cpq/quotes/build-quote/utils";

import Section from "./Section";
import ApprovalsModal from "../approvals/ApprovalsModal";
import { QuotePotentialCommission } from "../QuotePotentialCommission";

const Sections = ({ initialLoad }) => {
  const progressInterval = useRef(null);
  const {
    formState: { errors },
  } = useFormContext();
  const {
    evaluateRule,
    activeSectionKey,
    approvalCycles,
    isApprovalsLoading,
    formSpec: { sections },
    visibleSectionOrder,
    activeSectionStep,
    totalSteps,
  } = useBuildQuote();

  const formValues = useFormValues();
  const prevErrors = usePrevious(cloneDeep(errors));
  const isErrorsChanged = !isEqual(errors, prevErrors);

  const [progressPercent, setProgressPercent] = useState(0);
  const [sectionProgress, setSectionProgress] = useState({});
  const [showApprovalsModal, setShowApprovalsModal] = useState(false);
  const [approvers, setApprovers] = useState([]);

  const approvalRulesLoading = evaluateRule.isLoading || isApprovalsLoading;

  const { fieldIds, flatFieldIds } = useMemo(() => {
    const tempFieldIds = {};
    for (const sectionKey of visibleSectionOrder) {
      const section = sections[sectionKey];
      tempFieldIds[sectionKey] = getFlatFieldIds(section);
    }
    return {
      fieldIds: tempFieldIds,
      flatFieldIds: flatten(Object.values(tempFieldIds)),
    };
  }, [visibleSectionOrder, sections]);

  const errorKeys = useMemo(
    () => intersection(Object.keys(errors), flatFieldIds),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [isErrorsChanged, flatFieldIds]
  );

  const updateProgress = useCallback(
    (progress) => {
      clearInterval(progressInterval.current);
      const { overallProgress, sectionProgress } = progress;
      const targetPercent = getPercent(
        overallProgress.completedCount,
        overallProgress.totalCount
      );

      setSectionProgress(sectionProgress);
      progressInterval.current = setInterval(() => {
        setProgressPercent((prevPercent) => {
          if (prevPercent === targetPercent) {
            clearInterval(progressInterval.current);
            return prevPercent;
          }

          return targetPercent > prevPercent
            ? Math.min(prevPercent + 1, targetPercent)
            : Math.max(prevPercent - 1, targetPercent);
        });
      }, 10);
    },
    [setProgressPercent]
  );

  const debouncedUpdate = useMemo(
    () => debounce(updateProgress, 100),
    [updateProgress]
  );

  useEffect(() => {
    let overallCompletedCount = 0;
    const sectionProgress = {};

    for (const sectionKey of visibleSectionOrder) {
      const ids = fieldIds[sectionKey];
      const completedCount = getCompletedFieldCount(ids, formValues, errorKeys);
      overallCompletedCount += completedCount;
      sectionProgress[sectionKey] = {
        completedCount: completedCount,
        totalCount: ids.length,
      };
    }

    const progress = {
      overallProgress: {
        completedCount: overallCompletedCount,
        totalCount: flatFieldIds.length,
      },
      sectionProgress,
    };

    debouncedUpdate(progress);

    return () => {
      debouncedUpdate.cancel();
    };
  }, [
    fieldIds,
    flatFieldIds,
    formValues,
    errorKeys,
    debouncedUpdate,
    visibleSectionOrder,
  ]);

  useEffect(() => {
    if (approvalCycles && approvalCycles.length > 0) {
      const allApprovers = approvalCycles[0].stages.flatMap((item) =>
        Object.keys(item.approver_details).map((key) => ({
          approver: key,
          firstName: item.approver_details[key][0].first_name,
          lastName: item.approver_details[key][0].last_name,
          approverPicture: item.approver_details[key][0]?.profile_picture,
        }))
      );

      const uniqueApprovers = allApprovers.filter(
        (approver, index, self) =>
          index ===
          self.findIndex(
            (a) =>
              a.approver === approver.approver &&
              a.firstName === approver.firstName &&
              a.lastName === approver.lastName
          )
      );

      setApprovers(uniqueApprovers);
    }
  }, [approvalCycles]);

  const formattedUsers = approvers.map((user) => ({
    firstName: user.firstName,
    lastName: user.lastName,
    image: user.approverPicture,
    className: "w-7 h-7 !-ml-1",
  }));

  const closeModal = () => {
    setShowApprovalsModal(false);
  };

  const handleApprovalsClick = () => {
    if (approvalRulesLoading) {
      return;
    }
    setShowApprovalsModal(true);
  };

  return (
    <div className="flex flex-col ml-6 pb-8 shrink-0 w-72 h-full z-[1010] overflow-auto border border-solid border-ever-base-200 bg-ever-base-50 rounded-xl whitespace-nowrap items-center">
      <div className="flex gap-2 p-6 items-center">
        <Progress
          type="dashboard"
          strokeColor={PROGRESS_BG}
          // TODO: Change the color which is not available in design system
          trailColor={"#" + "e7f7ff"}
          percent={progressPercent}
          gapDegree={45}
          width={48}
          strokeWidth={12}
          className="p-2"
          format={() => "🛠️"}
        />
        <div className="flex flex-col gap-1.5">
          <div className="text-ever-base-content-low text-xs font-medium">
            Steps {activeSectionStep} of {totalSteps}
          </div>
          <div>
            <span className="text-ever-base-content text-lg	font-medium">
              {progressPercent}%
            </span>
            <span className="text-ever-base-content-mid text-xs ml-1.5">
              {getProgressLabel(progressPercent).label}
            </span>
            <span className="ml-1 text-lg">
              {getProgressLabel(progressPercent).emoji}
            </span>
          </div>
        </div>
      </div>
      <div className="flex flex-col gap-6 p-6 overflow-auto">
        {visibleSectionOrder.map((sectionKey) => (
          <Section
            key={sectionKey}
            initialLoad={initialLoad}
            section={sections[sectionKey]}
            isActive={activeSectionKey === sectionKey}
            sectionProgress={sectionProgress[sectionKey]}
          />
        ))}
      </div>
      <div className="flex flex-col justify-center mt-auto w-full">
        <div className="flex w-full justify-center mb-8 p-4">
          <QuotePotentialCommission />
        </div>
        <div className="flex w-full justify-center">
          {((approvalCycles && approvalCycles.length > 0) ||
            approvalRulesLoading) && (
            <div
              className={twMerge(
                approvalRulesLoading ? "cursor-not-allowed " : "cursor-pointer"
              )}
              onClick={handleApprovalsClick}
            >
              <EverCard
                className={twMerge(
                  "flex w-fit h-10 justify-center items-center border-0 py-1 px-3.5 gap-2 rounded-md",
                  approvalRulesLoading
                    ? "bg-ever-base-100"
                    : "bg-ever-base shadow-[0px_1px_2px_0px_rgba(0,23,128,0.06),0px_1px_3px_0px_rgba(0,23,128,0.10)]"
                )}
              >
                <EverTg.Text className="text-ever-base-content-mid">
                  Needs approval from
                </EverTg.Text>
                {approvalRulesLoading ? (
                  <LoadingOutlined className="text-ever-base-content-low [&>svg]:!w-[20px] [&>svg]:!h-[20px]" />
                ) : (
                  <EverGroupAvatar
                    avatars={formattedUsers}
                    groupMaxCount={3}
                    limitInPopover={10}
                  />
                )}
              </EverCard>
            </div>
          )}
        </div>
      </div>
      {showApprovalsModal && (
        <ApprovalsModal
          approversData={approvalCycles}
          closeModal={closeModal}
          toPublish={false}
        />
      )}
    </div>
  );
};

export default Sections;

function getProgressLabel(progressPercent) {
  const thresholds = [
    { level: 0, label: "Let's get started", emoji: "" },
    { level: 49, label: "Making good progress", emoji: "" },
    { level: 99, label: "You're almost there", emoji: "🏁" },
    { level: 100, label: "A winning quote", emoji: "💰" },
  ];

  return thresholds.find((threshold) => progressPercent <= threshold.level);
}

function getFlatFieldIds(section) {
  const fieldIds = [];

  for (const f of Object.values(section.fields)) {
    if (!f.properties.is_hidden) {
      fieldIds.push(f.id);
    }
  }

  for (const subSection of Object.values(section?.sub_sections ?? {})) {
    if (!subSection.is_hidden) {
      for (const f of Object.values(subSection.fields)) {
        if (!f.properties.is_hidden) {
          fieldIds.push(f.id);
        }
      }
    }
  }

  return fieldIds;
}

function getCompletedFieldCount(fieldIds, values, errorKeys) {
  return fieldIds.filter((fieldId) => {
    const val = values[fieldId];
    if (isNil(val) || errorKeys.includes(fieldId)) return false;
    return Array.isArray(val) ? val.length > 0 : val !== "";
  }).length;
}
