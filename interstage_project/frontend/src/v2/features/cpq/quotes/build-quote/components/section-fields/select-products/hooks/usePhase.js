import { isNil, isEqual } from "lodash";
import { useState, useCallback, useEffect, useRef, useMemo } from "react";
import { useFormContext } from "react-hook-form";
import { useMutation } from "react-query";

import { useAuthStore } from "~/GlobalStores/AuthStore";
import { createHookProvider } from "~/v2/features/cpq/components";
import { useBuildQuote } from "~/v2/features/cpq/quotes/build-quote/hooks";
import {
  updateQuoteLineItems,
  addProductToQuote,
} from "~/v2/features/cpq/quotes/build-quote/services/restApi";

const usePhaseHook = (props) => {
  const {
    phase,
    phases,
    onUpdatePhase,
    onRemovePhase,
    onCloneProductsToPhases,
    onUpdatePricePointMap,
    setQliLoading,
    setSummary,
    setNewPhases,
  } = props;
  const { accessToken } = useAuthStore();
  const {
    isEditMode,
    quoteId,
    setIsAutoSaveLoading,
    updateLastSavedTime,
    updateTableField,
    setQuoteTotalSummary,
    evaluateRule,
    formId,
    formBuilderId,
    formSpec,
    startPolling,
  } = useBuildQuote();
  const phaseRef = useRef(null);
  const nodesInOrderBeforeDrag = useRef([]);
  const { getValues } = useFormContext();
  const [gridApi, setGridApi] = useState(null);
  const [expand, setExpand] = useState(true);
  const [rowData, setRowData] = useState([]);
  const [selectedProducts, setSelectedProducts] = useState(new Map());
  const [selectedRowForPricePoint, setSelectedRowForPricePoint] = useState({});
  const [isAddProductsModalOpen, setIsAddProductsModalOpen] = useState(false);
  const [showRemovePhaseModal, setShowRemovePhaseModal] = useState(false);
  const [updatingRows, setUpdatingRows] = useState([]);

  const addProducts = useMutation(
    (payload) => addProductToQuote(payload, accessToken),
    {
      onMutate: () => {
        setIsAutoSaveLoading(true);
      },
      onSuccess: ({ data }) => {
        const { line_item, sku_pricepoint, sub_total, summary } = data;

        evaluateRule.mutate({
          form_id: formId,
          form_builder_id: formBuilderId,
          changed_field: "quote_line_item",
          form_data: getValues(),
          form_spec: formSpec,
        });
        startPolling();
        updateTableField(true);
        updateLastSavedTime();
        setIsAddProductsModalOpen(false);

        const newPhases = [];
        const phaseIds = Object.keys(line_item);
        if (phases[0].row_data.length > 0) {
          phases.forEach((p) => {
            if (phaseIds.includes(p.phase_id)) {
              const rowData = Object.values(line_item[p.phase_id]);
              const { end_date, phase_id, phase_name, start_date } = rowData[0];
              newPhases.push({
                phase_id,
                phase_name,
                start_date,
                end_date,
                sub_total: sub_total[p.phase_id],
                row_data: [...p.row_data, ...rowData],
              });
            } else {
              newPhases.push(p);
            }
          });
        } else {
          Object.keys(line_item).forEach((key) => {
            const rowData = Object.values(line_item[key]);
            const { end_date, phase_id, phase_name, start_date } = rowData[0];
            newPhases.push({
              phase_id,
              phase_name,
              start_date,
              end_date,
              sub_total: sub_total[key],
              row_data: rowData,
            });
          });
        }

        setNewPhases(newPhases);
        onUpdatePricePointMap(sku_pricepoint);
        setSummary(summary);
        setQuoteTotalSummary(summary);
        setTimeout(() => {
          const nodes = [];
          gridApi.api.forEachNode((node) => {
            if (
              node.data.key !== "addProduct" &&
              node.data.sku === line_item.sku
            ) {
              nodes.push(node);
            }
          });
          gridApi.api.flashCells({
            rowNodes: nodes,
            flashDuration: 1000,
            fadeDuration: 750,
          });
          scrollIntoView();
        }, 100);
      },
    }
  );

  const { mutateAsync: updateQuoteLineItemsMutation } = useMutation(
    (body) => updateQuoteLineItems(body, accessToken),
    {
      onMutate: (variables) => {
        setQliLoading(variables.changed_attribute ?? true);
        setIsAutoSaveLoading(true);
      },
      onSuccess: (response, variables) => {
        const { phase_detail, summary } = response;

        updateLastSavedTime();
        onUpdatePhase(phase_detail);
        setSummary(summary);
        setQuoteTotalSummary(summary);

        if (variables.type === "remove") {
          setTimeout(() => {
            gridApi.api.refreshCells({ columns: ["sn"], force: true });
          }, 100);
        } else if (variables.changed_attribute === "pricepoint_data") {
          const sku = variables.phase_detail.row_data[0].sku;
          const node = gridApi.api.getRowNode(sku);
          gridApi.api.redrawRows({ rowNodes: [node] });
          gridApi.api.flashCells({
            rowNodes: [node],
            columns: ["list_unit_price"],
            flashDuration: 750,
            fadeDuration: 500,
          });
        } else {
          const nodes = gridApi.api.getRenderedNodes();
          gridApi.api.redrawRows({ rowNodes: nodes });
        }

        evaluateRule.mutate({
          form_id: formId,
          form_builder_id: formBuilderId,
          changed_field: "quote_line_item",
          form_data: getValues(),
          form_spec: formSpec,
        });
        startPolling();
      },
      onSettled: () => {
        setQliLoading(false);
        if (updatingRows.length > 0) {
          setUpdatingRows([]);
        }
      },
    }
  );

  useEffect(() => {
    const rowData =
      phase.row_data.length > 0
        ? isEditMode
          ? [...phase.row_data, { key: "addProduct" }]
          : [...phase.row_data]
        : [];
    setRowData(rowData);
  }, [phase.row_data, isEditMode]);

  const isMultiPhase = useMemo(() => phases.length > 1, [phases]);

  const isAllProductsSelected = useMemo(
    () => selectedProducts.size === phase.row_data.length,
    [selectedProducts, phase.row_data]
  );

  const onSelectProducts = useCallback(
    ({ e, data }) => {
      const updatedProducts = new Map(selectedProducts);

      if (e.target.checked) {
        updatedProducts.set(data.sku, data);
      } else {
        updatedProducts.delete(data.sku);
      }

      setSelectedProducts(updatedProducts);
    },
    [selectedProducts]
  );

  const onSelectAllProducts = useCallback(
    (e) => {
      if (phase.row_data.length === 0) return;

      e.stopPropagation();
      e.preventDefault();
      if (selectedProducts.size === phase.row_data.length) {
        setSelectedProducts(new Map());
      } else {
        setSelectedProducts(
          new Map(phase.row_data.map((row) => [row.sku, row]))
        );
      }
    },
    [phase.row_data, selectedProducts]
  );

  const onClearSelection = useCallback(() => {
    setSelectedProducts(new Map());
  }, []);

  const onOpenPricePointModal = useCallback((rowData) => {
    setSelectedRowForPricePoint(rowData);
  }, []);

  const onClosePricePointModal = useCallback(() => {
    setSelectedRowForPricePoint({});
  }, []);

  const onUpdatePhaseName = useCallback(
    (phaseName) => {
      updateQuoteLineItemsMutation({
        type: "update",
        quote_id: quoteId,
        changed_attribute: "phase_name",
        phase_detail: {
          ...phase,
          phase_name: phaseName,
        },
        copy_flag: false,
      });
    },
    [quoteId, phase, updateQuoteLineItemsMutation]
  );

  const onRemoveProductsFromPhase = useCallback(async () => {
    const rowsToRemove = phase.row_data.filter((row) =>
      selectedProducts.has(row.sku)
    );
    await updateQuoteLineItemsMutation({
      type: "remove",
      quote_id: quoteId,
      phase_detail: {
        ...phase,
        row_data: rowsToRemove,
      },
      copy_flag: true,
    });

    onClearSelection();
    setShowRemovePhaseModal(false);
  }, [
    quoteId,
    phase,
    selectedProducts,
    onClearSelection,
    setShowRemovePhaseModal,
    updateQuoteLineItemsMutation,
  ]);

  const onRemoveAllProductsFromPhase = useCallback(async () => {
    await onRemovePhase();
    onClearSelection();
    setShowRemovePhaseModal(false);
  }, [onRemovePhase, onClearSelection, setShowRemovePhaseModal]);

  const onUpdateProductPricePoint = useCallback(
    async (updatedRowData, copyFlag) => {
      const selectedRow = phase.row_data.find(
        (row) => row.sku === selectedRowForPricePoint.sku
      );

      await updateQuoteLineItemsMutation({
        type: "update",
        quote_id: quoteId,
        changed_attribute: "pricepoint_data",
        phase_detail: {
          ...phase,
          row_data: [
            {
              ...selectedRow,
              ...(!isNil(updatedRowData.listUnitPrice) && {
                list_unit_price: updatedRowData.listUnitPrice,
              }),
              ...(!isNil(updatedRowData.netUnitPrice) && {
                net_unit_price: updatedRowData.netUnitPrice,
              }),
              ...(!isNil(updatedRowData.discountPercent) && {
                discount_percent: updatedRowData.discountPercent,
              }),
              ...(!isNil(updatedRowData.pricePointData) && {
                pricepoint_data: updatedRowData.pricePointData,
              }),
              ...(!isNil(updatedRowData.quantity) && {
                quantity: updatedRowData.quantity,
              }),
            },
          ],
        },
        copy_flag: copyFlag,
      });
      onClosePricePointModal();
    },
    [
      quoteId,
      phase,
      selectedRowForPricePoint.sku,
      onClosePricePointModal,
      updateQuoteLineItemsMutation,
    ]
  );

  const onApplyDiscountToProducts = useCallback(
    (discount) => {
      const modifiedRows = [];

      for (const row of phase.row_data) {
        if (selectedProducts.has(row.sku)) {
          const modifiedRow = {
            ...row,
            discount_percent: isNil(row.max_discount)
              ? discount.discountValue
              : Math.min(row.max_discount, discount.discountValue),
          };
          modifiedRows.push(modifiedRow);
        }
      }

      onClearSelection();
      setUpdatingRows(modifiedRows.map((row) => row.sku));
      updateQuoteLineItemsMutation({
        type: "update",
        quote_id: quoteId,
        changed_attribute: "discount_percent",
        phase_detail: {
          ...phase,
          row_data: modifiedRows,
        },
        copy_flag: true,
        is_bulk: true,
      });
    },
    [
      quoteId,
      phase,
      selectedProducts,
      onClearSelection,
      updateQuoteLineItemsMutation,
      setUpdatingRows,
    ]
  );

  const onCloneProducts = useCallback(
    (phaseIds) => {
      onCloneProductsToPhases(phaseIds, selectedProducts);
      onClearSelection();
    },
    [selectedProducts, onCloneProductsToPhases, onClearSelection]
  );

  const onRemoveProducts = useCallback(() => {
    if (isAllProductsSelected) {
      onRemoveAllProductsFromPhase();
    } else {
      onRemoveProductsFromPhase();
    }
  }, [
    isAllProductsSelected,
    onRemoveAllProductsFromPhase,
    onRemoveProductsFromPhase,
  ]);

  const onCellValueChanged = useCallback(
    (params) => {
      const { colDef, data } = params;

      setUpdatingRows([data.sku]);
      updateQuoteLineItemsMutation({
        type: "update",
        quote_id: quoteId,
        changed_attribute: colDef.field,
        phase_detail: {
          ...phase,
          row_data: [data],
        },
        copy_flag: true,
      });
    },
    [quoteId, phase, updateQuoteLineItemsMutation, setUpdatingRows]
  );

  const handleRowOrderChange = useCallback(
    (newOrder) => {
      const currentRowOrder = phase.row_data
        .sort((a, b) => a.order - b.order)
        .map((data) => data.sku);

      if (!isEqual(newOrder.filter(Boolean), currentRowOrder)) {
        const updatedRowData = [];
        const modifiedRows = [];
        for (const [index, sku] of newOrder.entries()) {
          if (sku) {
            const product = phase.row_data.find((row) => row.sku === sku);
            const modifiedRow = {
              ...product,
              order: index,
            };
            if (product.order !== index) {
              modifiedRows.push(modifiedRow);
            }
            updatedRowData.push(modifiedRow);
          }
        }

        updateQuoteLineItemsMutation({
          type: "update",
          quote_id: quoteId,
          changed_attribute: "order",
          phase_detail: {
            ...phase,
            row_data: modifiedRows,
          },
          copy_flag: true,
        });
      }
    },
    [quoteId, phase, updateQuoteLineItemsMutation]
  );

  const onRowDragEnter = useCallback(() => {
    nodesInOrderBeforeDrag.current = rowData;
  }, [rowData]);

  const onRowDragEnd = useCallback(
    (event) => {
      const maxAllowedRow = phase.row_data.length - 1; // Max allowed row index to allow dragging
      const overIndex = event.overIndex;
      const api = event.api;

      // Revert to original row order if dragged beyond the allowed row
      if (overIndex === -1 || overIndex > maxAllowedRow) {
        setRowData([...nodesInOrderBeforeDrag.current]);
        return;
      }

      // Create a new order based on the updated row data
      const newOrder = [];
      api.forEachNode((node) => {
        newOrder.push(node.data.sku);
      });

      // Trigger row order change if new order is valid
      handleRowOrderChange(newOrder);
    },
    [handleRowOrderChange, phase.row_data.length]
  );

  const scrollIntoView = () => {
    setTimeout(() => {
      phaseRef.current?.scrollIntoView({ behavior: "smooth", block: "center" });
    }, 750);
  };

  useEffect(() => {
    if (!isMultiPhase && !expand) {
      setExpand(true);
    }
  }, [isMultiPhase, expand]);

  return {
    ...props,
    phaseRef,
    gridApi,
    expand,
    rowData,
    selectedProducts,
    selectedRowForPricePoint,
    isMultiPhase,
    isAllProductsSelected,
    isAddProductsModalOpen,
    showRemovePhaseModal,
    addProducts,
    updatingRows,
    setGridApi,
    setExpand,
    setIsAddProductsModalOpen,
    setShowRemovePhaseModal,
    onClearSelection,
    onClosePricePointModal,
    onUpdatePhaseName,
    onRemoveAllProductsFromPhase,
    onUpdateProductPricePoint,
    onApplyDiscountToProducts,
    onCloneProducts,
    onRemoveProducts,
    onCellValueChanged,
    onRowDragEnter,
    onRowDragEnd,
    onSelectProducts,
    onSelectAllProducts,
    onOpenPricePointModal,
  };
};

const { Provider: PhaseProvider, useHookContext: usePhase } =
  createHookProvider(usePhaseHook);

export { PhaseProvider, usePhase };
