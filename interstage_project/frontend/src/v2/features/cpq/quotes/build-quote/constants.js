import { Trash03Icon } from "@everstage/evericons/outlined";

import theme from "~/v2/themes/blueBias23";

const TAG_TYPE = {
  SELECT_PRODUCT: "select_product",
};

const DISCOUNT_TYPES = {
  PERCENTAGE: "percentage",
  PRICE: "price",
};

const DISCOUNT_IN_OPTIONS = [
  { value: DISCOUNT_TYPES.PERCENTAGE, label: "Percentage" },
  // { value: DISCOUNT_TYPES.PRICE, label: "Price" },
];

const DISCOUNT_RANGE = {
  MIN: 0,
  MAX: 100,
};

const PHASE_ACTIONS = {
  DELETE: "delete",
};

const PHASE_MENU_OPTIONS = [
  {
    key: PHASE_ACTIONS.DELETE,
    icon: <Trash03Icon className="w-4 h-4 text-ever-error-hover" />,
    label: "Remove",
  },
];

// TODO: Change the colors which is not available in design system
const PROGRESS_BG = {
  "0%": "#" + "A3D0FF",
  "100%": theme.colors.ever.chartColors[1],
};

const SECTION_PROGRESS_BG = {
  selected: {
    trailColor: "#" + "E1E8FD",
    strokeColor: {
      "0%": theme.colors.ever.chartColors[1],
      "100%": theme.colors.ever.chartColors[19],
    },
  },
  unselected: {
    trailColor: theme.colors.ever.base[400],
    strokeColor: {
      "0%": "#" + "414449",
      "100%": theme.colors.ever.base.content.low,
    },
  },
};

export {
  PROGRESS_BG,
  SECTION_PROGRESS_BG,
  DISCOUNT_TYPES,
  DISCOUNT_IN_OPTIONS,
  DISCOUNT_RANGE,
  PHASE_ACTIONS,
  PHASE_MENU_OPTIONS,
  TAG_TYPE,
};

// Constants for Docusign Flow

export const RECIPIENT_ACTIONS = Object.freeze({
  ADD: "add",
  UPDATE: "update",
  DELETE: "delete",
  REORDER: "reorder",
});

export const SIGNING_PREFERENCES = Object.freeze({
  NEEDS_TO_SIGN: "Needs To Sign",
  RECEIVES_A_COPY: "Receives A Copy",
  NEEDS_TO_VIEW: "Needs To View",
});

export const SIGNING_PREFERENCES_OPTIONS = Object.freeze([
  {
    label: "Needs to sign",
    value: SIGNING_PREFERENCES.NEEDS_TO_SIGN,
  },
  { label: "Receives a copy", value: SIGNING_PREFERENCES.RECEIVES_A_COPY },
  { label: "Needs to view", value: SIGNING_PREFERENCES.NEEDS_TO_VIEW },
]);

export const RECIPIENT_TYPES = Object.freeze({
  CLIENT: "client",
  SUPER_SIDE: "super_side",
});
