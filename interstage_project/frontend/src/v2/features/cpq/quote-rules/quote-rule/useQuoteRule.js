import { get, isEmpty, isNil } from "lodash";
import { useState, useMemo } from "react";
import { useQuery, useMutation } from "react-query";
import { useParams } from "react-router-dom";

import { convertKeysToCamelCase } from "~/Utils";
import { message } from "~/v2/components";
import { createHookProvider } from "~/v2/features/cpq/components";
import { useFetchApi } from "~/v2/features/cpq/hooks";

import {
  ACTIONS_TYPES,
  ACTION_OPTIONS,
  ACTION_GROUPS_BY_TYPE,
  QUOTE_LINE_ITEM_FIELDS,
} from "./constants";
import { API, API_ENDPOINTS } from "../constants";

const getGroupedActionsAndSku = (actions) => {
  if (actions.length === 0) {
    return {
      groupedActions: [
        {
          condition: [],
          actionsToPerform: [],
        },
      ],
      productSKUs: new Set(),
    };
  }

  const productSKUs = new Set();
  const groupedActions = actions.map((action) => {
    // Build a lookup map from function name -> array of action objects

    const thenActionByGroup = {};
    for (const thenAction of action.then) {
      const { function: fnName } = thenAction;
      Object.entries(ACTION_GROUPS_BY_TYPE).forEach(
        ([groupType, functionNames]) => {
          if (functionNames.includes(fnName)) {
            // Filter actions based on form component type if required
            if (
              groupType === ACTIONS_TYPES.UPDATE_QUOTE_FIELD &&
              thenAction.form_component !== "field"
            ) {
              return;
            }
            if (
              groupType === ACTIONS_TYPES.UPDATE_QUOTE_SECTION &&
              thenAction.form_component !== "section"
            ) {
              return;
            }

            // Collect SKUs based on action type and value structure
            if (groupType === ACTIONS_TYPES.UPDATE_PRODUCT_VALUE) {
              for (const v of thenAction.value) {
                productSKUs.add(v.sku);
              }
            } else if (
              [
                ACTIONS_TYPES.ADD_PRODUCTS_TO_QUOTE,
                ACTIONS_TYPES.REMOVE_PRODUCTS_FROM_QUOTE,
              ].includes(groupType)
            ) {
              for (const v of thenAction.value) {
                productSKUs.add(v);
              }
            }

            if (!thenActionByGroup[groupType]) {
              thenActionByGroup[groupType] = {
                ...ACTION_OPTIONS.find((action) => action.key === groupType),
                individualActions: [],
              };
            }
            thenActionByGroup[groupType].individualActions.push(thenAction);
          }
        }
      );
    }

    // convert keys to camel case
    // expressionBox accepts camelCase keys only,
    // we get the condition in snake_case from backend, so we need to convert it to camelCase
    return {
      condition: convertKeysToCamelCase(action.condition),
      actionsToPerform: Object.values(thenActionByGroup),
    };
  });

  return { groupedActions, productSKUs };
};

const addSourceField = (condition, sourceFields) => {
  const newSourceFields = new Set(sourceFields);
  if (QUOTE_LINE_ITEM_FIELDS.includes(condition.token.key)) {
    newSourceFields.add("quote_line_item");
  } else {
    newSourceFields.add(condition.token.key);
  }
  return newSourceFields;
};

const transformActions = (actions) => {
  let sourceFields = new Set();
  const destinationFields = new Set();

  const actionsToUpdate = actions.map((actionGroup) => {
    // Add source fields to the set
    for (const condition of actionGroup.condition) {
      if (condition.tokenType === "FORM_VARIABLES") {
        sourceFields = addSourceField(condition, sourceFields);
      }
      if (condition.tokenType === "FUNCTIONS") {
        const args = condition?.token?.args || [];
        for (const arg of args) {
          if (arg?.tokenType === "FORM_VARIABLES") {
            sourceFields = addSourceField(arg, sourceFields);
          }
        }
      }
    }

    const transformedActions = actionGroup.actionsToPerform.flatMap(
      (actionToPerform) => {
        const { key, individualActions } = actionToPerform;
        return individualActions
          .map((individualAction) => {
            const { component_id, value } = individualAction;

            // Add destination fields to the set
            if (
              key === ACTIONS_TYPES.UPDATE_QUOTE_FIELD &&
              !isNil(component_id)
            ) {
              destinationFields.add(component_id);
            }

            // Then action transformation logic
            if (
              (key === ACTIONS_TYPES.UPDATE_QUOTE_FIELD ||
                key === ACTIONS_TYPES.UPDATE_QUOTE_SECTION) &&
              isNil(component_id)
            ) {
              return null;
            }
            if (key === ACTIONS_TYPES.UPDATE_PRODUCT_VALUE) {
              const filteredValues = value
                .map((v) => (isNil(v.property) ? null : v))
                .filter(Boolean);
              return { ...individualAction, value: filteredValues };
            }
            return individualAction;
          })
          .filter(Boolean);
      }
    );
    return {
      condition: actionGroup.condition,
      actionsToPerform: transformedActions,
    };
  });

  return {
    actions: actionsToUpdate,
    source_fields: [...sourceFields],
    destination_fields: [...destinationFields],
    trigger: `HasChanged(${sourceFields[0]})`,
  };
};

const useQuoteRuleHook = () => {
  const { fetchEndpoint } = useFetchApi(API_ENDPOINTS);
  const { quoteRuleId } = useParams();

  const [quoteRule, setQuoteRule] = useState({});
  const [groupedActions, setGroupedActions] = useState([]);
  const [productsMap, setProductsMap] = useState({});

  const getProductsBySkus = useMutation(
    (body) => fetchEndpoint(API.GET_PRODUCTS_BY_SKUS, { body }),
    {
      retry: false,
      onSuccess: (data) => {
        const newProductsMap = {};
        for (const product of data.products) {
          newProductsMap[product.product_id] = {
            label: product.name,
            value: product.product_id,
            key: `${product.name}##::##${product.product_id}`,
            ...product,
          };
        }
        setProductsMap(newProductsMap);
      },
    }
  );

  const getQuoteRule = useQuery(
    [API.GET_QUOTE_RULE, quoteRuleId],
    () => fetchEndpoint(API.GET_QUOTE_RULE, {}, { quoteRuleId }),
    {
      retry: false,
      cacheTime: 0,
      refetchOnWindowFocus: false,
      onSuccess: (data) => {
        const actions = get(data, "form_rule.actions", []) ?? [];
        setQuoteRule(get(data, "form_rule", {}));
        const { groupedActions, productSKUs } =
          getGroupedActionsAndSku(actions);
        setGroupedActions(groupedActions);
        getProductsBySkus.mutate({ skus: [...productSKUs] });
      },
      onError: () => {
        message.error("Something went wrong! Please try again later.");
      },
    }
  );

  const updateQuoteRule = useMutation(
    (body) => fetchEndpoint(API.UPDATE_QUOTE_RULE, { body }),
    {
      onSuccess: (_, variables) => {
        setQuoteRule((prev) => ({
          ...prev,
          ...variables?.updated_fields,
        }));
        message.success("Quote rule updated successfully!");
      },
      onError: (error) => {
        if (error.message) {
          message.error(error.message);
        } else {
          message.error("Failed to update quote rule!");
        }
      },
    }
  );

  // Load field metadata
  const { data: fieldData, isLoading: isLoadingFieldData } = useQuery(
    [API.GET_FIELDS_SECTIONS, quoteRule.form_builder_id],
    () =>
      fetchEndpoint(
        API.GET_FIELDS_SECTIONS,
        {},
        { quoteFormId: quoteRule.form_builder_id }
      ),
    {
      onError: () => message.error("Failed loading fields"),
      enabled: !!quoteRule.form_builder_id,
    }
  );

  const fieldsMeta = useMemo(() => get(fieldData, "fields", {}), [fieldData]);

  const fieldOptions = useMemo(
    () =>
      Object.values(fieldsMeta).map((f) => ({
        value: f.id,
        label: f.label,
      })),
    [fieldsMeta]
  );

  const sectionOptions = useMemo(
    () =>
      Object.values(get(fieldData, "sections", {})).map((s) => ({
        value: s.id,
        label: s.label,
      })),
    [fieldData]
  );

  const onSave = (fieldsToUpdate) => {
    message.loading("Quote rule is being saved...");
    if (!isEmpty(fieldsToUpdate.actions)) {
      fieldsToUpdate = transformActions(fieldsToUpdate.actions);
    }
    return updateQuoteRule.mutateAsync({
      form_rule_id: quoteRule.form_rule_id,
      updated_fields: { ...fieldsToUpdate },
    });
  };

  const onError = (errors) => {
    const rootError = get(
      errors,
      "actions.0.actionsToPerform.root.message",
      ""
    );
    if (rootError) {
      message.error(rootError);
    }
  };

  return {
    quoteFormId: get(quoteRule, "form_builder_id", ""),
    quoteRule,
    getQuoteRule,
    updateQuoteRule,
    getProductsBySkus,
    groupedActions,
    productsMap,
    fieldsMeta,
    fieldOptions,
    sectionOptions,
    isLoadingFieldData,
    onSave,
    onError,
  };
};

const { Provider: QuoteRuleProvider, useHookContext: useQuoteRule } =
  createHookProvider(useQuoteRuleHook);

export { QuoteRuleProvider, useQuoteRule };
