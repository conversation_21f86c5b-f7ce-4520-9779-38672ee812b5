const ACTIONS_TYPES = {
  ADD_PRODUCTS_TO_QUOTE: "add_products_to_quote",
  UPDATE_PRODUCT_VALUE: "update_product_value",
  UPDATE_QUOTE_FIELD: "update_quote_field",
  REMOVE_PRODUCTS_FROM_QUOTE: "remove_products_from_quote",
  UPDATE_QUOTE_SECTION: "update_quote_section",
};

const ACTION_GROUPS_BY_TYPE = {
  [ACTIONS_TYPES.ADD_PRODUCTS_TO_QUOTE]: ["AddQuoteLineItem"],
  [ACTIONS_TYPES.UPDATE_PRODUCT_VALUE]: ["UpdateQuoteLineItem"],
  [ACTIONS_TYPES.UPDATE_QUOTE_FIELD]: [
    "SetOptions",
    "SetValue",
    "MakeVisible",
    "MakeMandatory",
    "MakeReadOnly",
  ],
  [ACTIONS_TYPES.REMOVE_PRODUCTS_FROM_QUOTE]: ["RemoveQuoteLineItem"],
  [ACTIONS_TYPES.UPDATE_QUOTE_SECTION]: ["MakeVisible"],
};

const ACTION_OPTIONS = [
  {
    key: ACTIONS_TYPES.ADD_PRODUCTS_TO_QUOTE,
    label: "Add products to quote",
    title: "Add products",
    description: "Products are added to the quote in the selected order.",
  },
  {
    key: ACTIONS_TYPES.UPDATE_PRODUCT_VALUE,
    label: "Update product value",
    title: "Update product value",
    description:
      "Set quantity, price point, discount %. Action is skipped if products are not found on quote.",
  },
  {
    key: ACTIONS_TYPES.UPDATE_QUOTE_FIELD,
    label: "Update quote field",
    title: "Update quote field",
    description:
      "Set value to any quote field or make them read-only, hidden, visible, set dropdown options based on business needs.",
  },
  {
    key: ACTIONS_TYPES.UPDATE_QUOTE_SECTION,
    label: "Update quote section",
    title: "Update quote section",
    description: "Set visibility of any quote section based on business needs.",
  },
  {
    key: ACTIONS_TYPES.REMOVE_PRODUCTS_FROM_QUOTE,
    label: "Remove products from quote",
    title: "Remove products from quote",
    description:
      "Selected products will be removed from all phases of the quote. Action is skipped if products are not found on quote.",
  },
];

const FIELD_PROPERTIES = [
  { value: "MakeVisible", label: "Visibility" },
  { value: "MakeMandatory", label: "Required" },
  { value: "MakeReadOnly", label: "Read only" },
  { value: "SetOptions", label: "Set dropdown options" },
  { value: "SetValue", label: "Set value" },
];

const SECTION_PROPERTIES = [{ value: "MakeVisible", label: "Visibility" }];

const PRODUCT_PROPERTIES = [
  { value: "quantity", label: "Quantity" },
  { value: "discount_percent", label: "Discount Percent" },
  { value: "pricepoints", label: "Price Point" },
];

const BOOLEAN_OPTIONS = [
  { value: true, label: "True" },
  { value: false, label: "False" },
];

const DEFAULT_FIELD_ROW = {
  form_component: "field",
  component_id: null,
  function: null,
  value: null,
};

const DEFAULT_SECTION_ROW = {
  form_component: "section",
  component_id: null,
  function: null,
  value: null,
};

const QUOTE_LINE_ITEM_FIELDS = [
  "start_date",
  "end_date",
  "phase_name",
  "product_name",
  "quantity",
  "list_unit_price",
  "net_unit_price",
  "discount_percent",
  "prorated_net_total",
];

export {
  ACTIONS_TYPES,
  ACTION_OPTIONS,
  ACTION_GROUPS_BY_TYPE,
  FIELD_PROPERTIES,
  BOOLEAN_OPTIONS,
  PRODUCT_PROPERTIES,
  SECTION_PROPERTIES,
  DEFAULT_FIELD_ROW,
  DEFAULT_SECTION_ROW,
  QUOTE_LINE_ITEM_FIELDS,
};
