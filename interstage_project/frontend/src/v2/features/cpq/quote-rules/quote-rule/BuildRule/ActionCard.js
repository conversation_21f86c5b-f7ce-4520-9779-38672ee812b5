import { Trash03Icon, DotsVerticalIcon } from "@everstage/evericons/outlined";
import { useRef, useEffect } from "react";
import { useFormContext } from "react-hook-form";

import { EverTg, IconButton } from "~/v2/components";

import { ACTION_COMPONENTS } from "../ActionComponents";

const ActionCard = ({
  formKey,
  action,
  scrollToActionKey,
  setScrollToActionKey,
  onRemoveAction,
}) => {
  const actionRef = useRef(null);
  const { clearErrors } = useFormContext();

  const ActionComponent = ACTION_COMPONENTS[action.key];

  useEffect(() => {
    if (scrollToActionKey === action.key) {
      actionRef.current?.scrollIntoView({
        behavior: "smooth",
        block: "center",
      });
      setScrollToActionKey(null);
    }
  }, [action.key, scrollToActionKey, setScrollToActionKey]);

  return (
    <div
      ref={actionRef}
      className="flex flex-col border mt-5 border-ever-base-400 bg-ever-base rounded-lg overflow-hidden"
    >
      <div className="flex items-center gap-3 py-3 px-4 bg-ever-base-100">
        <div className="flex items-center gap-0.5 px-px">
          <DotsVerticalIcon className="w-1 h-5 text-ever-base-content-low" />
          <DotsVerticalIcon className="w-1 h-5 text-ever-base-content-low" />
        </div>
        <div className="flex flex-col gap-0.5">
          <EverTg.SubHeading4 className="text-ever-base-content">
            {action.title}
          </EverTg.SubHeading4>
          <EverTg.Caption className="text-ever-base-content-mid">
            {action.description}
          </EverTg.Caption>
        </div>
        <IconButton
          type="text"
          color="error"
          size="small"
          onClick={() => {
            onRemoveAction(action.key);
            clearErrors(formKey);
          }}
          className="w-5 h-5 text-ever-base-content-low ml-auto"
          icon={<Trash03Icon />}
        />
      </div>
      <div className="py-5 px-10">
        <ActionComponent formKey={formKey} />
      </div>
    </div>
  );
};

export default ActionCard;
