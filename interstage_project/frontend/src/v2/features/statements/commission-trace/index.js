import {
  CommissionTraceIcon,
  InfoCircleIcon,
} from "@everstage/evericons/outlined";
import { isEmpty } from "lodash";
import React, { Fragment, useState, useEffect } from "react";
import ReactHtmlParser from "react-html-parser";
import { useTranslation } from "react-i18next";
import { useQuery } from "react-query";
import { twMerge } from "tailwind-merge";

import { getCommissionTrace } from "~/Api/CommissionTraceService";
import { useAuthStore } from "~/GlobalStores/AuthStore";
import { EverDrawer, EverHotToastBanner, EverModal } from "~/v2/components";
import { toast } from "~/v2/components/ever-popups";
import { EverHotToastMessage } from "~/v2/components/ever-popups/EverHotToastMessage";

import { ERROR_MESSAGE } from "./constants";
import Render from "./Render";
import { LocaleIdContext } from "./store";

/**
 * @typedef {Object} DrawerData
 * @property {string} planName - The name of the commission plan.
 * @property {string} criteriaName - The name of the commission criteria.
 * @property {string} planId - The ID of the commission plan.
 * @property {string} criteriaId - The ID of the commission criteria.
 * @property {string} payeeEmail - The email address of the commission payee.
 * @property {Date} psd - The start date of the commission period.
 * @property {Date} ped - The end date of the commission period.
 */

/**
 * @typedef {Object} SelectedTrace
 * @property {string} rowKey - The selected trace unique key.
 * @property {string} tierName - Tier name.
 */

/**
 *  A component that displays the commission trace for a line item/overall sum.
 *
 * @param {object} props - Component props
 * @param {SelectedTrace} props.selectedTrace - The selected trace details.
 * @param {DrawerData} props.drawerData - The data to display in the drawer.
 * @param {string} props.currencySymbol - The currency symbol to display.
 * @param {Function} props.onClose - Function to close the drawer.
 * @param {object} props.hyperlinkMap - The Hyperlink map.
 * @param {Function} props.onLoadingChange - Function to notify parent component about loading state changes.
 * @returns {JSX.Element} - CommissionTrace component UI.
 */

export default function CommissionTrace({
  selectedTrace,
  periodLabel,
  isBaseCurrency,
  drawerData,
  currencySymbol,
  localeId,
  onClose,
  hyperlinkMap,
  onLoadingChange,
}) {
  const { planName, criteriaName, planId, criteriaId, payeeEmail, psd, ped } =
    drawerData;
  const { accessToken } = useAuthStore();
  const { t } = useTranslation();
  const [showError, setShowError] = useState(false);
  const [showFullTrace, setShowFullTrace] = useState(false);
  const toastIdRef = React.useRef(null);

  // This ensures the loading toast is removed if the user navigates away
  // or closes the payout table drawer while loading
  useEffect(() => {
    return () => {
      if (toastIdRef.current) {
        toast.remove(toastIdRef.current);
        toastIdRef.current = null;
      }
    };
  }, []);

  const { data: apiResponse, isLoading } = useQuery(
    ["getCommissionTrace", selectedTrace.rowKey],
    () =>
      getCommissionTrace(
        {
          planId,
          criteriaId,
          payeeEmail,
          isBaseCurrency,
          periodStartDate: psd,
          periodEndDate: ped,
          rowKey: selectedTrace.rowKey,
        },
        accessToken
      ),
    {
      cacheTime: 0,
      refetchOnWindowFocus: false,
      enabled: !isEmpty(selectedTrace),
      onSettled: (_, error) => {
        setShowError(!!error);
        onLoadingChange?.(false);
      },
    }
  );

  const aiContent = apiResponse?.aiGeneratedContent;
  const traceData = apiResponse?.data;
  const handleClose = () => {
    setShowFullTrace(false);
    onClose();
  };

  // Show loading toast
  if (isLoading && !toastIdRef.current) {
    onLoadingChange?.(true);
    toastIdRef.current = toast.custom(
      (toastId) => (
        <EverHotToastMessage
          type="loading"
          description={t("COMMISSION_TRACE.GENERATING_COMMISSION_TRACE")}
          toastId={toastId.id}
        />
      ),
      { position: "top-center", duration: Infinity }
    );
  } else if (!isLoading && toastIdRef.current) {
    toast.remove(toastIdRef.current);
    toastIdRef.current = null;
  }

  if (isLoading) return null;

  // AI Summary Modal Flow
  if (aiContent && !showFullTrace) {
    return (
      <EverModal
        visible={true}
        onCancel={handleClose}
        width={754}
        centered
        destroyOnClose
        footer={<AIFeedbackFooter />}
        title={
          <div className="flex items-center gap-3">
            <div className="flex items-center gap-1.5">
              <CommissionTraceIcon className="w-6 h-6" />
              <span className="font-ibm font-semibold text-base leading-6 tracking-tight text-ever-base-content">
                Trace IQ
              </span>
            </div>
            <div className="relative p-[1px] rounded">
              <div className="flex items-center h-6 px-2 py-0.5 gap-1 rounded bg-ever-chartColors-2">
                <span className="text-xs font-ibm font-medium leading-5 tracking-tight text-center text-ever-chartColors-20">
                  Beta
                </span>
              </div>
            </div>
          </div>
        }
        closable
        bodyStyle={{ padding: 0, overflow: "hidden" }}
      >
        <AIGeneratedContent
          content={aiContent}
          onShowFullTrace={() => setShowFullTrace(true)}
        />
      </EverModal>
    );
  }

  // Full Trace Drawer Flow
  if (traceData) {
    return (
      <EverDrawer
        destroyOnClose
        height="95%"
        className="commission-trace-drawer"
        title={
          <CommissionTraceHeader
            breadcrumbs={[planName, criteriaName, selectedTrace.rowKey]}
          />
        }
        placement="top"
        onClose={handleClose}
        visible
        footer={null}
      >
        {showError ? (
          <ErrorMsgComponent />
        ) : (
          <LocaleIdContext.Provider value={localeId}>
            <Render
              periodLabel={periodLabel}
              traceData={traceData}
              currencySymbol={currencySymbol}
              selectedTierName={selectedTrace.tierName || ""}
              hyperlinkMap={hyperlinkMap}
              aiGeneratedContent={aiContent}
            />
          </LocaleIdContext.Provider>
        )}
      </EverDrawer>
    );
  }

  return null;
}

/** AI Content + Blue Bar */
function AIGeneratedContent({ content, onShowFullTrace }) {
  return (
    <div className="flex flex-col w-full h-[27rem] overflow-hidden">
      {/* 1) Scrollable AI content */}
      <div className="flex-1 overflow-y-auto px-6 py-6">
        <div className="prose prose-sm max-w-none break-words whitespace-normal">
          {ReactHtmlParser(content)}
        </div>
      </div>

      {/* 2) Blue bar just above footer */}
      <div className="h-10 px-6 flex items-center gap-2.5 bg-gradient-to-r from-ever-primary-200/40 via-ever-primary-ring/20 to-ever-primary-200/50 rounded-t-lg">
        <span className="font-ibm text-sm leading-5 tracking-tight text-ever-base-content">
          For a full breakdown of your payout,
        </span>
        <button
          className="font-ibm font-medium text-sm leading-5 tracking-tight text-ever-primary underline cursor-pointer"
          onClick={onShowFullTrace}
        >
          click here
        </button>
      </div>
    </div>
  );
}

function CommissionTraceHeader({ breadcrumbs }) {
  return (
    <div>
      {breadcrumbs.map((breadcrumb, index) => {
        const lastIndex = index === breadcrumbs.length - 1;
        return (
          <Fragment key={index}>
            <div
              className={twMerge(
                "inline-block truncate max-w-32 align-middle",
                lastIndex && "text-ever-base-content-mid"
              )}
              title={breadcrumb}
            >
              {breadcrumb}
            </div>
            {!lastIndex && <span className="mx-2">/</span>}
          </Fragment>
        );
      })}
    </div>
  );
}

function ErrorMsgComponent() {
  return (
    <EverHotToastBanner
      className="mt-2 mx-auto"
      type="error"
      description={ERROR_MESSAGE}
    />
  );
}
function AIFeedbackFooter() {
  return (
    <div className="w-full h-6 flex justify-between p-1 bg-ever-base-50 rounded-b-xl">
      <div className="flex items-center gap-2 w-64">
        <InfoCircleIcon className="w-4 h-4 text-ever-info" />
        <span className="font-ibm text-xs text-ever-base-content-mid">
          Generative AI is experimental
        </span>
      </div>
    </div>
  );
}
