import React, { useMemo } from "react";
import { useRecoilValue } from "recoil";

import { RBAC_ROLES } from "~/Enums";
import { navPortalAtom } from "~/GlobalStores/atoms";
import { useUserPermissionStore } from "~/GlobalStores/UserPermissionStore";
import {
  EverDatePicker,
  EverButtonGroup,
  EverButton,
  EverNumberBadge,
  EverNavPortal,
} from "~/v2/components";

import CreateUpdatePlanModal from "../CreateUpdatePlanModal.js";

const NavigationBar = ({
  selectedYear,
  setSelectedYear,
  selectedStatus,
  setSelectedStatus,
  draftCount,
  publishedCount,
  showCreatePlanModal,
  setShowCreatePlanModal,
  onPlanCreated,
}) => {
  const { hasPermissions } = useUserPermissionStore();
  const navPortalLocation = useRecoilValue(navPortalAtom);

  const canEditTerritoryPlans = useMemo(() => {
    return hasPermissions(RBAC_ROLES.EDIT_TERRITORY_PLANS);
  }, [hasPermissions]);

  const allCount = draftCount + publishedCount;

  const statusOptions = [
    { label: "All", value: "all", count: allCount },
    { label: "Published", value: "published", count: publishedCount },
    { label: "Draft", value: "draft", count: draftCount },
  ];

  return (
    <EverNavPortal target={navPortalLocation}>
      <div className="flex justify-between">
        <div className="flex gap-4">
          <EverDatePicker
            className="max-h-8 !w-44"
            picker="year"
            placeholder="Select Fiscal Year"
            value={selectedYear}
            onChange={(value) => {
              setSelectedYear(value);
            }}
          />
          <EverButtonGroup
            className="bg-ever-base-200"
            activeBtnType="text"
            activeBtnColor="primary"
            inactiveButtonClassname="text-ever-base-content"
            defActiveBtnIndex={statusOptions.findIndex(
              (option) => option.value === selectedStatus
            )}
            size="small"
          >
            {statusOptions.map((option) => {
              return (
                <EverButton
                  key={option.value}
                  onClick={() => setSelectedStatus(option.value)}
                  appendIcon={
                    <EverNumberBadge
                      className="bg-ever-base-300"
                      count={Number(option.count)}
                    />
                  }
                >
                  {option.label}
                </EverButton>
              );
            })}
          </EverButtonGroup>
        </div>
        <div>
          {canEditTerritoryPlans && (
            <>
              <EverButton
                size="small"
                onClick={() => setShowCreatePlanModal(true)}
              >
                Create Plan
              </EverButton>
              <CreateUpdatePlanModal
                open={showCreatePlanModal}
                setOpen={setShowCreatePlanModal}
                onPlanCreated={onPlanCreated}
                onPlanUpdated={() => {}}
              />
            </>
          )}
        </div>
      </div>
    </EverNavPortal>
  );
};

export default NavigationBar;
