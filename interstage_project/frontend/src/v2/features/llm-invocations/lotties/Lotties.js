import Lottie from "~/v2/components/Lottie";

import Sparkle from "./Sparkle.json";
import Thinker from "./Thinker.json";

export const ThinkerAnimation = () => {
  const defaultOptions = {
    loop: true,
    autoplay: true,
    animationData: Thinker,
    rendererSettings: {
      preserveAspectRatio: "xMidYMid slice",
    },
  };

  return (
    <div className="flex flex-col justify-center items-center text-center h-full -mt-12">
      <div className="w-[500px] h-auto">
        <Lottie options={defaultOptions} width="100%" height="auto" />
      </div>
    </div>
  );
};

export const SparkleAnimation = () => {
  const defaultOptions = {
    loop: true,
    autoplay: true,
    animationData: Sparkle,
    rendererSettings: {
      preserveAspectRatio: "xMidYMid slice",
    },
  };

  return (
    <Lottie
      options={defaultOptions}
      width="20%"
      styles={{
        display: "flex",
        margin: "0",
      }}
    />
  );
};
