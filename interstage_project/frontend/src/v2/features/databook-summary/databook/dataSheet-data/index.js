import { gql, useQuery, useLazyQuery } from "@apollo/client";
import {
  ChevronDownIcon,
  LayoutAltIcon,
  AlignTopArrowIcon,
  PlusIcon,
  EditIcon,
  FunctionIcon,
  BoxIcon,
} from "@everstage/evericons/outlined";
import { Alert, Dropdown, Menu, message, Divider } from "antd";
import classNames from "classnames";
import { cloneDeep, debounce, isEmpty, isEqual, pick, sortBy } from "lodash";
import { observer } from "mobx-react";
import moment from "moment";
import React, { useEffect, useState, useRef, useCallback } from "react";
import { useTranslation } from "react-i18next";
import { useQuery as useReactQuery } from "react-query";
import { useRecoilValue } from "recoil";
import { twMerge } from "tailwind-merge";

import { sendAnalyticsEvent } from "~/Api/AnalyticsService";
import {
  datasheetIsStale,
  deleteDatabookFilter,
  exportDatasheetAsCsv,
  saveDatasheetFilter,
  updateDatasheetFilter,
} from "~/Api/DatabookService";
import {
  DATA_ORIGIN,
  T_CODES,
  FILTER_CONFIG_TYPE,
  PIVOT_AGG_MAP,
  RBAC_ROLES,
  ANALYTICS_EVENTS,
  ANALYTICS_PROPERTIES,
  COMMON_MOMENT_DATE_FORMAT,
} from "~/Enums";
import { myClientAtom } from "~/GlobalStores/atoms";
import { useAuthStore } from "~/GlobalStores/AuthStore";
import { useUserPermissionStore } from "~/GlobalStores/UserPermissionStore";
import { useVariableStore } from "~/GlobalStores/VariableStore";
import { getClientFeatures } from "~/Utils/ClientFeaturesUtils";
import { formatCurrencyWrapper } from "~/Utils/CurrencyUtils";
import { currentUTCTimeMsInString } from "~/Utils/DateUtils";
import {
  EverTabs,
  EverTg,
  EverButton,
  EverTooltip,
  EverLoader,
  toast,
  EverHotToastMessage,
  EverHotToastNotification,
  EverHotToastBanner,
} from "~/v2/components";
import { EverAgCellTooltip } from "~/v2/components/ag-grid";
import {
  AgGridIconsMap,
  dataTypeColorMap,
} from "~/v2/components/ag-grid/AgGridIconsMap";
import {
  comparator,
  dateComparator,
  numComparator,
} from "~/v2/components/ag-grid/utils";
import { formatDateTime } from "~/v2/components/ever-formatter/EverFormatter";
import RBACProtectedComponent from "~/v2/components/rbac-wrapper/RBACProtectedComponent";
import { dogUnpluggingWires, lostAstronaut } from "~/v2/images";

import DatasheetFilterActions from "./filters/DatasheetFilterActions";
import DatasheetFilterDrawer from "./filters/DatasheetFilterDrawer";
import DatasheetFilterViewDeleteModal from "./filters/DatasheetFilterViewDeleteModal";
import DatasheetFilterViewMenu from "./filters/DatasheetFilterViewMenu";
import DatasheetFilterViewSaveModal from "./filters/DatasheetFilterViewSaveModal";
import DatasheetTable from "./Table";
import ViewFormulaVisible from "./view-formula-popup";
import { AdjustRecord } from "../../adjust-record";
import { AdjustmentActions } from "../../adjust-record/AdjustmentActions";
import "./style.scss";
import { LATES_DATASHEET_REFRESH_INFO } from "../graphql";
import { DraggableTabs } from "../Render";
import styles from "../style.module.scss";

// polling frequency - 15s
const DATASHEET_POLLING_RATE = 15 * 1000;
// max time datasheet sync can run before warning
// message is displayed - 60 mins
const DATASHEET_SYNC_THRESHOLD = 60 * 60;

const ARCHIVED_DATASHEET_TOOLTIP = "This datasheet is archived"; // Constant for tooltip
const ARCHIVED_DATASHEET_DESCRIPTION =
  "This is an archived Datasheet. You can still view and use it, but to optimise performance it's excluded from all automated and manual syncs triggered from the 'Commission & Data Sync' page.";

async function fetchDatasheetArchivedStatuses(accessToken, databookId) {
  const requestOptions = {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${accessToken}`,
    },
  };
  const res = await fetch(
    `/spm/get_force_skip_statuses?databook_id=${databookId}`,
    requestOptions
  );
  const data = await res.json();
  if (!res.ok) {
    throw new Error("Failed to fetch datasheet force skip statuses");
  }
  return data?.forceSkipStatuses;
}

async function fetchDatasheetArchivedStatus(accessToken, datasheetId) {
  const requestOptions = {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${accessToken}`,
    },
  };
  const res = await fetch(
    `/spm/is_datasheet_force_skipped?datasheet_id=${datasheetId}`,
    requestOptions
  );
  const data = await res.json();
  if (!res.ok) {
    throw new Error("Failed to fetch datasheet force skip status");
  }
  return data?.isForceSkipped;
}

const GET_DATASHEET_DATA = gql`
  query DatasheetData(
    $databookId: String!
    $databookName: String!
    $datasheetId: String!
    $pageNumber: Int!
    $pageSize: Int!
    $sessionKd: String!
    $filters: [JSONString]
    $pivots: JSONString
  ) {
    datasheetData(
      databookId: $databookId
      databookName: $databookName
      datasheetId: $datasheetId
      pageNumber: $pageNumber
      pageSize: $pageSize
      sessionKd: $sessionKd
      filters: $filters
      pivots: $pivots
    ) {
      data
      dataCount
      pivotColumns
    }
  }
`;

const GET_ADJUSTMENT_DATA = gql`
  query AdjustmentData(
    $databookId: String!
    $databookName: String!
    $datasheetId: String!
  ) {
    adjustmentData(
      databookId: $databookId
      databookName: $databookName
      datasheetId: $datasheetId
    ) {
      adjustmentData
      danglingRows
    }
  }
`;

const GET_DATASHEET_FILTERS = gql`
  query AllDsFilterList(
    $databookId: String!
    $datasheetId: String!
    $sessionKd: String!
  ) {
    allDsFilterList(
      databookId: $databookId
      datasheetId: $datasheetId
      sessionKd: $sessionKd
    ) {
      lastUpdatedBy
      filterId
      filterList
      filterName
      datasheetId
      databookId
      configType
      pivotObject
    }
  }
`;

const GET_DATASHEET_FILTER_OPERATORS = gql`
  query DsFilterOperators {
    dsFilterOperators {
      name
      altName
      category
      operandTypeIds
      outputTypeIds
      needsOperand
      multiValued
    }
  }
`;

const formatCellDate = (params) => {
  if (params.value) {
    return moment
      .utc(params.value.toString())
      .format(COMMON_MOMENT_DATE_FORMAT);
  }
  return null;
};

const DataSheetData = observer((props) => {
  const {
    store,
    isReportDataStale,
    setIsReportDataStale,
    onEditCalculatedField,
    dsVariableStore,
    onEdited,
    hideAdded,
    onChanged,
    renderTabBarPropsVal,
    onTabDropCompletedVal,
    tabPanes,
    dropDownMenu,
    datasheetName,
    dataOrigin,
    setButtonText,
    buttonText,
    isStaleLoading,
    setIsStaleLoading,
    genrateDataReason,
  } = props;
  const {
    databookId,
    databookName,
    getDatasheetConfig,
    datasheetId,
    saveRefetch,
    saveSessionKd,
    columnTypeMap,
    saveAllSessionKd,
    allSessionKds,
    callIsStale,
    setCallIsStale,
    isDefaultDashboardDatabook,
  } = store;
  const DEFAULT_FILTER_VALUE = { type: T_CODES.FILTER, is_valid: false };
  const DEFAULT_FILTER_LIST = [DEFAULT_FILTER_VALUE];

  const [tableData, setTableData] = useState([]);
  const [adjustmentData, setAdjustmentData] = useState([]);
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [columns, setColumns] = useState([]);
  const { systemNameToDisplayNameMap } = dsVariableStore;
  const { dataTypesById } = useVariableStore();
  const [dataError, setDataError] = useState(null);
  // The pageSize/pageNumber state store the current page size/page number of the datasheet as object,with key as datasheetId and value as pagenumber/pagesize, Eg: {'908927-2308-2380-238902':2,'2489-8428-2489280-2480':1}
  const [pageSize, setPageSize] = useState({});
  const [pageNumber, setPageNumber] = useState({});
  const [dataCount, setDataCount] = useState(0);
  const [selectedRecord, setSelectedRecord] = useState({});
  const [modalVisible, setModalVisible] = useState(false);
  //const [popoverVisible, setPopoverVisible] = useState(false);
  const [adjustmentTitle, setAdjustmentTitle] = useState("");
  const [enableRevert, setEnableRevert] = useState(false);
  const { accessToken, isLoggedInAsUser } = useAuthStore();
  const [adjustmentVisible, setAdjustmentVisible] = useState(false);
  const [exportCsvTooltip, setExportCsvTooltip] = useState("");
  const [showFiltersDrawer, setShowFiltersDrawer] = useState(false);
  // The filters state stores the current filter of the datasheet as object, with key as datasheetId and value as filter given, Eg: {'908927-2308-2380-238902':[]}
  const [filters, setFilters] = useState({});
  const [views, setViews] = useState([]);
  const [isFilterListValid, setIsFilterListValid] = useState(false);
  const [activeViewName, setActiveViewName] = useState();
  const [activeViewId, setActiveViewId] = useState();
  const [activeViewType, setActiveViewType] = useState();
  const [showSaveFilterViewDialog, setShowSaveFilterViewDialog] =
    useState(false);
  const [showViewMenu, setShowViewMenu] = useState(false);
  const [showDeleteFilterViewDialog, setShowDeleteFilterViewDialog] =
    useState(false);
  const [columnOptions, setColumnOptions] = useState([]);

  const [viewToBeDeleted, setViewToBeDeleted] = useState({});
  const [viewSearchText, setViewSearchText] = useState("");
  const [appliedFilters, setAppliedFilters] = useState([]);
  const [dropDownOperators, setDropDownOperators] = useState([]);
  const [adjustmentsCount, setAdjustmentsCount] = useState(0);
  const [sessionFilterKd, setSessionFilterKd] = useState(
    currentUTCTimeMsInString()
  );

  const [isViewFormulaVisible, setIsViewFormulaVisible] = useState(false);
  const [selectedSystemName, setSelectedSystemName] = useState(false);
  const previousFilterRef = React.useRef();

  const [saveFilterType, setSaveFilterType] = useState("");
  // The isPivotMode state stores the pivot mode of the datasheet as object, with key as datasheetId and value as boolean, Eg: {'908927-2308-2380-238902':true}
  const [isPivotMode, setPivotMode] = useState({});
  const [pivotColumns, setPivotColumns] = useState([]);
  const [appliedPivot, setAppliedPivot] = useState({});
  const [newPivot, setNewPivot] = useState({});
  const [errorMessage, setErrorMessage] = useState("");
  const previousPivotRef = React.useRef();
  const [adjustmentDetailsMap, setAdjustmentDetailsMap] = useState(new Map());
  const [rowKeysSet, setRowKeysSet] = useState(new Set());
  const [originalValuesMap, setOriginalValuesMap] = useState(new Map());
  const [staleError, setStaleError] = useState("");
  const [isTableLoading, setIsTableLoading] = useState(false);
  const [archivedStatuses, setArchivedStatuses] = useState({}); // State for archived statuses

  const [tableState, setTableState] = useState({});
  const [datasheetRestrictedAccessBanner, setDatasheetRestrictedAccessBanner] =
    useState(false);
  const [onLoad, setOnLoad] = useState({});
  const [deleteActiveView, setDeleteActiveView] = useState(false);
  // The isStaleError state stores the staleness error(if the stale api fails) of the datasheet as object, with key as datasheetId and value as boolean, Eg: {'908927-2308-2380-238902':true}
  const [isStaleError, setIsStaleError] = useState({});
  // The isSheetSyncInProgress state stores the progress of the datasheet while generation of the datasheet as object, with key as datasheetId and value as boolean, Eg: {'908927-2308-2380-238902':true}
  const [isSheetSyncInProgress, setIsSheetSyncInProgress] = useState({});
  const [isLoading, setIsLoading] = useState({});
  const abortController = useRef();
  const currentPageNumber = pageNumber[datasheetId] || 1;
  const currentPageSize = pageSize[datasheetId] || 20;
  const currentAppliedFilters = appliedFilters[datasheetId] || [];
  const currentAppliedPivot = appliedPivot[datasheetId] || {};
  const currentIsPivotMode = isPivotMode[datasheetId] || false;
  const currentIsStaleError = isStaleError[datasheetId] || false;
  const currentIsSheetSyncInProgress =
    isSheetSyncInProgress[datasheetId] || false;
  const currentIsLoading = isLoading[datasheetId] || false;
  const { hasPermissions } = useUserPermissionStore();
  const [submittingReq, setSubmittingReq] = useState(false);
  const myClient = useRecoilValue(myClientAtom);
  const clientFeatures = getClientFeatures(myClient);

  const handleViewsRef = useRef(false);
  const { t } = useTranslation();

  // state variable for loading when sync is generating
  // ref variable for determining if the sync running too long message is displayed
  const dsSyncTooLongMessage = useRef(undefined);

  const ButtonTextObj = {
    GENERATE_DATASHEET: "Generate Datasheet",
    UPDATE_DATA: "Update Data",
  };

  //const [isStaleLoading, setIsStaleLoading] = useState(false);
  const {
    startPolling: datasheetStartPolling,
    stopPolling: datasheetStopPolling,
  } = useQuery(LATES_DATASHEET_REFRESH_INFO, {
    variables: { databookId: databookId, datasheetId: datasheetId },
    fetchPolicy: "no-cache",
    notifyOnNetworkStatusChange: true,
    skip: !databookId || !datasheetId,
    onCompleted: (res) => {
      if (res?.recentDatasheetRefreshForDbId) {
        const syncDetails = JSON.parse(res.recentDatasheetRefreshForDbId);
        const { sync_start_time, sync_status } = syncDetails;
        const startDate = new Date(sync_start_time);
        const endDate = new Date();
        const seconds = (endDate.getTime() - startDate.getTime()) / 1000;
        const showWarningMsg = seconds > DATASHEET_SYNC_THRESHOLD;

        // when component renders initially
        if (
          syncDetails["sync_end_time"] === "None" &&
          !currentIsSheetSyncInProgress
        ) {
          console.log("Started polling -- component render");

          setIsLoading({ ...isLoading, [datasheetId]: true });
          setIsSheetSyncInProgress({
            ...isSheetSyncInProgress,
            [datasheetId]: true,
          });
          // start polling only if time for sync < threshold
          if (showWarningMsg) {
            console.log("Taking too long...not polling for result");
            dsSyncTooLongMessage.current = message.warning(
              "Datasheet sync has been running for a long time. Please contact the support team",
              0
            );
          } else datasheetStartPolling(DATASHEET_POLLING_RATE);
        }

        // sync is running currently, but running for > threshold time
        if (currentIsSheetSyncInProgress && showWarningMsg) {
          //stop polling
          datasheetStopPolling();
          dsSyncTooLongMessage.current = message.warning(
            "Datasheet sync has been running for a long time. Please contact the support team",
            0
          );
        }

        // if sync has completed and the inprogress state
        // is true in the component stop pollng and loading
        if (
          syncDetails["sync_end_time"] != "None" &&
          currentIsSheetSyncInProgress
        ) {
          console.log("Stopping polling");
          datasheetStopPolling();

          // if too long banner is being displayed, stop it
          // since sync has finished running
          if (dsSyncTooLongMessage.current) dsSyncTooLongMessage.current();

          if (sync_status === "complete") {
            saveAllSessionKd(currentUTCTimeMsInString());
            setButtonText(null);
            setIsReportDataStale(false);
            refetch && refetch();
            refetchDatasheetFilters && refetchDatasheetFilters();
            toast.custom(
              () => (
                <EverHotToastMessage
                  type="success"
                  description={"Datasheet has been generated successfully"}
                />
              ),
              { position: "top-center" }
            );
            //message.success("Datasheet has been generated successfully");
          } else if (
            sync_status === "failed" ||
            sync_status === "partially_failed"
          ) {
            if (syncDetails["error_info"] == "row_limit_exceeded") {
              const defaultMessage =
                "The datasheet generation has failed because the number of rows produced in certain datasheets has exceeded the threshold limit. To increase the limit, please contact Everstage support.";
              toast.custom(
                () => (
                  <EverHotToastMessage
                    type="error"
                    description={
                      syncDetails.error_details?.message || defaultMessage
                    }
                  />
                ),
                { position: "top-center", duration: 4000 }
              );
            } else {
              toast.custom(
                () => (
                  <EverHotToastMessage
                    type="error"
                    description={
                      "Error in generating datasheet. Please contact the support team for help"
                    }
                  />
                ),
                { position: "top-center" }
              );
            }
            // message.error(
            //   "Error in generating datasheet. Please contact the support team for help"
            // );
          } else if (sync_status == "lock_failed") {
            toast.custom(
              () => (
                <EverHotToastMessage
                  type="error"
                  description={
                    "Another process is trying to update the datasheet. Please try again later"
                  }
                />
              ),
              { position: "top-center" }
            );
            // message.error(
            //   "Another process is trying to update the datasheet. Please try again later"
            // );
          } else {
            toast.custom(
              () => (
                <EverHotToastMessage
                  type="error"
                  description={"Datasheet sync failed"}
                />
              ),
              { position: "top-center" }
            );
            //message.error("Datasheet sync failed");
          }
          setCallIsStale(currentUTCTimeMsInString());
          setIsLoading({ ...isLoading, [datasheetId]: false });
          setIsSheetSyncInProgress({
            ...isSheetSyncInProgress,
            [datasheetId]: false,
          });
          console.log("Sync has completed");
        }
      } else {
        datasheetStopPolling();
        setIsLoading({ ...isLoading, [datasheetId]: false });
        setIsSheetSyncInProgress({
          ...isSheetSyncInProgress,
          [datasheetId]: false,
        });
        console.log("Invalid response from backend -- stopping sync");
      }
    },
  });
  // When the datasheetId changes, this useEffect checks if the user has access to the newly opened datasheet
  useEffect(() => {
    //** TODO make API call */
    saveSessionKd(datasheetId, currentUTCTimeMsInString());
    // When we switch datasheets, we need to reset the loaders related to datasheet sync
    if (datasheetId) {
      setCallIsStale(currentUTCTimeMsInString());
      setIsLoading({ ...isLoading, [datasheetId]: false });
      setIsSheetSyncInProgress({
        ...isSheetSyncInProgress,
        [datasheetId]: false,
      });
      setIsStaleError({ ...isStaleError, [datasheetId]: false });
    }
    if (datasheetId && !(datasheetId in filters)) {
      setFilters({ ...filters, [datasheetId]: DEFAULT_FILTER_LIST });
    }
    if (datasheetId) {
      const requestOptions = {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${accessToken}`,
        },
      };
      fetch(
        `/spm/access_to_datasheet?databook_id=${databookId}&datasheet_id=${datasheetId}`,
        requestOptions
      )
        .then(async (res) => {
          if (res.ok) {
            const data = await res.json();
            setDatasheetRestrictedAccessBanner(
              !data.customObjectPermission || data.datasheetRestrictedAccess
            );
          }
        })
        .catch(() => {});
    }
  }, [datasheetId]);

  // On mount sessionKd for the active datasheet is set to current time
  useEffect(() => {
    saveSessionKd(datasheetId, currentUTCTimeMsInString());
    setSessionFilterKd(currentUTCTimeMsInString());
  }, []);

  useEffect(() => {
    // when datasheet sync is running for too long,
    // an error message will be displayed at the top of the screen,
    // which must be removed when the component is unmounted
    return () => {
      if (dsSyncTooLongMessage?.current) {
        dsSyncTooLongMessage.current();
      }
    };
  }, []);

  useEffect(() => {
    // if sync in progress, then stop polling when the
    // component is unmounted
    return () => {
      if (isSheetSyncInProgress[datasheetId]) {
        datasheetStopPolling();
      }
    };
  }, [isSheetSyncInProgress[datasheetId]]);

  // This useEffect sets the isFilterListValid state variable, which is used to enable/disable the apply filters button in DatasheetFilterDrawer
  useEffect(() => {
    if (isFilterEmpty(filters[datasheetId])) {
      setIsFilterListValid(false);
    } else {
      setIsFilterListValid(
        filters[datasheetId].filter((filter) => !filter.is_valid).length == 0
      );
    }
  }, [filters]);

  const { data: allArchivedStatusesMap, isError: isArchivedStatusError } =
    useReactQuery(
      ["datasheetArchivedStatuses", databookId], // Query key
      () => fetchDatasheetArchivedStatuses(accessToken, databookId), // Fetch function
      {
        retry: false,
        refetchOnWindowFocus: false,
        enabled: !!databookId && !!accessToken, // Only run if databookId and accessToken are available
        onError: (error) => {
          console.error("Failed to fetch bulk archived statuses:", error);
        },
      }
    );

  // Update state when the bulk statuses are fetched
  useEffect(() => {
    if (allArchivedStatusesMap && !isArchivedStatusError) {
      setArchivedStatuses(allArchivedStatusesMap);
    }
  }, [allArchivedStatusesMap, isArchivedStatusError]);

  const [getAdjustmentData, { data: adjustmentdata }] = useLazyQuery(
    GET_ADJUSTMENT_DATA,
    {
      variables: {
        databookId: databookId,
        databookName: databookName,
        datasheetId: datasheetId,
      },
      fetchPolicy: "network-only",
      onCompleted: (data) => {
        updateAdjustmentData(data);
        setIsTableLoading(false);
      },
      onError: () => {
        setIsTableLoading(false);
      },
    }
  );

  const {
    loading: loadingDatasheetData,
    data,
    refetch,
  } = useQuery(GET_DATASHEET_DATA, {
    variables: {
      databookId: databookId,
      databookName: databookName,
      datasheetId: datasheetId,
      pageNumber: currentPageNumber - 1,
      pageSize: currentPageSize,
      sessionKd: allSessionKds[datasheetId],
      filters: currentAppliedFilters,
      pivots:
        currentIsPivotMode && currentAppliedPivot?.["is_valid"]
          ? JSON.stringify(currentAppliedPivot)
          : "{}",
    },
    fetchPolicy:
      databookId &&
      datasheetId &&
      allSessionKds[datasheetId] &&
      (buttonText?.text == "UPDATE_DATA" || buttonText == null)
        ? "cache-first"
        : "cache-only",
    onCompleted: () => {
      getAdjustmentData();
    },
    onError: () => {
      setIsTableLoading(false);
    },
  });

  useEffect(() => {
    if (loadingDatasheetData) {
      setIsTableLoading(true);
    }
  }, [loadingDatasheetData]);

  // As we set TableLoading in useEffect, before TableLoading get update it render the table and show "No Rows to show"
  // To avoid this lag, below code is added
  const loading = isTableLoading || loadingDatasheetData;

  const isFilterColumnInDatasheetColumns = (indvidualFilter) => {
    const result = indvidualFilter["col_name"] in columnTypeMap;
    return result;
  };

  const isPivotColumnInDatasheetColumns = (pivot) => {
    pivot["index"] = pivot["index"].filter((el) => el in columnTypeMap);
    pivot["columns"] = pivot["columns"].filter((el) => el in columnTypeMap);
    pivot["values"] = pivot["values"].filter((el) => {
      if (!(el in columnTypeMap)) {
        delete pivot["aggfunc"][el];
        return false;
      }
      return true;
    });

    if (
      pivot["index"].length &&
      pivot["values"].length &&
      pivot["columns"].length
    )
      return true;
    return false;
  };

  const { data: datasheetFilters, refetch: refetchDatasheetFilters } = useQuery(
    GET_DATASHEET_FILTERS,
    {
      variables: {
        databookId: databookId,
        datasheetId: datasheetId,
        sessionKd: sessionFilterKd,
      },
      fetchPolicy:
        databookId &&
        datasheetId &&
        sessionFilterKd &&
        (buttonText?.text == "UPDATE_DATA" || buttonText == null)
          ? "cache-first"
          : "network-only",
      skip: !databookId || !datasheetId,
    }
  );
  const { data: datasheetFiltersOperators } = useQuery(
    GET_DATASHEET_FILTER_OPERATORS
  );

  // The staleness is checked when the datasheet,databook is changed or when data is fetched
  const fetchIsStale = () => {
    const data = {
      databookId: databookId,
      datasheetId: datasheetId,
    };
    if (abortController.current) {
      abortController.current.abort();
    }
    abortController.current = new AbortController();
    const { signal } = abortController.current;
    setIsStaleLoading(true);
    datasheetIsStale(accessToken, data, signal)
      .then(async (response) => {
        if (response.ok) {
          return response.json();
        } else {
          const res = await response.json();
          setIsStaleError({ ...isStaleError, [datasheetId]: true });
          setStaleError(res.reason);
          setIsStaleLoading(false);
          throw new Error("Request failed, please try again.");
        }
      })
      .then((data) => {
        setIsStaleLoading(false);
        if (data?.isDatasheetStale || data?.isReportDataStale) {
          if (data?.action) {
            setButtonText({
              text:
                data?.action == "SPEC_CHANGE"
                  ? "GENERATE_DATASHEET"
                  : "UPDATE_DATA",
              reason: data?.reason,
            });
          } else {
            setButtonText({
              text: "UPDATE_DATA",
              reason: data?.reason,
            });
          }
          if (data?.isReportDataStale) {
            setIsReportDataStale(true);
          }
        } else {
          setButtonText(null);
        }
      })
      .catch((error) => {
        console.log(error.message);
      });
  };

  // TODO
  //For now, there is two different use effect to handle the refetching the is_stale
  //Needs to be refactored this use effects

  useEffect(() => {
    if (datasheetId && databookId) {
      fetchIsStale();
    }
  }, [callIsStale]);

  // When the filters are changed in the filter drawer, and the previous filter is not same as the current filter
  // Then, refetch is called
  useEffect(() => {
    if (
      previousFilterRef?.current &&
      appliedFilters !== previousFilterRef.current
    ) {
      refetch && refetch();
    }
    previousFilterRef.current = appliedFilters;
  }, [appliedFilters]);

  // When the pivot is toggled to true, and the previous pivot is not same as the current pivot, refetch is called
  // When the pivot is toggled to false, and the previous pivot is not null, refetch is called
  useEffect(() => {
    if (currentIsPivotMode) {
      if (
        previousPivotRef?.current &&
        appliedPivot !== previousPivotRef.current
      ) {
        refetch && refetch();
      }
      previousPivotRef.current = appliedPivot;
    } else if (previousPivotRef.current) {
      refetch && refetch();
    }
  }, [isPivotMode, appliedPivot]);

  // When the datasheetdata is fetched, the table data is set to the new data
  useEffect(() => {
    let tabData = "init",
      dataCount = 0,
      adjData = [];
    if (adjustmentdata && adjustmentdata.adjustmentData) {
      adjData = JSON.parse(adjustmentdata.adjustmentData.adjustmentData);
      if (adjData && "status" in adjData && adjData.status === "Error") {
        setDataError(`This column '${adjData.message}' contains some adjusted data that has been removed from the view.
  Please add the column to view data.`);

        setExportCsvTooltip("Datasheet contains errors");
      }
    }

    if (data && data.datasheetData) {
      const dsData = JSON.parse(data.datasheetData.data);
      const pColumns = JSON.parse(data.datasheetData.pivotColumns);
      if (dsData && "status" in dsData && dsData.status === "Error") {
        if (dsData.message === "DATA_PIVOT_MEMORY_ERROR") {
          toast.custom(
            () => (
              <EverHotToastMessage
                type="error"
                description={"Data is too large to pivot!"}
              />
            ),
            { position: "top-center" }
          );
          //message.error("Data is too large to pivot!");
        } else {
          setDataError(dsData.message);
          setExportCsvTooltip("Datasheet contains errors");
        }
      } else {
        tabData = dsData.map((data) => {
          return {
            ...data,
            key: data.row_key,
          };
        });

        setDataError(null);
        dataCount = data.datasheetData.dataCount;
        if (dataCount == 0) {
          setExportCsvTooltip("No data to export");
        } else {
          setExportCsvTooltip("");
          if (tabData !== "init" && tabData.length === 0) {
            setPageNumber({ ...pageNumber, [datasheetId]: 1 });
          }
        }
      }
      saveRefetch(datasheetId, refetch);
      setPivotColumns(pColumns);
    }

    setTableData(tabData);
    setDataCount(dataCount);
  }, [data, adjustmentData]);

  // When adjustment data is fetched, the adjustment table data is set with newly fetched data
  function updateAdjustmentData(adjustmentdata) {
    let adjData = [];
    let danglingRows = [];
    if (adjustmentdata && adjustmentdata.adjustmentData) {
      adjData = JSON.parse(adjustmentdata.adjustmentData.adjustmentData);
      if (
        clientFeatures?.isShowDanglingAdjustmentsEnabled &&
        adjustmentdata.adjustmentData.danglingRows
      ) {
        danglingRows = JSON.parse(adjustmentdata.adjustmentData.danglingRows);
      }
      if (adjData && "status" in adjData && adjData.status === "Error") {
        setDataError(`This column '${adjData.message}' contains some adjusted data that has been removed from the view.
  Please add the column to view data.`);
        setExportCsvTooltip("Datasheet contains errors");
      } else if (
        hasPermissions(
          [
            RBAC_ROLES.MANAGE_DATABOOK,
            RBAC_ROLES.MANAGE_DATASHEETADJUSTMENTS,
            RBAC_ROLES.MANAGE_DATASHEETPERMISSIONS,
          ],
          false
        )
      ) {
        setAdjustmentData({
          ...adjData,
          ...danglingRows,
        });
        const row_keys_set = new Set();

        // For each property in the adjData object, we checks if the value is an array
        //  and if the length of the array is greater than zero. If these conditions are met.
        // the code then loops over each element of the array using the forEach() method

        for (var adjId in adjData) {
          const arr = adjData[adjId];
          if (arr && arr.length > 0) {
            arr.forEach((rec) => {
              const row_key = rec.row_key;
              row_keys_set.add(row_key);
              setAdjustmentDetailsMap(
                adjustmentDetailsMap.set(row_key, rec.adjustment_details)
              );
              setOriginalValuesMap(
                originalValuesMap.set(row_key, rec.original_values)
              );
            });
          }
        }
        setRowKeysSet(row_keys_set);
        setDataError(null);
      }
    } else {
      setDataError(null);
      setAdjustmentData([]);
      setAdjustmentDetailsMap(new Map());
      setOriginalValuesMap(new Map());
      setRowKeysSet(new Set());
    }
  }

  // When a filter or pivot is saved, this useEffect is called and sets the views state variable,
  // which is used to populate the saved views menu
  useEffect(() => {
    if (datasheetFilters) {
      if (!isEmpty(datasheetFilters.allDsFilterList)) {
        const _views = [];
        // Set the saved views in the menu
        const dsFilterList = datasheetFilters.allDsFilterList;
        dsFilterList.forEach((filter) => {
          let parseFilters, parsePivots;
          if (filter["configType"] === FILTER_CONFIG_TYPE.FILTER) {
            parseFilters = JSON.parse(filter["filterList"]);
            if (parseFilters) {
              for (let index = 0; index < parseFilters.length; index++) {
                const isValid =
                  parseFilters[index]["is_valid"] &&
                  isFilterColumnInDatasheetColumns(parseFilters[index]);
                parseFilters[index]["is_valid"] = isValid;
                if (!isValid) {
                  parseFilters[index] = {
                    ...DEFAULT_FILTER_VALUE,
                    is_error: true,
                  }; // added is_error to differentiate isEqual with default filter
                }
              }
            }
          } else {
            parsePivots = JSON.parse(filter["pivotObject"]);
            if (parsePivots) {
              const isValid = isPivotColumnInDatasheetColumns(parsePivots);
              parsePivots["is_valid"] = isValid;
            }
          }
          _views.push({
            filterName: filter["filterName"],
            filterId: filter["filterId"],
            filterList: parseFilters ? parseFilters : [],
            pivotObject: parsePivots ? parsePivots : {},
            inEditFilterName: filter["filterName"],
            onEditViewName: false,
            datasheetId: filter["datasheetId"],
            databookId: filter["databookId"],
            configType: filter["configType"],
          });
        });
        setViews(_views);

        // Set active view
        resetFilterToDefault();
        if (
          !(datasheetId in onLoad) ||
          (datasheetId in onLoad && onLoad[datasheetId] !== false)
        ) {
          const activeFilter = _views[_views.length - 1];
          const _config = activeFilter["configType"];
          setActiveViewName(activeFilter["filterName"]);
          setActiveViewId(activeFilter["filterId"]);
          setActiveViewType(_config);
          if (_config === FILTER_CONFIG_TYPE.FILTER) {
            const _filters = activeFilter["filterList"];
            setFilters({ ...filters, [datasheetId]: cloneDeep(_filters) });
            updateAppliedFilters(_filters);
          } else {
            const _pivots = activeFilter["pivotObject"];
            setPivotMode({
              ...isPivotMode,
              [datasheetId]: _pivots["is_valid"],
            });
            setAppliedPivot({ ...appliedPivot, [datasheetId]: _pivots });
          }
        }
      } else {
        setViews([]);
        resetFilterToDefault();
      }
    }
  }, [dataTypesById, columnTypeMap, datasheetFilters]);

  const updateAppliedFilters = (filters) => {
    if (filters.find((filter) => filter["is_valid"] === false)) {
      setAppliedFilters({ ...appliedFilters, [datasheetId]: [] });
    } else {
      setAppliedFilters({
        ...appliedFilters,
        [datasheetId]: filters.map((filter) => JSON.stringify(filter)),
      });
    }
  };

  const updateAppliedPivots = (pivots) => {
    pivots["is_valid"]
      ? setPivotMode({ ...isPivotMode, [datasheetId]: true })
      : setPivotMode({ ...isPivotMode, [datasheetId]: false });
    setAppliedPivot({ ...appliedPivot, [datasheetId]: pivots });
  };

  // The dropdown operators to be shown for each column in the filter drawer is set here
  useEffect(() => {
    if (datasheetFiltersOperators) {
      // Create operatorsMap for current datasheet's columns's datatype
      const operatorsMap = {};
      Object.entries(columnTypeMap).map(([_, value]) => {
        operatorsMap[dataTypesById[value]] = [];
      });

      // Add the avaiable operators for datatypes
      const dsFilterOperators =
        datasheetFiltersOperators?.dsFilterOperators || [];
      dsFilterOperators.forEach((dsFilterOperator) => {
        const operandTypeIds = dsFilterOperator.operandTypeIds;
        JSON.parse(operandTypeIds)
          .filter((operandTypeId) => operatorsMap[dataTypesById[operandTypeId]])
          .forEach((operandTypeId) => {
            const datatype = dataTypesById[operandTypeId];
            const operatorOptions = {
              multi_valued: dsFilterOperator["multiValued"],
              needs_operand: dsFilterOperator["needsOperand"],
              label: dsFilterOperator["altName"],
              value: dsFilterOperator["name"],
            };
            operatorsMap[datatype].push(operatorOptions);
          });
      });

      // Sort operators based on label
      Object.entries(operatorsMap).forEach(([dataType, operators]) => {
        operatorsMap[dataType] = sortBy(operators, ["label"]);
      });
      setDropDownOperators(operatorsMap);
    }
  }, [dataTypesById, columnTypeMap, datasheetFiltersOperators, datasheetId]);

  // refetchDatasheetFilters is fetched when the datasheetConfig is changed(like renamed columns)
  useEffect(() => {
    async function renameFilterColumns() {
      handleViewsRef.current = true;
      // Rename the filters for columns that are renamed
      let refetchDSFilters = false;
      if (getDatasheetConfig && getDatasheetConfig.variables) {
        for (const var_obj of getDatasheetConfig.variables) {
          for (const view of views) {
            const viewFilterList = view.filterList.filter(
              (filter) => filter["is_valid"]
            );
            let filterList = cloneDeep(viewFilterList);
            filterList = filterList.map((filter) => {
              if (filter["col_name"] == var_obj.systemName) {
                filter["col_display_name"] = var_obj.displayName;
              }
              return pick(filter, [
                "type",
                "col_name",
                "operator",
                "data_type",
                "value",
                "is_valid",
                "col_display_name",
                "needs_operand",
                "multi_valued",
              ]);
            });
            if (!isEqual(filterList, viewFilterList)) {
              const updatedView = { ...view, filterList: filterList };
              await updateDatasheetFilter(updatedView, accessToken);
              refetchDSFilters = true;
            }
          }
        }
      }
      if (refetchDSFilters && currentAppliedFilters) {
        refetchDatasheetFilters();
      }
      handleViewsRef.current = false;
    }

    if (!handleViewsRef.current) {
      renameFilterColumns();
    }
  }, [getDatasheetConfig, views]);

  // The column definitions for AgGrid are set here, when the pivot columns are changed
  useEffect(() => {
    if (
      currentIsPivotMode &&
      !isEmpty(pivotColumns) &&
      Object.keys(currentAppliedPivot).length > 0 &&
      systemNameToDisplayNameMap?.[datasheetId] &&
      getDatasheetConfig &&
      getDatasheetConfig.variables
    ) {
      // Note: systemNameToDisplayNameMap from dsVariableStore shouldn't be used as
      // the renamed columns are not reflected in that store.
      const systemNameToDisplayNameMap = getDatasheetConfig.variables.reduce(
        (acc, variable) => ({
          ...acc,
          [variable.systemName]: variable.displayName,
        }),
        {}
      );

      const cols = [
        ...pivotColumns.index_columns.map((var_obj) => {
          const datatype = dataTypesById[columnTypeMap[var_obj]];
          return {
            field: var_obj,
            headerName: systemNameToDisplayNameMap[var_obj],
            sortable: hasPermissions(RBAC_ROLES.MANAGE_DATABOOK),
            headerComponentParams: {
              datasheetTable: true,
              menuIcon: datatype,
            },
            ...(datatype === "Percentage" && {
              comparator: numComparator,
              type: "rightAligned",
            }),
            ...(datatype === "Integer" && {
              comparator: numComparator,
              type: "rightAligned",
              cellRenderer: (params) => (
                <EverTooltip
                  title={formatCurrencyWrapper(params.value, {
                    decimalPlaces: 6,
                    truncate: true,
                  })}
                  placement="bottom"
                  overlayClassName="roundedTooltip"
                >
                  {formatCurrencyWrapper(params.value)}
                </EverTooltip>
              ),
            }),
            ...(datatype === "Date" && {
              valueFormatter: (params) => {
                if (params?.value) {
                  return moment
                    .utc(params.value.toString())
                    .format(COMMON_MOMENT_DATE_FORMAT);
                }
                return null;
              },
            }),
            ...(datatype === "Boolean" && {
              cellDataType: false,
              valueFormatter: (params) => {
                if (params.value === true) {
                  return "true";
                } else if (params.value === false) {
                  return "false";
                } else {
                  return "";
                }
              },
            }),
          };
        }),
      ];

      const hierarchy = {};
      for (let i = 0; i < pivotColumns.pivot_columns.length; i++) {
        const split = pivotColumns.pivot_columns[i].split("__");
        if (!(split[0] in hierarchy)) {
          hierarchy[split[0]] = [];
        }
        hierarchy[split[0]].push({
          label: split[1],
          value: pivotColumns.pivot_columns[i],
        });
      }

      for (const parentHeader in hierarchy) {
        const colName = systemNameToDisplayNameMap[parentHeader];
        const datatype = dataTypesById[columnTypeMap[parentHeader]];
        const pivotColumnAggType = currentAppliedPivot["aggfunc"][parentHeader];
        // While displaying Pivot Data, datatype of column in pivot data will not necessarily be same as datatype of column in datasheet
        // example: In string type column, if we apply pivot with agg func as count or distinct count then datatype of column in pivot data will be integer and not string
        cols.push({
          headerName: currentAppliedPivot["aggfunc"][parentHeader]
            ? `${
                PIVOT_AGG_MAP[currentAppliedPivot["aggfunc"][parentHeader]]
              } of ${colName}`
            : colName,
          children: hierarchy[parentHeader].map((child) => {
            return {
              field: child["value"],
              headerName: child["label"],
              sortable: hasPermissions(RBAC_ROLES.MANAGE_DATABOOK),
              // Right aligning integer and percentage fields and pivot data having agg func count and distinct count
              ...((datatype === "Integer" ||
                datatype === "Percentage" ||
                pivotColumnAggType == "count" ||
                pivotColumnAggType == "nunique") && {
                type: "rightAligned",
              }),
              // When column datatype is Date with agg function "max" or "min" only then data will be in date type
              // else in every agg func case(count, distinct count, sum ...) , data will be in numComparator comparable type
              comparator:
                datatype == "Date" &&
                (pivotColumnAggType == "min" || pivotColumnAggType == "max")
                  ? dateComparator
                  : numComparator,
              valueFormatter: (params) => {
                if (params?.value) {
                  if (datatype === "Integer") {
                    return formatCurrencyWrapper(params.value, {
                      decimalPlaces: 2,
                      truncate: true,
                    });
                  } else if (
                    // only if date dataype columm has agg func as min or max then only it will be in date format else it will be in num format
                    datatype === "Date" &&
                    (pivotColumnAggType == "min" || pivotColumnAggType == "max")
                  ) {
                    return moment
                      .utc(params.value.toString())
                      .format(COMMON_MOMENT_DATE_FORMAT);
                  }
                  return params.value;
                }

                return null;
              },
            };
          }),
        });
      }
      setColumns(cols);
    }
  }, [
    pivotColumns,
    isPivotMode,
    getDatasheetConfig,
    systemNameToDisplayNameMap,
  ]);

  // The column definitions for AgGrid are set here
  useEffect(() => {
    if (
      !currentIsPivotMode &&
      getDatasheetConfig &&
      getDatasheetConfig.variablesWithPermission &&
      !isEmpty(dataTypesById)
    ) {
      const datasheetVariables = getDatasheetConfig.variablesWithPermission;

      const colDefArr = [
        {
          lockPosition: "left",
          suppressMenu: true,
          field: "serialNumber",
          headerName: "",
          suppressColumnsToolPanel: true,
          width: 80,
          maxWidth: 80,
          minWidth: 60,
          suppressSizeToFit: true,
          cellStyle: {
            paddingRight: "16px",
            paddingLeft: "16px",
          },
          suppressAutoSize: true,
        },
        {
          resizable: false,
          minWidth: 50,
          maxWidth: 100,
          field: "edit",
          headerName: "",
          lockPosition: "left",
          suppressMovable: true,
          suppressMenu: true,
          sortable: false,
          suppressColumnsToolPanel: true,
          suppressAutoSize: true,
          cellStyle: { paddingRight: "16px", paddingLeft: "16px" },
          cellClassRules: {
            cellStyle: () => {
              return true;
            },
          },
          cellRenderer: (params) => {
            if (
              dataOrigin != DATA_ORIGIN.COMMISSION_OBJECT &&
              dataOrigin != DATA_ORIGIN.INTER_OBJECT &&
              dataOrigin != DATA_ORIGIN.FORECAST_OBJECT &&
              dataOrigin != DATA_ORIGIN.INTER_FORECAST_OBJECT
            ) {
              if (
                !hasPermissions(
                  [
                    RBAC_ROLES.MANAGE_DATABOOK,
                    RBAC_ROLES.MANAGE_DATASHEETPERMISSIONS,
                    RBAC_ROLES.MANAGE_DATASHEETADJUSTMENTS,
                  ],
                  false
                )
              )
                return <></>;

              const record = params.data;
              const row_key = record?.row_key;
              const isAdjusted = row_key && rowKeysSet.has(row_key);
              let isGlobalAdjustment = false;
              const newRecord = { ...record };
              if (isAdjusted) {
                newRecord.is_adjusted = true;
                newRecord.adjustment_details =
                  adjustmentDetailsMap.get(row_key);
                newRecord.original_values = originalValuesMap.get(row_key);
                try {
                  isGlobalAdjustment = !isEmpty(newRecord)
                    ? newRecord["adjustment_details"]["is_global"]
                    : false;
                } catch (err) {
                  newRecord.adjustment_details = null;
                  newRecord.original_values = null;
                }
              } else {
                newRecord.adjustment_details = null;
                newRecord.original_values = null;
              }
              return (
                <div
                  className="text-center adjust-record-icon flex items-center cursor-pointer h-full w-max"
                  onClick={() => {
                    setTableState({
                      ...tableState,
                      [datasheetId]: params.api.getColumnState(),
                    });
                  }}
                >
                  {isAdjusted ? (
                    <AdjustedIcon
                      selectedRecord={newRecord}
                      isGlobalAdjustment={isGlobalAdjustment}
                    />
                  ) : (
                    hasPermissions(RBAC_ROLES.MANAGE_DATASHEETADJUSTMENTS) &&
                    !isDefaultDashboardDatabook && (
                      <AdjustEditIcon selectedRecord={newRecord} />
                    )
                  )}
                </div>
              );
            } else {
              return <></>;
            }
          },
        },
        ...datasheetVariables.map((var_obj) => {
          const isCalculatedField =
            var_obj?.fieldOrder > 0 || var_obj?.metaData?.infix != null;
          const datatype = dataTypesById[columnTypeMap[var_obj.systemName]];
          return {
            field: var_obj.systemName,
            headerName: var_obj.displayName,
            minWidth: 200,
            tooltipComponent: EverAgCellTooltip,
            tooltipValueGetter: (params) => params.value,
            cellClass: "truncate !inline-block",
            sortable: hasPermissions(RBAC_ROLES.MANAGE_DATABOOK),
            comparator: comparator,
            headerComponentParams: {
              datasheetTable: true,
              ...(isCalculatedField
                ? {
                    customMenuIcon: (
                      <EverTooltip
                        title="Formula Field. Click to view formula."
                        placement="bottom"
                        overlayClassName="dataSheetTooltip"
                        mouseEnterDelay={1.5}
                      >
                        <div
                          className="flex items-center m-0 mr-1 border border-solid border-ever-base-400 rounded-sm gap-2 p-1"
                          onClick={(event) => {
                            event.stopPropagation();
                            setSelectedSystemName(var_obj.systemName);
                            setIsViewFormulaVisible(true);
                          }}
                        >
                          <FunctionIcon className="w-4 h-4" />
                          <div
                            className={twMerge(
                              "h-5 w-5 rounded-sm flex items-center justify-center",
                              dataTypeColorMap[datatype]
                            )}
                          >
                            <AgGridIconsMap
                              className="w-4 h-4"
                              name={datatype}
                            />
                          </div>
                        </div>
                      </EverTooltip>
                    ),
                  }
                : { menuIcon: datatype }),
            },
            ...(datatype === "Percentage" && {
              comparator: numComparator,
              type: "rightAligned",
            }),
            ...(datatype === "Integer" && {
              comparator: numComparator,
              type: "rightAligned",
              minWidth: 150,
              tooltipValueGetter: (params) =>
                formatCurrencyWrapper(params.value, {
                  decimalPlaces: 6,
                  truncate: true,
                }),
              cellRenderer: (params) => {
                return (
                  <div data-testid={`pt-${params.value}`}>
                    {formatCurrencyWrapper(params.value)}
                  </div>
                );
              },
            }),
            ...(datatype === "Date" && {
              comparator: dateComparator,
              valueFormatter: formatCellDate,
            }),
            ...(datatype === "Boolean" && {
              cellDataType: false,
              valueFormatter: (params) => {
                if (params.value === true) {
                  return "true";
                } else if (params.value === false) {
                  return "false";
                } else {
                  return "";
                }
              },
            }),
          };
        }),
      ];
      setColumns(colDefArr);
    }
  }, [
    rowKeysSet,
    getDatasheetConfig,
    dataTypesById,
    modalVisible,
    drawerVisible,
    adjustmentData,
    isPivotMode,
    systemNameToDisplayNameMap,
  ]);

  // The column names to be displayed in the customize columns & the filter drawer are set here
  useEffect(() => {
    if (
      getDatasheetConfig &&
      getDatasheetConfig.variables &&
      !isEmpty(dataTypesById)
    ) {
      const colOptions = getDatasheetConfig.variables.map((var_obj) => {
        return {
          label: var_obj.displayName,
          value: var_obj.systemName,
          dataType: dataTypesById[columnTypeMap[var_obj.systemName]],
        };
      });
      if (!isEqual(columnOptions, colOptions)) setColumnOptions(colOptions);
    }
  }, [getDatasheetConfig, dataTypesById]);

  const AdjustEditIcon = (props) => {
    const { selectedRecord } = props;
    return (
      <EverTooltip title="Adjust Record" overlayClassName="dataSheetTooltip">
        {/* <Button
          onClick={() => {
            setDrawerVisible(true);
            //setPopoverVisible(false);
            setSelectedRecord(selectedRecord);
          }}
          type="link datasheetPencilVisibility"
          style={{ height: "100%" }}
        >
          <EverstageIcon icon="edit" fixed style={{ color: "#2465ff" }} />
        </Button> */}
        <EditIcon
          onClick={() => {
            setDrawerVisible(true);
            //setPopoverVisible(false);
            setSelectedRecord(selectedRecord);
          }}
          className="w-4 h-4 text-ever-base-content-mid cursor-pointer"
        />
      </EverTooltip>
    );
  };
  const AdjustedIcon = (props) => {
    const { selectedRecord, isGlobalAdjustment } = props;

    const adjustmentAction = () => {
      setSelectedRecord(selectedRecord);
    };

    return (
      <AdjustmentActions
        handleViewClick={handleViewClick}
        handleRevertClick={handleRevertClick}
        handleEditClick={handleEditClick}
        adjustmentAction={adjustmentAction}
        isGlobalAdjustment={isGlobalAdjustment}
        modalVisible={modalVisible}
        isEditAdjustment={drawerVisible}
      />
    );
  };
  const handleEditClick = () => {
    setDrawerVisible(true);
    //setPopoverVisible(!popoverVisible);
  };
  const handleViewClick = (event) => {
    event.stopPropagation();
    document.getElementsByTagName("body")[0].focus();
    setAdjustmentTitle(t("VIEW_ADJUSTMENT"));
    //  setPopoverVisible(!popoverVisible);
    setModalVisible(true);
    setEnableRevert(false);
  };

  const handleRevertClick = (event) => {
    event.stopPropagation();
    setAdjustmentTitle(t("REVERT_ADJUSTMENT"));
    setModalVisible(true);
    //   setPopoverVisible(!popoverVisible);
    setEnableRevert(true);
  };

  const isFilterEmpty = () => {
    return (
      isEmpty(filters[datasheetId]) ||
      (filters[datasheetId].length == 1 &&
        isEqual(filters[datasheetId][0], DEFAULT_FILTER_VALUE))
    );
  };

  const onExportCsv = (applyAdj) => {
    const data = {
      databookId: databookId,
      datasheetId: datasheetId,
      applyAdjustments: applyAdj,
      filters: filters[datasheetId].filter((filter) => filter["is_valid"]),
      pivots:
        currentIsPivotMode && currentAppliedPivot["is_valid"]
          ? currentAppliedPivot
          : {},
    };
    const exportCSVToast = toast.custom(
      () => (
        <EverHotToastMessage
          type="loading"
          description={"Exporting datasheet..."}
        />
      ),
      { position: "top-center", duration: Infinity }
    );
    // message.loading({
    //   content: "Exporting datasheet...",
    //   key: "loading",
    //   duration: 0,
    //   icon: <EverProgress type="spinner" inline={true} size={40} />,
    // });
    exportDatasheetAsCsv(data, accessToken)
      .then((response) => {
        if (response.ok) {
          return response;
        } else {
          throw new Error("Request failed");
        }
      })
      .then((response) => response.blob())
      .then((blobby) => {
        const objectUrl = window.URL.createObjectURL(blobby);
        const anchor = document.createElement("a");
        anchor.href = objectUrl;
        anchor.download = `${datasheetName}.csv`;
        anchor.click();

        window.URL.revokeObjectURL(objectUrl);
        toast.remove(exportCSVToast);
        // message.destroy("loading");
        toast.custom(
          () => (
            <EverHotToastMessage
              type="success"
              description={"Downloaded Successfully!!"}
            />
          ),
          { position: "top-center" }
        );
        //message.success("Downloaded Successfully!!");
      })
      .catch((error) => {
        toast.remove(exportCSVToast);
        // message.destroy("loading");
        toast.custom(
          () => (
            <EverHotToastMessage
              type="error"
              description={"Error while exporting datasheet"}
            />
          ),
          { position: "top-center" }
        );
        //message.error("Error while exporting datasheet");
        console.log(error.message);
      });
  };

  const saveView = () => {
    return new Promise((settled) => {
      const savingFiltersView = toast.custom(
        () => (
          <EverHotToastMessage type="loading" description={"Saving View..."} />
        ),
        { position: "top-center", duration: Infinity }
      );
      // message.loading({
      //   content: "Saving View...",
      //   key: "savingFiltersView",
      //   duration: 0,
      //   icon: <EverProgress type="spinner" inline={true} size={40} />,
      // });
      let filterList = [];
      if (saveFilterType === FILTER_CONFIG_TYPE.FILTER) {
        filterList = filters[datasheetId].map((filter) => {
          return pick(filter, [
            "type",
            "col_name",
            "operator",
            "data_type",
            "value",
            "is_valid",
            "col_display_name",
            "needs_operand",
            "multi_valued",
          ]);
        });
      }

      const data = {
        databookId: databookId,
        datasheetId: datasheetId,
        filterName: activeViewName,
        filterList:
          saveFilterType === FILTER_CONFIG_TYPE.FILTER ? filterList : [],
        pivotObject:
          saveFilterType === FILTER_CONFIG_TYPE.PIVOT ? newPivot : {},
        configType: saveFilterType,
      };
      saveDatasheetFilter(data, accessToken)
        .then((response) => {
          if (response.ok) {
            setShowSaveFilterViewDialog(false);
            toast.remove(savingFiltersView);
            //message.destroy("savingFiltersView");
            toast.custom(
              () => (
                <EverHotToastMessage
                  type="success"
                  description={"View saved Successfully!"}
                />
              ),
              { position: "top-center" }
            );
            // message.success({
            //   content: "View saved Successfully!",
            //   key: "savedFiltersView",
            // });
            refetchDatasheetFilters();
            savedViewsAnalytics(true);
            return response.json();
          } else {
            response.json().then((res) => {
              if (res.status === "PIVOT_NAME_ALREADY_EXIST")
                setErrorMessage("View name already exists!");
              else {
                setShowSaveFilterViewDialog(false);
                toast.custom(
                  () => (
                    <EverHotToastMessage
                      type="error"
                      description={"Error while saving view!"}
                    />
                  ),
                  { position: "top-center" }
                );
                //message.error("Error while saving view!");
              }
              setActiveViewName("");
            });
          }

          return null;
        })
        .catch((error) => {
          toast.custom(
            () => (
              <EverHotToastMessage
                type="error"
                description={"Error while saving view!"}
              />
            ),
            { position: "top-center" }
          );
          // message.error("Error while saving view!");
          console.log(error.message);
        })
        .finally(() => {
          toast.remove(savingFiltersView);
          //message.destroy("savingFiltersView");
          settled();
        });
    });
  };

  const updatePivotView = (index, updateData) => {
    toast.custom(
      () => (
        <EverHotToastMessage
          type="loading"
          description={"Updating pivots..."}
        />
      ),
      { position: "top-center", duration: Infinity }
    );
    // message.loading({
    //   content: "Updating pivots...",
    //   key: "updatingFiltersView",
    //   duration: 0,
    //   icon: <EverProgress type="spinner" inline={true} size={40} />,
    // });
    let data = {};
    if (views[index]) {
      data = views[index];
    } else {
      data = {
        filterId: activeViewId,
        filterName: activeViewName,
        pivotObject: updateData,
        configType: FILTER_CONFIG_TYPE.PIVOT,
      };
    }
    updateView(data);
  };

  const updateFilterView = (index) => {
    toast.remove();
    const updatingFiltersView = toast.custom(
      () => (
        <EverHotToastMessage
          type="loading"
          description={"Updating filters..."}
        />
      ),
      { position: "top-center", duration: Infinity }
    );
    // message.loading({
    //   content: "Updating filters...",
    //   key: "updatingFiltersView",
    //   duration: 0,
    //   icon: <EverProgress type="spinner" inline={true} size={40} />,
    // });
    let data;

    if (views[index]) {
      data = views[index];
    } else {
      const _filterList = filters[datasheetId].map((filter) => {
        return pick(filter, [
          "type",
          "col_name",
          "operator",
          "data_type",
          "value",
          "is_valid",
          "col_display_name",
          "needs_operand",
          "multi_valued",
        ]);
      });
      data = {
        filterId: activeViewId,
        filterName: activeViewName,
        filterList: _filterList,
      };
    }
    data["configType"] = FILTER_CONFIG_TYPE.FILTER;
    updateView(data, updatingFiltersView);
  };

  const updateView = (data, updatingFiltersView) => {
    data["datasheetId"] = datasheetId;
    data["databookId"] = databookId;
    updateDatasheetFilter(data, accessToken)
      .then((response) => {
        setShowSaveFilterViewDialog(false);
        if (response.ok) {
          toast.remove(updatingFiltersView);
          //message.destroy("updatingFiltersView");
          toast.custom(
            () => (
              <EverHotToastMessage
                type="success"
                description={"View updated Successfully!"}
              />
            ),
            { position: "top-center" }
          );
          // message.success({
          //   content: "View updated Successfully!",
          //   key: "updatedFiltersView",
          // });
          refetchDatasheetFilters();
          return response.json();
        } else {
          response.json().then((data) => {
            toast.custom(
              () => (
                <EverHotToastMessage
                  type="error"
                  description={"Error while updating filters!"}
                />
              ),
              { position: "top-center" }
            );
            toast.custom(
              () => (
                <EverHotToastMessage type="error" description={data.error} />
              ),
              { position: "top-center" }
            );
            // message.error("Error while updating filters!");
            // message.error(data.error);
          });
        }

        return null;
      })
      .catch((error) => {
        toast.custom(
          () => (
            <EverHotToastMessage
              type="error"
              description={"Error while updating filters!"}
            />
          ),
          { position: "top-center" }
        );
        // message.error("Error while updating filters!");
        console.log(error.message);
      })
      .finally(() => {
        toast.remove(updatingFiltersView);
        // message.destroy("updatingFiltersView");
      });
  };

  const onViewNameChange = (e) => {
    setActiveViewName(e.target.value);
  };

  const onCloseModal = () => {
    setShowDeleteFilterViewDialog(false);
    setShowSaveFilterViewDialog(false);
    setShowViewMenu(false);
  };

  const handleMenuClick = (e) => {
    sendAnalyticsEvent(accessToken, ANALYTICS_EVENTS.EXPORT_CSV, {
      [ANALYTICS_PROPERTIES.EXPORT_TYPE]:
        e.key == 1 ? t("PRE_ADJUSTMENT") : t("POST_ADJUSTMENT"),
      [ANALYTICS_PROPERTIES.DATASHEET_NAME]: datasheetName,
    });
    if (e.key == 1) {
      onExportCsv(false);
    } else if (e.key == 2) {
      onExportCsv(true);
    }
  };

  const menu = (
    <Menu onClick={handleMenuClick}>
      <Menu.Item key="1">{t("PRE_ADJUSTMENT")}</Menu.Item>
      <Menu.Item key="2">{t("POST_ADJUSTMENT")}</Menu.Item>
    </Menu>
  );

  const resetFilterToDefault = () => {
    setActiveViewName("");
    setActiveViewId("");
    setActiveViewType("");
  };

  const deleteView = () => {
    if (deleteActiveView) {
      resetFilterToDefault();
      setAppliedFilters({ ...appliedFilters, [datasheetId]: [] });
      setPivotMode({ ...isPivotMode, [datasheetId]: false });
      setAppliedPivot({ ...appliedPivot, [datasheetId]: {} });
      setOnLoad({ ...onLoad, [datasheetId]: false });
      setFilters({ ...filters, [datasheetId]: DEFAULT_FILTER_LIST });
    }
    setShowViewMenu(false);
    const deletingFiltersView = toast.custom(
      () => (
        <EverHotToastMessage type="loading" description={"Deleting view..."} />
      ),
      { position: "top-center", duration: Infinity }
    );
    // message.loading({
    //   content: "Deleting view...",
    //   key: "deletingFiltersView",
    //   duration: 0,
    //   icon: <EverProgress type="spinner" inline={true} size={40} />,
    // });
    const data = {
      filterId: viewToBeDeleted.filterId,
      filterName: viewToBeDeleted.filterName,
      configType: viewToBeDeleted?.configType,
      datasheetId: datasheetId,
    };

    deleteDatabookFilter(data, accessToken)
      .then((response) => {
        setShowDeleteFilterViewDialog(false);
        if (response.ok) {
          const _response = response.json();
          toast.remove(deletingFiltersView);
          //message.destroy("deletingFiltersView");
          toast.custom(
            () => (
              <EverHotToastMessage
                type="success"
                description={"View deleted Successfully!"}
              />
            ),
            { position: "top-center" }
          );
          // message.success({
          //   content: "View deleted Successfully!",
          //   key: "deletedFiltersView",
          // });
          setViewToBeDeleted({});
          refetchDatasheetFilters();
          setFilters({ ...filters, [datasheetId]: DEFAULT_FILTER_LIST });
          savedViewsAnalytics(false);
          return _response;
        } else {
          response.json().then(() => {
            toast.custom(
              () => (
                <EverHotToastMessage
                  type="error"
                  description={"Error while updating view!"}
                />
              ),
              { position: "top-center" }
            );
            // message.error("Error while updating view!");
          });
        }
        return null;
      })
      .catch((error) => {
        toast.custom(
          () => (
            <EverHotToastMessage
              type="error"
              description={"Error while updating view!"}
            />
          ),
          { position: "top-center" }
        );
        // message.error("Error while updating view!");
        console.log(error.message);
      })
      .finally(() => {
        toast.remove(deletingFiltersView);
        //message.destroy("deletingFiltersView");
      });
  };

  const addDefaultNewFilter = () => {
    setFilters({
      ...filters,
      [datasheetId]: [...filters[datasheetId], DEFAULT_FILTER_VALUE],
    });
  };

  const setActiveView = (view) => {
    setActiveViewName(view["filterName"]);
    setFilters({ ...filters, [datasheetId]: cloneDeep(view["filterList"]) });
    setActiveViewId(view["filterId"]);
    setActiveViewType(FILTER_CONFIG_TYPE.FILTER);
    updateAppliedFilters(view["filterList"]);
    setPivotMode({ ...isPivotMode, [datasheetId]: false });
    setAppliedPivot({ ...appliedPivot, [datasheetId]: {} });
  };

  const setActivePivotView = (view) => {
    setFilters({ ...filters, [datasheetId]: DEFAULT_FILTER_LIST });
    setAppliedFilters({ ...appliedFilters, [datasheetId]: [] });
    setActiveViewName(view["filterName"]);
    setActiveViewId(view["filterId"]);
    setActiveViewType(FILTER_CONFIG_TYPE.PIVOT);
    updateAppliedPivots(view["pivotObject"]);
  };

  const generateDatasheetData = (isForceInvalidate = false) => {
    const submitGenerateRequest = toast.custom(
      () => (
        <EverHotToastMessage
          type="loading"
          description={"Submitting generate datasheet task..."}
        />
      ),
      { position: "top-center", duration: Infinity }
    );
    // message.loading({
    //   content: "Submitting generate datasheet task...",
    //   key: "submitGenerateRequest",
    //   duration: 0,
    //   icon: <EverProgress type="spinner" inline={true} size={40} />,
    // });

    const requestOptions = {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${accessToken}`,
      },
      body: JSON.stringify({
        databook_id: databookId,
        datasheet_id: datasheetId,
        is_report_data_stale: isReportDataStale,
        is_force_invalidate: isForceInvalidate,
        is_triggered_from_datasheet_ui: true,
      }),
    };
    setSubmittingReq(true);
    fetch("/commission_engine/generate_datasheet_data", requestOptions)
      .then((res) => {
        setSubmittingReq(false);
        toast.remove(submitGenerateRequest);
        //message.destroy("submitGenerateRequest");
        if (res.ok) {
          // start polling and set sync in progress to true
          toast.custom(
            () => (
              <EverHotToastMessage
                type="success"
                description={"Datasheet sync request has been submitted"}
              />
            ),
            { position: "top-center" }
          );
          //message.success("Datasheet sync request has been submitted");
          datasheetStartPolling(DATASHEET_POLLING_RATE);
          setIsLoading({ ...isLoading, [datasheetId]: true });
          setIsSheetSyncInProgress({
            ...isSheetSyncInProgress,
            [datasheetId]: true,
          });

          console.log("Submitting datasheet sync request");
        } else {
          res.json().then((error) => {
            if (res.status === 400 && error.toLowerCase() === "locked") {
              toast.custom(
                () => (
                  <EverHotToastMessage
                    type="error"
                    description={
                      "Another process is trying to update datasheet. Please wait or try again later"
                    }
                  />
                ),
                { position: "top-center" }
              );
              // message.error(
              //   "Another process is trying to update datasheet. Please wait or try again later"
              // );
            } else if (
              res.status === 400 &&
              error.toLowerCase() === "e2e_running"
            ) {
              toast.custom(
                () => (
                  <EverHotToastMessage
                    type="info"
                    description={
                      "Connector sync is in progress in the background. Please try later."
                    }
                  />
                ),
                { position: "top-center" }
              );
              // message.info(
              //   "Connector sync is in progress in the background. Please try later."
              // );
            } else if (
              res.status === 429 &&
              error.toLowerCase() === "report_locked"
            ) {
              toast.custom(
                () => (
                  <EverHotToastMessage
                    type="info"
                    description={
                      "Report Update triggered by another process. Please wait or try again later to see the update data"
                    }
                  />
                ),
                { position: "top-center" }
              );
              // message.info(
              //   "Report Update triggered by another process. Please wait or try again later to see the update data"
              // );
            } else if (
              res.status === 403 &&
              error.toLowerCase() === "global_sync_false"
            ) {
              toast.custom(
                () => (
                  <EverHotToastMessage
                    type="info"
                    description={
                      "We’re updating Everstage to improve your experience, so sync is paused for now. You can try again in about an hour."
                    }
                  />
                ),
                { position: "top-center" }
              );
              // message.error(
              //   "Datasheet sync job cannot be submitted as the global sync has been disabled"
              // );
            } else {
              toast.custom(
                () => (
                  <EverHotToastMessage
                    type="error"
                    description={"Datasheet Data generation failed"}
                  />
                ),
                { position: "top-center" }
              );
              // message.error("Datasheet Data generation failed");
            }

            setIsLoading({ ...isLoading, [datasheetId]: false });
            setIsSheetSyncInProgress({
              ...isSheetSyncInProgress,
              [datasheetId]: false,
            });
          });
        }
      })
      .catch(() => setSubmittingReq(false));
  };

  const debouncedGenerateData = useCallback(
    debounce(
      (isForceInvalidate = false) => generateDatasheetData(isForceInvalidate),
      500
    ),
    [generateDatasheetData]
  );

  function savedViewsAnalytics(newViewAdded) {
    sendAnalyticsEvent(
      accessToken,
      ANALYTICS_EVENTS.COUNT_OF_SAVED_VIEWS_PER_SHEET,
      {
        [ANALYTICS_PROPERTIES.DATASHEET_NAME]: datasheetName,
        [ANALYTICS_PROPERTIES.COUNT_OF_SAVED_VIEWS]: newViewAdded
          ? views.length + 1
          : views.length - 1,
      }
    );
  }

  return (
    <>
      <div className="absolute left-[calc(50%-288px)] top-2">
        {genrateDataReason && !isStaleLoading && !currentIsLoading && (
          <EverHotToastBanner
            type="warning"
            className={classNames("max-w-[650px] h-auto p-2", {
              "mt-[60px]": isLoggedInAsUser, // added top-margin if impersonation banner is present.
            })}
            description={genrateDataReason}
          />
        )}
      </div>
      <div className="datasheet-data h-full">
        <DraggableTabs
          type="editable-card"
          className={`${styles.datasheetTab} datasheetTab `} // for global selector reference
          onEdit={onEdited}
          addIcon={
            <div className="bg-ever-primary-lite w-6 h-6 rounded-md flex items-center justify-center">
              <PlusIcon className="w-4 h-4 text-ever-primary-pressed" />
            </div>
          }
          hideAdd={hideAdded}
          onChange={onChanged}
          activeKey={datasheetId}
          animated={{ inkBar: true }}
          renderTabBarProps={renderTabBarPropsVal}
          destroyInactiveTabPane={true}
          isDraggable={hasPermissions(RBAC_ROLES.MANAGE_DATABOOK)}
          onTabDropCompleted={onTabDropCompletedVal}
          updateData={
            <div className="flex items-center gap-2">
              {hasPermissions(
                [
                  RBAC_ROLES.MANAGE_DATABOOK,
                  RBAC_ROLES.MANAGE_DATASHEETADJUSTMENTS,
                ],
                false
              ) &&
                !currentIsLoading &&
                !isStaleLoading &&
                !loading &&
                !currentIsStaleError &&
                buttonText?.text == "UPDATE_DATA" && (
                  <EverButton
                    type="filled"
                    color="primary"
                    onClick={() => debouncedGenerateData()}
                    disabled={submittingReq}
                  >
                    {ButtonTextObj[buttonText?.text]}
                  </EverButton>
                )}
            </div>
          }
        >
          {tabPanes.map((x) => (
            <EverTabs.TabPane
              tab={
                <div className="flex items-center gap-2">
                  <ArchivedStatusIndicator
                    datasheetId={x.id}
                    archivedStatuses={archivedStatuses}
                  />
                  <EverTg.SubHeading4
                    className={`${
                      x.id === datasheetId
                        ? "text-ever-primary"
                        : "text-ever-base-content-mid"
                    }`}
                  >
                    {x.name}
                  </EverTg.SubHeading4>
                </div>
              }
              key={x.id}
              defaultActiveKey={x.id}
              closeIcon={
                isDefaultDashboardDatabook ? (
                  <> </>
                ) : (
                  <RBACProtectedComponent
                    permissionId={[
                      RBAC_ROLES.MANAGE_DATABOOK,
                      RBAC_ROLES.MANAGE_DATASHEETPERMISSIONS,
                      RBAC_ROLES.DELETE_DATASHEET,
                    ]}
                  >
                    <Dropdown overlay={dropDownMenu} trigger={["click"]}>
                      <ChevronDownIcon className="text-ever-base-content-mid w-4 stroke-2 h-4" />
                    </Dropdown>
                  </RBACProtectedComponent>
                )
              }
            >
              <>
                {!currentIsLoading &&
                  buttonText?.text == "GENERATE_DATASHEET" &&
                  (hasPermissions(
                    [
                      RBAC_ROLES.MANAGE_DATABOOK,
                      RBAC_ROLES.MANAGE_DATASHEETADJUSTMENTS,
                    ],
                    false
                  ) ? (
                    isStaleLoading ? (
                      <div className="w-full h-full flex justify-center items-center">
                        <EverLoader.SpinnerLottie className="w-20" />
                      </div>
                    ) : (
                      <RegenerateDataMessage
                        buttonText={buttonText}
                        hasPermissions={hasPermissions}
                        debouncedGenerateData={debouncedGenerateData}
                        ButtonTextObj={ButtonTextObj}
                        submittingReq={submittingReq}
                      />
                    )
                  ) : (
                    <RegenerateDataNoPermsMessage />
                  ))}

                {currentIsLoading ? <DataGenerationInProgress /> : null}

                {/* Table */}

                {!currentIsLoading &&
                  (buttonText?.text == "UPDATE_DATA" || buttonText == null) &&
                  (currentIsStaleError && !isStaleLoading && !loading ? (
                    <StaleDataErrorDisplay staleError={staleError} />
                  ) : isStaleLoading || loading ? (
                    <div className="w-full h-full flex justify-center items-center">
                      <EverLoader.SpinnerLottie className="w-20" />
                    </div>
                  ) : (
                    <>
                      <DatasheetArchivedStatus datasheetId={datasheetId} />

                      <DatasheetFilterDrawer
                        showFiltersDrawer={showFiltersDrawer}
                        filters={filters}
                        isFilterListValid={isFilterListValid}
                        columnOptions={columnOptions}
                        isFilterColumnInDatasheetColumns={
                          isFilterColumnInDatasheetColumns
                        }
                        setFilters={setFilters}
                        setShowFiltersDrawer={setShowFiltersDrawer}
                        setAppliedFilters={setAppliedFilters}
                        dropDownOperators={dropDownOperators}
                        addDefaultNewFilter={addDefaultNewFilter}
                        activeViewId={activeViewId}
                        updateFilterView={updateFilterView}
                        setShowSaveFilterViewDialog={
                          setShowSaveFilterViewDialog
                        }
                        resetFilterToDefault={resetFilterToDefault}
                        setSaveFilterType={setSaveFilterType}
                        activeViewType={activeViewType}
                        appliedFilters={appliedFilters}
                        datasheetId={datasheetId}
                        setPivotMode={setPivotMode}
                        isPivotMode={isPivotMode}
                        setAppliedPivot={setAppliedPivot}
                        appliedPivot={appliedPivot}
                        defaultFilterList={DEFAULT_FILTER_LIST}
                        onLoad={onLoad}
                        setOnLoad={setOnLoad}
                      />
                      <div className="flex items-center bg-ever-base-100 rounded-lg px-4 py-2">
                        <div className="grow items-center flex gap-4">
                          <DatasheetFilterViewMenu
                            showViewMenu={showViewMenu}
                            setViewSearchText={setViewSearchText}
                            viewSearchText={viewSearchText}
                            views={views}
                            activeViewName={activeViewName}
                            activeViewType={activeViewType}
                            setViewToBeDeleted={setViewToBeDeleted}
                            setShowDeleteFilterViewDialog={
                              setShowDeleteFilterViewDialog
                            }
                            setShowViewMenu={setShowViewMenu}
                            setActiveView={setActiveView}
                            setActivePivotView={setActivePivotView}
                            setViews={setViews}
                            resetFilterToDefault={resetFilterToDefault}
                            activeViewId={activeViewId}
                            setActiveViewName={setActiveViewName}
                            updateFilterView={updateFilterView}
                            updatePivotView={updatePivotView}
                            datasheetId={datasheetId}
                            appliedFilters={appliedFilters}
                            setAppliedFilters={setAppliedFilters}
                            setOnLoad={setOnLoad}
                            onLoad={onLoad}
                            setPivotMode={setPivotMode}
                            isPivotMode={isPivotMode}
                            setAppliedPivot={setAppliedPivot}
                            appliedPivot={appliedPivot}
                            setFilters={setFilters}
                            filters={filters}
                            defaultFilterList={DEFAULT_FILTER_LIST}
                            setDeleteActiveView={setDeleteActiveView}
                          />
                          <DatasheetFilterActions
                            appliedFilters={appliedFilters}
                            appliedPivot={appliedPivot}
                            dropDownOperators={dropDownOperators}
                            setShowFiltersDrawer={setShowFiltersDrawer}
                            resetFilterToDefault={resetFilterToDefault}
                            setPivotMode={setPivotMode}
                            datasheetId={datasheetId}
                            setAppliedFilters={setAppliedFilters}
                            isPivotMode={isPivotMode}
                            setAppliedPivot={setAppliedPivot}
                            filters={filters}
                            setFilters={setFilters}
                            defaultFilterList={DEFAULT_FILTER_LIST}
                            onLoad={onLoad}
                            setOnLoad={setOnLoad}
                          />
                        </div>
                        <div className="flex items-center gap-2">
                          <Divider type="vertical h-8 bg-ever-base-400 " />
                          {hasPermissions(
                            [
                              RBAC_ROLES.MANAGE_DATABOOK,
                              RBAC_ROLES.MANAGE_DATASHEETPERMISSIONS,
                              RBAC_ROLES.MANAGE_DATASHEETADJUSTMENTS,
                            ],
                            false
                          ) &&
                            !isDefaultDashboardDatabook && (
                              <AdjustmentsDisplay
                                adjustmentsCount={adjustmentsCount}
                                setAdjustmentVisible={setAdjustmentVisible}
                              />
                            )}
                          {hasPermissions(RBAC_ROLES.EXPORT_DATASHEET) && (
                            <ExportDatasheet
                              exportCsvTooltip={exportCsvTooltip}
                              menu={menu}
                              tableData={tableData}
                              dataError={dataError}
                            />
                          )}
                        </div>
                      </div>
                      <DatasheetFilterViewSaveModal
                        onCloseSaveFilterViewDialog={onCloseModal}
                        saveView={saveView}
                        onViewNameChange={onViewNameChange}
                        showSaveFilterViewDialog={showSaveFilterViewDialog}
                        errorMessage={errorMessage}
                      />
                      <DatasheetFilterViewDeleteModal
                        onCloseDeleteFilterViewDialog={onCloseModal}
                        deleteView={deleteView}
                        viewToBeDeleted={viewToBeDeleted}
                        showDeleteFilterViewDialog={showDeleteFilterViewDialog}
                      />

                      <DatasheetTable
                        genrateDataReason={genrateDataReason}
                        pageCount={Math.ceil(dataCount / currentPageSize)} //number of pages
                        pageSize={pageSize} //rows per page
                        totalRows={dataCount} //total rows
                        currentPage={currentPageNumber - 1} //current page number
                        setPageSize={setPageSize}
                        setCurrentPage={setPageNumber}
                        pageNumber={pageNumber}
                        lastUpdated={
                          <DatasheetLastGeneratedTime
                            databookId={databookId}
                            datasheetId={datasheetId}
                          />
                        }
                        columns={columns}
                        data={tableData}
                        refetch={refetch}
                        datasheetId={datasheetId}
                        saveSessionKd={saveSessionKd}
                        isStaleLoading={isStaleLoading}
                        tableState={tableState}
                        setTableState={setTableState}
                        isPivotMode={isPivotMode}
                        pivotParams={{
                          appliedPivot: appliedPivot,
                          setAppliedPivot,
                          setPivotMode,
                          isPivotMode: isPivotMode,
                          columnOptions,
                          setShowSaveFilterViewDialog,
                          setSaveFilterType,
                          setNewPivot,
                          activeViewId,
                          activeViewType,
                          updatePivotView,
                          columnTypeMap,
                          dataTypesById,
                          datasheetId,
                          onLoad,
                          setOnLoad,
                        }}
                      />

                      {dataError && (
                        <Alert
                          message="Error"
                          description={dataError}
                          type="error"
                          showIcon
                        />
                      )}
                      {hasPermissions(
                        [
                          RBAC_ROLES.MANAGE_DATABOOK,
                          RBAC_ROLES.MANAGE_DATASHEETADJUSTMENTS,
                          RBAC_ROLES.MANAGE_DATASHEETPERMISSIONS,
                        ],
                        false
                      ) &&
                        !isDefaultDashboardDatabook && (
                          <AdjustRecord
                            saveSessionKd={saveSessionKd}
                            adjustmentData={adjustmentData}
                            setAdjustmentsCount={setAdjustmentsCount}
                            setAdjustmentVisible={setAdjustmentVisible}
                            adjustmentVisible={adjustmentVisible}
                            store={store}
                            datasheetId={datasheetId}
                            handleViewClick={handleViewClick}
                            handleEditClick={handleEditClick}
                            handleRevertClick={handleRevertClick}
                            setSelectedRecord={setSelectedRecord}
                            modalVisible={modalVisible}
                            setModalVisible={setModalVisible}
                            adjustmentTitle={adjustmentTitle}
                            enableRevert={enableRevert}
                            selectedRecord={selectedRecord}
                            databookId={databookId}
                            refetch={refetch}
                            handleRevert={() => {
                              setSelectedRecord({});
                            }}
                            datasheetName={datasheetName}
                            setDrawerVisible={setDrawerVisible}
                            drawerVisible={drawerVisible}
                            handleCancel={() => {
                              setSelectedRecord({});
                            }}
                            getDatasheetConfig={getDatasheetConfig}
                            dsVariableStore={dsVariableStore}
                            setCallIsStale={setCallIsStale}
                          />
                        )}
                    </>
                  ))}
                {isViewFormulaVisible && (
                  <ViewFormulaVisible
                    datasheetId={datasheetId}
                    databookId={databookId}
                    systemName={selectedSystemName}
                    onClosePopup={() => {
                      setSelectedSystemName(null);
                      setIsViewFormulaVisible(false);
                    }}
                    onEditCalculatedField={(systemName) => {
                      setSelectedSystemName(null);
                      setIsViewFormulaVisible(false);
                      onEditCalculatedField(systemName);
                    }}
                  />
                )}
                {datasheetRestrictedAccessBanner && (
                  <LimitedVisibilityMessage
                    setDatasheetRestrictedAccessBanner={
                      setDatasheetRestrictedAccessBanner
                    }
                  />
                )}
              </>
            </EverTabs.TabPane>
          ))}
        </DraggableTabs>
      </div>
    </>
  );
});

export default DataSheetData;

async function fetchDatasheetLastGeneratedTime(
  accessToken,
  databookId,
  datasheetId
) {
  const requestOptions = {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${accessToken}`,
    },
  };
  const res = await fetch(
    `/spm/datasheet_last_generated_time?databook_id=${databookId}&datasheet_id=${datasheetId}`,
    requestOptions
  );
  const data = await res.json();
  if (!res.ok) {
    throw new Error("Failed to fetch datasheet last generated time");
  }
  return data?.lastGeneratedTime;
}

function DatasheetLastGeneratedTime({ databookId, datasheetId }) {
  const { accessToken } = useAuthStore();
  const { isFetching, data, isError } = useReactQuery(
    ["datasheetLastGeneratedTime", databookId, datasheetId],
    () => fetchDatasheetLastGeneratedTime(accessToken, databookId, datasheetId),
    {
      retry: false,
      refetchOnWindowFocus: false,
      enabled: !!databookId && !!datasheetId, // skip the query if databookId or datasheetId is null
    }
  );
  if (isFetching) {
    return <div>Last generated: Fetching...</div>;
  }
  if (!isError && data !== null && data !== undefined) {
    return <div>Last generated : {formatDateTime({ date: data })}</div>;
  }
  return "";
}

// Component for displaying the archived status indicator
const ArchivedStatusIndicator = ({ datasheetId, archivedStatuses }) => {
  if (!archivedStatuses[datasheetId]) {
    return null;
  }

  return (
    <EverTooltip title={ARCHIVED_DATASHEET_TOOLTIP}>
      <span className="inline-flex items-center justify-center p-0.5 rounded">
        <BoxIcon className="w-3.5 h-3.5 text-ever-base-content-mid" />
      </span>
    </EverTooltip>
  );
};

function DatasheetArchivedStatus({ datasheetId }) {
  const { accessToken } = useAuthStore();
  const { data, isError } = useReactQuery(
    ["datasheetArchivedStatus", datasheetId],
    () => fetchDatasheetArchivedStatus(accessToken, datasheetId),
    {
      retry: false,
      refetchOnWindowFocus: false,
      enabled: !!datasheetId, // skip the query if datasheetId is null
    }
  );
  if (!isError && data !== null && data !== undefined && data === true) {
    return (
      <div className="flex mb-4">
        <EverHotToastBanner
          className="shadow-none"
          type="info"
          description={ARCHIVED_DATASHEET_DESCRIPTION}
        ></EverHotToastBanner>
      </div>
    );
  }
  return null;
}

function AdjustmentsDisplay({ adjustmentsCount, setAdjustmentVisible }) {
  const { t } = useTranslation();
  return (
    <div className="border-x ">
      <EverTooltip
        overlayClassName="dataSheetTooltip"
        placement="top"
        title={`${adjustmentsCount} ${t("ADJUSTMENTS").toLowerCase()}`}
      >
        <EverButton.Icon
          onClick={() => {
            setAdjustmentVisible(true);
            //setPopoverVisible(false);
          }}
          size="small"
          color="base"
          type="ghost"
          className={"bg-ever-base-25"}
          icon={
            <LayoutAltIcon className="text-ever-base-content-mid w-5 h-5 hover:text-ever-base-content" />
          }
        ></EverButton.Icon>
      </EverTooltip>
    </div>
  );
}

function ExportDatasheet({ exportCsvTooltip, menu, tableData, dataError }) {
  return (
    <div className="self-center">
      <EverTooltip title={exportCsvTooltip}>
        <Dropdown overlay={menu} disabled={isEmpty(tableData) || dataError}>
          <EverButton
            size="small"
            color="base"
            type="ghost"
            className={"bg-ever-base-25"}
            prependIcon={
              <AlignTopArrowIcon className="w-5 h-5 text-ever-base-content" />
            }
            appendIcon={
              <ChevronDownIcon className="w-5 h-5 text-ever-base-content" />
            }
          >
            Export CSV
          </EverButton>
        </Dropdown>
      </EverTooltip>
    </div>
  );
}

function LimitedVisibilityMessage({ setDatasheetRestrictedAccessBanner }) {
  return (
    <div className="absolute top-16 right-6 w-auto h-auto">
      <EverHotToastNotification
        type="info"
        title="You have limited access to data in this sheet"
        description="Please reach out to your administrator for more details."
        onClose={() => {
          setDatasheetRestrictedAccessBanner(false);
        }}
      />
    </div>
  );
}

function RegenerateDataNoPermsMessage() {
  return (
    <div className="flex h-full justify-center items-center">
      <div>
        <div className="text-center">
          <div className="flex justify-center items-center p-10 mb-2">
            <img src={lostAstronaut} className="w-54 h-54" />
          </div>
          <div className="mb-2">
            <EverTg.Heading2>
              Please ask your administrator to generate this datasheet.
            </EverTg.Heading2>
          </div>
        </div>
      </div>
    </div>
  );
}

function RegenerateDataMessage({
  buttonText,
  hasPermissions,
  debouncedGenerateData,
  ButtonTextObj,
  submittingReq,
}) {
  return (
    <div className="flex h-full justify-center items-center">
      <div>
        <div className="text-center">
          <div className="flex justify-center items-center p-10 mb-2">
            <img src={lostAstronaut} className="w-54 h-54" />
          </div>
          <div className="mb-2 ">
            <EverTg.Heading2>{buttonText?.reason}</EverTg.Heading2>
          </div>
          {hasPermissions(RBAC_ROLES.MANAGE_ALL_ADMINS) ? (
            <div className="mb-6">
              <EverTg.Text className="text-ever-base-content-mid">
                <span className="font-semibold">Hard Regenerate</span> is useful
                when some configuration outside a datasheet changes. <br /> Eg:
                Client Fiscal Month
              </EverTg.Text>
            </div>
          ) : null}
        </div>
        <div className="flex items-center gap-4 justify-center">
          <EverButton
            type="ghost"
            color="base"
            size="medium"
            onClick={() => debouncedGenerateData()}
            disabled={submittingReq}
          >
            {ButtonTextObj[buttonText?.text]}
          </EverButton>
          {hasPermissions(RBAC_ROLES.MANAGE_ALL_ADMINS) ? (
            <EverButton
              type="filled"
              color="primary"
              size="medium"
              onClick={() => debouncedGenerateData(true)}
              disabled={submittingReq}
            >
              Hard Regenerate Datasheet
            </EverButton>
          ) : null}
        </div>
      </div>
    </div>
  );
}

function DataGenerationInProgress() {
  return (
    <div className="flex h-full justify-center items-center">
      <div>
        <div className="text-center">
          <div className="flex justify-center items-center p-5 mb-2">
            <img src={dogUnpluggingWires} className="w-60 h-60" />
          </div>
          <div className="mb-2 ">
            <EverTg.Heading2>
              This datasheet is currently being generated
            </EverTg.Heading2>
          </div>
          <div className="mb-6">
            <EverTg.Text className="text-ever-base-content-mid">
              Please wait until it is complete to view the data.
            </EverTg.Text>
          </div>
          <div className="w-full flex justify-center">
            <EverLoader.SpinnerLottie className="w-9" />
          </div>
        </div>
      </div>
    </div>
  );
}

function StaleDataErrorDisplay({ staleError }) {
  return (
    <div className="flex h-full justify-center items-center">
      <div className="bg-white rounded-xl ring ring-neutral-200 p-10 w-auto mx-auto">
        <div className="text-center">
          <div className="flex justify-center items-center p-10 mb-2">
            <img src={lostAstronaut} className="w-54 h-54" />
          </div>
          <EverTg.Description className="mb-6">
            <EverTg.Heading2>Request failed, please try again.</EverTg.Heading2>{" "}
            <br />
            <EverTg.Text className="text-ever-base-content-mid">
              {staleError}
            </EverTg.Text>
          </EverTg.Description>
        </div>
      </div>
    </div>
  );
}
