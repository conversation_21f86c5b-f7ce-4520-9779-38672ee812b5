import {
  CommissionTraceIcon,
  HelpCircleIcon,
  CalendarIcon,
} from "@everstage/evericons/outlined";
import { AgGridReact } from "ag-grid-react";
import { Layout, Table } from "antd";
import { isEmpty } from "lodash";
import { observer } from "mobx-react";
import moment from "moment";
import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useImmer } from "use-immer";

import {
  COMMON_MOMENT_DATE_FORMAT,
  DATATYPE,
  DANGLING_REASON,
  ADJUSTMENT_TYPE,
} from "~/Enums";
import { useDatabookStore } from "~/GlobalStores/DatabookStore";
import { useVariableStore } from "~/GlobalStores/VariableStore";
import {
  EverTg,
  EverDrawer,
  EverTooltip,
  EverDivider,
  EverModal,
  EverButton,
} from "~/v2/components";
import { getDefaultOptions } from "~/v2/components/ag-grid/ever-ag-grid-options";
import { formatDate } from "~/v2/components/ever-formatter/EverFormatter";
import { adjustmentDanglingInfo } from "~/v2/images";

import { AdjustmentActions } from "./AdjustmentActions";
import { useAdjustRecordStore } from "./store";

const monthNames = [
  "Jan",
  "Feb",
  "Mar",
  "Apr",
  "May",
  "Jun",
  "Jul",
  "Aug",
  "Sep",
  "Oct",
  "Nov",
  "Dec",
];

const dateFormat = (date) => {
  const formatedDate = new Date(date);
  if (formatedDate) {
    return `${formatedDate.getDate()} ${
      monthNames[formatedDate.getMonth()]
    } ${formatedDate.getFullYear()}`;
  }
  return "";
};

/**
 * Modal component to display details of invalid/dangling adjustments
 * @param {Object} props
 * @param {Object} props.danglingAdjustmentDetails - Details of the dangling adjustment
 * @param {boolean} props.danglingAdjustmentDetails.isModalVisible - Controls modal visibility
 * @param {Object} props.danglingAdjustmentDetails.adjustmentDetails - Adjustment data
 * @param {Array} props.danglingAdjustmentDetails.columnDefs - Column definitions for grid
 * @param {string} props.danglingAdjustmentDetails.invalidatedOn - Timestamp when adjustment became invalid
 * @param {string} props.danglingAdjustmentDetails.danglingReason - Reason why adjustment is dangling
 * @param {string} props.danglingAdjustmentDetails.adjustmentType - Type of adjustment
 * @param {Function} props.setDanglingAdjustmentDetails - Function to update dangling adjustment details
 * @returns {JSX.Element} Modal component showing dangling adjustment details
 */

const DanglingAdjustmentDetailModal = ({
  danglingAdjustmentDetails,
  setDanglingAdjustmentDetails,
}) => {
  const { t } = useTranslation();
  return (
    <EverModal
      visible={danglingAdjustmentDetails.isModalVisible}
      onCancel={() =>
        setDanglingAdjustmentDetails({
          isModalVisible: false,
          adjustmentDetails: {},
          columnDefs: [],
          invalidatedOn: "",
          danglingReason: "",
          adjustmentType: "",
        })
      }
      title={`Invalid ${t("adjustment")} details`}
      footer={
        <EverButton
          onClick={() =>
            setDanglingAdjustmentDetails({
              isModalVisible: false,
              adjustmentDetails: {},
              columnDefs: [],
              invalidatedOn: "",
              danglingReason: "",
              adjustmentType: "",
            })
          }
        >
          Close
        </EverButton>
      }
      width={700}
    >
      <div className="flex flex-col gap-4">
        <div className="flex flex-col gap-6">
          <EverTg.Text>
            {danglingAdjustmentDetails.danglingReason ===
            DANGLING_REASON.ROW_INVALIDATED
              ? `This ${t(
                  "adjustment"
                )} is no longer valid due to changes in upstream data `
              : `This ${t(
                  "adjustment"
                )} was made on a record that had field(s), which are
              no longer shown in the datasheet. This may be due to changes in
              the transformation logic after the ${t(
                "adjustment"
              )} was created.`}
          </EverTg.Text>
          {danglingAdjustmentDetails.adjustmentType !==
            ADJUSTMENT_TYPE.IGNORE && (
            <div className="flex flex-col gap-4">
              <EverTg.Heading4>
                Removed field(s) with their adjusted values
              </EverTg.Heading4>
              <div className="w-full h-full ag-theme-material no-min-height">
                <AgGridReact
                  {...getDefaultOptions({ type: "sm" })}
                  rowData={danglingAdjustmentDetails?.adjustmentDetails || []}
                  columnDefs={danglingAdjustmentDetails?.columnDefs || []}
                  domLayout="autoHeight"
                />
              </div>
            </div>
          )}
          <EverDivider />
        </div>
        <div className="flex items-center gap-2">
          <CalendarIcon className="w-3 h-3 text-ever-warning" />
          <EverTg.Caption className="text-ever-content font-medium">
            Invalidated on {danglingAdjustmentDetails.invalidatedOn || ""}
          </EverTg.Caption>
        </div>
      </div>
    </EverModal>
  );
};

export const AllAdjustments = observer((props) => {
  const {
    store,
    adjustmentData,
    datasheetId,
    adjustmentVisible,
    handleAdjustmentVisible,
    handleAdjustmentsCount,
    handleEditClick,
    handleViewClick,
    setSelectedRecord,
    handleRevertClick,
    modalVisible,
    drawerVisible,
    dsVariableStore,
  } = props;
  const adjustRecordStore = useAdjustRecordStore();
  const { isEditAdjustment } = adjustRecordStore;

  const { systemNameToDisplayNameMap } = dsVariableStore;
  const { dataTypesById } = useVariableStore();
  const { getDatasheetPrimaryKey } = useDatabookStore();

  const [columns, setColumns] = useState([]);
  const [tableData, setTableData] = useState([]);
  const [primaryKeys, setPrimaryKeys] = useState([]);
  const [hasDanglingAdjustments, setHasDanglingAdjustments] = useState(false);
  const { getDatasheetConfig, columnTypeMap } = store;

  const [danglingAdjustmentDetails, setDanglingAdjustmentDetails] = useImmer({
    isModalVisible: false,
    adjustmentDetails: {},
    columnDefs: [],
    invalidatedOn: "",
    danglingReason: "",
    adjustmentType: "",
  });

  const { t } = useTranslation();

  const handleDanglingDetailsClick = (record) => {
    let columnDefs, adjustedData;
    const isSplit = record?.["row_span"] > 1;
    // Helper function to format data based on type
    const formatDataValue = (datatype, value) => {
      switch (datatype) {
        case DATATYPE.DATE:
          return formatDate({ date: value, type: "date" });
        case DATATYPE.BOOLEAN:
          return value ? "true" : "false";
        case DATATYPE.INTEGER:
          return value;
        default:
          return value;
      }
    };
    // Generate column definitions for split adjustments
    const generateSplitColumnDefs = () => {
      const columnDefs = [
        {
          field: "display_name",
          headerName: "Field Name",
          flex: 1,
        },
      ];

      const rowKeys = record?.adjustment_details?.adjusted_data.map(
        (ele) => ele?.row_key || ""
      );

      const uniqueRowKeys = [...new Set(rowKeys)];

      const headerNameMap = uniqueRowKeys.reduce((acc, key, index) => {
        const fieldName = `adjusted_${index + 1}`;
        acc[key] = fieldName;

        columnDefs.push({
          field: fieldName,
          headerName: `Adjusted value in split ${index + 1}`,
          flex: 1,
          cellRenderer: (params) => params.value || "-",
        });

        return acc;
      }, {});

      return { columnDefs, headerNameMap };
    };

    // Generate row data for split adjustments
    const generateSplitRowData = (headerNameMap) => {
      // Get the adjusted data from params
      const adjustedData = record?.adjustment_details?.adjusted_data;

      // Get unique display names from adjusted data
      const displayNames = [
        ...new Set(adjustedData.map((ele) => ele?.display_name || "")),
      ];

      // Map display names to row data
      return displayNames.map((displayName) => {
        // Initialize row data object with display name
        const data = { display_name: displayName };

        // For each adjusted data item matching the display name
        // add its value to the corresponding split column
        adjustedData.forEach((item) => {
          if (item.display_name === displayName) {
            data[headerNameMap[item.row_key]] = item.value;
          }
        });

        return data;
      });
    };

    // Generate data for regular adjustments
    const generateRegularData = () => {
      return record?.adjustment_details?.adjusted_data.map((ele) => ({
        display_name: ele.display_name,
        value: formatDataValue(dataTypesById[ele.data_type_id], ele.value),
        data_type_id: ele.data_type_id,
      }));
    };

    // Handle split vs regular adjustments differently
    if (isSplit) {
      // For split adjustments, generate columns and data based on splits
      const { columnDefs: splitCols, headerNameMap } =
        generateSplitColumnDefs();
      columnDefs = splitCols;
      adjustedData = generateSplitRowData(headerNameMap);
    } else {
      // For regular adjustments, use simple 2-column layout
      columnDefs = [
        { field: "display_name", headerName: "Field Name", flex: 1 },
        { field: "value", headerName: "Adjusted Value", flex: 1 },
      ];
      adjustedData = generateRegularData();
    }
    // Set modal state with adjustment details
    setDanglingAdjustmentDetails({
      isModalVisible: true,
      adjustmentDetails: adjustedData,
      // Format invalidation date if exists
      invalidatedOn: record?.adjustment_details?.invalidated_at
        ? formatDate({
            date: record?.adjustment_details?.invalidated_at,
            type: "date",
          })
        : "",
      columnDefs,
      // Include reason for dangling adjustment if exists
      danglingReason: record?.dangling_reason || "",
      // Include adjustment type if exists
      adjustmentType: record?.adjustment_details?.type || "",
    });
  };

  useEffect(() => {
    const dataWithAdjustments = [];
    if (!isEmpty(adjustmentData)) {
      Object.values(adjustmentData).forEach((dataArr) => {
        const filteredDataArr = dataArr.filter(
          (data) => !isEmpty(data.adjustment_details)
        );
        for (let index = 0; index < filteredDataArr.length; index++) {
          const data = filteredDataArr[index];
          const originalValues = {};
          filteredDataArr.forEach((item) => {
            const itemData = item.original_values;
            if (
              itemData &&
              typeof itemData === "object" &&
              !Array.isArray(itemData)
            ) {
              Object.assign(originalValues, itemData); // Later values overwrite earlier ones
            }
          });
          data.original_values = originalValues;
          const obj = {
            ...data,
            adjustment_number: data["adjustment_details"]["adjustment_number"],
            type: data["adjustment_details"]["type"],
            comments: data["adjustment_details"]["comments"],
            adjustment_date: dateFormat(
              data["adjustment_details"]["adjustment_date"]
            ),
            applied_by: data["adjustment_details"]["applied_by"] || "",
            scope: data["adjustment_details"]["is_global"] ? "Global" : "Local",
          };
          if (filteredDataArr.length > 1) {
            if (index === 0) {
              obj.row_span = filteredDataArr.length;
            } else {
              obj.row_span = 0;
            }
          }
          dataWithAdjustments.push(obj);
        }
      });
    }
    if (datasheetId) {
      const pKeys = getDatasheetPrimaryKey(datasheetId);
      setPrimaryKeys(pKeys);
    }

    const hasDanglingAdjustment = dataWithAdjustments.some(
      (adjustment) => !!adjustment.dangling_reason
    );
    setHasDanglingAdjustments(hasDanglingAdjustment);
    setTableData(dataWithAdjustments);
    handleAdjustmentsCount(dataWithAdjustments.length);
  }, [adjustmentData, datasheetId]);

  const TableTitle = ({ title }) => {
    return <EverTg.Text className=" font-semibold">{title}</EverTg.Text>;
  };

  const DateRenderer = (props) => {
    return (
      <div>
        {props.value
          ? moment(props.value).format(COMMON_MOMENT_DATE_FORMAT)
          : props.value}
      </div>
    );
  };

  const handleEditAction = (event) => {
    handleEditClick(event);
    // setPopoverVisible(false);
  };
  const handleViewAction = (event) => {
    //setPopoverVisible(!popoverVisible);
    handleViewClick(event);
  };
  const handleRevertAction = (event) => {
    handleRevertClick(event);
    //setPopoverVisible(false);
  };

  const tableColumns = () => {
    const colMap = columnTypeMap;
    const cols = [];

    const actioncolumn = () => {
      return {
        title: "Actions",
        dataIndex: "adjustments-list",
        width: "100%",
        className: "adjustments-list-container adjust-record-icon-container",
        render: (text, record) => {
          const adjustmentAction = () => {
            //setPopoverVisible(!popoverVisible);
            setSelectedRecord(record);
          };
          console.log("** editAdjustment", isEditAdjustment);
          const obj = {
            children: record["dangling_reason"] ? (
              <></>
            ) : (
              <div className={"adjustment-action-container"} onClick={() => {}}>
                <AdjustmentActions
                  handleViewClick={handleViewAction}
                  handleRevertClick={handleRevertAction}
                  handleEditClick={handleEditAction}
                  isIgnoreRecord={record.type === "IGNORE"}
                  isGlobalAdjustment={record.scope == "Global"}
                  adjustmentAction={adjustmentAction}
                  modalVisible={modalVisible}
                  isEditAdjustment={drawerVisible}
                  allAdjustments={true}
                />
              </div>
            ),
            props: {
              rowSpan: record["row_span"],
            },
          };
          return obj;
        },
      };
    };
    const typeColor = (type) => {
      if (type === "SPLIT" || type === "UPDATE") {
        return "text-ever-success";
      }
      return "text-ever-error";
    };
    const typeName = (type) => {
      switch (type) {
        case "IGNORE":
          return "Ignored";
        case "UPDATE":
          return "Update";
        case "SPLIT":
          return "Insert";
      }
      return null;
    };
    const columnData = (variable) => {
      return {
        title: <TableTitle title={variable.displayName} />,
        dataIndex: variable.systemName,
        className: "whitespace-nowrap",
        width: "",
        key: variable.systemName,
        render: (text, record) => {
          const id = colMap[variable.systemName];
          const props = {};
          if (
            variable.systemName === "adjustment_date" ||
            variable.systemName === "comments" ||
            variable.systemName === "scope" ||
            variable.systemName === "adjustment_number"
          ) {
            props.rowSpan = record["row_span"];
          }
          const renderText =
            variable.systemName === "type"
              ? typeName(text)
              : text?.toString() || "";
          const textColor =
            variable.systemName === "type"
              ? typeColor(text)
              : "text-ever-base-content";

          const obj = {
            children: (
              <div className="flex items-center gap-2">
                <EverTg.Caption className={`break-words ${textColor}`}>
                  {dataTypesById[id] === "Date" ? (
                    <DateRenderer value={text} />
                  ) : (
                    renderText
                  )}
                </EverTg.Caption>
                <div className="flex items-center gap-2">
                  {record["row_span"] === 0 ||
                    (record["row_span"] > 1 &&
                      variable?.systemName === "adjustment_number" && (
                        <div className="flex items-center gap-2">
                          <CommissionTraceIcon className="w-4 h-4 text-ever-base-content-low" />
                          <EverTg.Text className="text-ever-base-content">
                            {record["row_span"] - 1}
                          </EverTg.Text>
                        </div>
                      ))}
                </div>
                {record?.dangling_reason &&
                  variable?.systemName === "adjustment_number" && (
                    <div className="flex items-center gap-2">
                      <div className="flex items-center gap-3">
                        <EverDivider type="vertical" className="h-3" />
                        <div className="flex items-center gap-2">
                          <span className="text-ever-error-lite-content bg-ever-error-lite-content/10 rounded-md flex items-center justify-center h-5 px-2 py-1 ">
                            Invalid
                          </span>

                          <EverTooltip title="Click to view details">
                            <HelpCircleIcon
                              className="w-4 h-4 text-ever-base-content-low cursor-pointer"
                              onClick={() => handleDanglingDetailsClick(record)}
                            />
                          </EverTooltip>
                        </div>
                      </div>
                    </div>
                  )}
              </div>
            ),
            props: props,
          };
          return obj;
        },
      };
    };
    primaryKeys.forEach((primaryKey) => {
      cols.push(
        columnData({
          systemName: primaryKey,
          displayName: systemNameToDisplayNameMap[datasheetId]?.[primaryKey],
        })
      );
    });
    cols.push(
      columnData({
        systemName: "adjustment_number",
        displayName: t("ADJUSTMENT_ID"),
      })
    );
    if (getDatasheetConfig && getDatasheetConfig.variables) {
      getDatasheetConfig.variables.forEach((variable) => {
        if (!primaryKeys.includes(variable.systemName)) {
          cols.push(columnData(variable));
        }
      });
    }
    cols.push(
      columnData({
        systemName: "adjustment_date",
        displayName: t("ADJUSTMENT_DATE"),
      })
    );
    cols.push(
      columnData({
        systemName: "type",
        displayName: t("ADJUSTMENT_TYPE"),
      })
    );
    cols.push(
      columnData({
        systemName: "scope",
        displayName: t("ADJUSTMENT_SCOPE"),
      })
    );
    cols.push(columnData({ systemName: "comments", displayName: "Comments" }));
    cols.push(
      columnData({ systemName: "applied_by", displayName: "Applied By" })
    );
    cols.push(actioncolumn());
    return cols;
  };

  useEffect(() => {
    setColumns(tableColumns());
  }, [modalVisible, drawerVisible]);

  return (
    <>
      <EverDrawer
        title={<EverTg.Heading3>{t("ADJUSTMENTS")}</EverTg.Heading3>}
        width={"85%"}
        className="adjustment-list-drawer z-10"
        placement="right"
        // headerStyle={{
        //   borderBottom: "1px solid #E2E8F8",
        //   padding: "18px 18px 18px 30px",
        // }}
        onClose={() => {
          handleAdjustmentVisible(false);
          //  setPopoverVisible(false);
        }}
        visible={adjustmentVisible}
      >
        <Layout>
          <div className="flex flex-col gap-4 bg-ever-base">
            {hasDanglingAdjustments && (
              <div className="flex  px-4 py-3 rounded-lg bg-adjustmentDangling gap-4">
                <div className="flex items-center justify-center">
                  <img src={adjustmentDanglingInfo} className="w-14 h-12" />
                </div>
                <div>
                  <EverTg.Heading4 className="text-ever-base-content">
                    Some {t("adjustments")} are invalid
                  </EverTg.Heading4>
                  <EverTg.Text className="text-ever-base-content flex items-center gap-1">
                    {t("Adjustments")} with the Invalid tag will not be applied.
                    Hover over
                    <HelpCircleIcon className="w-4 h-4 text-ever-base-content-low" />
                    to view details
                  </EverTg.Text>
                </div>
              </div>
            )}
            <Table
              rowKey={datasheetId}
              columns={columns}
              dataSource={tableData}
              className="all-adjustments"
              pagination={false}
              scroll={{ x: 500 }}
              bordered
              rowClassName={(record) => {
                return record["dangling_reason"] ? "" : "";
              }}
            />
          </div>
        </Layout>
      </EverDrawer>
      <DanglingAdjustmentDetailModal
        danglingAdjustmentDetails={danglingAdjustmentDetails}
        setDanglingAdjustmentDetails={setDanglingAdjustmentDetails}
      />
    </>
  );
});
