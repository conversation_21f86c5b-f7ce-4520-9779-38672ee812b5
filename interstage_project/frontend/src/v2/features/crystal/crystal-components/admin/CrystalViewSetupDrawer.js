import { gql, useQuery } from "@apollo/client";
import {
  EqualIcon,
  InfoCircleIcon,
  HelpCircleIcon,
} from "@everstage/evericons/outlined";
import {
  DotsVerticalIcon,
  PlusCircleIcon as PlusCircleIconSolid,
} from "@everstage/evericons/solid";
import { Space } from "antd";
import { parseJSON, format } from "date-fns";
import { isEqual } from "lodash";
import PropTypes from "prop-types";
import React, { useEffect, useState, useMemo } from "react";
import { useQueryClient } from "react-query";
import { useRecoilValue } from "recoil";
import { twMerge } from "tailwind-merge";

import { DATA_TYPES_TO_BE_IGNORED_IN_MODULE } from "~/Enums";
import { myClientAtom } from "~/GlobalStores/atoms";
import { getClientFeatures } from "~/Utils/ClientFeaturesUtils";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>utton,
  EverInput,
  <PERSON><PERSON>ool<PERSON>,
  <PERSON>Label,
  EverForm,
  EverSelect,
  EverDatePicker,
  EverCard,
  EverTg,
  EverSwitch,
  EverDraggableColumns,
  EverCheckbox,
  toast,
  EverHotToastMessage,
  RemovableInputGroup,
} from "~/v2/components";
import { useCrystalAPI } from "~/v2/features/crystal/api";

import { CrystalDisplayCondition } from "../CrystalDisplayCondition";
const { Option } = EverSelect;

export const CONNECTION_LIST = gql`
  query ConnectedObjects {
    connectedObjects(
      shouldIncludeDisabledObjects: false
      serviceName: "salesforce"
    ) {
      integrationId
      name
    }
  }
`;

CrystalViewSetupDrawer.propTypes = {
  showForm: PropTypes.bool,
  crystalTableId: PropTypes.string,
  onCreate: PropTypes.func,
  onEdit: PropTypes.func,
  crystalViewId: PropTypes.string,
  onCancel: PropTypes.func,
  refetchCrystalView: PropTypes.func,
  objectDataList: PropTypes.array,
  filterData: PropTypes.object,
  initialValues: PropTypes.object,
};

const selectedSourceType = "datasheet";

/*
This function is used to format the date string to ISO format 
with time set to 0 for each of the display conditions that involve date.

This is done because the datepicker component inside a Form Item 
returns the date without time set to 0.
*/
const dateFormatter = (data) =>
  data.map((condition) => {
    if (Object.prototype.toString.call(condition.value) === "[object Date]") {
      let newDate;
      newDate = condition.value.setHours(0, 0, 0, 0);
      const formattedDate = format(newDate, "yyyy-MM-dd'T'HH:mm:ss.SSS");
      return { ...condition, value: formattedDate };
    } else {
      return condition;
    }
  });

const addOperator = (data, filterData) => {
  data.forEach((value) => {
    value["operator"] = filterData.operators.find(
      (operator) => operator.id === value.operatorId
    ).name;
  });
  return data;
};

// Utility function to filter out ignored data types
const filterIgnoredDataTypes = (columns, module = "CRYSTAL") => {
  return columns.filter(
    ({ dataType }) =>
      !DATA_TYPES_TO_BE_IGNORED_IN_MODULE[module].includes(dataType)
  );
};

export function CrystalViewSetupDrawer({
  showForm,
  onCreate,
  onEdit,
  crystalViewId,
  crystalTableId,
  refetchCrystalView,
  setSelectedTab,
  onCancel,
  objectDataList,
  filterData,
  initialValues,
  usedDsList,
}) {
  const [form] = EverForm.useForm();
  const crystalAPI = useCrystalAPI(crystalViewId);
  const [disableButton, setDisableButton] = useState(false);
  const [firstTime, setFirstTime] = useState(false);
  // this will be used to validate if form is changed or not in edit mode
  const [initialValueData, setInitialValueData] = useState();
  const queryClient = useQueryClient();
  // const [selectedSourceType, setSelectedSourceType] = useState(null);
  const [selectedObjectDetail, setSelectedObjectDetail] = useState(null);
  const [selectedSuccessActions, setSelectedSuccessActions] = useState([
    {
      typeName: "",
      columnName: "",
    },
  ]);
  const [selectedFilter, setSelectedFilter] = useState([
    {
      typeId: null,
      operatorName: "",
      typeName: "",
      needsOperand: true,
      multiValued: false,
    },
  ]);
  const [selectedColumns, setSelectedColumns] = useState([]);
  const [columnConfigData, setColumnConfigData] = useState([]);
  // let availableDatabooks;

  const { data: connectionData } = useQuery(CONNECTION_LIST, {
    fetchPolicy: "no-cache",
  });

  const myAtom = useRecoilValue(myClientAtom);
  const clientFeatures = getClientFeatures(myAtom);
  const hasCpq =
    Array.isArray(clientFeatures.modules) &&
    clientFeatures.modules.includes("CPQ");

  //if table edit mode
  useEffect(() => {
    if (
      initialValues != undefined &&
      initialValues.crystalTableName !== undefined
    ) {
      setFirstTime(true);
      setInitialValueData(initialValues);
      const colTypeList = {};
      // setSelectedSourceType(initialValues["sourceType"]);
      console.log("initialValues", initialValues);
      objectDataList.map((object) => {
        if (initialValues["sourceId"] === object.id) {
          object.variables.map(({ systemName, dataType }) => {
            colTypeList[systemName] = dataType;
          });
          setSelectedObjectDetail(object.variables);
        }
      });
      console.log("colTypeList", colTypeList);
      if (initialValues["displayConditions"].length > 0) {
        const dummy = [];
        const dateConvertedList = [];
        initialValues["displayConditions"].map((data) => {
          const { colName, operator, value, operatorId } = data;
          const dataTypeMapIdList = [];
          if (colTypeList[colName] === "Date") {
            dateConvertedList.push({
              colName: colName,
              operator: operator,
              value: typeof value === "string" ? parseJSON(value) : value,
              operatorId: operatorId,
            });
          } else {
            dateConvertedList.push({
              colName: colName,
              operator: operator,
              value: value,
              operatorId: operatorId,
            });
          }
          filterData.dataTypes.map(({ dataType, id }) => {
            dataTypeMapIdList[dataType] = id;
          });
          filterData.operators.map(({ needsOperand, multiValued, id }) => {
            if (id === operatorId) {
              dummy.push({
                typeId: dataTypeMapIdList[colTypeList[colName]],
                operatorName: operator,
                typeName: colTypeList[colName],
                needsOperand: needsOperand,
                multiValued: multiValued,
              });
            }
          });
          setSelectedFilter(dummy);
          initialValues["displayConditions"] = dateConvertedList;
        });
      }
      if (initialValues["successActions"].length > 0) {
        const dummy = [];
        const dateConvertedList = [];
        initialValues["successActions"].map(({ columnName, value }) => {
          if (colTypeList[columnName] === "Date") {
            dateConvertedList.push({
              columnName: columnName,
              value: typeof value === "string" ? parseJSON(value) : value,
            });
          } else {
            dateConvertedList.push({
              columnName: columnName,
              value: value,
            });
          }
          dummy.push({
            typeName: colTypeList[columnName],
            columnName: columnName,
          });
        });
        setSelectedSuccessActions(dummy);
        initialValues["successActions"] = dateConvertedList;
      }
      // convert datestring to moment
      form.setFieldsValue(initialValues);
      setSelectedColumns(initialValues["displayColumns"]);
    } else {
      form.setFieldsValue({
        displayConditions: [{}],
        successActions: [{}],
        isEstimator: false,
      });
    }
  }, [initialValues]);

  // function onChangeSourceType(value) {
  //   let dummy = [];
  //   // if (selectedSourceType === "datasheet") {
  //   //   availableDatabooks = objectDataList
  //   //     .map((source) => source.databookName)
  //   //     .filter((value, index, self) => self.indexOf(value) === index);
  //   //   console.log(availableDatabooks);
  //   // }
  //   // setSelectedSourceType(value);
  //   setSelectedObjectDetail(null);
  //   setColumnConfigData([]);
  //   setSelectedSuccessActions([
  //     {
  //       typeName: "",
  //       columnName: "",
  //     },
  //   ]);
  //   setSelectedFilter([
  //     {
  //       typeId: null,
  //       operatorName: "",
  //       typeName: "",
  //       needsOperand: true,
  //       multiValued: false,
  //     },
  //   ]);
  //   // variables.map(({ systemName }) => {
  //   //   dummy.push(systemName);
  //   // });
  //   // form.setFieldsValue({ columnConfig: dummy });
  //   setSelectedColumns(dummy);
  //   form.setFieldsValue({
  //     sourceId: null,
  //     displayConditions: [{}],
  //     successActions: [{}],
  //     emailField: null,
  //     rowName: null,
  //     dateField: null,
  //     // displayColumns: dummy,
  //   });
  // }

  function onSourceTypeChange(value) {
    const dummy = [];
    objectDataList.map((object) => {
      const { id, variables } = object;
      if (id === value) {
        setSelectedObjectDetail(variables);
        setColumnConfigData([]);
        setSelectedSuccessActions([
          {
            typeName: "",
            columnName: "",
          },
        ]);
        setSelectedFilter([
          {
            typeId: null,
            operatorName: "",
            typeName: "",
            needsOperand: true,
            multiValued: false,
          },
        ]);
        // clear the form value
        variables.map(({ systemName }) => {
          dummy.push(systemName);
        });
        // form.setFieldsValue({ columnConfig: dummy });
        setSelectedColumns(dummy);
        form.setFieldsValue({
          displayConditions: [{}],
          successActions: [{}],
          emailField: null,
          rowName: null,
          dateField: null,
          displayColumns: dummy,
        });
      }
    });
  }

  function onCreateTable() {
    form
      .validateFields()
      .then(async (values) => {
        setDisableButton(true);
        const loadingToastId = toast.custom(
          () => (
            <EverHotToastMessage
              type="loading"
              description={"Creating Table..."}
            />
          ),
          { position: "top-center", duration: Infinity }
        );
        const displayColumns = [];
        const editableColumns = [];
        columnConfigData.map((columnData) => {
          const { systemName, isEditable } = columnData;
          displayColumns.push(systemName);
          if (isEditable) {
            editableColumns.push(systemName);
          }
        });
        if (displayColumns.includes(values["rowName"])) {
          values["displayColumns"] = displayColumns;
          values["editableColumns"] = editableColumns;
          values["sourceType"] = "datasheet";
          //adding databook name
          values["databookId"] = objectDataList.find(
            (data) => data.id === values["sourceId"]
          ).databookId;
          values["displayConditions"] = dateFormatter(
            values["displayConditions"]
          );
          values["displayConditions"] = addOperator(
            values["displayConditions"],
            filterData
          );
          try {
            const resp = await queryClient.fetchQuery({
              queryKey: ["postAddTableToView"],
              queryFn: () => crystalAPI.postAddTableToView(values),
            });
            setSelectedTab(resp.crystalTableId);
            refetchCrystalView();
            toast.dismiss(loadingToastId);
            toast.custom(
              () => (
                <EverHotToastMessage
                  type="success"
                  description={"Table created successfully"}
                />
              ),
              { position: "top-center" }
            );
            //reset form values
            form.resetFields();
            // setSelectedSourceType(null);
            setDisableButton(false);
            setSelectedObjectDetail(null);
            setSelectedColumns([]);
            setColumnConfigData([]);
            setSelectedSuccessActions([
              {
                typeName: "",
                columnName: "",
              },
            ]);
            setSelectedFilter([
              {
                typeId: null,
                operatorName: "",
                typeName: "",
                needsOperand: true,
                multiValued: false,
              },
            ]);

            onCreate(form);
          } catch (error) {
            toast.dismiss(loadingToastId);
            toast.custom(
              () => (
                <EverHotToastMessage type="error" description={error.message} />
              ),
              { position: "top-center" }
            );
            setDisableButton(false);
          }
        } else {
          setDisableButton(false);
          toast.dismiss(loadingToastId);
          toast.custom(
            () => (
              <EverHotToastMessage
                type="error"
                description={
                  "Row Name field should be present in choose columns field"
                }
              />
            ),
            { position: "top-center" }
          );
        }
      })
      .catch(() => {
        // console.log("Validate Failed:", info);
      });
  }

  function onEditTable() {
    form
      .validateFields()
      .then(async (values) => {
        setDisableButton(true);
        const loadingToastId = toast.custom(
          () => (
            <EverHotToastMessage
              type="loading"
              description={"Modifying Crystal Table..."}
            />
          ),
          { position: "top-center" }
        );
        const displayColumns = [];
        const editableColumns = [];
        columnConfigData.map((columnData) => {
          const { systemName, isEditable } = columnData;
          displayColumns.push(systemName);
          if (isEditable) {
            editableColumns.push(systemName);
          }
        });
        if (displayColumns.includes(values["rowName"])) {
          values["crystalTableId"] = crystalTableId;
          values["displayColumns"] = displayColumns;
          values["editableColumns"] = editableColumns;
          values["sourceType"] = "datasheet";
          values["databookId"] = objectDataList.find(
            (data) => data.id === values["sourceId"]
          ).databookId;
          values["displayConditions"] = dateFormatter(
            values["displayConditions"]
          );
          values["displayConditions"] = addOperator(
            values["displayConditions"],
            filterData
          );
          if (isEqual(initialValueData, values)) {
            setDisableButton(true);
            toast.remove(loadingToastId);
            toast.custom(
              () => (
                <EverHotToastMessage
                  type="warning"
                  description={"Values not changed"}
                />
              ),
              { position: "top-center" }
            );
          } else {
            try {
              await queryClient.fetchQuery({
                queryKey: ["postEditCrystalTable"],
                queryFn: () => crystalAPI.postEditCrystalTable(values),
              });
              refetchCrystalView();
              toast.dismiss(loadingToastId);
              toast.custom(
                () => (
                  <EverHotToastMessage
                    type="success"
                    description={"Crystal Table Modified successfully"}
                  />
                ),
                { position: "top-center" }
              );
              form.resetFields();
              // setSelectedSourceType(null);
              setDisableButton(false);
              setSelectedObjectDetail(null);
              setSelectedColumns([]);
              setColumnConfigData([]);
              setSelectedSuccessActions([
                {
                  typeName: "",
                  columnName: "",
                },
              ]);
              setSelectedFilter([
                {
                  typeId: null,
                  operatorName: "",
                  typeName: "",
                  needsOperand: true,
                  multiValued: false,
                },
              ]);
              onEdit(form);
            } catch (error) {
              toast.dismiss(loadingToastId);
              toast.custom(
                () => (
                  <EverHotToastMessage
                    type="error"
                    description={error.message}
                  />
                ),
                { position: "top-center" }
              );
              setDisableButton(false);
              console.error(error);
            }
          }
        } else {
          setDisableButton(false);
          toast.dismiss(loadingToastId);
          toast.custom(
            () => (
              <EverHotToastMessage
                type="warning"
                description="Row Name field should be present in choose columns field"
              />
            ),
            { position: "top-center" }
          );
        }
      })
      .catch(() => {
        // console.log("Validate Failed:", info);
      });
  }

  function onDrawerClose() {
    // setSelectedSourceType(null);
    setSelectedObjectDetail(null);
    setSelectedColumns([]);
    setColumnConfigData([]);
    setSelectedSuccessActions([
      {
        typeName: "",
        columnName: "",
      },
    ]);
    setSelectedFilter([
      {
        typeId: null,
        operatorName: "",
        typeName: "",
        needsOperand: true,
        multiValued: false,
      },
    ]);
    onCancel(form);
  }

  return (
    <EverDrawer
      title={initialValues ? "Edit Configuration" : "Create View"}
      visible={showForm}
      keyboard={false}
      placement="top"
      destroyOnClose={true}
      onClose={onDrawerClose}
      footer={
        <div className="flex justify-end gap-3">
          <EverButton color="base" onClick={onDrawerClose}>
            Cancel
          </EverButton>
          <EverButton
            onClick={() => {
              if (initialValues == undefined) {
                onCreateTable();
              } else {
                onEditTable();
              }
            }}
          >
            Confirm
          </EverButton>
        </div>
      }
    >
      <div className="w-full px-4">
        <EverForm
          form={form}
          key={
            initialValues === null
              ? "newTableForm"
              : initialValues.crystalTableId
          }
          layout="vertical"
          name={
            initialValues === null
              ? "newTableForm"
              : initialValues.crystalTableId
          }
          className="w-1/2"
        >
          {/* Crystal View Name */}
          <TitleWithToolTip
            title={"View Name"}
            tooltipMessage={null}
            tooltipNeeded={false}
            required={true}
            topMarginNeeded={false}
          />
          <EverForm.Item
            name="crystalTableName"
            className="w-96"
            rules={[
              {
                required: true,
                message: "Please enter the view name",
              },
            ]}
          >
            <EverInput placeholder="Enter the View name" />
          </EverForm.Item>
          {/* Description */}
          <TitleWithToolTip
            title={"Description"}
            tooltipMessage={
              "Write a brief description of how this simulator works so payees know how to use it"
            }
            tooltipNeeded={true}
            required={true}
          />
          <EverForm.Item
            name="crystalTableDescription"
            className="w-96"
            rules={[
              {
                required: true,
                message: "Please enter description",
              },
            ]}
          >
            <EverInput.TextArea autoSize={false} />
          </EverForm.Item>
          <EverForm.Item
            className="!mb-0"
            noStyle
            shouldUpdate={(prevValues, currentValues) =>
              prevValues.isEstimator !== currentValues.isEstimator
            }
          >
            {({ getFieldValue }) => {
              return (
                <Space
                  size="small"
                  direction="horizontal"
                  className="w-96 items-start flex-col"
                >
                  <EverForm.Item
                    name="isEstimator"
                    className="!m-0 pt-1 !mt-4"
                    shouldUpdate
                  >
                    <EverCheckbox
                      checked={getFieldValue("isEstimator")}
                      onChange={(event) => {
                        form.setFieldsValue({
                          isEstimator: event.target.checked,
                        });
                      }}
                      label={`Publish to Salesforce Estimator`}
                    />
                  </EverForm.Item>
                  {getFieldValue("isEstimator") && (
                    <>
                      <TitleWithToolTip
                        title="Salesforce Object"
                        tooltipMessage={null}
                        tooltipNeeded={false}
                        required
                      />
                      <EverForm.Item
                        name="salesforceObject"
                        className="w-96"
                        rules={[
                          {
                            required: true,
                            message: "Please select salesforce object",
                          },
                        ]}
                        shouldUpdate
                      >
                        <EverSelect
                          showSearch
                          placeholder="Select Object"
                          getPopupContainer={(trigger) => trigger.parentNode}
                        >
                          {(connectionData?.connectedObjects || []).map(
                            (object) => {
                              return (
                                <Option
                                  key={object.integrationId}
                                  value={object.name?.toLowerCase()}
                                >
                                  {object.name}
                                </Option>
                              );
                            }
                          )}
                        </EverSelect>
                      </EverForm.Item>
                    </>
                  )}
                </Space>
              );
            }}
          </EverForm.Item>
          {/* Data Source Radio button */}
          <TitleWithToolTip
            title={"Select Datasheet"}
            tooltipMessage={null}
            tooltipNeeded={false}
            required={true}
          />
          <SourceTypeSelector
            onSourceTypeChange={onSourceTypeChange}
            selectedSourceType={selectedSourceType}
            objectDataList={objectDataList}
            usedDsList={usedDsList}
            selectedSourceId={initialValues?.sourceId}
          />
          {/* Display Condition */}
          <TitleWithToolTip
            title={"Display Conditions"}
            tooltipMessage={
              "Define the conditions based on which you want to display data on Crystal"
            }
            required={false}
            tooltipNeeded={true}
          />
          <CrystalDisplayCondition
            selectedFilter={selectedFilter}
            setSelectedFilter={(value) => setSelectedFilter(value)}
            form={form}
            filterData={filterData}
            selectedObjectDetail={selectedObjectDetail}
            disableButton={disableButton}
            addText="Add Condition"
          />
          {/* Success Criteria */}
          <TitleWithToolTip
            title={"Success Actions"}
            tooltipMessage={
              "Determine how the records selected by the payees should be processed by Everstage to consider it a success"
            }
            required={false}
            tooltipNeeded={true}
          />
          <SuccessCriteriaComponent
            selectedSuccessActions={selectedSuccessActions}
            form={form}
            setSelectedSuccessActions={(value) =>
              setSelectedSuccessActions(value)
            }
            selectedObjectDetail={selectedObjectDetail}
            disableButton={disableButton}
          />
          {/* Email field */}
          <TitleWithToolTip
            title={"Email field"}
            tooltipMessage={
              "Select the email column of the role for which this Simulator is created"
            }
            required={true}
            tooltipNeeded={true}
          />
          <EmailComponent selectedObjectDetail={selectedObjectDetail} />
          {/* date field */}
          <TitleWithToolTip
            title={"Date Field"}
            tooltipMessage={
              "Select the date column that needs to be updated when payees project their commissions"
            }
            required={true}
            tooltipNeeded={true}
          />
          <DateComponent selectedObjectDetail={selectedObjectDetail} />
          {/* Row Name */}
          <TitleWithToolTip
            title={"Row name field"}
            tooltipMessage={
              "The column name using which payees can quickly identify each record in their Crystal table. E.g., opportunity name, lead name, etc."
            }
            required={true}
            tooltipNeeded={true}
          />
          <RowNameComponent
            selectedObjectDetail={selectedObjectDetail}
            key="rowName"
          />
          {selectedObjectDetail ? (
            <>
              <TitleWithToolTip
                title={"Choose columns"}
                tooltipMessage={
                  "Choose the most important columns you want to display to your payees and enable editing if needed"
                }
                required={true}
                tooltipNeeded={true}
              />
              {/* Column Config */}
              <ColumnConfig
                selectedObjectDetail={selectedObjectDetail}
                setSelectedColumns={(selectedColumnList) =>
                  setSelectedColumns(selectedColumnList)
                }
                form={form}
              />
              <div className="w-[428px] mt-[-2px]">
                {selectedColumns.length > 0 && (
                  <div className="flex justify-between">
                    <EverTg.Text className="text-ever-base-content-mid text-xs">
                      Field name
                    </EverTg.Text>
                    <EverTg.Text className="text-ever-base-content-mid text-xs">
                      Cell editing
                    </EverTg.Text>
                  </div>
                )}

                <DraggableList
                  columnConfigData={columnConfigData}
                  initialValues={initialValues}
                  firstTime={firstTime}
                  setFirstTime={(data) => setFirstTime(data)}
                  selectedColumns={selectedColumns}
                  selectedObjectDetail={selectedObjectDetail}
                  setColumnConfigData={(obj) => setColumnConfigData(obj)}
                />
              </div>
            </>
          ) : null}

          {clientFeatures?.chromeExtensionEnabled ? (
            <>
              <div className="flex flex-col gap-2 mt-8">
                <EverTg.SubHeading4 className="text-ever-base-content flex items-center gap-1">
                  Configure Everstage Extension
                </EverTg.SubHeading4>
                <EverTg.Text className="text-ever-base-content-mid">
                  Your reps can see potential commissions for deals right within
                  email conversations.
                </EverTg.Text>
                <EverTg.Text className="text-ever-base-content-mid">
                  Tell us which fields in your data best represent the following
                  information.
                </EverTg.Text>
              </div>

              <TitleWithToolTip
                title={"Domain name field"}
                tooltipMessage={
                  "Column containing website or email domain of your prospects"
                }
                required={false}
                tooltipNeeded={true}
              />
              <DomainDealField
                selectedObjectDetail={selectedObjectDetail}
                name="domainName"
              />
              <TitleWithToolTip
                title={"Deal name field"}
                tooltipMessage={
                  "Column containing your prospect's company name"
                }
                required={false}
                tooltipNeeded={true}
              />
              <DomainDealField
                selectedObjectDetail={selectedObjectDetail}
                name="dealName"
              />
            </>
          ) : (
            ""
          )}
          {hasCpq ? (
            <CpqCrystalSetupForm selectedObjectDetail={selectedObjectDetail} />
          ) : null}
        </EverForm>
      </div>
    </EverDrawer>
  );
}

function SourceTypeSelector({
  onSourceTypeChange,
  selectedSourceType,
  objectDataList,
  usedDsList,
  selectedSourceId,
}) {
  return (
    <EverForm.Item
      name="sourceId"
      className="w-96"
      rules={[
        {
          required: true,
          message: "Please select source",
        },
      ]}
      shouldUpdate
    >
      <EverSelect
        showSearch
        placeholder="Select"
        getPopupContainer={(trigger) => trigger.parentNode}
        filterOption={(input, option) => {
          return (
            option?.children !== undefined &&
            option.children.toLowerCase().includes(input.toLowerCase())
          );
        }}
        onChange={(data) => {
          onSourceTypeChange(data);
        }}
        disabled={selectedSourceType === null}
      >
        {selectedSourceType === "datasheet" &&
          [
            ...new Map(
              objectDataList
                .filter((value) => value.databookName !== undefined)
                .map((sourceData) => [sourceData["databookName"], sourceData])
            ).values(),
          ].map(({ databookName }, index) => {
            return (
              <EverSelect.OptGroup
                key={index}
                label={databookName}
                getPopupContainer={(trigger) => trigger.parentNode}
              >
                {objectDataList
                  .filter(
                    (sourceData) => sourceData?.databookName === databookName
                  )
                  .map(({ name, id }) => (
                    <Option
                      key={id}
                      value={id}
                      disabled={
                        usedDsList.includes(id) && // to disable the already used datasheets
                        selectedSourceId !== id // unless its the currently selected one, because we want user to be able to select it back if they change it temporarily
                      }
                    >
                      {name}
                    </Option>
                  ))}
              </EverSelect.OptGroup>
            );
          })}
      </EverSelect>
    </EverForm.Item>
  );
}

function SuccessCriteriaComponent({
  selectedSuccessActions,
  setSelectedSuccessActions,
  selectedObjectDetail,
  disableButton,
  form,
}) {
  function onAddSuccessActions(add) {
    if (selectedObjectDetail) {
      const dummy = [...selectedSuccessActions];
      dummy.push({
        typeName: "",
        columnName: "",
      });
      setSelectedSuccessActions(dummy);
      add();
    }
  }
  function onRemoveSuccessActions(remove, field, index) {
    if (selectedObjectDetail) {
      const dummy = [...selectedSuccessActions];
      dummy.splice(index, 1);
      setSelectedSuccessActions(dummy);
      remove(field.name);
    }
  }

  function onChangeSuccessActions(colName, dataType, index) {
    const prevValue = form.getFieldsValue(true);
    prevValue["successActions"][index].value = null;
    form.setFieldsValue({ successActions: prevValue["successActions"] });
    let dummy = [...selectedSuccessActions];
    dummy[index]["columnName"] = colName;
    dummy[index]["typeName"] = dataType;
    setSelectedSuccessActions(dummy);
  }

  return (
    <EverForm.List name="successActions">
      {(fields, { add, remove }) => (
        <>
          {fields.map((field, rowIndex) => (
            <div
              key={rowIndex}
              className={`flex w-auto ${
                fields.length === rowIndex + 1 ? `mb-0` : `mb-3`
              }`}
            >
              <RemovableInputGroup
                onRemove={() => onRemoveSuccessActions(remove, field, rowIndex)}
              >
                <Space>
                  <EverForm.Item
                    {...field}
                    name={[field.name, "columnName"]}
                    className="w-56 !mb-0"
                    rules={[
                      {
                        required: true,
                        message: "Please select field",
                      },
                    ]}
                  >
                    <EverSelect
                      showSearch
                      filterOption={(input, option) => {
                        return (
                          option?.children !== undefined &&
                          option.children
                            .toLowerCase()
                            .includes(input.toLowerCase())
                        );
                      }}
                      placeholder="Select Type"
                      onChange={(value, option) =>
                        onChangeSuccessActions(value, option.datatype, rowIndex)
                      }
                      disabled={selectedObjectDetail ? false : true}
                      getPopupContainer={(trigger) => trigger.parentNode}
                    >
                      {selectedObjectDetail &&
                        selectedObjectDetail.map(
                          ({ systemName, displayName, dataType }) => {
                            if (
                              DATA_TYPES_TO_BE_IGNORED_IN_MODULE.CRYSTAL.includes(
                                dataType
                              )
                            ) {
                              return null;
                            }
                            const selectedColumns = selectedSuccessActions.map(
                              ({ columnName }) => {
                                return columnName;
                              }
                            );
                            return selectedColumns.indexOf(systemName) ===
                              rowIndex ? (
                              <Option
                                key={systemName}
                                datatype={dataType}
                                value={systemName}
                              >
                                {displayName}
                              </Option>
                            ) : (
                              !selectedColumns.includes(systemName) && (
                                <Option
                                  key={systemName}
                                  datatype={dataType}
                                  value={systemName}
                                >
                                  {displayName}
                                </Option>
                              )
                            );
                          }
                        )}
                    </EverSelect>
                  </EverForm.Item>
                  <div className="flex h-10 items-center px-4">
                    <EqualIcon className="h-5 w-5" />
                  </div>
                  <EverForm.Item
                    {...field}
                    name={[field.name, "value"]}
                    className="w-56 !mb-0"
                    shouldUpdate={true}
                    rules={[
                      {
                        required: true,
                        message: "Please enter value",
                      },
                    ]}
                  >
                    {selectedSuccessActions[rowIndex].typeName === "Date" ? (
                      <EverDatePicker format="YYYY-MM-DD" className="w-full" />
                    ) : selectedSuccessActions[rowIndex].typeName ==
                      "Boolean" ? (
                      <EverSelect
                        className="w-full"
                        dropdownMatchSelectWidth
                        getPopupContainer={(trigger) => trigger.parentNode}
                      >
                        <Option key="true" label="True" value="True">
                          True
                        </Option>
                        <Option key="false" label="False" value="False">
                          False
                        </Option>
                      </EverSelect>
                    ) : selectedSuccessActions[rowIndex].typeName ===
                        "Integer" ||
                      selectedSuccessActions[rowIndex].typeName ===
                        "Percentage" ? (
                      <EverInput.Number
                        className="w-full"
                        autoFocus
                        type="number"
                        decimalSeparator="."
                      />
                    ) : (
                      <EverInput
                        className="w-full"
                        disabled={selectedObjectDetail ? false : true}
                      />
                    )}
                  </EverForm.Item>
                </Space>
              </RemovableInputGroup>
            </div>
          ))}

          <EverForm.Item shouldUpdate>
            <EverButton
              type="link"
              onClick={() => onAddSuccessActions(add)}
              icon={<PlusCircleIconSolid className="h-4 w-4" />}
              disabled={selectedObjectDetail ? false : true || disableButton}
            >
              Add Criteria
            </EverButton>
          </EverForm.Item>
        </>
      )}
    </EverForm.List>
  );
}

function EmailComponent({ selectedObjectDetail }) {
  return (
    <EverForm.Item
      name="emailField"
      className="w-64"
      rules={[
        {
          required: true,
          message: "Please select email field",
        },
      ]}
    >
      <EverSelect
        showSearch
        filterOption={(input, option) => {
          return (
            option?.children !== undefined &&
            option.children.toLowerCase().includes(input.toLowerCase())
          );
        }}
        placeholder="Select Object"
        disabled={selectedObjectDetail ? false : true}
        getPopupContainer={(trigger) => trigger.parentNode}
      >
        {selectedObjectDetail &&
          selectedObjectDetail.map(
            ({ systemName, displayName, dataType }, index) => {
              return dataType === "Email" ? (
                <Option key={index} value={systemName}>
                  {displayName}
                </Option>
              ) : null;
            }
          )}
      </EverSelect>
    </EverForm.Item>
  );
}

function DateComponent({ selectedObjectDetail }) {
  return (
    <EverForm.Item
      name="dateField"
      className="w-64"
      rules={[
        {
          required: true,
          message: "Please select date field",
        },
      ]}
    >
      <EverSelect
        showSearch
        filterOption={(input, option) => {
          return (
            option?.children !== undefined &&
            option.children.toLowerCase().includes(input.toLowerCase())
          );
        }}
        placeholder="Select Object"
        disabled={selectedObjectDetail ? false : true}
        getPopupContainer={(trigger) => trigger.parentNode}
      >
        {selectedObjectDetail &&
          selectedObjectDetail.map(({ systemName, displayName, dataType }) => {
            return dataType === "Date" ? (
              <Option key={systemName} value={systemName}>
                {displayName}
              </Option>
            ) : null;
          })}
      </EverSelect>
    </EverForm.Item>
  );
}

function RowNameComponent({ selectedObjectDetail }) {
  return (
    <EverForm.Item
      name="rowName"
      className="w-64"
      rules={[
        {
          required: true,
          message: "Please set row name",
        },
      ]}
    >
      <EverSelect
        showSearch
        filterOption={(input, option) => {
          return (
            option?.children !== undefined &&
            option.children.toLowerCase().includes(input.toLowerCase())
          );
        }}
        placeholder="Select Object"
        disabled={selectedObjectDetail ? false : true}
        getPopupContainer={(trigger) => trigger.parentNode}
      >
        {selectedObjectDetail &&
          selectedObjectDetail.map(
            ({ systemName, displayName, dataType }, index) => {
              if (
                DATA_TYPES_TO_BE_IGNORED_IN_MODULE.CRYSTAL.includes(dataType)
              ) {
                return null;
              }
              return (
                <Option key={index} value={systemName}>
                  {displayName}
                </Option>
              );
            }
          )}
      </EverSelect>
    </EverForm.Item>
  );
}

function DomainDealField({ selectedObjectDetail, name, required = false }) {
  return (
    <EverForm.Item
      name={name}
      className="w-96"
      rules={[
        {
          required: required,
          message: "Please set row name",
        },
      ]}
    >
      <EverSelect
        allowClear
        showSearch
        filterOption={(input, option) => {
          return (
            option?.children !== undefined &&
            option.children.toLowerCase().includes(input.toLowerCase())
          );
        }}
        placeholder="Select Object"
        disabled={selectedObjectDetail ? false : true}
        getPopupContainer={(trigger) => trigger.parentNode}
      >
        {selectedObjectDetail &&
          selectedObjectDetail.map(
            ({ systemName, displayName, dataType }, index) => {
              if (
                DATA_TYPES_TO_BE_IGNORED_IN_MODULE.CRYSTAL.includes(dataType)
              ) {
                return null;
              }
              return (
                <Option key={index} value={systemName}>
                  {displayName}
                </Option>
              );
            }
          )}
      </EverSelect>
    </EverForm.Item>
  );
}

// function tagRender(props) {
//   const { label, closable, onClose } = props;
//   const onPreventMouseDown = (event) => {
//     event.preventDefault();
//     event.stopPropagation();
//   };
//   return (
//     <Tag
//       color={"blue"}
//       onMouseDown={onPreventMouseDown}
//       closable={closable}
//       onClose={onClose}
//       className="!mr-2"
//     >
//       {label}
//     </Tag>
//   );
// }
function ColumnConfig({ selectedObjectDetail, setSelectedColumns, form }) {
  const [selectedValues, setSelectedValues] = useState(() => {
    // If we're in edit mode and have initialValues, use those
    if (form.getFieldValue("displayColumns")) {
      return form.getFieldValue("displayColumns");
    }
    // Otherwise, select all columns by default for new tables
    return filterIgnoredDataTypes(selectedObjectDetail).map(
      ({ systemName }) => systemName
    );
  });

  const filteredOptions = useMemo(() => {
    return filterIgnoredDataTypes(selectedObjectDetail).map(
      ({ systemName, displayName }) => ({
        key: systemName,
        value: systemName,
        label: displayName,
      })
    );
  }, [selectedObjectDetail]);

  const isAllSelected = useMemo(() => {
    return (
      filteredOptions.length > 0 &&
      selectedValues.length === filteredOptions.length
    );
  }, [selectedValues, filteredOptions]);

  const handleSelectAll = (checked) => {
    try {
      const newValues = checked
        ? filteredOptions.map((option) => option.value)
        : [];
      setSelectedValues(newValues);
      setSelectedColumns(newValues);
      form.setFieldsValue({ displayColumns: newValues });
      form.validateFields(["displayColumns"]);
    } catch (error) {
      console.error("Error in handleSelectAll:", error);
      toast.custom(
        () => (
          <EverHotToastMessage
            type="error"
            description="Failed to update column selection"
          />
        ),
        { position: "top-center" }
      );
    }
  };

  return (
    <>
      {selectedObjectDetail && (
        <EverForm.Item
          name="displayColumns"
          className="w-[428px]"
          initialValue={selectedValues}
          rules={[
            {
              required: true,
              message: "Please select columns to be displayed",
            },
          ]}
        >
          <EverSelect
            placeholder="Select Columns"
            className="w-[428px]"
            mode="multiple"
            value={selectedValues}
            onChange={(values) => {
              setSelectedValues(values);
              setSelectedColumns(values);
            }}
            multiSelectOverflow="scroll"
            maxTagCount={3}
            tagClassName="font-semibold"
            optionLabelProp="label"
            menuItemSelectedIcon={false}
            dropdownRender={(menu) => (
              <div className="px-2 py-2" onClick={(e) => e.stopPropagation()}>
                <div className="px-4 py-1 border-b border-ever-base-200 mb-1">
                  <EverCheckbox
                    className="mb-2"
                    value="selectAll"
                    label={
                      <EverTg.SubHeading4 className="text-ever-base-content">
                        Select all
                      </EverTg.SubHeading4>
                    }
                    checked={isAllSelected}
                    onChange={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      handleSelectAll(e.target.checked);
                    }}
                  />
                </div>
                {menu}
              </div>
            )}
          >
            {filteredOptions.map((item) => (
              <EverSelect.Option
                key={item.key}
                value={item.value}
                label={item.label}
                className={`!p-0 [&_.ant-select-item-option-content]:!p-0 ${
                  selectedValues.includes(item.value) ? "!bg-ever-base-100" : ""
                }`}
              >
                <div className="flex items-center gap-3">
                  <EverCheckbox
                    className="h-max pointer-events-none"
                    checked={selectedValues.includes(item.value)}
                  />
                  <EverTg.Text className="!text-ever-base-content !font-normal">
                    {item.label}
                  </EverTg.Text>
                </div>
              </EverSelect.Option>
            ))}
          </EverSelect>
        </EverForm.Item>
      )}
    </>
  );
}

function DraggableList({
  initialValues,
  firstTime,
  setFirstTime,
  columnConfigData,
  selectedObjectDetail,
  selectedColumns,
  setColumnConfigData,
}) {
  const myAtom = useRecoilValue(myClientAtom);
  const clientFeatures = getClientFeatures(myAtom);
  const [cards, setCards] = useState([]);
  useEffect(() => {
    setFirstTime(true);
  }, [initialValues]);

  useEffect(() => {
    if (columnConfigData.length > selectedColumns.length) {
      // Delete operation
      const dummy = [];
      columnConfigData.map((columnData) => {
        const { systemName, displayName, isEditable, metaData } = columnData;

        if (selectedColumns.includes(systemName)) {
          dummy.push({
            id: systemName,
            systemName: systemName,
            displayName: displayName,
            isEditable: isEditable,
            metaData: metaData,
          });
        }
      });
      setColumnConfigData(dummy);
    }
    if (columnConfigData.length < selectedColumns.length) {
      //Add operation
      const dummy = [...columnConfigData];
      selectedColumns.map((columnId) => {
        if (!_checkIfValueExists(columnConfigData, "systemName", columnId)) {
          selectedObjectDetail.map((object) => {
            const { systemName, displayName, dataType, metaData } = object;
            if (
              !DATA_TYPES_TO_BE_IGNORED_IN_MODULE.CRYSTAL.includes(dataType) &&
              systemName === columnId
            ) {
              if (firstTime && initialValues.editableColumns !== undefined) {
                dummy.push({
                  id: systemName,
                  systemName: systemName,
                  displayName: displayName,
                  isEditable: initialValues.editableColumns.includes(systemName)
                    ? true
                    : false,
                  metaData: metaData,
                });
              } else {
                dummy.push({
                  id: systemName,
                  systemName: systemName,
                  displayName: displayName,
                  isEditable: false,
                  metaData: metaData,
                });
              }
            }
          });
        }
      });
      setColumnConfigData(dummy);
    }
    setFirstTime(false);
  }, [selectedColumns]);

  useEffect(() => {
    if (columnConfigData) {
      const cards = [];
      let isSwitchDisabled = true;
      for (const data of columnConfigData) {
        isSwitchDisabled =
          clientFeatures?.crystalCalcFieldsOverrideLogicV2 &&
          data.metaData !== null;
        cards.push({
          id: data.systemName,
          displayName: data.displayName,
          isEditable: data.isEditable,
          systemName: data.systemName,
          metaData: data.metaData,
          content: (
            <EverCard className="mt-2 w-[428px]" paddingSize="sm">
              <div className="flex items-center justify-between gap-3">
                <div className="flex items-center gap-3 w-1/2">
                  <div className="w-5 flex items-center justify-center">
                    <DotsVerticalIcon className="w-5 h-5 text-ever-base-400" />
                    <DotsVerticalIcon className="w-5 h-5 text-ever-base-400 -ml-2" />
                  </div>
                  <EverTg.Caption.Medium className="text-ellipsis overflow-hidden w-auto">
                    {data.displayName}
                  </EverTg.Caption.Medium>
                  {isSwitchDisabled && (
                    <EverTooltip title="Calculated field cannot be edited.">
                      <HelpCircleIcon className="w-4 h-4 text-ever-base-content-mid" />
                    </EverTooltip>
                  )}
                </div>

                <div className="flex items-center justify-end grow w-1/2 gap-2">
                  <EverSwitch
                    checked={data.isEditable}
                    disabled={isSwitchDisabled}
                    size="small"
                    onChange={(event) => {
                      columnCheckOnchange(event, data.systemName);
                    }}
                  />
                </div>
              </div>
            </EverCard>
          ),
        });
      }
      setCards(cards);
    }
  }, [columnConfigData]);

  function onCardsOrderChange(cards) {
    let cd = [];
    cards.map((data) => {
      cd.push({
        id: data.systemName,
        displayName: data.displayName,
        systemName: data.systemName,
        isEditable: data.isEditable,
        metaData: data.metaData,
      });
    });
    setColumnConfigData(cd);
  }

  function columnCheckOnchange(isEditable, systemName) {
    let cd = [];
    columnConfigData.map((card) => {
      if (systemName === card.systemName) {
        cd.push({
          id: card.systemName,
          displayName: card.displayName,
          isEditable: isEditable,
          systemName: card.systemName,
          metaData: card.metaData,
        });
      } else {
        cd.push({
          id: card.systemName,
          displayName: card.displayName,
          systemName: card.systemName,
          isEditable: card.isEditable,
          metaData: card.metaData,
        });
      }
    });
    setColumnConfigData(cd);
  }

  return (
    <>
      {cards.length > 0 ? (
        <EverDraggableColumns cards={cards} setCards={onCardsOrderChange} />
      ) : (
        <></>
      )}
    </>
  );
}

function TitleWithToolTip({
  title,
  tooltipMessage,
  required,
  tooltipNeeded,
  topMarginNeeded = true,
}) {
  return (
    <div className={twMerge("mb-2", topMarginNeeded ? "mt-8" : "")}>
      <EverLabel
        className="flex items-center"
        required={required}
        append={
          tooltipNeeded ? (
            <EverTooltip
              placement="top"
              title={
                <EverTg.Caption className="text-center">
                  {tooltipMessage}
                </EverTg.Caption>
              }
            >
              <InfoCircleIcon className="h-5 w-5 text-ever-base-content-mid" />
            </EverTooltip>
          ) : null
        }
      >
        {title}
      </EverLabel>
    </div>
  );
}

function _checkIfValueExists(ObjectList, key, value) {
  let isPresent = false;
  ObjectList.map((object) => {
    if (object[key] === value) {
      isPresent = true;
    }
  });
  return isPresent;
}

//This component is used to configure the crystal setup form for cpq
function CpqCrystalSetupForm({ selectedObjectDetail }) {
  return (
    <>
      <div className="flex flex-col gap-2 mt-8">
        <EverTg.SubHeading4 className="text-ever-base-content flex items-center gap-1">
          Configure Everstage CPQ Crystal Integration
        </EverTg.SubHeading4>
      </div>

      <TitleWithToolTip
        title={"Deal ID Field"}
        tooltipMessage={"Choose the column that represents deal ID"}
        required={true}
        tooltipNeeded={true}
      />
      <DomainDealField
        selectedObjectDetail={selectedObjectDetail}
        name="dealId"
        required={true}
      />
      <TitleWithToolTip
        title={"Amount"}
        tooltipMessage={"Choose the column that represents deal amount"}
        required={true}
        tooltipNeeded={true}
      />
      <DomainDealField
        selectedObjectDetail={selectedObjectDetail}
        name="dealAmount"
        required={true}
      />
    </>
  );
}
