import { parseISO } from "date-fns";
import { cloneDeep, isEmpty } from "lodash";
import React, { useCallback, useState } from "react";
import { useTranslation } from "react-i18next";
import { Portal } from "react-portal";
import { useQuery } from "react-query";
import { useNavigate, useLocation, useParams } from "react-router-dom";
import { v4 as uuidv4 } from "uuid";

import { RBAC_ROLES } from "~/Enums";
import { useEmployeeStore } from "~/GlobalStores/EmployeeStore";
import { useUserPermissionStore } from "~/GlobalStores/UserPermissionStore";
import { useStepedQuery, EverLoader, EverTg } from "~/v2/components";
import { useCrystalAPI } from "~/v2/features/crystal/api";
import { CommonLottie } from "~/v2/features/crystal/common/CommonLottie";
import overAchieverAnimationData from "~/v2/features/crystal/crystal-lotties/ballooncelebration.json";
import achieverAnimationData from "~/v2/features/crystal/crystal-lotties/celebration-animation.json";
import almostThereAnimationData from "~/v2/features/crystal/crystal-lotties/minor-confetti.json";

import { CrystalDashboard } from "./CrystalDashboard";
import { CrystalTablesEditor } from "./CrystalTablesEditor";
import IntelligentLoader from "./intelligent-loader/IntelligentLoader";
import useFetchApiWithAuth from "../../datasheet/useFetchApiWithAuth";

export function CrystalPayeePage() {
  const { forUser, simulatorId } = useParams();
  const { userEmail } = useEmployeeStore();
  const crystalAPI = useCrystalAPI();
  const navigate = useNavigate();
  const { fetchData } = useFetchApiWithAuth();

  const [payeeEmail, setPayeeEmail] = useState(userEmail);
  const [whatIfData, setWhatIfData] = useState();

  const [isDrawerVisible, setDrawerVisible] = useState(false);
  const [selectedTableName, setSelectedTableName] = useState("");
  const [disableAddProjection, setDisableAddProjection] = useState(true);
  const [projectionWidgetData, setProjectionWidgetData] = useState([]);
  const [selectedPeriod, setSelectedPeriod] = useState(null);
  const [uniqueKey, setUniqueKey] = useState(uuidv4());
  // const [celebrate, setCelebrate] = useState(null);

  const { hasPermissions } = useUserPermissionStore();

  const { t } = useTranslation();

  const celebrationDelay = 1000;

  const onOpenProjectionDrawer = useCallback((selectedTableName) => {
    setSelectedTableName(selectedTableName);
    setDrawerVisible(true);
  }, []);
  const onApplyProjection = useCallback(
    (projectedData, keepEditorOpen = false) => {
      // remove item data if no row is selected from a table
      projectedData = projectedData.filter((x) => x.data[0]);
      setProjectionWidgetData(projectedData);
      setWhatIfData(
        cloneDeep(projectedData).map((x) => {
          delete x.crystalTableName;
          delete x.crystalTableMeta;
          return x;
        })
      );
      if (!keepEditorOpen) {
        setDrawerVisible(false);
      }
      if (projectedData.length === 0) {
        setUniqueKey(uuidv4());
      }
    },
    []
  );
  const onPayeeChange = useCallback((email) => {
    if (hasPermissions(RBAC_ROLES.MANAGE_CRYSTAL)) {
      navigate(`/crystal/preview/${simulatorId}/${email}`);
    } else {
      setPayeeEmail(email);
    }
    setWhatIfData();
    setProjectionWidgetData([]);
    onPeriodChange(null);
  }, []);

  const onPeriodChange = (val, projectedData = null) => {
    //ICM has always projectedData as null
    if (!val) {
      setSelectedPeriod();
      return;
    }

    const periods = val.split(" <-> ");
    const psd = parseISO(periods[0]);
    const ped = parseISO(periods[1]);
    setSelectedPeriod({
      [val]: [psd, ped],
    });
    if (projectedData?.length) {
      /**
       * This block is for CPQ: Initialize with the data which passed in the state of the CPQ crystal URL
       * This initial data is used to calculate the commision when this page loaded initailly
       */
      setProjectionWidgetData(projectedData);
      setWhatIfData(
        cloneDeep(projectedData).map((x) => {
          delete x.crystalTableName;
          delete x.crystalTableMeta;
          return x;
        })
      );
      setUniqueKey(uuidv4());
    } else {
      /**
       * This block is for ICM : This is the existing behaviour for the ICM (Initially data is empty)
       * If the CPQ crystal URL has no Projected Data then used this ICM default behaviour
       */
      setWhatIfData();
      setProjectionWidgetData([]);
    }
  };

  const currentPayee = forUser || payeeEmail;
  const { search, state } = useLocation();
  const query = new URLSearchParams(search);
  const knowledgeDate = query.get("kd") || null;

  const postData = dashboardDataPayload(
    currentPayee,
    whatIfData,
    simulatorId,
    selectedPeriod
  );

  const {
    data: payeePeriodOptions,
    isError: payeePeriodOptionsError,
    error: payeePeriodOptionsErrorData,
  } = useQuery(
    ["fetchFuturePeriods", currentPayee],
    () => {
      return fetchData(`/crystal/future-periods?payee=${currentPayee}`);
    },
    {
      refetchOnWindowFocus: false,
      onSuccess: (data) => {
        data.data?.[0] &&
          onPeriodChange(data.data[0].value, state?.whatIfData || null);
      },
      retry: false,
    }
  );

  const { data, isError, isLoading, error } = useQuery(
    ["fetchDashboardData", currentPayee, whatIfData, selectedPeriod],
    () => crystalAPI.fetchDashboardData(postData),
    {
      enabled: selectedPeriod !== null,
      refetchOnWindowFocus: false,
    }
  );

  const { currentStep, stepsCompleted } = useStepedQuery(isLoading, [
    {
      timeout: 3000,
    },
    {
      timeout: 3000,
    },
    {
      timeout: 3000,
    },
  ]);

  if (data?.dashboardData) {
    data.dashboardData.projections = projectionWidgetData;
    if (projectionWidgetData.length === 0) {
      data.dashboardData.payoutData.projectedPayout =
        data.dashboardData.payoutData.projectedPayout || null;
      for (const item of data.dashboardData.quotaAttainmentList) {
        item.quotaAttainmentData.projectedAttainment =
          item.quotaAttainmentData.projectedAttainment || null;
      }
    }
  }

  const showCalculating = isLoading && !isEmpty(whatIfData);

  // useEffect(() => {
  //   setTimeout(() => {
  //     data?.dashboardData.projections.length > 0 &&
  //       setCelebrate(_getCelebrateType(data.dashboardData));
  //   }, celebrationDelay);
  // }, [data]);

  const showIntelligentLoader = true;
  const backupData = t("CALCULATING_COMMISSIONS_CRYSTAL");

  return (
    <div className="crystal">
      {showIntelligentLoader
        ? !isEmpty(whatIfData) &&
          (isLoading || !stepsCompleted) && (
            <IntelligentLoader
              currentStep={currentStep}
              stepsCompleted={stepsCompleted}
              extraTime={isLoading && stepsCompleted}
            />
          )
        : showCalculating && <SimpleLoader backupData={backupData} />}
      <CrystalDashboard
        isLoading={isLoading}
        onOpenProjectionDrawer={onOpenProjectionDrawer}
        onPayeeChange={onPayeeChange}
        selectedPeriod={selectedPeriod}
        onPeriodChange={onPeriodChange}
        payeePeriodOptions={payeePeriodOptions?.data}
        payeeEmail={currentPayee}
        disableAddProjection={disableAddProjection}
        dashboardData={showCalculating ? null : data?.dashboardData}
        dashboardDataError={isError ? error : null}
        payeePeriodOptionsError={
          payeePeriodOptionsError ? payeePeriodOptionsErrorData : null
        }
        onApplyProjection={onApplyProjection}
        celebrationDelay={celebrationDelay}
      />
      <CrystalTablesEditor
        knowledgeDate={knowledgeDate}
        key={uniqueKey}
        isDrawerVisible={isDrawerVisible}
        selectedTableName={selectedTableName}
        payeeEmail={currentPayee}
        setDisableAddProjection={setDisableAddProjection}
        onApplyProjection={onApplyProjection}
        onCloseProjectionDrawer={() => setDrawerVisible(false)}
        whatIfData={whatIfData}
      />
      {/* Will enable this celebration later */}
      {/* {celebrate && (
        <EverstageCelebration type={celebrate}></EverstageCelebration>
      )} */}
    </div>
  );
}

/**
 *
 * @param {dashboardData}
 * @returns void
 *
 * Will be uncommented when celebration is enabled
 */

// function _getCelebrateType(dashboardData) {
//   if (
//     !dashboardData ||
//     dashboardData.quotaAttainmentList[0].quotaAttainmentData.quotaAttainment ===
//       0 ||
//     dashboardData.payoutData.projectedPayout === 0
//   )
//     return null;
//   let qAttainment =
//     dashboardData.quotaAttainmentList[0].quotaAttainmentData.quotaAttainment;
//   if (qAttainment < 90) {
//     return null;
//   } else if (qAttainment <= 99) {
//     return "almost";
//   } else if (qAttainment <= 150) {
//     return "achiever";
//   } else {
//     return "overAchiever";
//   }
// }

export function EverstageCelebration({ type }) {
  const { animationOn } = useEmployeeStore();
  if (!animationOn) return "";
  if (!type) return "";

  let Celebration = <></>;
  switch (type) {
    case "almost": {
      Celebration = AlmostThere;
      break;
    }
    case "achiever": {
      Celebration = Achiever;
      break;
    }
    case "overAchiever": {
      Celebration = OverAchiever;
      break;
    }
  }
  return (
    <Portal>
      <Celebration />
    </Portal>
  );
}

function AlmostThere() {
  return (
    <CommonLottie
      animationData={almostThereAnimationData}
      runningTime={10_000}
    />
  );
}

function Achiever() {
  return (
    <CommonLottie animationData={achieverAnimationData} runningTime={10_000} />
  );
}

function OverAchiever() {
  return (
    <CommonLottie
      animationData={overAchieverAnimationData}
      runningTime={10_000}
    />
  );
}

function dashboardDataPayload(
  payeeEmail,
  projectedData,
  simulatorId,
  selectedPeriod
) {
  let projectedDataArr = Object.entries(projectedData || []).map((x) => x[1]);
  let label =
    (selectedPeriod && Object.keys(selectedPeriod)[0]?.split(" <-> ")) || null;
  let res = {
    crystal_view_id: simulatorId,
    for_user: payeeEmail,
    whatIfData: projectedDataArr || [],
    kd: (label && label[1]) || null,
  };
  if (simulatorId) res["crystal_view_id"] = simulatorId;
  return res;
}

function SimpleLoader({ backupData }) {
  return (
    <EverLoader indicatorType="spinner" spinning={true}>
      <EverTg.Heading3 className="my-4 w-[750px]">{backupData}</EverTg.Heading3>
    </EverLoader>
  );
}
