import {
  DataflowIcon,
  InfoCircleIcon,
  LinkExternalIcon,
} from "@everstage/evericons/outlined";
import { cloneDeep } from "lodash";
import React, { useState, useEffect, useRef, useLayoutEffect } from "react";
import { useTranslation } from "react-i18next";
import { useQuery as useReactQuery } from "react-query";
import { useRecoilValue } from "recoil";

import {
  createApprovalRequest,
  getApprovalConfig,
} from "~/Api/ApprovalWorkflowService";
import {
  RBAC_ROLES,
  PAYOUT_APPROVAL_TYPES,
  LINE_ITEM_TYPE_ENTITY_TYPE,
} from "~/Enums";
import { myClientAtom } from "~/GlobalStores/atoms";
import { useAuthStore } from "~/GlobalStores/AuthStore";
import { useEmployeeStore } from "~/GlobalStores/EmployeeStore";
import { useUserPermissionStore } from "~/GlobalStores/UserPermissionStore";
import { getClientFeatures } from "~/Utils/ClientFeaturesUtils";
import {
  safeGetLocalStorage,
  safeSetLocalStorage,
  LOCAL_STORAGE_KEYS,
} from "~/Utils/localStorageUtils";
import {
  EverLoader,
  EverButton,
  EverModal,
  EverCard,
  EverHotToastMessage,
  toast,
  EverTg,
  EverInput,
  EverRadio,
  EverTooltip,
} from "~/v2/components";
import RBACProtectedComponent from "~/v2/components/rbac-wrapper/RBACProtectedComponent";
import { ApprovalWorkFlowTemplateBuilder } from "~/v2/features/approvals/approval-workflow-builder";
import { noWorkflowsFound } from "~/v2/images";

export const ApprovalWorkFlowTemplateSelector = (props) => {
  const { accessToken } = useAuthStore();
  const { t } = useTranslation();
  const {
    showSelector,
    setShowSelector,
    title,
    templatesRefetch,
    templatesMetaData,
    requestParams,
    refetch,
    loading,
  } = props;
  const [selectedTemplateId, setSelectedTemplateId] = useState(null);
  const [showTemplateBuilder, setShowTemplateBuilder] = useState(false);
  const { rolesRefetch } = useEmployeeStore();
  const [recentWorkflows, setRecentWorkflows] = useState([]);
  const [allWorkflows, setAllWorkflows] = useState([]);
  const [searchWorkflows, setSearchWorkflows] = useState([]);
  const [isFocused, setIsFocused] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");

  const [payoutLevel, setPayoutLevel] = useState(
    PAYOUT_APPROVAL_TYPES.STATEMENT_LEVEL
  );

  const { data: approvalConfigData } = useReactQuery(
    ["getApprovalConfig"],
    () => getApprovalConfig(accessToken),
    {
      retry: false,
      cacheTime: 0,
      refetchOnWindowFocus: false,
    }
  );

  useEffect(() => {
    if (approvalConfigData?.status === "SUCCESS") {
      const config = approvalConfigData?.data;
      const approvalType = config?.payoutApprovals?.lineItemLevelApproval
        ? PAYOUT_APPROVAL_TYPES.LINE_ITEM_LEVEL
        : PAYOUT_APPROVAL_TYPES.STATEMENT_LEVEL;
      setPayoutLevel(approvalType);
    }
  }, [approvalConfigData]);

  // Update local storage with recent workflows
  const updateRecentWorkflows = (selectedTemplateId) => {
    // Store recently used approval workflow templates
    const recentWorkflowIds = safeGetLocalStorage(
      LOCAL_STORAGE_KEYS.RECENT_APPROVAL_WORKFLOW_IDS,
      []
    );

    // Remove if same template exists
    const filteredWorkflowIds = recentWorkflowIds.filter(
      (templateId) => templateId !== selectedTemplateId
    );

    // Add new workflow at start
    filteredWorkflowIds.unshift(selectedTemplateId);

    // Keep only latest 3 workflows and update localStorage
    const success = safeSetLocalStorage(
      LOCAL_STORAGE_KEYS.RECENT_APPROVAL_WORKFLOW_IDS,
      filteredWorkflowIds.slice(0, 3)
    );

    if (!success) {
      // Optionally show a user-friendly message
      // toast.warning("Unable to save recent workflows");
    }
  };

  const onSendApprovalRequest = () => {
    const params = cloneDeep(requestParams);
    if (payoutLevel === PAYOUT_APPROVAL_TYPES.LINE_ITEM_LEVEL) {
      params["entity_type"] = LINE_ITEM_TYPE_ENTITY_TYPE;
    }
    params["template_id"] = selectedTemplateId;

    params.instance_params.instance_details.forEach(
      (ele) => delete ele["name"]
    );

    // Empty out instance details when isSelectedAll is true
    // The instance details will be fetched in backend to process.
    if (params.bulk_mode && params.isSelectedAll) {
      params.instance_params.instance_details = [];
    }

    return new Promise(() => {
      createApprovalRequest(params, accessToken)
        .then((response) => {
          if (response.ok) {
            response.json().then((data) => {
              toast.custom(
                () => (
                  <EverHotToastMessage
                    type="success"
                    description={data.message}
                  />
                ),
                { position: "top-center" }
              );
              updateRecentWorkflows(selectedTemplateId);
            });
          } else {
            response.json().then((data) => {
              toast.custom(
                () => (
                  <EverHotToastMessage
                    type="error"
                    description={
                      data?.message ?? t("APPROVAL_REQUEST_CREATION_FAILED")
                    }
                  />
                ),
                { position: "top-center" }
              );
            });
          }
        })
        .finally(() => {
          setSelectedTemplateId(null);
          setShowSelector(false);
          refetch();
          rolesRefetch();
        });
    });
  };

  useEffect(() => {
    // Get recently used workflow IDs from localStorage
    const recentWorkflowIds = safeGetLocalStorage(
      LOCAL_STORAGE_KEYS.RECENT_APPROVAL_WORKFLOW_IDS,
      []
    );

    // Get all available approval templates
    const allTemplates = templatesMetaData?.approvalTemplatesByEntityType || [];

    // Filter templates to get recently used workflows based on stored IDs in order
    const recentWorkflows = recentWorkflowIds
      .map((id) => allTemplates.find((template) => template.templateId === id))
      .filter(Boolean); // Remove any undefined entries if template not found

    // Filter templates to get workflows that haven't been used recently
    const allWorkflows = allTemplates.filter(
      (template) => !recentWorkflowIds.includes(template.templateId)
    );

    // Update state with filtered workflows
    setRecentWorkflows(recentWorkflows);
    setAllWorkflows(allWorkflows);
  }, [templatesMetaData]); // Re-run effect when templates data changes

  /**
   * Handles search functionality for approval workflows
   * Only searches when input is 3 or more characters
   * Filters templates based on case-insensitive template name matching
   *
   * @param {Object} event - Search input change event
   */
  const handleSearch = (event) => {
    // Only set search value if 3+ characters, otherwise empty string
    const searchValue =
      event.target.value.length >= 3 ? event.target.value.toLowerCase() : "";

    // Get all available templates, default to empty array if none
    const allTemplates = templatesMetaData?.approvalTemplatesByEntityType || [];

    // Filter templates if search value exists
    let searchWorkflows = [];
    if (searchValue) {
      searchWorkflows = allTemplates.filter((template) =>
        template.templateName.toLowerCase().includes(searchValue)
      );
      // Reset selectedTemplateId if it's not in filtered results
      if (
        selectedTemplateId &&
        !searchWorkflows.find(
          (template) => template.templateId === selectedTemplateId
        )
      ) {
        setSelectedTemplateId(null);
      }
    }

    // Update search state
    setSearchTerm(searchValue);
    setSearchWorkflows(searchWorkflows);
  };

  /**
   * Returns the modal title component
   *
   * @returns {React.ReactNode} The modal title component
   */
  const modalTitle = () => {
    return (
      <div className="flex flex-col gap-6">
        {title}{" "}
        <div className="flex justify-between gap-2 ">
          <EverTg.SubHeading4 className="text-ever-base-content flex items-center">
            {t("PICK_WORKFLOW_SEND_REQUEST")}
          </EverTg.SubHeading4>
          <EverInput.Search
            placeholder={isFocused ? "Enter at least 3 characters" : "Search"}
            onChange={handleSearch}
            onFocus={() => setIsFocused(true)}
            onBlur={() => setIsFocused(false)}
            size="small"
            className="w-56"
          />
        </div>
      </div>
    );
  };

  return (
    <>
      <EverModal
        closable={true}
        bodyStyle={{ height: 480, overflowY: "auto" }}
        width="950px"
        visible={showSelector}
        onCancel={() => setShowSelector(false)}
        maskClosable={false}
        title={modalTitle()}
        footer={
          <EverModal.Footer>
            <ApprovalTemplateSelectorFooter
              setShowSelector={setShowSelector}
              setShowTemplateBuilder={setShowTemplateBuilder}
              isSendApprovalDisabled={selectedTemplateId == null}
              onSendApprovalRequest={onSendApprovalRequest}
            />
          </EverModal.Footer>
        }
      >
        <div className="flex flex-col gap-6">
          {searchTerm.length > 0 ? (
            <SearchWorkflowResults
              searchWorkflows={searchWorkflows}
              selectedTemplateId={selectedTemplateId}
              setSelectedTemplateId={setSelectedTemplateId}
            />
          ) : (
            <ApprovalWorkflowListView
              recentWorkflows={recentWorkflows}
              allWorkflows={allWorkflows}
              selectedTemplateId={selectedTemplateId}
              setSelectedTemplateId={setSelectedTemplateId}
              templatesMetaData={templatesMetaData}
              loading={loading}
            />
          )}
        </div>
      </EverModal>
      {showTemplateBuilder && (
        <ApprovalWorkFlowTemplateBuilder
          showApproval={showTemplateBuilder}
          setShowApproval={setShowTemplateBuilder}
          mode="create"
          save="optional"
          title={title}
          type="instance"
          setEditTemplateId={() => {}}
          existingTemplatesRefetch={templatesRefetch}
          onSaveCloseBuilder={true}
          showBackToWorkflows={true}
          requestParams={requestParams}
          setShowSelector={setShowSelector}
          refetch={refetch}
        />
      )}
    </>
  );
};

/**
 * SearchWorkflowResults component
 * Displays a list of search results for approval workflows
 *
 * @param {Object} props - Component props
 * @param {Array} props.searchWorkflows - Array of workflow templates to display
 * @param {string} props.selectedTemplateId - The ID of the selected template
 * @param {function} props.setSelectedTemplateId - Function to set the selected template ID
 */
const SearchWorkflowResults = (props) => {
  const { searchWorkflows, selectedTemplateId, setSelectedTemplateId } = props;

  return searchWorkflows.length > 0 ? (
    <div className="flex flex-col gap-3">
      {searchWorkflows.map((templateMetaData, index) => (
        <ApprovalTemplateCardView
          key={index}
          templateData={templateMetaData}
          isSelected={selectedTemplateId == templateMetaData.templateId}
          setSelected={setSelectedTemplateId}
        />
      ))}
    </div>
  ) : (
    <div className="flex flex-col items-center justify-center w-full h-[465px] gap-8">
      <img
        src={noWorkflowsFound}
        className="w-32 h-32"
        alt="No workflows found"
      />
      <div className="flex flex-col gap-1">
        <EverTg.Heading4 className="text-ever-base-content text-center">
          No workflows found
        </EverTg.Heading4>
        <EverTg.Caption className="text-ever-base-content-mid text-center">
          Try a different search term
        </EverTg.Caption>
      </div>
    </div>
  );
};

/**
 * ApprovalWorkflowListView component
 * Displays a list of approval workflows
 *
 * @param {Object} props - Component props
 * @param {Array} props.recentWorkflows - Array of recently used workflow templates
 * @param {Array} props.allWorkflows - Array of all workflow templates
 * @param {string} props.selectedTemplateId - The ID of the selected template
 * @param {function} props.setSelectedTemplateId - Function to set the selected template ID
 * @param {Object} props.templatesMetaData - Metadata for templates
 * @param {boolean} props.loading - Whether the all workflows data is loading
 */
const ApprovalWorkflowListView = (props) => {
  const {
    recentWorkflows,
    allWorkflows,
    selectedTemplateId,
    setSelectedTemplateId,
    templatesMetaData,
    loading,
  } = props;

  if (loading) {
    return <EverLoader indicatorType="spinner" tip="" />;
  }

  return (
    <>
      {recentWorkflows.length > 0 && (
        <div className="flex flex-col gap-4">
          <EverTg.Text className="text-ever-base-content-mid font-medium">
            RECENTS
          </EverTg.Text>
          <div className="flex flex-col gap-3">
            {recentWorkflows.map((templateMetaData, index) => (
              <ApprovalTemplateCardView
                key={index}
                templateData={templateMetaData}
                isSelected={selectedTemplateId == templateMetaData.templateId}
                setSelected={setSelectedTemplateId}
              />
            ))}
          </div>
        </div>
      )}
      <div className="flex flex-col gap-4">
        <EverTg.Text className="text-ever-base-content-mid font-medium">
          ALL
        </EverTg.Text>
        <div className="flex flex-col gap-3">
          {templatesMetaData &&
            allWorkflows.map((templateMetaData, index) => (
              <ApprovalTemplateCardView
                key={index}
                templateData={templateMetaData}
                isSelected={selectedTemplateId == templateMetaData.templateId}
                setSelected={setSelectedTemplateId}
              />
            ))}
        </div>
      </div>
    </>
  );
};

const ApprovalTemplateCardView = (props) => {
  const myAtom = useRecoilValue(myClientAtom);
  const clientFeatures = getClientFeatures(myAtom);
  const { hasPermissions } = useUserPermissionStore();
  const { templateData, isSelected, setSelected } = props;
  const { templateId, templateName, templateDescription } = templateData;
  const [isTruncated, setIsTruncated] = useState(false);
  const descriptionRef = useRef(null);
  useLayoutEffect(() => {
    if (descriptionRef.current) {
      setIsTruncated(
        descriptionRef.current.offsetWidth < descriptionRef.current.scrollWidth
      );
    }
  }, []);
  return (
    <EverCard
      className={`w-full cursor-pointer bg-ever-base px-6 hover:ring-2 hover:border-ever-primary hover:border-b ${
        isSelected ? "ring-2 border-ever-primary border-b" : ""
      }`}
      //interactive
      onClick={() => {
        setSelected(templateId);
      }}
    >
      <div className="flex items-center w-full">
        <EverRadio size="small" checked={isSelected} />
        <div className="flex flex-col gap-2 truncate">
          <div className="flex items-center gap-2">
            <EverTg.Heading3>{templateName}</EverTg.Heading3>
            {hasPermissions(RBAC_ROLES.MANAGE_APPROVAL_WORKFLOWS) &&
              clientFeatures?.showApprovalFeature && (
                <EverTooltip title="Go to approval configuration">
                  <LinkExternalIcon
                    className="w-3.5 h-3.5 text-ever-primary"
                    onClick={(e) => {
                      e.stopPropagation();
                      window.open(
                        `/settings/workflows?templateId=${templateId}`,
                        "_blank"
                      );
                    }}
                  />
                </EverTooltip>
              )}
          </div>
          <div className="flex items-center gap-1">
            <span ref={descriptionRef} className="truncate">
              <EverTg.Caption className="text-ever-base-content-mid whitespace-nowrap overflow-hidden text-ellipsis">
                {templateDescription ? templateDescription : "-"}
              </EverTg.Caption>
            </span>
            {isTruncated && (
              <EverTooltip title={templateDescription}>
                <InfoCircleIcon className="w-4 h-4 text-ever-base-content-mid flex shrink-0" />
              </EverTooltip>
            )}
          </div>
        </div>
      </div>
    </EverCard>
  );
};

const ApprovalTemplateSelectorFooter = (props) => {
  const {
    setShowSelector,
    isSendApprovalDisabled,
    setShowTemplateBuilder,
    onSendApprovalRequest,
  } = props;
  const { t } = useTranslation();

  return (
    <div className="flex items-center text-left">
      <div className="grow">
        <RBACProtectedComponent permissionId={RBAC_ROLES.MANAGE_CONFIG}>
          <EverButton
            type="link"
            onClick={() => {
              setShowTemplateBuilder(true);
            }}
            prependIcon={<DataflowIcon className="w-4 h-4" />}
          >
            {t("CREATE_CUSTOM_WORKFLOW")}
          </EverButton>
        </RBACProtectedComponent>
      </div>
      <EverButton color="base" onClick={() => setShowSelector(false)}>
        {t("CANCEL")}
      </EverButton>
      <EverButton
        onClick={onSendApprovalRequest}
        disabled={isSendApprovalDisabled}
      >
        {t("SEND_APPROVAL_REQUEST")}
      </EverButton>
    </div>
  );
};
