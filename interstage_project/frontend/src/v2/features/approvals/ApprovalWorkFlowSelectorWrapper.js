import { useQuery, gql } from "@apollo/client";
import moment from "moment";
import React from "react";
import { useTranslation } from "react-i18next";
import { useRecoilValue } from "recoil";

import { RBAC_ROLES, DATE_FORMATS } from "~/Enums";
import { myClientAtom } from "~/GlobalStores/atoms";
import { getClientFeatures } from "~/Utils/ClientFeaturesUtils";
import { EverDivider, EverTg } from "~/v2/components";
import RBACProtectedComponent from "~/v2/components/rbac-wrapper/RBACProtectedComponent";
import { ApprovalWorkFlowTemplateBuilder } from "~/v2/features/approvals/approval-workflow-builder";
import { ApprovalWorkFlowTemplateSelector } from "~/v2/features/approvals/ApprovalWorkFlowSelector";

import { ENTITY_TYPES } from "./Constants";

const { DDMMMYYYY, MMMYYYY } = DATE_FORMATS;

const GET_EXISTING_TEMPLATES = gql`
  query AllApprovalTemplatesByType(
    $excludeInactive: Boolean!
    $entityType: String!
  ) {
    approvalTemplatesByEntityType(
      excludeInactive: $excludeInactive
      entityType: $entityType
    ) {
      templateId
      templateName
      templateDescription
    }
  }
`;

export const ApprovalWorkFlowTemplateSelectorWrapper = (props) => {
  const { showSelector, setShowSelector, requestParams, refetch } = props;
  const myAtom = useRecoilValue(myClientAtom);
  const clientFeatures = getClientFeatures(myAtom);
  const { data: templatesMetaData, refetch: templatesRefetch } = useQuery(
    GET_EXISTING_TEMPLATES,
    {
      variables: {
        excludeInactive: true,
        entityType: requestParams.entity_type,
      },
      fetchPolicy: "no-cache",
    }
  );

  const { t } = useTranslation();

  const getDisplayDate = (date) => {
    const momentDate = moment(date, "DD-MM-YYYY");
    if (clientFeatures.customCalendar) {
      return momentDate.format(DDMMMYYYY);
    }
    return momentDate.format(MMMYYYY);
  };

  const Title = () => {
    let nameText = "";
    if (requestParams.entity_type == ENTITY_TYPES.PAYOUT_APPROVALS) {
      nameText =
        requestParams.instance_params.instance_details.length > 1
          ? requestParams.instance_params.instance_details.length +
            ` ${t("PAYOUTS").toLowerCase()}`
          : requestParams.instance_params.instance_details[0].name;
    }
    return (
      <div className="flex items-center">
        <EverTg.Text>{t("REQUEST_APPROVAL")}</EverTg.Text>
        <EverDivider type="vertical" className="mx-2 h-5" />
        <EverTg.Description className="font-medium">
          {nameText}
        </EverTg.Description>
        <EverDivider type="vertical" className="mx-2 h-5" />
        <EverTg.Description className="font-medium text-ever-base-content-mid">
          {getDisplayDate(requestParams.instance_params.date)}
        </EverTg.Description>
      </div>
    );
  };
  return (
    <>
      {templatesMetaData &&
        templatesMetaData?.approvalTemplatesByEntityType.length > 0 && (
          <ApprovalWorkFlowTemplateSelector
            showSelector={showSelector}
            setShowSelector={setShowSelector}
            title={<Title />}
            templatesMetaData={templatesMetaData}
            templatesRefetch={templatesRefetch}
            requestParams={requestParams}
            refetch={refetch}
          />
        )}

      {templatesMetaData &&
        !templatesMetaData?.approvalTemplatesByEntityType.length > 0 && (
          <RBACProtectedComponent permissionId={RBAC_ROLES.MANAGE_CONFIG}>
            <ApprovalWorkFlowTemplateBuilder
              showApproval={showSelector}
              setShowApproval={setShowSelector}
              mode="create"
              save="optional"
              title={<Title />}
              type="instance"
              setEditTemplateId={() => {}}
              existingTemplatesRefetch={templatesRefetch}
              onSaveCloseBuilder={true}
              showBackToWorkflows={false}
              requestParams={requestParams}
              refetch={refetch}
            />
          </RBACProtectedComponent>
        )}
    </>
  );
};
