import { EditPencilIcon } from "@everstage/evericons/outlined";
import { debounce, isEmpty, orderBy } from "lodash";
import { observer, useLocalStore } from "mobx-react";
import moment from "moment";
import React, { useCallback, useEffect, useRef, useState } from "react";
import { useQuery as useReactQuery } from "react-query";
import { useRecoilValue } from "recoil";

import { getPayoutFrequencyList } from "~/Api/CommissionPlanService";
import { getDataPermissionForTeams } from "~/Api/TeamsService";
import { COMPONENTS, SEARCH_BOX } from "~/Enums";
import { navPortalAtom } from "~/GlobalStores/atoms";
import { useAuthStore } from "~/GlobalStores/AuthStore";
import {
  EverInput,
  EverNavPortal,
  EverTabs,
  EverButton,
} from "~/v2/components";
import {
  getAllUserRoles,
  getLocalizationLanguages,
} from "~/v2/features/admin-settings/user-roles/services";

import AddTeamButton from "./addTeamButton";
import TeamStore from "./store";
import DynamicTable from "./teamsTabView/dynamicTable";
import StaticTable from "./teamsTabView/staticTable";

const { TabPane } = EverTabs;
const teamKeys = [];

const sortAllUserRoles = (userRoles) => {
  const sortedUserRoles = orderBy(
    userRoles,
    [
      (userRole) =>
        moment(userRole.createdAt, moment.HTML5_FMT.DATETIME_LOCAL_MS),
    ],
    ["asc"]
  );
  return sortedUserRoles;
};

const Teams = observer((props) => {
  const teamStore = useLocalStore(() => new TeamStore(), props);
  const { accessToken, email } = useAuthStore();

  const tempSearchValue = useRef("");
  const [tabKey, setTabKey] = useState("1");
  const [searchValue, setSearchValue] = useState("");
  const [isSearchInProgress, setIsSearchInProgress] = useState(false);

  const [dataPermission, setDataPermission] = useState(null);
  const [isDataPermissionLoading, setIsDataPermissionLoading] = useState(false);
  const [alluserRoles, setAlluserRoles] = useState([]);
  const [allAvailableLanguages, setAllAvailableLanguages] = useState([]);
  const [expandedKeys, setExpandedKeys] = useState([]);

  const navPortalLocation = useRecoilValue(navPortalAtom);

  useReactQuery(
    ["getPayoutFrequencyList", "teams"],
    () => getPayoutFrequencyList(accessToken),
    {
      retry: false,
      cacheTime: 0,
      refetchOnWindowFocus: false,
      onSuccess: (response) => {
        teamStore.setPayoutFreqList(response);
      },
    }
  );

  useEffect(() => {
    setIsDataPermissionLoading(true);
    getDataPermissionForTeams(
      {
        emailId: email,
        component: COMPONENTS.MANAGE_USERS,
      },
      accessToken
    )
      .then((res) => res.json())
      .then(({ type }) => {
        setDataPermission(type);
      })
      .finally(() => {
        setIsDataPermissionLoading(false);
      });
  }, []);

  useEffect(() => {
    getAllUserRoles(accessToken)
      .then((userRoles) => {
        /*
          userRoles => [{ displayName : String, rolePermissionId: String  }]
        */
        if (!isEmpty(userRoles)) {
          const sortedAllUserRoles = sortAllUserRoles(userRoles);
          setAlluserRoles(sortedAllUserRoles);
        } else {
          setAlluserRoles([]);
        }
      })
      .catch((error) => {
        console.log(error);
      });
    getLocalizationLanguages(accessToken)
      .then((response) => {
        const { languages } = response;
        if (!isEmpty(languages)) {
          setAllAvailableLanguages(languages.filter((lang) => lang.isSelected));
        } else {
          setAllAvailableLanguages([]);
        }
      })
      .catch((error) => {
        console.log(error);
      });
  }, []);

  const searchCallback = useCallback(
    debounce(() => {
      setIsSearchInProgress(false);
    }, 500),
    []
  );

  const onSearch = (event) => {
    const value = event.target.value;
    const trimmedValue = value.trim();
    searchCallback();
    if (
      trimmedValue.length >= SEARCH_BOX.MINIMUM_CHARS &&
      tempSearchValue.current !== trimmedValue
    ) {
      setIsSearchInProgress(true);
    }
    tempSearchValue.current = trimmedValue;
    setSearchValue(value);
  };

  const handleExpandOrCollapse = () => {
    teamKeys.length !== expandedKeys.length
      ? setExpandedKeys(teamKeys)
      : setExpandedKeys([]);
  };

  const buttonActions = (
    <div className="flex items-center justify-end gap-3">
      {tabKey === "1" && (
        <EverInput.Search
          size="small"
          className="w-64"
          placeholder="Search by name or email"
          onChange={onSearch}
          value={searchValue}
          allowClear
        />
      )}
      {/* {tabKey === "1" && (
        <ModifyHierarchyButton dataPermission={dataPermission} />
      )} */}
      {tabKey === "2" && (
        <EverButton
          type="link"
          prependIcon={<EditPencilIcon className="w-4 h-4" />}
          onClick={handleExpandOrCollapse}
        >
          Expand / Collapse All
        </EverButton>
      )}
      <AddTeamButton addStaticTeam={teamStore.addStaticTeam} />
    </div>
  );

  const renderTabBar = (props, DefaultTabBar) => (
    <EverNavPortal target={navPortalLocation}>
      <div className="ant-tabs ant-tabs-top !pt-1">
        <DefaultTabBar {...props} className="!m-0 before:hidden" />
      </div>
    </EverNavPortal>
  );

  useEffect(() => {
    if (accessToken) teamStore.setAccessToken(accessToken);
  }, [accessToken]);

  return (
    <EverTabs
      onChange={(key) => {
        setTabKey(key);
      }}
      tabBarExtraContent={buttonActions}
      renderTabBar={renderTabBar}
      size="small"
    >
      <TabPane tab="Reporting Hierarchy" key="1">
        <DynamicTable
          store={teamStore}
          key="dynamic"
          searchValue={searchValue.trim()}
          isSearchInProgress={isSearchInProgress}
          alluserRoles={alluserRoles}
          allAvailableLanguages={allAvailableLanguages}
        />
      </TabPane>
      <TabPane tab="Custom Teams" key="2">
        {!isDataPermissionLoading && !isEmpty(dataPermission) && (
          <StaticTable
            store={teamStore}
            teamType="team"
            key="team"
            dataPermission={dataPermission}
            alluserRoles={alluserRoles}
            expandedKeys={expandedKeys}
            setExpandedKeys={setExpandedKeys}
            teamKeys={teamKeys}
            allAvailableLanguages={allAvailableLanguages}
          />
        )}
      </TabPane>
      <TabPane tab="Pod Relationships" key="3">
        {!isDataPermissionLoading && !isEmpty(dataPermission) && (
          <StaticTable
            store={teamStore}
            teamType="pod"
            key="pod"
            dataPermission={dataPermission}
            alluserRoles={alluserRoles}
            expandedKeys={expandedKeys}
            setExpandedKeys={setExpandedKeys}
            teamKeys={teamKeys}
            allAvailableLanguages={allAvailableLanguages}
          />
        )}
      </TabPane>
    </EverTabs>
  );
});

export default Teams;
