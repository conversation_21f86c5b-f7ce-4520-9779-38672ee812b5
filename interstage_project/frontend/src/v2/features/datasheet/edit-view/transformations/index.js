import { ChevronDownIcon } from "@everstage/evericons/outlined";
import { Dropdown, Menu } from "antd";
import { cloneDeep, debounce } from "lodash";
import { observer } from "mobx-react";
import React, { useEffect, useCallback, useState, useRef } from "react";
import { useMutation } from "react-query";
import { useRecoilValue } from "recoil";
import { twMerge } from "tailwind-merge";
import { v4 as uuidv4 } from "uuid";

import { sendAnalyticsEvent } from "~/Api/AnalyticsService";
import {
  T_CODES,
  T_NAMES,
  ANALYTICS_EVENTS,
  ANALYTICS_PROPERTIES,
} from "~/Enums";
import { myClientAtom } from "~/GlobalStores/atoms";
import { useAuthStore } from "~/GlobalStores/AuthStore";
import { getClientFeatures } from "~/Utils/ClientFeaturesUtils";
import { EverTg, EverButton, message } from "~/v2/components";
import { noTransformations } from "~/v2/images";

import { TransformManager } from "./TransformationManager";
import useFetchApiWithAuth from "../../useFetchApiWithAuth";
import {
  createVariableMap,
  menuIconsMap,
  showSupersetNotification,
} from "../utils";

/**
 * Transformation component manages data transformations by allowing users to add, validate, and remove transformations.
 */
export const Transformation = observer(
  ({
    sourceVariables,
    sources,
    selectedSource,
    databookList,
    datasheetMap,
    sourceOptions,
    savedTransformations,
    datasheetId,
    hasIntermediateVariables,
    setSavedTransformationSpec,
    setSourceVariables,
    setHasIntermediateVariables,
    baseVariables,
    transformations,
    setTransformations,
    currentDatasheetName,
    unsavedEdits,
    setUnsavedEdits,
    isInitialValidation,
    setIsInitialValidation,
    isValidateButtonDisabled,
    setIsValidateButtonDisabled,
    setIsValidationInProgress,
    isValidationInProgress,
    supersetDetails,
    updatedSourceId,
    isSourceValidationSuccessful,
    setIsSourceValidationSuccessful,
  }) => {
    const { fetchData } = useFetchApiWithAuth();
    const advanceFiltersRef = useRef([]);
    const [
      isAddTransformationButtonDisabled,
      setIsAddTransformationButtonDisabled,
    ] = useState(false);
    const myAtom = useRecoilValue(myClientAtom);
    const { accessToken } = useAuthStore();
    const clientFeatures = getClientFeatures(myAtom);
    const show_advanced_filter = clientFeatures?.showAdvancedFilter;

    // Define the desired order for transformation keys
    const desiredOrder = [
      "JOIN",
      "UNION",
      "ADVANCED_FILTER_V2",
      "GROUP_BY",
      "GET_USER_PROPERTIES",
      "FLATTEN",
      "TEMPORAL_SPLICE",
    ];

    // Effect to manage the state of the "Add Transformation" button based on validation status
    useEffect(() => {
      const allValid = transformations.every(
        (transformation) => transformation.isValid
      );
      setIsAddTransformationButtonDisabled(
        !isValidateButtonDisabled || !allValid
      );
    }, [isValidateButtonDisabled, transformations]);

    // Helper function to handle API mutations
    const handleMutation = (url, payload) => {
      const body = {
        transformations: payload?.transformations || [],
        variables: sourceVariables,
        sourceVariables: baseVariables,
        initialValidation: isInitialValidation,
        sourceType: selectedSource.type,
        sourceId: selectedSource.id,
      };
      return fetchData(url, "POST", body);
    };

    // Success handler for mutations
    const handleSuccess = (res) => {
      advanceFiltersRef.current.forEach((advanceFilterRef) => {
        advanceFilterRef.current?.refetchAutoSuggestion();
      });
      setSavedTransformationSpec(res?.transformations || []);
      setSourceVariables(res.variables || []);
      setHasIntermediateVariables(true);
      setIsValidateButtonDisabled(true);
      setIsInitialValidation(false);
      if (supersetDetails?.isDatasheetUsedInSuperset) {
        showSupersetNotification(supersetDetails);
      }
      if (res.has_config_changed) {
        message.warning(
          "Config changed. All the records will be considered as new records in the associated workflows.",
          3000,
          "h-auto py-2"
        );
      }
    };

    // Error handler for mutations
    const handleError = (error) => {
      message.error(error.message || "An error occurred!", 3000, "h-auto py-2");
      setIsValidateButtonDisabled(false);
    };

    // Mutation for validating transformations
    const handleValidateTransformation = useMutation(
      (payload) =>
        handleMutation(`datasheets/${datasheetId}/validate`, payload),
      {
        onSuccess: (res) => {
          handleSuccess(res);
          setIsSourceValidationSuccessful(false);
        },
        onError: handleError,
      }
    );

    // Mutation for deleting transformations
    const handleDeleteTransformation = useMutation(
      (payload) =>
        handleMutation(`datasheets/${datasheetId}/validate`, payload),
      {
        onSuccess: handleSuccess,
        onError: handleError,
      }
    );

    // Effect to manage validation progress state
    useEffect(() => {
      setIsValidationInProgress(
        handleValidateTransformation.isLoading ||
          handleDeleteTransformation.isLoading
      );
    }, [
      handleValidateTransformation.isLoading,
      handleDeleteTransformation.isLoading,
    ]);

    // Effect to initialize transformations from saved data
    useEffect(() => {
      const newTransformations =
        savedTransformations?.map((savedTransformation, index) => ({
          name: savedTransformation.type,
          isValid: savedTransformation?.is_valid,
          columnMap: [
            T_CODES.ADVANCED_FILTER_V2,
            T_CODES.ADVANCED_FILTER,
          ].includes(savedTransformation.type)
            ? savedTransformation?.infix || []
            : createVariableMap(
                index === 0
                  ? baseVariables
                  : savedTransformations[index - 1]?.output_columns || []
              ),
          transformationSpec: savedTransformation,
        })) || [];

      setTransformations(newTransformations);
    }, [savedTransformations, baseVariables, setTransformations]);

    // Menu for adding new transformations
    const menu = (
      <Menu
        onClick={(e) => {
          sendAnalyticsEvent(accessToken, ANALYTICS_EVENTS.ADD_TRANSFORMATION, {
            [ANALYTICS_PROPERTIES.DATASHEET_NAME]: currentDatasheetName || null,
            [ANALYTICS_PROPERTIES.TRANSFORMATION_OPTION]: e.key,
          });
          if (!unsavedEdits) setUnsavedEdits(true);
          let variables = baseVariables;

          if (transformations.length > 0) {
            variables =
              transformations[transformations.length - 1]?.transformationSpec
                ?.output_columns || [];
          }

          const newTransformation = {
            name: e.key,
            isValid: false,
            columnMap:
              e.key === T_CODES.ADVANCED_FILTER_V2
                ? []
                : createVariableMap(variables),
            transformationSpec: {
              transformation_id: uuidv4(),
            },
          };

          if (e.key === T_CODES.TEMPORAL_SPLICE) {
            newTransformation.transformationSpec.meta = [
              {
                source_type: selectedSource?.type,
                source_id: selectedSource?.id,
                with_databook_id: selectedSource?.databookId,
              },
            ];
          }

          setTransformations([...transformations, newTransformation]);
        }}
      >
        {Object.keys(T_CODES)
          .filter(
            (item) =>
              T_CODES[item] !== T_CODES.SORT &&
              T_CODES[item] !== T_CODES.ADVANCED_FILTER
          )
          .sort((a, b) => desiredOrder.indexOf(a) - desiredOrder.indexOf(b))
          .filter((item) => {
            switch (T_CODES[item]) {
              case T_CODES.FILTER:
                return !show_advanced_filter;
              case T_CODES.ADVANCED_FILTER_V2:
                return show_advanced_filter;
              case T_CODES.FLATTEN:
                return !transformations.some(
                  (transformation) => transformation.name === T_CODES.FLATTEN
                );
              default:
                return true;
            }
          })
          .map((c) => ({ name: T_NAMES[c], value: T_CODES[c] }))
          .map((x) => (
            <Menu.Item key={x.value}>
              <div className="flex gap-2 items-center">
                {menuIconsMap[x.value]}
                <EverTg.Text className="text-ever-base-content">
                  {x.name}
                </EverTg.Text>
              </div>
            </Menu.Item>
          ))}
      </Menu>
    );

    // Mutation to get output columns for a transformation
    const getOutputColumnsRequest = useMutation(
      ({ columns, spec }) => {
        const body = {
          output_columns: Object.values(columns),
          transformation_spec: spec,
        };
        return fetchData(
          `/datasheets/${datasheetId}/transformation/get-output-columns`,
          "POST",
          body
        );
      },
      {
        onError: (error) => {
          message.error(error?.message || "An error occurred!");
        },
      }
    );

    // Function to handle transformation validation
    const handleTransformationValidation = useCallback(
      async (idx, valid, spec) => {
        const clonedTransformations = cloneDeep(transformations);
        if (clonedTransformations.length > 0) {
          if (valid) {
            const previousOutputColumns =
              clonedTransformations[idx]?.transformationSpec?.output_columns;
            clonedTransformations[idx].isValid = true;
            clonedTransformations[idx].transformationSpec = {
              ...spec,
              output_columns: cloneDeep(previousOutputColumns || []),
              is_valid: false,
            };
            if (
              isSourceValidationSuccessful &&
              transformations.length - 1 !== idx
            ) {
              let outputColumns = clonedTransformations[idx].columnMap;
              if (
                [T_CODES.ADVANCED_FILTER_V2, T_CODES.ADVANCED_FILTER].includes(
                  clonedTransformations[idx].transformationSpec.type
                )
              ) {
                if (idx === 0) {
                  outputColumns = baseVariables;
                } else {
                  outputColumns =
                    clonedTransformations[idx - 1].transformationSpec
                      ?.output_columns;
                }
              }
              try {
                const data = await getOutputColumnsRequest.mutateAsync({
                  columns: outputColumns,
                  spec: clonedTransformations[idx].transformationSpec,
                });

                if (data?.transformation_spec) {
                  clonedTransformations[idx].transformationSpec =
                    data.transformation_spec;
                  if (
                    [
                      T_CODES.ADVANCED_FILTER_V2,
                      T_CODES.ADVANCED_FILTER,
                    ].includes(
                      clonedTransformations[idx + 1]?.transformationSpec?.type
                    )
                  ) {
                    clonedTransformations[idx + 1].columnMap = [];
                    clonedTransformations[idx + 1].outputColumns =
                      data.transformation_spec?.output_columns;
                  } else {
                    clonedTransformations[idx + 1].columnMap =
                      createVariableMap(
                        data.transformation_spec?.output_columns || []
                      );
                  }
                }
              } catch (error) {
                console.log("error", error);
              }
            }
          } else {
            clonedTransformations[idx].isValid = false;
          }
          const allValid = clonedTransformations.every(
            (transformation) => transformation.isValid
          );
          setIsValidateButtonDisabled(!allValid);
          setTransformations(clonedTransformations);
        }
      },
      [
        setTransformations,
        getOutputColumnsRequest,
        setIsValidateButtonDisabled,
        isSourceValidationSuccessful,
        transformations,
      ]
    );

    // Debounced function to handle validation
    const handleValidate = useCallback(
      debounce(() => {
        handleValidateTransformation.mutate({
          transformations:
            transformations.map(
              (transformation) => transformation.transformationSpec
            ) || [],
        });
      }, 100),
      [handleValidateTransformation, transformations]
    );

    // Debounced function to handle deletion
    const handleDelete = useCallback(
      debounce((params) => {
        handleDeleteTransformation.mutate(params);
      }, 200),
      [handleDeleteTransformation]
    );

    return (
      <div className="flex flex-col h-full">
        <div className="px-6 flex flex-col gap-4 pb-4 h-auto">
          {/* Title */}
          <EverTg.Heading3 className="semi-bold text-ever-base-content">
            Data Transformation
          </EverTg.Heading3>
          <TransformManager
            transformations={transformations}
            handleTransformationValidation={handleTransformationValidation}
            sources={sources}
            selectedSource={selectedSource}
            datasheetId={datasheetId}
            databookList={databookList}
            datasheetMap={datasheetMap}
            sourceOptions={sourceOptions}
            handleDelete={handleDelete}
            hasIntermediateVariables={hasIntermediateVariables}
            savedTransformations={savedTransformations}
            setTransformations={setTransformations}
            setIsValidateButtonDisabled={setIsValidateButtonDisabled}
            setUnsavedEdits={setUnsavedEdits}
            unsavedEdits={unsavedEdits}
            isValidationInProgress={isValidationInProgress}
            advanceFiltersRef={advanceFiltersRef}
            updatedSourceId={updatedSourceId}
            isSourceValidationSuccessful={isSourceValidationSuccessful}
            setSavedTransformationSpec={setSavedTransformationSpec}
            baseVariables={baseVariables}
          />
        </div>
        <div
          className={twMerge(
            "flex gap-4 items-center space-between w-full px-6 py-4",
            !isAddTransformationButtonDisabled &&
              transformations.length === 0 &&
              "h-full",
            transformations.length > 2 &&
              "sticky bottom-0 border-t border-ever-base-400 bg-ever-base"
          )}
        >
          <div
            className={twMerge(
              transformations.length === 0 ? "w-full h-full" : "flex-1"
            )}
          >
            {/* Conditional rendering for the Add Transformation button or validation message */}
            {transformations.length === 0 ? (
              <div className="w-full h-[70vh] flex items-center justify-center">
                <div className="flex flex-col gap-6 items-center">
                  <img src={noTransformations} className="w-44" />
                  <div className="flex flex-col items-center">
                    <EverTg.Heading1>
                      Transformations help you modify data for your needs
                    </EverTg.Heading1>
                    <EverTg.Description>
                      You can join other sheets to bring in more columns for
                      context, add filters to include only required rows, and
                      much more.
                    </EverTg.Description>
                  </div>
                  <div id="add-transformation-button" className="relative">
                    <Dropdown
                      overlay={menu}
                      trigger={["click"]}
                      getPopupContainer={() =>
                        document.querySelector("#add-transformation-button")
                      }
                    >
                      <EverButton type="filled" color="base" className={"w-48"}>
                        <div className="flex gap-3 items-center">
                          <EverTg.SubHeading4 className="text-ever-base-content font-medium">
                            Add Transformation
                          </EverTg.SubHeading4>
                          <ChevronDownIcon className="text-ever-base-content-mid w-5 h-5" />
                        </div>
                      </EverButton>
                    </Dropdown>
                  </div>
                </div>
              </div>
            ) : isAddTransformationButtonDisabled ? (
              <EverTg.Caption className="text-ever-base-content-mid">
                Validate changes before adding a new transformation
              </EverTg.Caption>
            ) : (
              <div id="add-transformation-button-next" className="relative">
                <Dropdown
                  overlay={menu}
                  trigger={["click"]}
                  getPopupContainer={() =>
                    document.querySelector("#add-transformation-button-next")
                  }
                >
                  <EverButton type="ghost" color="base" className={"w-48"}>
                    <div className="flex gap-3 items-center">
                      <EverTg.SubHeading4 className="text-ever-base-content font-medium">
                        Add Transformation
                      </EverTg.SubHeading4>
                      <ChevronDownIcon className="text-ever-base-content-mid w-5 h-5" />
                    </div>
                  </EverButton>
                </Dropdown>
              </div>
            )}
          </div>
          <div className="flex-1 flex justify-end">
            {/* Validate button is shown if there are transformations present */}
            {transformations.length > 0 && (
              <EverButton
                type="ghost"
                color="primary"
                disabled={isValidationInProgress || isValidateButtonDisabled}
                loading={handleValidateTransformation.isLoading}
                onClick={handleValidate}
              >
                Validate
              </EverButton>
            )}
          </div>
        </div>
      </div>
    );
  }
);
