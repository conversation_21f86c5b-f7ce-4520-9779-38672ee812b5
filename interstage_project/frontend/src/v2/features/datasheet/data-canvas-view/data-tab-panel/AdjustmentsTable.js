import {
  DotsVerticalIcon,
  HelpCircleIcon,
  CommissionTraceIcon,
} from "@everstage/evericons/outlined";
import { AgGridReact } from "ag-grid-react";
import { Dropdown } from "antd";
import { format, isValid, parseISO } from "date-fns";
import { cloneDeep, isEmpty } from "lodash";
import { observer } from "mobx-react";
import React, { useContext, useEffect, useRef, useState } from "react";
import { useQuery } from "react-query";
import { useImmer } from "use-immer";

import {
  RBAC_ROLES,
  DATASHEET_VIEW_ID,
  DATATYPE,
  ADJUSTMENT_TYPE,
} from "~/Enums";
import { useAuthStore } from "~/GlobalStores/AuthStore";
import { useUserPermissionStore } from "~/GlobalStores/UserPermissionStore";
import { useVariableStore } from "~/GlobalStores/VariableStore";
import { formatCurrencyWrapper } from "~/Utils/CurrencyUtils";
import { EverTool<PERSON>, Icon<PERSON><PERSON>on, EverDivider, EverTg } from "~/v2/components";
import { CustomHeader, everAgGridCallbacks } from "~/v2/components/ag-grid";
import { getDefaultOptions } from "~/v2/components/ag-grid/ever-ag-grid-options";
import { dateComparator, numComparator } from "~/v2/components/ag-grid/utils";
import { formatDate } from "~/v2/components/ever-formatter/EverFormatter";
import { dogTearingNewspaper, noDataAvailable } from "~/v2/images";

import { AdjustmentDrawer } from "./adjustment-drawer";
import { DanglingAdjustmentDetailModal } from "./DanglingAdjustmentDetailModal";
import { LoaderTable } from "./LoaderTable";
import { DatasheetContext } from "../../DatasheetStore";
import { getDataSheetData } from "../../restApi";

/**
 * Formats and returns the adjustment row data.
 * @param {Object} adjustmentData - The adjustment data.
 * @returns {Array} - The formatted adjustment row data.
 */
function getAdjustmentRowData(adjustmentData) {
  if (!adjustmentData) return [];
  const rows = [];
  const keys = Object.keys(adjustmentData);
  for (const ele of keys) {
    if (adjustmentData[ele].length > 1 && adjustmentData[ele][0]) {
      adjustmentData[ele][0].rowSpan = adjustmentData[ele].length;
    } else if (adjustmentData[ele][0]) {
      adjustmentData[ele][0].rowSpan = 1;
    }
    rows.push(...adjustmentData[ele]);
  }
  return rows;
}

/**
 * AdjustmentsTable component for displaying adjustment data in a table format.
 * @param {Object} props - The component props.
 * @param {boolean} props.isError - Indicates if there is an error.
 * @param {boolean} props.isLoading - Indicates if the data is loading.
 * @param {boolean} props.isRefetching - Indicates if the data is being refetched.
 * @param {string} props.recentView - The recent view id.
 */
export const AdjustmentsTable = observer(
  ({
    isError,
    isLoading,
    isRefetching,
    recentView,
    error,
    editAdjustmentMenu,
    showEditAdjustmentDrawer,
    setShowEditAdjustmentDrawer,
    adjustmentRecordId,
    setAdjustmentRecordId,
  }) => {
    const { dataTypesById } = useVariableStore();
    const { hasPermissions } = useUserPermissionStore();
    const { accessToken } = useAuthStore();
    const [danglingAdjustmentDetails, setDanglingAdjustmentDetails] = useImmer({
      isModalVisible: false,
      adjustmentDetails: {},
      columnDefs: [],
      invalidatedOn: "",
      danglingReason: "",
      adjustmentType: "",
    });
    const datasheetStore = useContext(DatasheetContext);

    const [tableData, setTableData] = useState({
      data: getAdjustmentRowData(datasheetStore.adjustmentData),
      total_records: 1,
      variables: datasheetStore.adjustmentVariables,
    });
    const [columns, setColumns] = useState([]);
    const [colsApi, setColsApi] = useState(null);
    const [gridLoading, setGridLoading] = useState(false);

    const gridRef = useRef(null);

    const handleDanglingDetailsClick = (params) => {
      let columnDefs, adjustedData;
      const isSplit = params?.data?.rowSpan > 1;
      // Helper function to format data based on type
      const formatDataValue = (datatype, value) => {
        switch (datatype) {
          case DATATYPE.DATE:
            return formatDate({ date: value, type: "date" });
          case DATATYPE.BOOLEAN:
            return value ? "true" : "false";
          case DATATYPE.INTEGER:
            return formatCurrencyWrapper(value);
          default:
            return value;
        }
      };
      // Generate column definitions for split adjustments
      const generateSplitColumnDefs = () => {
        const columnDefs = [
          {
            field: "display_name",
            headerName: "Field Name",
            flex: 1,
          },
        ];

        const rowKeys = params?.data?.adjustment_details?.adjusted_data.map(
          (ele) => ele?.row_key || ""
        );

        const uniqueRowKeys = [...new Set(rowKeys)];

        const headerNameMap = uniqueRowKeys.reduce((acc, key, index) => {
          const fieldName = `adjusted_${index + 1}`;
          acc[key] = fieldName;

          columnDefs.push({
            field: fieldName,
            headerName: `Adjusted value in split ${index + 1}`,
            flex: 1,
            cellRenderer: (params) => params.value || "-",
          });

          return acc;
        }, {});

        return { columnDefs, headerNameMap };
      };

      // Generate row data for split adjustments
      const generateSplitRowData = (headerNameMap) => {
        // Get the adjusted data from params
        const adjustedData = params?.data?.adjustment_details?.adjusted_data;

        // Get unique display names from adjusted data
        const displayNames = [
          ...new Set(adjustedData.map((ele) => ele?.display_name || "")),
        ];

        // Map display names to row data
        return displayNames.map((displayName) => {
          // Initialize row data object with display name
          const data = { display_name: displayName };

          // For each adjusted data item matching the display name
          // add its value to the corresponding split column
          adjustedData.forEach((item) => {
            if (item.display_name === displayName) {
              data[headerNameMap[item.row_key]] = item.value;
            }
          });

          return data;
        });
      };

      // Generate data for regular adjustments
      const generateRegularData = () => {
        return params?.data?.adjustment_details?.adjusted_data.map((ele) => ({
          display_name: ele.display_name,
          value: formatDataValue(dataTypesById[ele.data_type_id], ele.value),
          data_type_id: ele.data_type_id,
        }));
      };

      // Handle split vs regular adjustments differently
      if (isSplit) {
        // For split adjustments, generate columns and data based on splits
        const { columnDefs: splitCols, headerNameMap } =
          generateSplitColumnDefs();
        columnDefs = splitCols;
        adjustedData = generateSplitRowData(headerNameMap);
      } else {
        // For regular adjustments, use simple 2-column layout
        columnDefs = [
          { field: "display_name", headerName: "Field Name", flex: 1 },
          { field: "value", headerName: "Adjusted Value", flex: 1 },
        ];
        adjustedData = generateRegularData();
      }
      // Set modal state with adjustment details
      setDanglingAdjustmentDetails({
        isModalVisible: true,
        adjustmentDetails: adjustedData,
        // Format invalidation date if exists
        invalidatedOn: params?.data?.adjustment_details?.invalidated_at
          ? formatDate({
              date: params?.data?.adjustment_details?.invalidated_at,
              type: "date",
            })
          : "",
        columnDefs,
        // Include reason for dangling adjustment if exists
        danglingReason: params?.data?.dangling_reason || "",
        // Include adjustment type if exists
        adjustmentType: params.data?.adjustment_details?.type || "",
      });
    };

    const { isLoading: isLoadingVars } = useQuery(
      ["getTableVars"],
      () => {
        return getDataSheetData(accessToken, datasheetStore.datasheetId, {
          pageSize: 1,
          pageNumber: 1,
        });
      },
      {
        cacheTime: 0,
        refetchOnWindowFocus: false,
        onSettled: (data, error) => {
          if (error) {
            console.log("Error occurred:", error);
          } else {
            if (data.variables) {
              setTableData({
                ...tableData,
                variables: data.variables,
              });
            }
          }
        },
      }
    );

    useEffect(() => {
      /**
       * Returns the display name for the adjustment type.
       * @param {string} type - The adjustment type.
       * @returns {string|null} - The display name.
       */
      function typeName(type) {
        switch (type) {
          case ADJUSTMENT_TYPE.IGNORE: {
            return "Ignored";
          }
          case ADJUSTMENT_TYPE.UPDATE: {
            return "Update";
          }
          case ADJUSTMENT_TYPE.SPLIT: {
            return "New Split";
          }
        }
        return null;
      }
      // Define all columns for the table
      let allColumns = [
        {
          field: "adjustment_details",
          headerName: `Adjustment ID`,
          suppressColumnsToolPanel: true,
          pinned: "left",
          lockPinned: true,
          minWidth: 140,
          spanRows: true,
          valueGetter: (params) => {
            return params.data.adjustment_details.adjustment_number;
          },
          cellRenderer: (params) => {
            return (
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-2">
                  <span className="text-ever-base-content">
                    {params.data.adjustment_details.adjustment_number}
                  </span>
                  {params?.data?.rowSpan > 1 && (
                    <div className="flex items-center gap-2">
                      <CommissionTraceIcon className="w-4 h-4 text-ever-base-content-low" />
                      <EverTg.Text className="text-ever-base-content">
                        {params?.data?.rowSpan - 1}
                      </EverTg.Text>
                    </div>
                  )}
                </div>
                {params.data.dangling_reason && (
                  <div className="flex items-center gap-3">
                    <EverDivider type="vertical" className="h-3" />
                    <div className="flex items-center gap-2">
                      <span className="text-ever-error-lite-content bg-ever-error-lite-content/10 rounded-md flex items-center justify-center h-5 px-2 py-1 ">
                        Invalid
                      </span>

                      <EverTooltip title="Click to view details">
                        <HelpCircleIcon
                          className="w-4 h-4 text-ever-base-content-mid  cursor-pointer"
                          onClick={() => handleDanglingDetailsClick(params)}
                        />
                      </EverTooltip>
                    </div>
                  </div>
                )}
              </div>
            );
          },

          rowSpan: (params) => {
            return params.data.rowSpan || 1;
          },
          cellClass: (params) => {
            if (params.data.rowSpan > 1)
              return `clubbed-cell !border-b !border-t-0 !border-r-0 !border-l-0 border-solid !border-ever-base-400  ${
                !params.data.dangling_reason
                  ? "!bg-ever-base"
                  : "ag-cell-under-span !bg-ever-error-lite"
              }`;
          },
        },
        {
          field: "adjustment_details",
          headerName: `Adjustment Date`,
          suppressColumnsToolPanel: true,
          valueGetter: (params) => {
            const formattedDate = parseISO(
              params.data.adjustment_details.adjustment_date
            );
            return format(formattedDate, "dd MMM yyyy");
          },
        },
        {
          field: "adjustment_details",
          headerName: `Added By`,
          suppressColumnsToolPanel: true,
          valueGetter: (params) => {
            return params.data.adjustment_details.applied_by ?? "-";
          },
        },
        {
          field: "adjustment_details",
          headerName: `Adjustment Scope`,
          suppressColumnsToolPanel: true,
          valueGetter: (params) => {
            return params.data.adjustment_details.is_global
              ? "Global"
              : "Local";
          },
        },
        {
          field: "adjustment_details",
          headerName: `Adjustment Type`,
          suppressColumnsToolPanel: true,
          valueGetter: (params) => {
            return typeName(params.data.adjustment_details.type);
          },
        },
        {
          field: "adjustment_details",
          headerName: "Comments",
          suppressColumnsToolPanel: true,
          valueGetter: (params) => {
            return params.data?.adjustment_details?.comments;
          },
        },
        ...tableData.variables.map((varObj) => {
          const datatype = dataTypesById[varObj.data_type_id];
          const isCalculatedField = varObj?.meta_data?.infix != null;
          return {
            dataType: datatype,
            field: varObj.system_name,
            headerName: varObj.display_name,
            suppressColumnsToolPanel: true,
            headerComponentParams: {
              ...(isCalculatedField
                ? {
                    isCalculatedField: true,
                  }
                : { menuIcon: datatype }),
            },
            ...(datatype === "Percentage" && {
              comparator: numComparator,
              type: "rightAligned",
              valueGetter: (params) => {
                if (params.data[varObj.system_name] === "") return null;
                return params.data[varObj.system_name];
              },
            }),
            ...(datatype === "Integer" && {
              comparator: numComparator,
              type: "rightAligned",
              valueGetter: (params) => {
                if (params.data[varObj.system_name] === "") return null;
                return params.data[varObj.system_name];
              },
              cellRenderer: (params) => (
                <EverTooltip
                  title={formatCurrencyWrapper(params.value, {
                    decimalPlaces: 6,
                    truncate: true,
                  })}
                  placement="bottom"
                  overlayClassName="dataSheetTooltip"
                >
                  {formatCurrencyWrapper(params.value)}
                </EverTooltip>
              ),
            }),
            ...(datatype === "Date"
              ? {
                  comparator: dateComparator,
                  valueFormatter: (params) => {
                    if (params?.value) {
                      const date = new Date(params.value);
                      return isValid(date)
                        ? format(date, "dd MMM yyyy", { timeZone: "UTC" })
                        : "";
                    }
                    return null;
                  },
                }
              : {}),
            ...(datatype === "Boolean" && {
              cellDataType: false,
              valueFormatter: (params) => {
                if (params.value === true) {
                  return "true";
                } else if (params.value === false) {
                  return "false";
                } else {
                  return "";
                }
              },
            }),
          };
        }),
      ];
      // Add Action column if the user has the required permissions
      hasPermissions(RBAC_ROLES.MANAGE_DATASHEETADJUSTMENTS) &&
        allColumns.push({
          field: "adjustment_details",
          headerName: "Action",
          suppressColumnsToolPanel: true,
          pinned: "right",
          lockPinned: true,
          width: 80,
          rowSpan: (params) => {
            return params.data.rowSpan || 1;
          },
          cellClass: (params) => {
            if (params.data.rowSpan > 1) {
              const bg_color = params.data?.dangling_reason
                ? "!bg-ever-error-lite"
                : "!bg-ever-base";
              return `clubbed-cell  !border-b !border-t-0 !border-r-0 !border-l-0 border-solid !border-ever-base-400 ${bg_color}`;
            }
          },
          cellRenderer: (params) => {
            console.log("params", params);
            return params.data.rowSpan && !params.data?.dangling_reason ? (
              <div className="flex justify-center w-full">
                <Dropdown
                  trigger={["click"]}
                  overlay={editAdjustmentMenu(params.data)}
                >
                  <IconButton
                    type="text"
                    color="base"
                    size="small"
                    icon={<DotsVerticalIcon className="!w-4 !h-4" />}
                    className="!w-6 !h-6"
                  />
                </Dropdown>
              </div>
            ) : (
              <></>
            );
          },
        });
      setColumns(allColumns);
      colsApi?.autoSizeAllColumns();
    }, [tableData]);

    useEffect(() => {
      setTableData({
        ...tableData,
        data: getAdjustmentRowData(datasheetStore.adjustmentData),
      });
    }, [datasheetStore.adjustmentData]);

    useEffect(() => {
      setTableData({
        ...tableData,
        variables: datasheetStore.adjustmentVariables,
      });
    }, [datasheetStore.adjustmentVariables]);

    return (
      <>
        <div className="ag-theme-material no-border zebra-grid w-full h-full datasheet-table datasheet-v2-table border-t-0 border-r border-b-0 border-l border-solid border-ever-base-400">
          {isError ? (
            <div className="w-full h-full flex flex-col gap-2 items-center justify-center">
              <img src={dogTearingNewspaper} />{" "}
              <EverTg.Heading2>
                {error?.message || "Some Error has occurred"}
              </EverTg.Heading2>
            </div>
          ) : isLoading || isRefetching || isLoadingVars ? (
            <div className="w-full h-full">
              <LoaderTable />
            </div>
          ) : (
            <>
              <AgGridReact
                {...getDefaultOptions({ type: "md" })}
                loading={gridLoading}
                ref={gridRef}
                onGridSizeChanged={(params) => {
                  params.api.autoSizeAllColumns();
                }}
                loadingOverlayComponent={() => <></>}
                columnDefs={columns}
                rowData={tableData.data}
                rowHeight={32}
                headerHeight={32}
                components={{
                  agColumnHeader: (params) => CustomHeader(params, "small"),
                }}
                getRowClass={(params) => {
                  return params.data && params.data.dangling_reason
                    ? "dangling-row"
                    : "";
                }}
                onFirstDataRendered={(params) => {
                  if (isEmpty(params.api) || params.api.destroyCalled) return;
                  setGridLoading(true);
                  everAgGridCallbacks.adjustColumnWidth(params);
                  setTimeout(() => {
                    setGridLoading(false);
                  }, 200);
                }}
                onGridReady={(params) => {
                  if (isEmpty(params.api) || params.api.destroyCalled) return;
                  params.api.closeToolPanel();
                  everAgGridCallbacks.adjustColumnWidth(params);
                  setColsApi(params.api);
                }}
                onRowDataUpdated={(params) => {
                  if (isEmpty(params.api) || params.api.destroyCalled) return;
                  setTimeout(() => {
                    everAgGridCallbacks.adjustColumnWidth(params);
                  });
                }}
                noRowsOverlayComponentParams={{
                  title: "No adjustments made for this sheet",
                  imgSrc: noDataAvailable,
                  subTitle: "",
                }}
                suppressRowClickSelection={true}
                suppressColumnMoveAnimation={true}
                suppressRowTransform={true}
                suppressColumnVirtualisation={true}
                enableRangeSelection={true}
                getContextMenuItems={() => {
                  return [
                    "autoSizeAll",
                    "separator",
                    "copy",
                    "copyWithHeaders",
                  ];
                }}
                paginationPageSize={9999}
                cacheBlockSize={9999}
                suppressFieldDotNotation={true}
              />
              {showEditAdjustmentDrawer && (
                <AdjustmentDrawer
                  record={datasheetStore.adjustmentData[adjustmentRecordId]}
                  columns={cloneDeep(
                    columns.slice(
                      6,
                      hasPermissions(RBAC_ROLES.MANAGE_DATASHEETADJUSTMENTS)
                        ? columns.length - 1
                        : columns.length
                    )
                  )}
                  primaryVariables={tableData.variables.map((ele) => {
                    if (ele.is_primary) return ele.system_name;
                  })}
                  closeDrawer={() => {
                    setShowEditAdjustmentDrawer(false);
                    setAdjustmentRecordId(null);
                    window.history.replaceState(
                      null,
                      "",
                      `datasheet?id=${datasheetStore.datasheetId}&viewId=${
                        recentView || DATASHEET_VIEW_ID.ALL_DATA
                      }`
                    );
                  }}
                  visible={showEditAdjustmentDrawer}
                  accessToken={accessToken}
                  datasheetId={datasheetStore.datasheetId}
                  isEditAdjustment={true}
                />
              )}
            </>
          )}
        </div>
        <DanglingAdjustmentDetailModal
          danglingAdjustmentDetails={danglingAdjustmentDetails}
          setDanglingAdjustmentDetails={setDanglingAdjustmentDetails}
        />
      </>
    );
  }
);
