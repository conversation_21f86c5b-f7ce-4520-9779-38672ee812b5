import { CalendarIcon } from "@everstage/evericons/outlined";
import { AgGridReact } from "ag-grid-react";
import { useTranslation } from "react-i18next";

import { DANGLING_REASON, ADJUSTMENT_TYPE } from "~/Enums";
import { EverModal, EverTg, EverButton, EverDivider } from "~/v2/components";
import { getDefaultOptions } from "~/v2/components/ag-grid/ever-ag-grid-options";

/**
 * Modal component to display details of invalid/dangling adjustments
 * @param {Object} props
 * @param {Object} props.danglingAdjustmentDetails - Details of the dangling adjustment
 * @param {boolean} props.danglingAdjustmentDetails.isModalVisible - Controls modal visibility
 * @param {Object} props.danglingAdjustmentDetails.adjustmentDetails - Adjustment data
 * @param {Array} props.danglingAdjustmentDetails.columnDefs - Column definitions for grid
 * @param {string} props.danglingAdjustmentDetails.invalidatedOn - Timestamp when adjustment became invalid
 * @param {string} props.danglingAdjustmentDetails.danglingReason - Reason why adjustment is dangling
 * @param {string} props.danglingAdjustmentDetails.adjustmentType - Type of adjustment
 * @param {Function} props.setDanglingAdjustmentDetails - Function to update dangling adjustment details
 * @returns {JSX.Element} Modal component showing dangling adjustment details
 */

export const DanglingAdjustmentDetailModal = ({
  danglingAdjustmentDetails,
  setDanglingAdjustmentDetails,
}) => {
  const { t } = useTranslation();
  return (
    <EverModal
      visible={danglingAdjustmentDetails.isModalVisible}
      onCancel={() =>
        setDanglingAdjustmentDetails({
          isModalVisible: false,
          adjustmentDetails: {},
          columnDefs: [],
          invalidatedOn: "",
          danglingReason: "",
          adjustmentType: "",
        })
      }
      title={`Invalid ${t("adjustment")} details`}
      footer={
        <EverButton
          onClick={() =>
            setDanglingAdjustmentDetails({
              isModalVisible: false,
              adjustmentDetails: {},
              columnDefs: [],
              invalidatedOn: "",
              danglingReason: "",
              adjustmentType: "",
            })
          }
        >
          Close
        </EverButton>
      }
      width={700}
    >
      <div className="flex flex-col gap-4">
        <div className="flex flex-col gap-6">
          {danglingAdjustmentDetails.danglingReason ===
          DANGLING_REASON.ROW_INVALIDATED ? (
            <EverTg.Text className="text-ever-content">
              This {t("adjustment")} is no longer valid due to changes in
              upstream data
            </EverTg.Text>
          ) : (
            <EverTg.Text className="text-ever-content">
              This {t("adjustment")} was made on a record that had field(s),
              which are no longer shown in the datasheet. This may be due to
              changes in the transformation logic after the {t("adjustment")}
              was created.
            </EverTg.Text>
          )}
          {danglingAdjustmentDetails.adjustmentType !==
            ADJUSTMENT_TYPE.IGNORE && (
            <div className="flex flex-col gap-4">
              <EverTg.Heading4 className="text-ever-content">
                Removed field(s) with their adjusted values
              </EverTg.Heading4>
              <div className="w-full h-full ag-theme-material no-min-height">
                <AgGridReact
                  {...getDefaultOptions({ type: "sm" })}
                  rowData={danglingAdjustmentDetails?.adjustmentDetails || []}
                  columnDefs={danglingAdjustmentDetails?.columnDefs || []}
                  domLayout="autoHeight"
                />
              </div>
            </div>
          )}
          <EverDivider />
        </div>
        <div className="flex items-center gap-2">
          <CalendarIcon className="w-3 h-3 text-ever-warning" />
          <EverTg.Caption className="text-ever-content font-medium">
            Invalidated on {danglingAdjustmentDetails.invalidatedOn || ""}
          </EverTg.Caption>
        </div>
      </div>
    </EverModal>
  );
};
