import {
  EraserIcon,
  ColumnsIcon,
  LayoutAltIcon,
} from "@everstage/evericons/duotone";
import { TrashLottie } from "@everstage/evericons/lotties";
import {
  ChevronDownIcon,
  CopyLinkIcon,
  FilterLinesIcon,
  PlusIcon,
  CopyIcon,
  TextInputIcon,
  Trash03Icon,
} from "@everstage/evericons/outlined";
import { AlertSquareIcon } from "@everstage/evericons/solid";
import { Dropdown, Menu } from "antd";
import { cloneDeep, debounce, isEmpty } from "lodash";
import { toJS } from "mobx";
import { observer } from "mobx-react";
import React, {
  useContext,
  useEffect,
  useState,
  useRef,
  useCallback,
  useMemo,
} from "react";
import { useMutation } from "react-query";
import { useLocation } from "react-router-dom";
import { twMerge } from "tailwind-merge";

import { sendAnalyticsEvent } from "~/Api/AnalyticsService";
import {
  DATASHEET_VIEW_ID,
  RBAC_ROLES,
  ANALYTICS_EVENTS,
  ANALYTICS_PROPERTIES,
  PLATFORM,
  DATA_ORIGIN,
} from "~/Enums";
import { useAuthStore } from "~/GlobalStores/AuthStore";
import { useUserPermissionStore } from "~/GlobalStores/UserPermissionStore";
import {
  EverButton,
  EverTabs,
  EverTg,
  EverTooltip,
  EverModal,
  EverHotToastNotification,
  toast,
  EverLoader,
  IconButton,
  message,
  EverHotToastMessage,
} from "~/v2/components";

import { DataTab } from "./DataTab";
import { LoaderTable } from "./LoaderTable";
import { TabLabel } from "./TabLabel";
import { DatasheetContext } from "../../DatasheetStore";
import {
  cloneView,
  createNewView,
  deleteView,
  getViewDetails,
} from "../../restApi";
import { getUniqueViewName } from "../../utils";

/**
 * DataTabPanel component represents a panel containing data tabs for different views.
 * It allows users to switch between tabs, add new views, and configure the table.
 *
 * @param {Object} props - The props passed to the component.
 * @param {Function} props.onGenerateDatasheet - Function to trigger the generation of the datasheet.
 * @param {Object} props.canvasRef - Reference to the canvas element.
 * @param {Function} props.setShowGenerateSheetModal - Setter function to show or hide the generate sheet modal.
 * @param {boolean} props.showGenerateSheetModal - Indicates whether the generate sheet modal is visible.
 * @param {boolean} props.isRefreshRequestLoading - Indicates whether the refresh request is loading.
 * @returns {JSX.Element} JSX element representing the data tab panel.
 */

export const DataTabPanel = observer(
  ({
    onGenerateDatasheet,
    canvasRef,
    setShowGenerateSheetModal,
    showGenerateSheetModal,
    isRefreshRequestLoading,
    activeKey,
    setActiveKey,
    pivotConfig,
    setPivotConfig,
    pivotConfigPayload,
    setPivotConfigPayload,
  }) => {
    const { TabPane } = EverTabs;
    const gridRef = useRef([]);
    const gridApi = useRef(null);
    const isURLSynced = useRef(false);

    const { hasPermissions } = useUserPermissionStore();
    const datasheetStore = useContext(DatasheetContext);
    const { accessToken } = useAuthStore();
    const { search } = useLocation();

    const [tabs, setTabs] = useState([]);
    const [filterVisible, setFilterVisible] = useState(false);
    const [isEdit, setIsEdit] = useState("");
    const [deleteViewId, setDeleteViewId] = useState("");
    const [allDataCount, setAllDataCount] = useState(null);
    const [adjustmentCount, setAdjustmentCount] = useState(null);
    const [addFilterDataToView, setAddFilterDataToView] = useState(false);
    const dummyDataTableRef = useRef(null);

    const viewNameMap = useMemo(() => {
      if (!datasheetStore.datasheetDetails?.views) return new Map();
      return new Map(
        datasheetStore.datasheetDetails.views.map((view) => [view.name, view])
      );
    }, [datasheetStore.datasheetDetails?.views]);

    const deleteViewRequest = useMutation(
      (viewId) => deleteView(accessToken, datasheetStore.datasheetId, viewId),
      {
        onError: (error) => {
          console.log("error", error);
          message.error(error?.toString());
        },
        onSuccess: async (res) => {
          if (res.message === "Datasheet View deleted successfully") {
            message.success(res.message);
            setNewViews();
            // Update analytics with the count of saved views, excluding "All Data" and "Adjustments" tabs
            sendAnalyticsEvent(
              accessToken,
              ANALYTICS_EVENTS.COUNT_OF_SAVED_VIEWS_PER_SHEET,
              {
                [ANALYTICS_PROPERTIES.DATASHEET_NAME]:
                  datasheetStore?.currentDatasheetName,
                [ANALYTICS_PROPERTIES.COUNT_OF_SAVED_VIEWS]: tabs.length - 3,
              }
            );
          }
        },
      }
    );
    const handleCreateViewSuccess = (res) => {
      if (res.message === "Datasheet View created successfully") {
        message.success(res.message);
        updateViewDetails(res.view_id);
      }
      setAddFilterDataToView(false);
      sendAnalyticsEvent(
        accessToken,
        ANALYTICS_EVENTS.COUNT_OF_SAVED_VIEWS_PER_SHEET,
        {
          [ANALYTICS_PROPERTIES.DATASHEET_NAME]:
            datasheetStore.currentDatasheetName,
          [ANALYTICS_PROPERTIES.COUNT_OF_SAVED_VIEWS]:
            tabs.length > 2 ? tabs.length - 2 : 0,
        }
      );
    };
    const createViewRequest = useMutation(
      (name) => {
        const payload = {
          name: name,
        };
        return createNewView(accessToken, datasheetStore.datasheetId, payload);
      },
      {
        onError: (error) => {
          console.log("error", error);
          message.error(error?.toString());
        },
        onSuccess: (res) => {
          handleCreateViewSuccess(res);
        },
      }
    );
    const cloneViewRequest = useMutation(
      (viewId) => {
        toast.custom(() => (
          <EverHotToastMessage
            type="loading"
            description="Cloning datasheet view..."
            toastId="viewCloneLoading"
          />
        ));
        return cloneView(accessToken, datasheetStore.datasheetId, viewId);
      },
      {
        onError: (error) => {
          console.log("error", error);
          message.error(error?.toString());
        },
        onSuccess: async (res) => {
          if (res.message === "Datasheet view cloned successfully.") {
            toast.dismiss("viewCloneLoading");
            message.success(res.message);
            setNewViews(res.view);
          }
        },
      }
    );

    function updateViewDetails(newViewId) {
      getViewDetails(accessToken, datasheetStore.datasheetId, newViewId).then(
        (newViewData) => {
          const details = cloneDeep(toJS(datasheetStore.datasheetDetails));
          details.views.push(newViewData);
          datasheetStore.setDatasheetDetails(details);
          onTabChange(newViewId);
          datasheetStore.tableDataRefetch();
        }
      );
    }

    function setNewViews(newView) {
      const details = cloneDeep(toJS(datasheetStore.datasheetDetails));
      if (newView) {
        setPivotConfig({});
        details.views.push(newView);
        datasheetStore.setDatasheetDetails(details);
        onTabChange(newView.view_id);
      } else {
        details.views = details.views.filter(
          (view) => view.view_id !== deleteViewId
        );
        datasheetStore.setDatasheetDetails(details);
        onTabChange(DATASHEET_VIEW_ID.ALL_DATA);
        setDeleteViewId("");
      }
      datasheetStore.tableDataRefetch();
    }

    /**
     * useEffect hook to manage the visibility of the generate sheet modal and set the total record count.
     * - Shows the generate sheet modal if the datasheet is not generated, or if there are configuration or calculation field changes.
     * - Sets the total record count if it is not already set and the datasheet details are not loading.
     * Runs whenever the datasheetDetails change.
     */
    useEffect(() => {
      if (
        typeof datasheetStore.datasheetDetails.is_datasheet_generated !==
          "undefined" &&
        typeof datasheetStore.datasheetDetails.is_config_changed !== "undefined"
      ) {
        setShowGenerateSheetModal(
          !datasheetStore.datasheetDetails.is_datasheet_generated ||
            datasheetStore.datasheetDetails.is_config_changed ||
            datasheetStore.datasheetDetails.is_calc_field_changed
        );
      }
      if (allDataCount === null && !datasheetStore.datasheetDetailsIsLoading)
        setAllDataCount(datasheetStore.datasheetDetails?.total_records);
    }, [datasheetStore.datasheetDetails]);

    /**
     * useEffect hook to initialize the tabs based on the datasheetDetails.
     * Runs whenever the datasheetDetails change.
     */
    useEffect(() => {
      if (
        datasheetStore.datasheetDetails &&
        datasheetStore.datasheetDetails.views
      ) {
        setInititalTabs();
      }
    }, [datasheetStore.datasheetDetails, allDataCount, adjustmentCount]);

    /**
     * useEffect hook to set the active tab to "All Data" when the datasheetId changes.
     */
    useEffect(() => {
      onTabChange(DATASHEET_VIEW_ID.ALL_DATA);
      setPivotConfig({});
      setPivotConfigPayload({});
      setAllDataCount(null);
      setAdjustmentCount(null);
    }, [datasheetStore.datasheetId]);

    const handleGenerateDatasheet = useCallback(
      debounce(async () => {
        onGenerateDatasheet(false);
      }, 200), // Debounce time in milliseconds
      [onGenerateDatasheet]
    );

    /**
     * Updates the URL with the given viewId.
     *
     * @param {string} viewId - The ID of the view to set in the URL.
     */
    function updateURL(viewId) {
      if (window.location.pathname === "/datasheet") {
        const url = new URL(window.location);
        const searchParams = new URLSearchParams(url.search);
        // Set or update the query parameter with the provided key and value
        searchParams.set("viewId", viewId);
        url.search = searchParams.toString();
        // Use history.replaceState to update the URL without reloading the page
        window.history.replaceState({}, "", url);
      }
    }

    /**
     * Synchronizes the URL with the current tabs.
     *
     * @param {Array} tabs - The current tabs.
     */
    function syncURL(tabs) {
      const searchParams = new URLSearchParams(search);
      if (
        window.location.pathname === "/datasheet" &&
        searchParams.get("viewId")
      ) {
        const urlViewId = searchParams.get("viewId") ?? "all_data";
        if (tabs.some((obj) => obj.view_id === urlViewId)) {
          onTabChange(urlViewId);
        } else {
          toast.custom(
            (t) => (
              <EverHotToastNotification
                type="error"
                title="Invalid URL"
                description={"View not found for this datasheet"}
                toastId={t.id}
              />
            ),
            { position: "top-right", duration: 4000 }
          );
        }
        isURLSynced.current = true;
      }
    }

    /**
     * Handles tab change event.
     *
     * @param {string} newActiveKey - The key of the new active tab.
     */
    function onTabChange(newActiveKey) {
      if (newActiveKey === "newView") return;
      setActiveKey(newActiveKey);
      newActiveKey !== DATASHEET_VIEW_ID.ALL_DATA &&
      newActiveKey !== "adjustments"
        ? setFilterVisible(true)
        : setFilterVisible(false);
      newActiveKey === DATASHEET_VIEW_ID.ALL_DATA &&
        datasheetStore.dsFilterExpressions[newActiveKey] &&
        datasheetStore.dsFilterExpressions[newActiveKey]?.length !== 0 &&
        setFilterVisible(true);
      updateURL(newActiveKey);
    }

    const debouncedCreateNewView = debounce(() => {
      if (!datasheetStore.datasheetDetails?.is_datasheet_generated) return;
      getUniqueViewName(
        datasheetStore.datasheetDetails?.views,
        viewNameMap,
        createViewRequest.mutate
      );
    }, 300);

    /**
     * Renders the menu for the view dropdown.
     *
     * @returns {JSX.Element} The menu element.
     */
    const viewMenu = (view) => {
      return (
        <Menu>
          {/* TODO: Uncomment and wire when view export is functional */}
          {/* {hasPermissions(RBAC_ROLES.EXPORT_DATASHEET) && (
            <Menu.SubMenu
              key="export"
              className="datasheet-export-sub-menu"
              title={
                <div className="flex items-center !h-8 gap-2">
                  <UploadIcon className="w-4 h-4 text-ever-base-content-mid" />
                  <EverTg.Caption className="!ml-0 text-ever-base-content ">
                    Export
                  </EverTg.Caption>
                </div>
              }
              disabled={showGenerateSheetModal}
            >
              <Menu.Item
                className="!h-8"
                key="preAdjustment"
                onClick={() => {}}
              >
                <EverTg.Caption className="text-ever-base-content">
                  Pre Adjustment
                </EverTg.Caption>
              </Menu.Item>
              <Menu.Item
                className="!h-8"
                key="postAdjustment"
                onClick={() => {}}
              >
                <EverTg.Caption className="text-ever-base-content">
                  Post Adjustment
                </EverTg.Caption>
              </Menu.Item>
            </Menu.SubMenu>
          )} */}
          <Menu.Item
            className="!py-2 !px-3 flex gap-2 !h-8"
            key="copyLink"
            onClick={() => {
              const viewURL = `${window.location.origin}${window.location.pathname}?id=${datasheetStore.datasheetId}&viewId=${view.view_id}`;
              navigator.clipboard
                .writeText(viewURL)
                .then(() => {
                  message.success("Link copied to clipboard!");
                })
                .catch(() => {
                  message.error("Failed to copy link.");
                });
            }}
          >
            <div className="flex gap-2 w-full h-full items-center">
              <CopyLinkIcon className="w-4 h-4 text-ever-base-content-mid" />
              <EverTg.Caption className="text-ever-base-content">
                Copy link
              </EverTg.Caption>
            </div>
          </Menu.Item>
          <Menu.Item
            className="!py-2 !px-3 flex gap-2 !h-8"
            key="clone"
            onClick={async () => {
              await cloneViewRequest.mutate(view.view_id);
            }}
          >
            <div className={twMerge("flex gap-2 items-center")}>
              <CopyIcon className="w-4 h-4 text-ever-base-content-mid" />
              <EverTg.Caption className="text-ever-base-content">
                Clone
              </EverTg.Caption>
            </div>
          </Menu.Item>
          <Menu.Item
            key="rename"
            onClick={() => {
              setIsEdit(view.view_id);
            }}
            className="!py-2 !px-3 flex gap-2 !h-8"
          >
            <div className={"flex gap-2 items-center"}>
              <TextInputIcon className="w-4 h-4 text-ever-base-content-mid" />
              <EverTg.Caption className="text-ever-base-content">
                Rename
              </EverTg.Caption>
            </div>
          </Menu.Item>
          <Menu.Item
            key="delete"
            onClick={() => {
              setDeleteViewId(view.view_id);
            }}
            className="hover:!bg-ever-error-lite !py-2 !px-3 flex gap-2 !h-8"
          >
            <div className={"flex gap-2 items-center text-ever-error"}>
              <Trash03Icon className="w-4 h-4 text-ever-error" />
              <EverTg.Caption className="">Delete</EverTg.Caption>
            </div>
          </Menu.Item>
        </Menu>
      );
    };

    /**
     * Extra content for the tab bar, including filter and settings buttons.
     */
    const tabBarExtraContent = activeKey !==
      DATASHEET_VIEW_ID.ALL_ADJUSTMENTS && (
      <div className="flex items-center justify-end gap-2 pr-1">
        <EverButton
          type="text"
          color="base"
          prependIcon={
            <FilterLinesIcon className="text-ever-base-content-mid" />
          }
          size="small"
          onClick={() => setFilterVisible(!filterVisible)}
          disabled={showGenerateSheetModal}
        >
          Filter
        </EverButton>

        {hasPermissions(RBAC_ROLES.MANAGE_DATABOOK) && (
          <div className="flex gap-2">
            <EverTooltip title={"Show/hide columns"} placement="topRight">
              <EverButton.Icon
                type="text"
                color="base"
                icon={
                  <ColumnsIcon
                    className={`${
                      showGenerateSheetModal
                        ? "text-ever-base-content-low"
                        : "text-ever-base-content-mid"
                    }`}
                  />
                }
                size="small"
                disabled={showGenerateSheetModal}
                onClick={() => {
                  sendAnalyticsEvent(
                    accessToken,
                    ANALYTICS_EVENTS.CUSTOMIZE_COLUMNS,
                    {
                      [ANALYTICS_PROPERTIES.DATASHEET_NAME]:
                        datasheetStore?.currentDatasheetName,
                      [ANALYTICS_PROPERTIES.PLATFORM_USED]: PLATFORM.DATABOOK,
                    }
                  );
                  gridRef?.current[activeKey]?.api?.setSideBarVisible(true);
                  gridRef?.current[activeKey]?.api?.openToolPanel(
                    "customise-columns"
                  );
                }}
              />
            </EverTooltip>
            <EverTooltip title={"Pivot table"} placement="topRight">
              <EverButton.Icon
                type="text"
                color="base"
                icon={
                  <LayoutAltIcon
                    className={`${
                      showGenerateSheetModal
                        ? "text-ever-base-content-low"
                        : "text-ever-base-content-mid"
                    }`}
                  />
                }
                size="small"
                disabled={showGenerateSheetModal}
                onClick={() => {
                  gridRef?.current[activeKey]?.api?.setSideBarVisible(true);
                  gridRef?.current[activeKey]?.api?.openToolPanel(
                    "pivot-table"
                  );
                }}
              />
            </EverTooltip>
          </div>
        )}
      </div>
    );

    /**
     * Custom tab bar renderer.
     *
     * @param {object} props - The properties of the tab bar.
     * @param {React.Component} DefaultTabBar - The default tab bar component.
     * @returns {JSX.Element} The rendered tab bar.
     */
    const renderTabBar = (props, DefaultTabBar) => (
      <div>
        <div
          className={`ant-tabs ant-tabs-top ant-tabs-custom-button ${
            filterVisible && activeKey !== DATASHEET_VIEW_ID.ALL_ADJUSTMENTS
              ? "mt-4"
              : "my-4"
          }`}
        >
          <DefaultTabBar
            {...props}
            className="!m-0 before:hidden [&>.ant-tabs-nav-wrap>.ant-tabs-nav-list>.ant-tabs-tab]:!bg-transparent"
          />
        </div>
      </div>
    );

    /**
     * Initializes the tabs based on the datasheetDetails.
     */
    function setInititalTabs() {
      const arr = cloneDeep(datasheetStore.datasheetDetails.views);
      arr.forEach((ele) => {
        ele.showFilter = true;
        if (!isEmpty(ele?.pivot_data)) {
          ele.total_records = pivotConfig[ele.view_id]?.total_records ?? 0;
        }
      });
      const defTabs = [
        {
          name: "All data",
          showFilter: true,
          type: "allData",
          view_id: DATASHEET_VIEW_ID.ALL_DATA,
          showOptions: false,
          total_records: allDataCount,
        },
      ];
      if (
        datasheetStore.datasheetDetails.data_origin !=
          DATA_ORIGIN.COMMISSION_OBJECT &&
        datasheetStore.datasheetDetails.data_origin !=
          DATA_ORIGIN.INTER_OBJECT &&
        datasheetStore.datasheetDetails.data_origin !=
          DATA_ORIGIN.FORECAST_OBJECT &&
        datasheetStore.datasheetDetails.data_origin !=
          DATA_ORIGIN.INTER_FORECAST_OBJECT &&
        hasPermissions(RBAC_ROLES.MANAGE_DATASHEETADJUSTMENTS)
      ) {
        defTabs.push({
          name: "Adjustments",
          total_records: adjustmentCount,
          prependIcon: (
            <EraserIcon className="w-4 h-4 text-ever-base-content" />
          ),
          showFilter: false,
          type: "adjustments",
          view_id: DATASHEET_VIEW_ID.ALL_ADJUSTMENTS,
          showOptions: false,
        });
      }
      setTabs([...defTabs, ...arr]);
      if (!isURLSynced.current) {
        syncURL([...defTabs, ...arr]);
      }
    }

    if (
      datasheetStore.datasheetDetailsIsLoading ||
      datasheetStore.isDatasheetDetailsRefetching
    ) {
      return (
        <div className="flex flex-col gap-2 h-full w-full mt-2">
          <EverLoader.Skeleton
            config={[2]}
            className="h-10 !p-0 !pl-1 w-full"
          />

          <LoaderTable />
        </div>
      );
    }

    return (
      <>
        <EverTabs
          renderTabBar={renderTabBar}
          type="editable-card"
          onEdit={(e) => {
            if (!showGenerateSheetModal) {
              e.type === "click" && debouncedCreateNewView();
            }
          }}
          addIcon={
            <EverTooltip title={!showGenerateSheetModal && "Add New View"}>
              <span
                className={`w-6 h-6 rounded-lg flex items-center justify-center ${
                  showGenerateSheetModal
                    ? "cursor-not-allowed bg-ever-base-50"
                    : "bg-ever-base-100"
                } `}
              >
                <PlusIcon
                  className={`h-4 w-4 ${
                    showGenerateSheetModal
                      ? "text-ever-base-content-low"
                      : "text-ever-base-content-mid"
                  }`}
                />
              </span>
            </EverTooltip>
          }
          activeKey={activeKey}
          hideAdd={
            hasPermissions(RBAC_ROLES.MANAGE_DATABOOK) &&
            !datasheetStore.datasheetDetails?.is_archived
              ? false
              : true
          }
          className={`datasheet-tabs !text-ever-base-content-mid`}
          onChange={onTabChange}
          defaultActiveKey={DATASHEET_VIEW_ID.ALL_DATA}
          tabBarExtraContent={tabBarExtraContent}
        >
          {tabs.length > 0 &&
            tabs.map((view) => {
              return (
                <TabPane
                  tab={
                    <TabLabel
                      label={view.name}
                      count={view.total_records || 0}
                      prependIcon={view.prependIcon || <></>}
                      showOptions={view.showOptions}
                      accessToken={accessToken}
                      datasheetId={datasheetStore.datasheetId}
                      viewId={view.view_id}
                      refetchDatasheetDetails={
                        datasheetStore.refetchDatasheetDetails
                      }
                      isNewView={view.isNewView}
                      setInititalTabs={setInititalTabs}
                      onTabChange={onTabChange}
                      activeKey={activeKey}
                      isEdit={isEdit === view.view_id}
                      setIsEdit={setIsEdit}
                      viewsLength={tabs.length > 2 ? tabs.length - 2 : 0}
                      showGenerateSheetModal={showGenerateSheetModal}
                      addFilterDataToView={addFilterDataToView}
                      setAddFilterDataToView={setAddFilterDataToView}
                      viewNameMap={viewNameMap}
                    />
                  }
                  key={view.view_id}
                  closeIcon={
                    view.showOptions !== false &&
                    hasPermissions(RBAC_ROLES.MANAGE_DATABOOK) ? (
                      <Dropdown
                        trigger={["click"]}
                        overlay={() => viewMenu(view)}
                        onClick={(e) => e.stopPropagation()}
                      >
                        <IconButton
                          type="text"
                          color="base"
                          icon={
                            <ChevronDownIcon className="!w-4 !h-4 !text-ever-base-content-low" />
                          }
                          size="small"
                          className="!w-5 !h-5"
                        />
                      </Dropdown>
                    ) : (
                      <></>
                    )
                  }
                  disabled={showGenerateSheetModal}
                >
                  {showGenerateSheetModal ? (
                    <div
                      id="dummy-data-table"
                      className="w-full h-full relative"
                      ref={dummyDataTableRef}
                    >
                      <EverModal
                        visible={
                          showGenerateSheetModal &&
                          !datasheetStore.isDatasheetDetailsRefetching
                        }
                        closable={false}
                        className="w-96 h-52"
                        mask={false}
                        wrapClassName="absolute"
                        getContainer={() => dummyDataTableRef.current}
                      >
                        <div className="w-full h-full px-4 py-2 flex justify-center items-center flex flex-col gap-4">
                          {datasheetStore.isSyncInProgress ? (
                            <EverLoader.SpinnerLottie className="w-12 h-12" />
                          ) : (
                            <AlertSquareIcon className="text-ever-warning w-12 h-12" />
                          )}
                          {hasPermissions(
                            [
                              RBAC_ROLES.MANAGE_DATABOOK,
                              RBAC_ROLES.MANAGE_DATASHEETADJUSTMENTS,
                            ],
                            false
                          ) ? (
                            datasheetStore.isSyncInProgress ? (
                              <>
                                <EverTg.Heading3 className="text-ever-base-content">
                                  Refreshing this sheet with latest data from
                                  all sources
                                </EverTg.Heading3>
                                <EverTg.Text className="text-ever-base-content">
                                  Please wait, this might take some time.
                                </EverTg.Text>
                              </>
                            ) : (
                              <>
                                <div className="flex flex-col items-center">
                                  {!datasheetStore.datasheetDetails
                                    .is_datasheet_generated ? (
                                    <>
                                      <EverTg.Heading3 className="text-ever-base-content">
                                        Datasheet has been set up! Just one last
                                        step...
                                      </EverTg.Heading3>
                                    </>
                                  ) : (
                                    <EverTg.Heading3 className="text-ever-base-content">
                                      Changes take effect when a data refresh is
                                      performed
                                    </EverTg.Heading3>
                                  )}
                                </div>

                                <EverButton
                                  onClick={handleGenerateDatasheet}
                                  loading={isRefreshRequestLoading}
                                  disabled={isRefreshRequestLoading}
                                >
                                  {!datasheetStore.datasheetDetails
                                    .is_datasheet_generated
                                    ? "Load data into this sheet"
                                    : "Refresh Datasheet"}
                                </EverButton>
                              </>
                            )
                          ) : (
                            <div className="flex flex-col items-center">
                              <EverTg.Heading3 className="text-ever-base-content">
                                Please ask your administrator to generate this
                                datasheet.
                              </EverTg.Heading3>
                            </div>
                          )}
                        </div>
                      </EverModal>
                      <LoaderTable />
                    </div>
                  ) : (
                    <DataTab
                      showFilter={view.showFilter}
                      viewId={view.view_id}
                      tabs={tabs}
                      setTabs={setTabs}
                      initialFilter={view.filter_data}
                      filterVisible={filterVisible}
                      canvasRef={canvasRef}
                      gridRef={gridRef}
                      gridApi={gridApi}
                      activeKey={activeKey}
                      setAllDataCount={setAllDataCount}
                      setAdjustmentCount={setAdjustmentCount}
                      pivotConfig={pivotConfig}
                      setPivotConfig={setPivotConfig}
                      pivotConfigPayload={pivotConfigPayload}
                      setPivotConfigPayload={setPivotConfigPayload}
                      onTabChange={onTabChange}
                      viewNameMap={viewNameMap}
                      handleCreateViewSuccess={handleCreateViewSuccess}
                    />
                  )}
                </TabPane>
              );
            })}
        </EverTabs>
        <EverModal.Confirm
          visible={deleteViewId !== ""}
          iconContainerClasses="border-ever-error border-solid border bg-transparent"
          icon={
            <TrashLottie
              autoplay
              loop
              className="h-10 w-10 text-ever-error-content -translate-y-1"
            />
          }
          title="Are you sure to delete this view?"
          subtitle="This action can't be undone."
          confirmationButtons={[
            <EverButton
              key="delete"
              color="error"
              onClick={async () => {
                await deleteViewRequest.mutate(deleteViewId);
              }}
            >
              Yes, delete
            </EverButton>,
            <EverButton
              type="ghost"
              color="base"
              key="cancel"
              onClick={() => setDeleteViewId("")}
            >
              Cancel
            </EverButton>,
          ]}
        />
      </>
    );
  }
);
