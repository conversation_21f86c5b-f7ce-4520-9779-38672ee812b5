import {
  EyeIcon,
  EditPencilIcon,
  ReverseLeftIcon,
} from "@everstage/evericons/outlined";
import { Menu } from "antd";
import { observer } from "mobx-react";
import React, { useContext, useState } from "react";
import { useQuery } from "react-query";
import { useRecoilValue } from "recoil";

import { DATASHEET_VIEW_ID, RBAC_ROLES } from "~/Enums";
import { myClientAtom } from "~/GlobalStores/atoms";
import { useAuthStore } from "~/GlobalStores/AuthStore";
import { useUserPermissionStore } from "~/GlobalStores/UserPermissionStore";
import { getClientFeatures } from "~/Utils/ClientFeaturesUtils";
import { EverTg } from "~/v2/components";

import { AdjustmentsTable } from "./AdjustmentsTable";
import { DataTable } from "./DataTable";
import { FilterPanel } from "./FilterPanel";
import { AdjustmentDetail } from "./ViewAdjustmentModal";
import { DatasheetContext } from "../../DatasheetStore";
import { getAllAdjustments } from "../../restApi";

/**
 * Represents the DataTab component for each view.
 *
 * @param {Object} props - The props for the tab component.
 * @param {string} props.viewId - The view ID for the view.
 * @param {boolean} props.showFilter - Indicates whether to show the filter panel.
 * @param {Array} props.tabs - The tabs array to show the tab headers.
 * @param {Function} props.setTabs - Setter for the tabs state used when table data is fetched.
 * @param {Array} props.initialFilter - Initial filter expression for a view.
 * @param {boolean} props.filterVisible - State used to show/hide the filter expression box.
 * @param {Object} props.canvasRef - Reference to the canvas element.
 * @param {Object} props.gridRef - Reference to the grid element.
 * @param {Object} props.gridApi - API for interacting with the grid.
 * @param {string} props.activeKey - The active key for the current tab.
 * @param {Function} props.setAllDataCount - Setter function for the total data count.
 * @returns {JSX.Element} React component
 */

export const DataTab = observer(
  ({
    viewId,
    showFilter,
    tabs,
    setTabs,
    initialFilter,
    filterVisible,
    canvasRef,
    gridRef,
    gridApi,
    activeKey,
    pivotConfig,
    setPivotConfig,
    setAllDataCount,
    setAdjustmentCount,
    onTabChange,
    viewNameMap,
    handleCreateViewSuccess,
    pivotConfigPayload,
    setPivotConfigPayload,
  }) => {
    const { accessToken } = useAuthStore();
    const datasheetStore = useContext(DatasheetContext);
    const { hasPermissions } = useUserPermissionStore();
    const myClient = useRecoilValue(myClientAtom);
    const clientFeatures = getClientFeatures(myClient);

    const [dsVariables, setDsVariables] = useState([]);
    const [inProgressAdjustments, setInProgressAdjustments] = useState({});
    const [adjustedRowKeys, setAdjustedRowKeys] = useState({});
    const [adjustmentDetailRecord, setAdjustmentDetailRecord] = useState(null);
    const [showAdjustmentDetail, setShowAdjustmentDetails] = useState("");
    const [showEditAdjustmentDrawer, setShowEditAdjustmentDrawer] =
      useState(false);
    const [adjustmentRecordId, setAdjustmentRecordId] = useState(null);
    const [adjustmentRecord, setAdjustmentRecord] = useState(null);
    const [adjustmentOperation, setAdjustmentOperation] = useState("");
    const [isEditAdjustment, setIsEditAdjustment] = useState(false);

    const {
      refetch: adjustmentsRefetch,
      isLoading,
      isRefetching,
      isError,
      error,
    } = useQuery(
      ["getAllAdjustments"],
      () => {
        if (!hasPermissions(RBAC_ROLES.MANAGE_DATASHEETADJUSTMENTS))
          return null;
        if (
          datasheetStore.isDatasheetDetailsRefetching ||
          datasheetStore.datasheetDetailsIsLoading
        )
          return;
        return getAllAdjustments(accessToken, datasheetStore.datasheetId);
      },
      {
        cacheTime: 0,
        refetchOnWindowFocus: false,
        enabled: hasPermissions(RBAC_ROLES.MANAGE_DATASHEETADJUSTMENTS),
        onSettled: (data, error) => {
          if (error) {
            console.log("Error occurred:", error);
          } else if (data) {
            const rowKeyObj = {};
            data.not_applied_row_keys?.adjusted_row_keys?.map(
              (rowKey) => (rowKeyObj[rowKey] = true)
            );
            data.not_applied_row_keys?.original_row_keys?.map(
              (rowKey) => (rowKeyObj[rowKey] = true)
            );
            let obj = {};
            Object.keys(data.adjustment_data).map((adjId) => {
              data.adjustment_data[adjId].map((ele) => {
                obj[ele.row_key] = data.adjustment_data[adjId][0];
              });
            });
            setAdjustedRowKeys(obj);
            setInProgressAdjustments(rowKeyObj);
            const adjustmentDataWithDanglingRows = {
              ...(data?.adjustment_data || {}),
              ...(clientFeatures?.isShowDanglingAdjustmentsEnabled
                ? data?.dangling_rows || {}
                : {}),
            };
            datasheetStore.setAdjustmentData(adjustmentDataWithDanglingRows);
            // adjustedCount = the count of already adjusted records (excluding not applied adjustments)
            const adjustedCount = Object.values(
              adjustmentDataWithDanglingRows
            ).reduce(
              (count, adjustments) => count + (adjustments.length > 0 ? 1 : 0),
              0
            );
            // Update adjustment count when adjustment data changes
            setAdjustmentCount(adjustedCount);
          }
        },
      }
    );
    datasheetStore.setAdjustmentTableDataRefetch(adjustmentsRefetch);

    /**
     * Updates the URL to include the adjustment details.
     * @param {Object} record - The adjustment record.
     */
    function updateURL(record) {
      if (location.pathname === "/datasheet") {
        window.history.replaceState(
          null,
          "",
          `${location.pathname}/adjustments/edit?id=${datasheetStore.datasheetId}&adjustmentId=${record?.adjustment_details.adjustment_id}`
        );
      }
    }

    /**
     * Generates the dropdown menu for each row.
     * @param {Object} record - The adjustment record.
     * @returns {JSX.Element} - The menu component.
     */
    function editAdjustmentMenu(record, fromDataTable) {
      return (
        <Menu>
          <Menu.Item
            key="ignoreRecord"
            onClick={() => {
              setShowAdjustmentDetails("view");
              setAdjustmentDetailRecord(record);
            }}
            className="!py-2 !px-3 flex gap-2 !h-8"
            disabled={false}
          >
            <div className="flex gap-2 items-center">
              <EyeIcon className="w-4 h-4 text-ever-base-content-mid" />
              <EverTg.Caption className="text-ever-base-content">
                View
              </EverTg.Caption>
            </div>
          </Menu.Item>
          {hasPermissions(RBAC_ROLES.MANAGE_DATASHEETADJUSTMENTS) && (
            <>
              {record.adjustment_details.type !== "IGNORE" && (
                <Menu.Item
                  key="updateRecord"
                  onClick={() => {
                    if (fromDataTable) {
                      setAdjustmentRecord(
                        datasheetStore.adjustmentData[
                          record.adjustment_details.adjustment_id
                        ]
                      );
                      setIsEditAdjustment(true);
                    } else {
                      setAdjustmentRecordId(
                        record.adjustment_details.adjustment_id
                      );
                      setShowEditAdjustmentDrawer(true);
                    }
                    updateURL(record);
                  }}
                  className="!py-2 !px-3 flex gap-2 !h-8"
                  disabled={false}
                >
                  <div className="flex gap-2 items-center">
                    <EditPencilIcon className="w-4 h-4 text-ever-base-content-mid" />
                    <EverTg.Caption className="text-ever-base-content">
                      Edit
                    </EverTg.Caption>
                  </div>
                </Menu.Item>
              )}
              <Menu.Item
                key="splitRecord"
                onClick={() => {
                  setShowAdjustmentDetails("revert");
                  setAdjustmentDetailRecord(record);
                }}
                className="!py-2 !px-3 flex gap-2 !h-8"
                disabled={false}
              >
                <div className="flex gap-2  items-center">
                  <ReverseLeftIcon className="w-4 h-4 text-ever-base-content-mid" />
                  <EverTg.Caption className="text-ever-base-content">
                    Revert
                  </EverTg.Caption>
                </div>
              </Menu.Item>{" "}
            </>
          )}
        </Menu>
      );
    }

    return (
      <div className="flex flex-col h-full">
        {showFilter && (
          <FilterPanel
            dsVariables={dsVariables}
            initialFilter={initialFilter}
            showFilter={filterVisible}
            viewId={viewId}
            activeKey={activeKey}
            onTabChange={onTabChange}
            viewNameMap={viewNameMap}
          />
        )}
        {viewId === DATASHEET_VIEW_ID.ALL_ADJUSTMENTS ? (
          <AdjustmentsTable
            isError={isError}
            isLoading={isLoading}
            isRefetching={isRefetching}
            recentView={viewId}
            error={error}
            editAdjustmentMenu={editAdjustmentMenu}
            showEditAdjustmentDrawer={showEditAdjustmentDrawer}
            setShowEditAdjustmentDrawer={setShowEditAdjustmentDrawer}
            adjustmentRecordId={adjustmentRecordId}
            setAdjustmentRecordId={setAdjustmentRecordId}
          />
        ) : (
          <DataTable
            viewId={viewId}
            tabs={tabs}
            setTabs={setTabs}
            setDsVariables={setDsVariables}
            inProgressAdjustments={inProgressAdjustments}
            canvasRef={canvasRef}
            gridRef={gridRef}
            gridApi={gridApi}
            activeKey={activeKey}
            adjustedRowKeys={adjustedRowKeys}
            editAdjustmentMenu={editAdjustmentMenu}
            adjustmentRecord={adjustmentRecord}
            setAdjustmentRecord={setAdjustmentRecord}
            adjustmentOperation={adjustmentOperation}
            setAdjustmentOperation={setAdjustmentOperation}
            isEditAdjustment={isEditAdjustment}
            setIsEditAdjustment={setIsEditAdjustment}
            setAllDataCount={setAllDataCount}
            pivotConfig={pivotConfig}
            setPivotConfig={setPivotConfig}
            handleCreateViewSuccess={handleCreateViewSuccess}
            pivotConfigPayload={pivotConfigPayload}
            setPivotConfigPayload={setPivotConfigPayload}
          />
        )}
        {showAdjustmentDetail !== "" && (
          <AdjustmentDetail
            visible={showAdjustmentDetail !== ""}
            handleVisible={setShowAdjustmentDetails}
            title={
              showAdjustmentDetail === "revert"
                ? "Revert Adjustment"
                : "View Adjustment"
            }
            selectedRecord={adjustmentDetailRecord}
            isRevert={showAdjustmentDetail === "revert"}
            // saveSessionKd={saveSessionKd}
          />
        )}
      </div>
    );
  }
);
