import {
  TextInputIcon,
  Trash03Icon,
  DotsVerticalIcon,
  PinIcon,
  ArrowUpRightIcon,
  CopyLinkIcon,
  InfoCircleIcon,
  EditPencilIcon,
  CopyIcon,
  DataflowIcon,
} from "@everstage/evericons/outlined";
import { Dropdown, Menu } from "antd";
import { Reorder, useDragControls, AnimatePresence } from "framer-motion";
import { debounce } from "lodash";
import { observer } from "mobx-react";
import React, { useContext, useEffect, useState, useCallback } from "react";
import { useMutation } from "react-query";
import { twMerge } from "tailwind-merge";

import { RBAC_ROLES, DATASHEET_VIEW_BY } from "~/Enums";
import { useAuthStore } from "~/GlobalStores/AuthStore";
import { useUserPermissionStore } from "~/GlobalStores/UserPermissionStore";
import {
  EverTg,
  EverInput,
  message,
  EverDivider,
  EverButton,
  toast,
  EverHotToastMessage,
  <PERSON><PERSON><PERSON><PERSON>,
  EverFormatter,
} from "~/v2/components";
import { noData } from "~/v2/images";

import { DatasheetContext } from "../../DatasheetStore";
import { cloneDatasheet, setPinAction } from "../../restApi";
import useFetchApiWithAuth from "../../useFetchApiWithAuth";
import {
  DATASHEET_GRAPH_LEVEL,
  handleOpenDatasheetGraph,
  updateQueryParam,
} from "../../utils";
import { DsDisplayCard } from "../helperComponent/DsDisplayCard";

const archivedDatasheetActions = {
  newTab: true,
  copyLink: true,
  delete: true,
};

const datasheetActions = [
  {
    label: "Edit sheet",
    value: "edit",
    className: "text-ever-base-content",
    icon: <EditPencilIcon className="w-4 h-4 text-ever-base-content-mid" />,
    permission: RBAC_ROLES.MANAGE_DATABOOK,
  },
  {
    label: "Rename sheet",
    value: "rename",
    className: "text-ever-base-content",
    icon: <TextInputIcon className="w-4 h-4 text-ever-base-content-mid" />,
    permission: RBAC_ROLES.MANAGE_DATABOOK,
  },
  {
    label: "Open in new tab",
    value: "newTab",
    className: "text-ever-base-content",
    icon: <ArrowUpRightIcon className="w-4 h-4 text-ever-base-content-mid" />,
    permission: null,
  },
  {
    label: "seprator",
    value: "seprator",
  },
  {
    label: "Sheet details",
    value: "sheetDetails",
    className: "text-ever-base-content",
    icon: <InfoCircleIcon className="w-4 h-4 text-ever-base-content-mid" />,
    permission: null,
  },
  {
    label: "Pin this sheet",
    value: "pin",
    className: "text-ever-base-content",
    icon: <PinIcon className="w-4 h-4 text-ever-base-content-mid" />,
    permission: RBAC_ROLES.MANAGE_DATABOOK,
  },
  {
    label: "Show dependencies",
    value: "graph",
    className: "text-ever-base-content",
    icon: <DataflowIcon className="w-4 h-4 text-ever-base-content-mid" />,
    permission: RBAC_ROLES.MANAGE_DATABOOK,
  },
  {
    label: "Copy link to sheet",
    value: "copyLink",
    className: "text-ever-base-content",
    icon: <CopyLinkIcon className="w-4 h-4 text-ever-base-content-mid" />,
    permission: null,
  },
  {
    label: "Clone this sheet",
    value: "clone",
    className: "text-ever-base-content",
    icon: <CopyIcon className="w-4 h-4 text-ever-base-content-mid" />,
    permission: RBAC_ROLES.MANAGE_DATABOOK,
  },
  // {
  //   label: "Move",
  //   value: "move",
  //   className: "text-ever-base-content",
  //   icon: <ReverseRightIcon className="w-4 h-4 text-ever-base-content-mid" />,
  //   permission: null,
  // },
  {
    label: "seprator",
    value: "seprator",
  },
  {
    label: "Delete",
    value: "delete",
    className: "text-ever-error-lite-content",
    icon: <Trash03Icon className="w-4 h-4 text-ever-error" />,
    permission: RBAC_ROLES.DELETE_DATASHEET,
  },
];

/**
 * DatasheetCard component represents a card displaying information about a datasheet.
 * It allows users to perform various actions such as renaming, opening in a new tab, copying link, etc.
 * @param {object} props - The props passed to the component.
 * @param {object} props.datasheet - The datasheet object containing information about the datasheet ID and name.
 * @param {function} props.setOpenMovePopover - Function to set the state for opening move popover.
 * @param {string} props.databookId - The ID of the databook.
 * @param {string} props.viewBy - The mode of view (e.g., "commission_plans").
 * @param {function} props.refetchGroups - Function to refetch groups.
 * @returns {JSX.Element} JSX element representing the datasheet card.
 */
const DatasheetCard = observer(
  ({
    datasheet,
    setOpenMovePopover,
    databookId,
    databookName,
    viewBy,
    refetchGroups,
    preventAccordionOpenRef,
  }) => {
    const [isMoreOptionActive, setMoreOptionActive] = useState(false);
    const [sheetName, setSheetName] = useState(datasheet?.name);
    const [isEditMode, setIsEditMode] = useState(false);

    const datasheetStore = useContext(DatasheetContext);
    const { accessToken } = useAuthStore();
    const { fetchData } = useFetchApiWithAuth();
    const { hasPermissions } = useUserPermissionStore();

    const pinRequest = useMutation(
      (datasheetId) => setPinAction(accessToken, datasheetId, "pin"),
      {
        onError: (error) => {
          message.error(error?.toString());
        },
        onSuccess: async () => {
          await datasheetStore.pinnedRefetchFn();
        },
      }
    );

    const renameDataSheet = useMutation(
      () =>
        fetchData(`/datasheet_ninja/`, "PATCH", {
          datasheet_id: datasheet?.datasheet_id,
          databook_id: databookId,
          name: sheetName,
        }),

      {
        onError: async (error) => {
          setIsEditMode(false);
          setSheetName(datasheet?.name);
          message.error(error?.toString());
        },
        onSuccess: async (data) => {
          // Retrieve recent datasheets from localStorage or initialize as an empty array if not available
          const jsonRecentDatasheets =
            localStorage.getItem("recent_datasheets") || "[]";
          // Parse recent datasheets into an array
          const parsedRecentDatasheets = JSON.parse(jsonRecentDatasheets);
          // Initialize a flag to track if any recent datasheet has been renamed
          let isAnyRecentDatasheetRenamed = false;
          // Iterate over each datasheet in the parsed array
          parsedRecentDatasheets.forEach((sheet) => {
            // Check if the current datasheet matches the one being renamed
            if (sheet?.datasheet_id === datasheet.datasheet_id) {
              // Update the name of the matched datasheet
              sheet.name = sheetName;
              // Set the flag to true indicating a datasheet has been renamed
              isAnyRecentDatasheetRenamed = true;
            }
          });
          // Save the updated array of recent datasheets back to local storage
          localStorage.setItem(
            "recent_datasheets",
            JSON.stringify(parsedRecentDatasheets)
          );
          // If any datasheet has been renamed, update the recent sheets in the store
          if (isAnyRecentDatasheetRenamed) {
            datasheetStore.setRecentSheets(parsedRecentDatasheets);
          }
          // Check if the current datasheet is in the pinned datasheet list
          if (
            !datasheetStore.pinnedDatasheetList.every(
              (x) => x.datasheet_id !== datasheet.datasheet_id
            )
          ) {
            // If the datasheet is pinned, refetch the pinned datasheets
            await datasheetStore.pinnedRefetchFn();
          }
          setIsEditMode(false);
          datasheetStore.globalDatabookRefetch();
          if (data?.name && data.datasheet_id === datasheetStore.datasheetId) {
            // Check if the response contains the new name
            datasheetStore.setCurrentDatasheetName(data.name); // Update the store with the new name
          }
          const url = new URL(window.location);
          //Retrieve search parameters from the URL
          const searchParams = new URLSearchParams(url.search);
          searchParams.set("name", data?.name || "");
          sheetName && searchParams.set("name", sheetName);
          url.search = searchParams.toString();
          // Use history.replaceState to update the URL without reloading the page
          window.history.replaceState({}, "", url);
          preventAccordionOpenRef.current = true;
          await refetchGroups();
        },
      }
    );

    const handleRenameDatasheet = useCallback(
      debounce(async () => {
        await renameDataSheet.mutate();
      }, 300), // Debounce time in milliseconds
      [renameDataSheet.mutate]
    );

    const cloneDatasheetRequest = useMutation(
      (data) => {
        return cloneDatasheet(accessToken, datasheet.datasheet_id, data);
      },
      {
        onError: (error) => {
          toast.remove("cloneLoading");
          toast.custom(
            () => (
              <EverHotToastMessage
                type="error"
                description={error?.message || "Error occured"}
              />
            ),
            { position: "top-center" }
          );
        },
        onSuccess: () => {
          toast.remove("cloneLoading");
          toast.custom(
            () => (
              <EverHotToastMessage
                type="success"
                description={"Datasheet cloned!"}
              />
            ),
            { position: "top-center" }
          );
          datasheetStore.refetchDatasheetDetails();
          datasheetStore.viewByRefetchFn();
          datasheetStore.globalDatabookRefetch();
        },
      }
    );

    const handlePin = useCallback(
      debounce(async () => {
        pinRequest.mutate(datasheet?.datasheet_id);
      }, 200), // Debounce time in milliseconds
      [pinRequest.mutate, datasheet?.datasheet_id]
    );
    /**
     * Function to handle actions when more options are clicked.
     * @param {string} action - The action to perform.
     * @returns {void}
     */
    const moreActions = async (action) => {
      let sheetUrl = "";
      if (datasheetStore.datasheetId === datasheet?.datasheet_id) {
        sheetUrl = window.location?.href || "";
      } else {
        sheetUrl = `${window.location?.origin}${window.location?.pathname}?id=${datasheet?.datasheet_id}&name=${datasheet?.name}`;
      }

      switch (action) {
        case "pin": {
          await handlePin();
          return;
        }
        case "graph": {
          handleOpenDatasheetGraph(
            datasheetStore,
            datasheet?.datasheet_id,
            DATASHEET_GRAPH_LEVEL.SHEET,
            datasheet?.name,
            viewBy === DATASHEET_VIEW_BY.COMMISSION_PLAN
              ? datasheet?.databook_id
              : databookId
          );
          return;
        }
        case "rename": {
          setIsEditMode(true);
          break;
        }
        case "newTab": {
          // Open a new tab with the modified URL
          window.open(sheetUrl, "_blank");
          break;
        }
        case "clone": {
          toast.custom(
            () => (
              <EverHotToastMessage
                type="loading"
                description={"Cloning datasheet..."}
              />
            ),
            { position: "top-center", duration: Infinity, id: "cloneLoading" }
          );

          const data = {
            databookId: databookId,
            databookName: databookName,
          };
          await cloneDatasheetRequest.mutate(data);
          return;
        }
        case "move": {
          setOpenMovePopover(true);
          return;
        }
        case "copyLink": {
          try {
            await navigator.clipboard.writeText(sheetUrl);
            message.success("Copied to clipboard");
          } catch (error) {
            console.error("Failed to copy link to clipboard:", error);
          }
          break;
        }
        case "sheetDetails": {
          const url = new URL(window.location);
          const searchParams = new URLSearchParams(url.search);
          // Set or update the query parameter with the provided key and value
          searchParams.set("detailsDatasheetId", datasheetStore.datasheetId);
          url.search = searchParams.toString();
          // Use history.replaceState to update the URL without reloading the page
          window.history.replaceState({}, "", url);
          datasheetStore.setDetailsDatasheetId(datasheet?.datasheet_id);
          datasheetStore.setDrawerVisibility(true);
          return;
        }
        case "archive": {
          return;
        }
        case "delete": {
          datasheetStore.setDatasheetNameToDelete(datasheet?.name);
          datasheetStore.setDatasheetIdToDelete(datasheet?.datasheet_id);
          datasheetStore.setIsDeletesheetModalVisible(true);
          break;
        }
        case "edit": {
          updateQueryParam("id", datasheet?.datasheet_id);
          updateQueryParam("name", datasheet?.name);
          datasheetStore.setDatasheetId(datasheet?.datasheet_id);
          const url = new URL(window.location);
          const searchParams = new URLSearchParams(url.search);
          searchParams.set("isEditView", true);
          searchParams.delete("adjustmentId");
          searchParams.delete("rowKey");
          url.search = searchParams.toString();
          window.history.replaceState({}, "", url);
          datasheetStore.setEditDrawerVisiblity(true);
          break;
        }
      }
    };

    useEffect(() => {
      setSheetName(datasheet.name);
    }, [datasheet.name]);

    const renderMenuItem = (option) => {
      return (
        <Menu.Item
          className={twMerge(
            "!py-2 !px-3 flex gap-2 !h-8",
            option.value === "delete" && "hover:!bg-ever-error-lite"
          )}
          key={option.value}
          onClick={(event) => {
            setMoreOptionActive(false);
            moreActions(event.key);
          }}
          disabled={
            (option.value === "delete" || option.value === "edit") &&
            datasheetStore.isSyncInProgress &&
            datasheet?.datasheet_id === datasheetStore.datasheetId
          }
        >
          {option.icon}
          <EverTg.Caption className={option.className}>
            {option.label}
          </EverTg.Caption>
        </Menu.Item>
      );
    };

    // Render input field for editing sheet name if in edit mode, else render DsDisplayCard
    if (isEditMode) {
      return (
        <EverInput
          autoFocus={true}
          className="!h-8 "
          value={sheetName}
          onBlur={() => {
            if (sheetName.trim() != "") {
              renameDataSheet.mutate();
            }
            setIsEditMode(false);
            setSheetName(datasheet?.name);
          }}
          onKeyDown={(event) => {
            if (event.key === "Enter") {
              if (sheetName.trim() != "") {
                handleRenameDatasheet();
              } else {
                setIsEditMode(false);
                setSheetName(datasheet?.name);
              }
            }
            if (event.key === "Escape") {
              setIsEditMode(false);
              setSheetName(datasheet?.name);
            }
          }}
          onChange={(event) => {
            setSheetName(event.target.value);
          }}
        />
      );
    }

    return (
      <div>
        <DsDisplayCard
          item={datasheet}
          isDraggable={true}
          isCurrentSheet={
            datasheet?.datasheet_id === datasheetStore.datasheetId
          }
          moreOption={
            <Dropdown
              overlay={
                <Menu className="!p-1.5 rounded-lg border border-solid border-ever-base-400 ">
                  {datasheetActions
                    .map((item, idx) => {
                      if (viewBy === DATASHEET_VIEW_BY.ARCHIVED_DATABOOKS) {
                        if (!archivedDatasheetActions[item.value]) {
                          return undefined;
                        }
                      }
                      if (
                        viewBy === DATASHEET_VIEW_BY.COMMISSION_PLAN &&
                        ["delete", "rename", "clone"].includes(item.value)
                      ) {
                        return undefined;
                      }
                      if (item.permission) {
                        if (hasPermissions(item.permission)) {
                          return renderMenuItem(item);
                        }
                      } else if (item.value === "seprator") {
                        return (
                          <div className="py-1" key={`divider-${idx}`}>
                            <EverDivider />
                          </div>
                        );
                      } else {
                        return renderMenuItem(item);
                      }
                      return undefined; // Explicitly return undefined if the item is not rendered
                    })
                    .filter((value) => value !== undefined)}
                  {datasheet?.last_generated_at && (
                    <Menu.Item className="rounded-md bg-ever-base-100 py-3 !h-12 cursor-auto ">
                      <div className="w-full whitespace-normal overflow-hidden flex flex-col">
                        <EverTg.Caption className="text-ever-base-content-mid ">
                          Last refreshed on
                        </EverTg.Caption>

                        <EverFormatter.DateTime
                          date={datasheet?.last_generated_at}
                          type="short"
                          targetTimeZone="local"
                          className="font-normal text-xs text-ever-base-content-mid"
                        />
                      </div>
                    </Menu.Item>
                  )}
                </Menu>
              }
              trigger={["click"]}
              onVisibleChange={(open) => {
                setMoreOptionActive(open);
              }}
              disabled={!datasheet.does_user_has_co_permission}
            >
              <EverTooltip
                title={
                  datasheet.does_user_has_co_permission
                    ? ""
                    : "Cannot modify properties of a restricted sheet"
                }
              >
                <div className="w-full h-full flex items-center justify-center">
                  <DotsVerticalIcon
                    className={"text-ever-base-content w-3 h-3"}
                  />
                </div>
              </EverTooltip>
            </Dropdown>
          }
          moreOptionClassName={twMerge(
            "border border-solid border-transparent rounded-sm hover:border-ever-base-400 hover:bg-ever-base-300",
            isMoreOptionActive && "border-ever-base-400 bg-ever-base-300 flex"
          )}
          className={
            isMoreOptionActive ? "bg-ever-base-100 border-ever-base-300" : ""
          }
          databookId={
            viewBy === DATASHEET_VIEW_BY.COMMISSION_PLAN ? null : databookId
          }
          setDatasheetId={datasheetStore.setDatasheetId}
          setCurrentDatasheetName={datasheetStore.setCurrentDatasheetName}
        />
      </div>
    );
  }
);

/**
 * AccordionContent component renders the content for each accordion item in the grouped list.
 * It generates DatasheetCard components for each datasheet within the category.
 *
 * @param {object} props - The props passed to the component.
 * @param {object} props.item - The item object containing datasheets data.
 * @param {function} props.setOpenMovePopover - Function to set the state for opening move popover.
 * @param {string} props.viewBy - The mode of view (e.g., "commission_plans").
 * @param {function} props.refetchGroups - Function to refetch groups.
 * @returns {JSX.Element} JSX element representing the content of the accordion item.
 */
export const AccordionContent = observer(
  ({
    item,
    setOpenMovePopover,
    viewBy,
    refetchGroups,
    preventAccordionOpenRef,
  }) => {
    const datasheetStore = useContext(DatasheetContext);
    // State to manage the list of cards
    const [cards, setCards] = useState([]);
    const [savedCards, setSavedCards] = useState([]);

    function createCards(datasheets) {
      return datasheets?.map((datasheet) => ({
        id: datasheet.datasheet_id,
        content: (
          // Rendering DatasheetCard component for each datasheet
          <DatasheetCard
            datasheet={datasheet}
            databookId={item.id}
            databookName={item.name}
            setOpenMovePopover={setOpenMovePopover}
            viewBy={viewBy}
            refetchGroups={refetchGroups}
            preventAccordionOpenRef={preventAccordionOpenRef}
          />
        ),
      }));
    }

    useEffect(() => {
      const cardList = createCards(item?.datasheets || []);
      setCards(cardList);
      setSavedCards(cardList);
    }, [item]);

    // Determine the height of the content based on the number of datasheets
    const contentHeight = item?.datasheets?.length * 33;
    return (
      <>
        {/* Render either no data message or the list of datasheet cards */}
        {cards.length === 0 ? (
          <div className="py-6 flex flex-col items-center justify-center">
            <img src={noData} className="w-20 h-16 block" />

            <EverButton
              size="small"
              type="link"
              className="mt-1 !h-5 !text-xs"
              onClick={(event) => {
                event.stopPropagation();
                datasheetStore.setCreateModalVisible(true);
                datasheetStore.setSelectedDatabookId(item.id);
              }}
            >
              Create sheet
            </EverButton>
          </div>
        ) : (
          // eslint-disable-next-line no-inline-styles/no-inline-styles
          <div style={{ height: contentHeight }}>
            {cards.length > 0 && (
              <DragAndDropContainer
                cards={cards}
                setCards={setCards}
                databookId={item.id}
                savedCards={savedCards}
                preventAccordionOpenRef={preventAccordionOpenRef}
                setSavedCards={setSavedCards}
                refetchGroups={refetchGroups}
                viewBy={viewBy}
              />
            )}
          </div>
        )}
      </>
    );
  }
);

export const DragAndDropContainer = ({
  cards,
  setCards,
  databookId,
  savedCards,
  setSavedCards,
  refetchGroups,
  viewBy,

  preventAccordionOpenRef,
}) => {
  const { fetchData } = useFetchApiWithAuth();

  const handleReorder = useMutation(
    (payload) => {
      return fetchData(`databooks/${databookId}`, "PATCH", payload);
    },
    {
      onError: (error) => {
        message.error(error?.toString());
        setCards(savedCards);
      },
      onSuccess: () => {
        setSavedCards(cards);
        refetchGroups();
        preventAccordionOpenRef.current = true;
      },
    }
  );

  return (
    <Reorder.Group
      axis="y"
      layout
      values={cards}
      onReorder={setCards}
      className="p-0 overflow-auto flex flex-col gap-px"
    >
      <DragableContent
        cards={cards}
        handleReorder={handleReorder}
        viewBy={viewBy}
      />
    </Reorder.Group>
  );
};

export const DragableContent = ({ cards, handleReorder, viewBy }) => {
  const dragControls = useDragControls();
  return cards.map((card) => (
    <AnimatePresence key={`reorder-item-${card.id}`}>
      <Reorder.Item
        key={card.id}
        value={card}
        id={card.id}
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="list-none"
        dragListener={viewBy !== DATASHEET_VIEW_BY.COMMISSION_PLAN} // Disable default drag listener
        dragControls={dragControls} // Use custom drag controls
        onDragEnd={() => {
          cards.length > 1 &&
            handleReorder.mutate({
              datasheet_order: cards?.map((x) => x.id) || [],
            });
        }} // Handle drag end event
      >
        {card.content}
      </Reorder.Item>
    </AnimatePresence>
  ));
};
