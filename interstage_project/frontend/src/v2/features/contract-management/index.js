import { CalendarIcon } from "@everstage/evericons/duotone";
import {
  ListIcon,
  LayoutGridIcon,
  PlusCircleIcon,
  AlignTopArrowIcon,
  SwitchVerticalIcon,
} from "@everstage/evericons/outlined";
import { Dropdown, Menu } from "antd";
import { isEmpty, orderBy, uniqBy } from "lodash";
import { observer, useLocalStore } from "mobx-react";
import React, { useEffect, useMemo, useState } from "react";
import { useQuery } from "react-query";
import { useRecoilValue, useSetRecoilState } from "recoil";
import { twMerge } from "tailwind-merge";
import { v4 as uuidv4 } from "uuid";

import {
  exportContractsStatusAsCSV,
  getUserPermission,
  docusignOAuth,
} from "~/Api/DocusignService";
import {
  contentLoaderAtom,
  myClientAtom,
  navPortalAtom,
} from "~/GlobalStores/atoms";
import { useAuthStore } from "~/GlobalStores/AuthStore";
import { useContractsStore } from "~/GlobalStores/ContractsStore";
import { useEmployeeStore } from "~/GlobalStores/EmployeeStore";
import { getClientFeatures } from "~/Utils/ClientFeaturesUtils";
import { permissionsCount } from "~/Utils/contracts";
import { getClientFiscalYear } from "~/Utils/DateUtils";
import {
  EverButton,
  EverInput,
  EverLoader,
  EverSelect,
  EverTabs,
  EverNavPortal,
  EverButtonGroup,
  EverTg,
  EverNumberBadge,
  message,
  EverLabel,
  EverModal,
  EverBreadcrumbPortal,
  EverTooltip,
} from "~/v2/components";
import {
  EmailConfirmationModal,
  SENDOPTIONS,
} from "~/v2/components/EmailConfirmationModal";
import { dogTearingNewspaper } from "~/v2/images";

import ContractsSummaryCard from "./ContractsSummaryCard";
import ContractsSummaryTable from "./ContractsSummaryTable";
import CreateContractDrawer from "./create-contract-drawer";
import CreateContractStore from "./create-contract-drawer/store";
import DocusignIcon from "./images/docusign.svg?react";
import EmptyContract from "./images/EmptyContract.svg?react";
import { NoPermissionView } from "./NoPermissionView";
import { PermissionDetailsModal } from "./PermissionDetailsModal";
import secure from "./SecureComponent";

const SORT_OPTIONS = [
  "Ascending (A - Z)",
  "Descending ( Z - A )",
  "Created Date",
  "Last Modified",
];
const SORT_PARAMS = {
  "Ascending (A - Z)": { key: ["template_name"], order: ["asc"] },
  "Descending ( Z - A )": { key: ["template_name"], order: ["desc"] },
  "Created Date": { key: ["created_at"], order: ["desc"] },
  "Last Modified": { key: ["updated_at"], order: ["desc"] },
};

const ContractManagement = observer(() => {
  const fiscalStartMonth = useRecoilValue(myClientAtom).fiscalStartMonthZero;
  const [searchText, setSearchText] = useState("");
  const [showAsList, setShowAsList] = useState(false);
  const [sortOrder, setSortOrder] = useState();
  const [activeTab, setActiveTab] = useState("1");
  const [selectedYear, setSelectedYear] = useState(
    parseInt(getClientFiscalYear(fiscalStartMonth).format("YYYY"))
  );
  const [showEmailModal, setShowEmailModal] = useState(false);
  const [showConfirmationModal, setShowConfirmationModal] = useState(false);
  const [showdrawer, setShowdrawer] = useState(false);
  const store = useLocalStore(() => new CreateContractStore());
  const {
    contracts,
    loading,
    refetchTemplate,
    refetchAllTemplates,
    isInitialDataSet,
    onParamsChanged,
    setRefetchContractPermissions,
    hasAllPermissions,
    missingAccountPermissionsCount,
    missingUserPermissionsCount,
    userPermissions,
    accountPermissions,
    setUserPermissions,
    setAccountPermissions,
  } = useContractsStore();
  const setContentLoader = useSetRecoilState(contentLoaderAtom);
  const contentLoader = useRecoilValue(contentLoaderAtom);
  const { showContracts } = useEmployeeStore();
  const navPortalLocation = useRecoilValue(navPortalAtom);
  const { accessToken, email } = useAuthStore();

  // Get client configuration from Recoil atom
  const myAtom = useRecoilValue(myClientAtom);

  // Extract feature flags from client configuration
  const clientFeatures = getClientFeatures(myAtom);

  // Flag to enable/disable DocuSign permission checks based on client features
  const enableContractPermissions =
    clientFeatures?.enableContractPermissions || false;

  // State to control the visibility of the permissions modal
  const [open, setOpen] = useState(false);

  const [isTooltipsOpen, setIsTooltipsOpen] = useState(false);
  const {
    setTemplateId,
    setTemplateName,
    setTemplateRoles,
    setTemplateFiles,
    setIsLocked,
    setContractAction,
    setPlanId,
    setIsSigningOrderEnabled,
    setReminderEnabled,
    setReminderDelay,
    setReminderFrequency,
    setExpireAfter,
    setExpireWarn,
  } = store;
  // Destructure loading state and refetch function from useQuery hook
  const {
    isLoading: isLoadingUserPermissions,
    refetch: refetchUserPermissions,
    isFetching: isFetchingUserPermissions,
  } = useQuery(
    // Query key for caching
    ["get_user_permission"],
    // Query function that calls API to get user permissions
    () => {
      return getUserPermission(accessToken, email);
    },
    {
      // Don't retry failed requests
      retry: false,
      // Don't cache results
      cacheTime: 0,
      // Refetch every 10 minutes
      refetchInterval: 600000,
      // Don't refetch when window regains focus
      refetchOnWindowFocus: false,
      // Only run query if contract permissions are enabled
      enabled: enableContractPermissions,
      // Success callback when permissions are fetched
      onSuccess: (data) => {
        // Destructure account and user permissions from response
        const { accountPermissions, userPermissions } = data?.data || {};
        // Update permission states with fetched data
        setAccountPermissions(accountPermissions || {});
        setUserPermissions(userPermissions || {});
        // Calculate missing permissions counts
        const { missingAccountPermissionsCount, missingUserPermissionsCount } =
          permissionsCount({ accountPermissions, userPermissions });

        // If content loader is showing
        if (contentLoader.showLoader) {
          // Hide the loader
          setContentLoader({ showLoader: false });
          // If no permissions are missing
          if (
            missingAccountPermissionsCount === 0 &&
            missingUserPermissionsCount === 0
          ) {
            // Show success message
            message.success(
              "All permissions are set. You can now create contracts."
            );
          } else {
            // Show warning if permissions are missing
            message.warning(
              "Some permissions are still missing in your DocuSign account. Please review and try again.."
            );
          }
        }
      },
      // Error callback for failed requests
      onError: (error) => {
        // If content loader is showing
        if (contentLoader.showLoader) {
          // Hide the loader
          setContentLoader({ showLoader: false });
        }
        // Show error message with error details if available
        message.error(
          error?.message || "Couldn’t check permissions right now. Try again"
        );
      },
    }
  );

  useEffect(() => {
    if (refetchUserPermissions) {
      setRefetchContractPermissions(refetchUserPermissions);
    }
  }, [refetchUserPermissions]);

  const activateContract = (templateId) => {
    refetchTemplate(templateId, "");
  };

  const archiveContract = (templateId) => {
    refetchTemplate(templateId, "");
  };

  useEffect(() => {
    if (showContracts && !isInitialDataSet) {
      refetchAllTemplates("cache-first");
    }
  }, [showContracts]);

  const getBaseFilteredContracts = () => {
    let baseFinalContracts = contracts;
    if (sortOrder) {
      baseFinalContracts = orderBy(
        baseFinalContracts,
        SORT_PARAMS[sortOrder]["key"],
        SORT_PARAMS[sortOrder]["order"]
      );
    }

    const filterBasedOnYear = () => {
      if (selectedYear === 1) {
        return baseFinalContracts?.filter(
          (contract) => contract?.fiscal_year === null
        );
      }
      return baseFinalContracts?.filter(
        (contract) => contract?.fiscal_year === selectedYear
      );
    };

    if (selectedYear) {
      baseFinalContracts = filterBasedOnYear();
    }

    if (!isEmpty(searchText)) {
      baseFinalContracts = baseFinalContracts?.filter((contract) =>
        contract?.template_name
          ?.toLowerCase()
          .includes(searchText.toLowerCase())
      );
    }

    return baseFinalContracts;
  };

  const baseFilteredContracts = getBaseFilteredContracts();
  const activeContracts = baseFilteredContracts.filter(
    (contract) => !contract.is_archived
  );
  const archivedContracts = baseFilteredContracts.filter(
    (contract) => contract.is_archived
  );

  const handleExport = ({ recipientEmailId, emailUserOption }) => {
    setShowEmailModal(false);
    exportContractsStatusAsCSV(accessToken, {
      toEmailId:
        emailUserOption === SENDOPTIONS.sendUser ? email : recipientEmailId,
      fiscalYear: selectedYear === 1 ? null : selectedYear,
      isArchived: activeTab === "2",
    })
      .then((response) => {
        if (response.status === 200) {
          setShowConfirmationModal(true);
        } else {
          message.error("Error while exporting contracts status!");
        }
      })
      .catch((error) => {
        message.error("Error while exporting contracts status!");
        console.log(error.message);
      });
  };

  const contractsYearList = useMemo(
    () =>
      orderBy(
        uniqBy(
          contracts.map((contract) => {
            if (contract.fiscal_year === null)
              return {
                label: "NA",
                value: 1,
              };
            return {
              label: contract.fiscal_year,
              value: contract.fiscal_year,
            };
          }),
          "value"
        ),
        ["label"],
        ["desc"]
      ),
    [contracts]
  );

  const clearStore = () => {
    setTemplateId(null);
    setTemplateName(null);
    setTemplateRoles([
      {
        signingPreference: "Needs To Sign",
        id: uuidv4(),
        role: "",
        routingOrder: 1,
      },
    ]);
    setIsSigningOrderEnabled(false);
    setTemplateFiles([]);
    setIsLocked(false);
    setPlanId(null);
    setReminderEnabled(false);
    setReminderDelay(5);
    setReminderFrequency(3);
    setExpireAfter(120);
    setExpireWarn(0);
  };

  /**
   * Opens DocuSign OAuth flow in a new tab
   *
   * This function initiates the DocuSign OAuth authentication process by:
   * 1. Making an API call to get the DocuSign authorization URL using the current access token
   * 2. Extracting the origin URL from the response
   * 3. Opening the DocuSign login page in a new browser tab
   *
   * @throws {Error} If the API call fails or response is invalid
   */
  const openDocusign = () => {
    // Make API call to get DocuSign authorization URL
    docusignOAuth(accessToken)
      .then((response) => {
        // Check if response is successful, otherwise throw error
        if (response.ok) return response.text();
        else throw new Error("Error occurred");
      })
      .then((data) => {
        // Extract origin URL from response and remove quotes
        const url = new URL(data.slice(1, -1)).origin || "";
        // Open DocuSign login page in new tab and focus it
        window.open(url, "_blank").focus();
      })
      .catch((error) => {
        // Log any errors that occur during the process
        console.log(error.message);
      });
  };

  const operations = (
    <div className="flex gap-3 justify-end items-center">
      <div className="flex">
        <EverLabel>Fiscal Year</EverLabel>
        <EverSelect
          className="w-[116px]"
          size="small"
          placeholder="Select Year"
          options={contractsYearList}
          onChange={(val) => setSelectedYear(val)}
          value={selectedYear}
          prependIcon={
            <CalendarIcon className="size-4 text-ever-base-content-mid" />
          }
        />
      </div>
      <div>
        <Dropdown
          trigger={["click"]}
          overlay={
            <Menu>
              {SORT_OPTIONS.map((option) => (
                <Menu.Item
                  key={option}
                  onClick={() => {
                    setSortOrder(option);
                    onParamsChanged(searchText, option);
                  }}
                >
                  <div
                    className={twMerge(
                      sortOrder === option
                        ? "text-ever-primary"
                        : "text-ever-base-content"
                    )}
                  >
                    {option}
                  </div>
                </Menu.Item>
              ))}
            </Menu>
          }
        >
          <EverTooltip title="Sort">
            <EverButton.Icon
              size="small"
              icon={
                <SwitchVerticalIcon className="size-4 text-ever-base-content-mid" />
              }
              color="base"
              type="ghost"
            />
          </EverTooltip>
        </Dropdown>
      </div>
      <div>
        <EverInput.Search
          placeholder="Search by"
          className="w-56"
          value={searchText}
          onChange={(e) => {
            setSearchText(e.target.value);
            onParamsChanged(e.target.value, sortOrder);
          }}
          size="small"
        />
      </div>
      <EverButtonGroup
        className="bg-ever-base-200"
        activeBtnType="text"
        activeBtnColor="primary"
        size="small"
        isSquare
        defActiveBtnIndex={showAsList ? 0 : 1}
      >
        <EverButton type="outlined" onClick={() => setShowAsList(!showAsList)}>
          <ListIcon className="h-4 w-4" />
        </EverButton>

        <EverButton type="outlined" onClick={() => setShowAsList(!showAsList)}>
          <LayoutGridIcon className="h-4 w-4" />
        </EverButton>
      </EverButtonGroup>
      <EverButton
        size="small"
        prependIcon={<AlignTopArrowIcon />}
        type="ghost"
        color="primary"
        onClick={() => setShowEmailModal(true)}
        disabled={
          (activeTab === "2" && archivedContracts.length === 0) ||
          (activeTab === "1" && activeContracts.length === 0)
        }
      >
        Export Status
      </EverButton>
      <EverTooltip
        title={
          !hasAllPermissions && enableContractPermissions && isTooltipsOpen ? (
            <p>
              You don&apos;t have necessary{" "}
              <span
                className="text-ever-base underline font-medium cursor-pointer"
                onClick={() => {
                  setOpen(true);
                  setIsTooltipsOpen(false);
                }}
              >
                DocuSign permissions
              </span>{" "}
              to create contracts.Enable them to proceed.
            </p>
          ) : (
            ""
          )
        }
      >
        <div onMouseEnter={() => setIsTooltipsOpen(true)}>
          <EverButton
            prependIcon={<PlusCircleIcon className="w-4 h-4" />}
            onClick={() => {
              clearStore(); // Reset the form data before opening the drawer
              setShowdrawer(true);
              setContractAction("Create Contract");
            }}
            disabled={!hasAllPermissions && enableContractPermissions}
            size="small"
          >
            Create Contract
          </EverButton>
        </div>
      </EverTooltip>
    </div>
  );

  /**
   * Renders an empty state view for the contracts page when no contracts exist
   *
   * This component displays:
   * - An empty contract illustration
   * - A heading indicating DocuSign setup is complete
   * - A caption explaining permissions are configured
   * - A "Create Contract" button to start creating contracts
   *
   * The view is centered vertically and horizontally on the page with some negative
   * y-axis translation for visual balance.
   *
   * @returns {JSX.Element} The empty state view component
   */
  const renderEmptyContract = () => {
    return (
      <div className="flex flex-col items-center justify-center w-full h-full -translate-y-10">
        <div className="flex flex-col items-center justify-center h-full gap-6">
          <EmptyContract className="w-48" />
          <div className="flex flex-col gap-2 items-center">
            <EverTg.Heading2 className="text-ever-base-content">
              You&apos;re all set to create contracts
            </EverTg.Heading2>
            <EverTg.Caption className="text-ever-base-content-mid">
              Your DocuSign permissions are correctly configured. You can now
              create contracts
            </EverTg.Caption>
          </div>
          <div className="flex gap-4">
            <EverButton
              prependIcon={<PlusCircleIcon className="w-4 h-4" />}
              size="small"
              onClick={() => {
                setShowdrawer(true);
                setContractAction("Create Contract");
              }}
            >
              Create Contract
            </EverButton>
          </div>
        </div>
      </div>
    );
  };

  const renderEmptyArchivedContract = () => {
    return (
      <div className="flex flex-col items-center justify-center w-full h-full -translate-y-10">
        <div className="flex flex-col items-center justify-center h-full gap-6">
          <EmptyContract className="w-48" />
          <div className="flex flex-col gap-2 items-center">
            <EverTg.Heading2 className="text-ever-base-content">
              No archived contracts
            </EverTg.Heading2>
            <EverTg.Caption className="text-ever-base-content-mid">
              Contracts you archive will appear here.
            </EverTg.Caption>
          </div>
        </div>
      </div>
    );
  };
  return (
    <>
      <EverBreadcrumbPortal dividerIcon={<></>}>
        <div className="flex ml-auto">
          <a
            target="_blank"
            href="https://www.canva.com/design/DAF-PylePa8/jfVLztK4FvjGBGLItqQ0Cw/view?utm_content=DAF-PylePa8&utm_campaign=designshare&utm_medium=link&utm_source=editor"
            rel="noreferrer"
          >
            <DocusignIcon />
          </a>
        </div>
      </EverBreadcrumbPortal>
      <CreateContractDrawer
        setShowdrawer={setShowdrawer}
        showdrawer={showdrawer}
        store={store}
        resetData={clearStore}
      />
      {loading ||
      ((isLoadingUserPermissions || isFetchingUserPermissions) &&
        !contentLoader.showLoader) ? (
        <EverLoader indicatorType="spinner" />
      ) : (
        <>
          <EverNavPortal target={navPortalLocation}>
            <div className="flex justify-between items-center">
              <EverButtonGroup
                size="small"
                className="bg-ever-base-200"
                activeBtnType="text"
                activeBtnColor="primary"
                defActiveBtnIndex={Number(activeTab) - 1}
                activeButtonClassname="!pr-1"
                inactiveButtonClassname="!pr-1"
              >
                <EverButton onClick={() => setActiveTab("1")}>
                  <div className="flex flex-row gap-2 items-center">
                    Active
                    <EverNumberBadge
                      className="bg-ever-base-300 text-ever-base-content"
                      count={
                        activeContracts.length === 0
                          ? 0
                          : activeContracts.length
                      }
                    />
                  </div>
                </EverButton>
                <EverButton onClick={() => setActiveTab("2")}>
                  <div className="flex flex-row gap-2 items-center">
                    Archived
                    <EverNumberBadge
                      className="bg-ever-base-300 text-ever-base-content"
                      count={
                        archivedContracts.length === 0
                          ? 0
                          : archivedContracts.length
                      }
                    />
                  </div>
                </EverButton>
              </EverButtonGroup>
              <div>{operations}</div>
            </div>
          </EverNavPortal>

          <EverTabs
            renderTabBar={null}
            defaultActiveKey="1"
            activeKey={activeTab}
            className="-m-2 [&>div:first-child]:!hidden"
          >
            <EverTabs.TabPane
              key="1"
              className={
                showAsList
                  ? ""
                  : twMerge(
                      "m-2 block",
                      activeContracts.length === 0 ? "justify-center" : ""
                    )
              }
            >
              {activeContracts.length === 0 ? (
                !enableContractPermissions ? (
                  <div className="flex flex-col items-center justify-center w-full h-full -translate-y-10">
                    <img src={dogTearingNewspaper} className="w-56" />
                    <EverTg.Heading2>No contracts found</EverTg.Heading2>
                  </div>
                ) : hasAllPermissions ? (
                  renderEmptyContract()
                ) : (
                  <NoPermissionView
                    userPermissions={userPermissions}
                    accountPermissions={accountPermissions}
                    hasAllPermissions={hasAllPermissions}
                    missingAccountPermissionsCount={
                      missingAccountPermissionsCount
                    }
                    missingUserPermissionsCount={missingUserPermissionsCount}
                    refetchUserPermissions={refetchUserPermissions}
                    openDocusign={openDocusign}
                    open={open}
                    setOpen={setOpen}
                  />
                )
              ) : (
                <>
                  {showAsList ? (
                    <ContractsSummaryTable
                      contracts={activeContracts}
                      archiveContract={archiveContract}
                      activateContract={activateContract}
                    />
                  ) : (
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                      {activeContracts.map((contract) => (
                        <ContractsSummaryCard
                          key={contract.id}
                          contract={contract}
                          activateContract={activateContract}
                          archiveContract={archiveContract}
                        />
                      ))}
                    </div>
                  )}
                  <PermissionDetailsModal
                    open={open}
                    setOpen={setOpen}
                    userPermissions={userPermissions}
                    accountPermissions={accountPermissions}
                    hasAllPermissions={hasAllPermissions}
                    missingUserPermissionsCount={missingUserPermissionsCount}
                    missingAccountPermissionsCount={
                      missingAccountPermissionsCount
                    }
                    refetchUserPermissions={refetchUserPermissions}
                    openDocusign={openDocusign}
                  />
                </>
              )}
            </EverTabs.TabPane>
            <EverTabs.TabPane
              key="2"
              className={
                showAsList
                  ? ""
                  : twMerge(
                      "m-2 block",
                      activeContracts.length === 0 ? "justify-center" : ""
                    )
              }
            >
              {archivedContracts.length === 0 ? (
                !enableContractPermissions ? (
                  <div className="flex flex-col items-center justify-center w-full h-full -translate-y-10">
                    <img src={dogTearingNewspaper} className="w-56" />
                    <EverTg.Heading2>No contracts found</EverTg.Heading2>
                  </div>
                ) : (
                  renderEmptyArchivedContract()
                )
              ) : (
                <>
                  {showAsList ? (
                    <ContractsSummaryTable
                      contracts={archivedContracts}
                      archiveContract={archiveContract}
                      activateContract={activateContract}
                    />
                  ) : (
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                      {archivedContracts.map((contract) => (
                        <ContractsSummaryCard
                          key={contract.id}
                          contract={contract}
                          activateContract={activateContract}
                          archiveContract={archiveContract}
                        />
                      ))}
                    </div>
                  )}
                  <PermissionDetailsModal
                    open={open}
                    setOpen={setOpen}
                    userPermissions={userPermissions}
                    accountPermissions={accountPermissions}
                    hasAllPermissions={hasAllPermissions}
                    missingUserPermissionsCount={missingUserPermissionsCount}
                    missingAccountPermissionsCount={
                      missingAccountPermissionsCount
                    }
                    refetchUserPermissions={refetchUserPermissions}
                    openDocusign={openDocusign}
                  />
                </>
              )}
            </EverTabs.TabPane>
          </EverTabs>
        </>
      )}
      <EmailConfirmationModal
        title="Email contract status report to"
        isVisible={showEmailModal}
        onCancel={() => setShowEmailModal(false)}
        onConfirm={handleExport}
      />
      <EverModal.Confirm
        title="Once the export is finished, we will share the contract status as .csv via email."
        subtitle="You can go to Settings -> Activity logs to check the progress of your export."
        confirmationButtons={[
          <EverButton
            key="okay"
            onClick={() => setShowConfirmationModal(false)}
          >
            Got it
          </EverButton>,
        ]}
        type="success"
        visible={showConfirmationModal}
      />
    </>
  );
});

export default secure(ContractManagement);
