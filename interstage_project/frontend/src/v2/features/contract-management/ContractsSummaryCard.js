import { PercentCircleIcon } from "@everstage/evericons/duotone";
import { observer } from "mobx-react";
import React from "react";
import { useTranslation } from "react-i18next";
import { Link } from "react-router-dom";

import { Ever<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Ever<PERSON>oader, EverTg } from "~/v2/components";

import ContractsActions from "./ContractsActions";

const ContractsSummaryCard = observer((props) => {
  const { contract, activateContract, archiveContract } = props;

  const { t } = useTranslation();

  return (
    <Link
      to={`/contracts/${contract.template_id}`}
      state={{ contract_name: contract.template_name }}
    >
      <EverCard
        outlined={true}
        interactive={true}
        className="relative w-full h-40 hover:from-ever-base hover:via-ever-base hover:to-ever-base rounded-xl flex"
      >
        <div className="absolute right-4 top-5 z-10">
          <ContractsActions
            contract={contract}
            archiveContract={archiveContract}
            activateContract={activateContract}
          />
        </div>
        <div className="flex flex-col gap-5 justify-between grow">
          <div className="flex flex-col gap-1.5">
            <EverTg.SubHeading3 className="text-ever-base-content w-64 block line-clamp-2">
              {contract.template_name}
            </EverTg.SubHeading3>
            <div className="flex items-center gap-1.5">
              <PercentCircleIcon className="text-ever-base-content-mid size-5" />
              <EverTg.Text
                className="text-ever-base-content-mid font-medium whitespace-nowrap text-ellipsis overflow-hidden w-72 block"
                title={t("COMMISSION_PLAN")}
              >
                {contract.plan_name ? contract.plan_name : "-"}
              </EverTg.Text>
            </div>
          </div>
          {contract.is_envelope_details_fetched ? (
            <div className="flex gap-2 mt-2">
              <EverBadge
                type="success"
                title={`Signed - ${contract.completed}`}
              ></EverBadge>
              <EverBadge
                type="warning"
                title={`Awaiting - ${contract.awaiting}`}
              ></EverBadge>
            </div>
          ) : (
            <div className="flex">
              <EverLoader.SpinnerLottie className="w-10 h-10" />
            </div>
          )}
        </div>
      </EverCard>
    </Link>
  );
});

export default ContractsSummaryCard;
