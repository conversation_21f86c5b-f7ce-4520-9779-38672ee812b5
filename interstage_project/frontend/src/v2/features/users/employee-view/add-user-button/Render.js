import { Col, Row } from "antd";
import { isEqual } from "lodash";
import { observer } from "mobx-react";
import React, { Fragment, useEffect, useState } from "react";

import { RBAC_ROLES } from "~/Enums";
import { useUserPermissionStore } from "~/GlobalStores/UserPermissionStore";
import {
  EverButton,
  EverCheckbox,
  EverForm,
  EverHotToastMessage,
  EverInput,
  EverModal,
  EverSelect,
  EverTg,
  toast,
} from "~/v2/components";

const { Text } = EverTg;

const layout = {
  labelCol: { span: 8 },
  wrapperCol: { span: 16 },
};

const Render = observer((props) => {
  const {
    isLoggedInUserEmail,
    onUserAdded,
    addEmployee,
    buttonType,
    buttonColor,
    buttonText,
    savedUserDetails,
    alluserRoles,
    allAvailableLanguages,
    buttonIcon,
    buttonClassName,
  } = props;
  const [showModal, setShowModal] = useState(false);
  const [form] = EverForm.useForm();
  const [isLoading, setIsLoading] = useState(false);
  const [addAnother, setAddAnohter] = useState(false);
  const [initialValues, setInitialValues] = useState();
  const [isDirty, setIsDirty] = useState(false);
  const isHrisUser = savedUserDetails?.userSource === "hris";

  const { hasPermissions } = useUserPermissionStore();

  const handleCancel = () => {
    setShowModal(false);
  };
  const handleAddAnother = () => {
    if (!addAnother) {
      setShowModal(false);
    }
  };

  const onFinish = async (values) => {
    setIsLoading(true);
    const dismissLoader = savedUserDetails
      ? toast.custom(() => (
          <EverHotToastMessage
            type="loading"
            description="User updation in progress.."
          />
        ))
      : toast.custom(() => (
          <EverHotToastMessage
            type="loading"
            description="User creation in progress.."
          />
        ));
    handleAddemployee(values, dismissLoader);
  };

  const handleAddemployee = (values) => {
    addEmployee(values)
      .then((response) => {
        if (response.ok) {
          toast.remove();
          successMessage();
          onUserAdded();
          onReset();
          handleAddAnother();
        } else {
          toast.remove();
          if (response.status === 403)
            toast.custom(() => (
              <EverHotToastMessage
                type="error"
                description="You don't have permission for this operation"
              />
            ));
          else
            response.json().then((data) => {
              errorMessage(data, values.email);
            });
        }
      })
      .catch((err) => console.log("CONFIG ADD ERROR" + err))
      .finally(() => {
        setIsLoading(false);
        setIsDirty(false);
      });
  };

  const successMessage = () => {
    toast.custom(() => (
      <EverHotToastMessage
        type="success"
        description={`User ${
          savedUserDetails ? "updated" : "added"
        } successfully`}
      />
    ));
  };
  const errorMessage = (msg, email) => {
    toast.custom(() => (
      <EverHotToastMessage type="error" description={`${msg} - ${email}`} />
    ));
  };

  const onFinishFailed = (errorInfo) => {
    console.log("Failed:", errorInfo);
  };

  const onReset = () => {
    form.resetFields();
  };

  function setAddAnotherValue(e) {
    console.log(`checked = ${e.target.checked}`);
    setAddAnohter(e.target.checked);
  }

  const handleChange = (e) => {
    const fvalue = e.target.value;
    form.setFieldsValue({
      email: fvalue.toLowerCase(),
    });
  };

  useEffect(() => {
    if (savedUserDetails) {
      const userLanguage = allAvailableLanguages?.find(
        (language) => language.key === savedUserDetails.preferredLanguage
      );
      form.setFieldsValue({
        email: savedUserDetails.employeeEmailId,
        firstName: savedUserDetails.firstName,
        lastName: savedUserDetails.lastName,
        userRole: savedUserDetails.userRolePermissionID[0],
        preferredLanguage: userLanguage?.key,
      });
      setInitialValues({
        email: savedUserDetails.employeeEmailId,
        firstName: savedUserDetails.firstName,
        lastName: savedUserDetails.lastName,
        userRole: savedUserDetails.userRolePermissionID[0],
        preferredLanguage: userLanguage?.key,
        sendInvite: undefined,
      });
    }
  }, [savedUserDetails]);

  return (
    <Fragment>
      <EverButton
        type={buttonType ? buttonType : "filled"}
        color={buttonColor || "primary"}
        onClick={setShowModal}
        prependIcon={buttonIcon}
        size="small"
        className={buttonClassName}
      >
        {buttonText ? <Text>{buttonText}</Text> : "Add User"}
      </EverButton>
      <EverModal
        title={savedUserDetails ? "Update User" : "Add User"}
        visible={showModal}
        onCancel={handleCancel}
        onOk={form.submit}
        footer={
          <div className="flex items-center w-full justify-between px-4">
            <div>
              <EverCheckbox
                disabled={savedUserDetails}
                onChange={setAddAnotherValue}
                label="Create another"
                checked={addAnother}
              />
            </div>
            <div className="flex gap-2">
              <EverButton
                type="text"
                color="base"
                key="back"
                onClick={handleCancel}
              >
                Cancel
              </EverButton>
              <EverButton
                type="filled"
                color="primary"
                htmlType="submit"
                form="addUserForm"
                disabled={isLoading || !isDirty}
              >
                {savedUserDetails ? "Update User" : "Add User"}
              </EverButton>
            </div>
          </div>
        }
        width="548px"
        destroyOnClose={true}
      >
        <EverForm
          id="addUserForm"
          {...layout}
          form={form}
          layout="vertical"
          name="add-user"
          initialValues={{ remember: true }}
          onFinish={onFinish}
          onKeyUp={(e) => {
            if (e.key === "Enter") {
              form.submit();
            }
          }}
          onFinishFailed={onFinishFailed}
          onValuesChange={(changedValue, allValues) => {
            if (!isEqual(initialValues, allValues)) setIsDirty(true);
            else setIsDirty(false);
          }}
          className="add-user-modal"
          wrapperCol={{ span: 24 }}
          labelCol={{ span: 12 }}
        >
          <EverForm.Item
            name="email"
            label="E-mail"
            onChange={handleChange}
            rules={[
              {
                validator: (_, value) => {
                  const emailRegex =
                    /^[A-Za-z0-9._+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$/;
                  if (value && !emailRegex.test(value)) {
                    return Promise.reject(
                      new Error("The input is not valid E-mail!")
                    );
                  }
                  return Promise.resolve();
                },
              },
              {
                required: true,
                message: "Please input user E-mail!",
              },
            ]}
          >
            <EverInput disabled={savedUserDetails} placeholder="Enter Email" />
          </EverForm.Item>

          <Row gutter={12}>
            <Col span={12}>
              <EverForm.Item
                label="First Name"
                name="firstName"
                tooltip={
                  isHrisUser
                    ? "Updates to this field are managed via integrations."
                    : null
                }
                rules={[
                  {
                    required: true,
                    message: "Please input user first name!",
                    validator: (_, value) => {
                      if (value && value.trim() !== "") {
                        return Promise.resolve();
                      }
                      return Promise.reject(
                        new Error("Please input user first name!")
                      );
                    },
                  },
                ]}
              >
                <EverInput
                  placeholder="Enter First Name"
                  disabled={isHrisUser}
                />
              </EverForm.Item>
            </Col>
            <Col span={12}>
              <EverForm.Item
                label="Last Name"
                name="lastName"
                tooltip={
                  isHrisUser
                    ? "Updates to this field are managed via integrations."
                    : null
                }
                rules={[
                  {
                    required: true,
                    message: "Please input user last name!",
                    validator: (_, value) => {
                      if (value && value.trim() !== "") {
                        return Promise.resolve();
                      }
                      return Promise.reject(
                        new Error("Please input user last name!")
                      );
                    },
                  },
                ]}
              >
                <EverInput
                  placeholder="Enter Last Name"
                  disabled={isHrisUser}
                />
              </EverForm.Item>
            </Col>
          </Row>

          <Row gutter={12}>
            <Col span={12}>
              <EverForm.Item
                label="Role"
                name="userRole"
                tooltip={
                  isHrisUser
                    ? "Updates to this field are managed via integrations."
                    : null
                }
                rules={[
                  { required: true, message: "Please select user role!" },
                ]}
              >
                <EverSelect
                  placeholder="Select Role"
                  disabled={
                    isLoggedInUserEmail ||
                    !hasPermissions(RBAC_ROLES.MANAGE_USERS) ||
                    isHrisUser
                  }
                >
                  {alluserRoles.map((item) => {
                    return (
                      <EverSelect.Option
                        key={item.rolePermissionId}
                        value={item.rolePermissionId}
                        data-testid={`pt-${item.displayName}`}
                      >
                        {item.displayName}
                      </EverSelect.Option>
                    );
                  })}
                </EverSelect>
              </EverForm.Item>
            </Col>
            {allAvailableLanguages?.length > 1 ? (
              <Col span={12}>
                <EverForm.Item
                  label="Language"
                  name="preferredLanguage"
                  rules={[
                    { required: true, message: "Please select user language!" },
                  ]}
                  tooltip="Select the language preference for the user."
                >
                  <EverSelect placeholder="Select Language">
                    {allAvailableLanguages?.map((item) => (
                      <EverSelect.Option
                        key={item.key}
                        value={item.key}
                        data-testid={`pt-${item.displayName}`}
                      >
                        {item.displayName}
                      </EverSelect.Option>
                    ))}
                  </EverSelect>
                </EverForm.Item>
              </Col>
            ) : (
              <Col span={12} className="flex items-start flex-col justify-end">
                <EverForm.Item name="sendInvite" valuePropName="checked">
                  <EverCheckbox
                    disabled={savedUserDetails}
                    label="Send activation email"
                  />
                </EverForm.Item>
              </Col>
            )}
          </Row>
          {allAvailableLanguages?.length > 1 && (
            <Row gutter={12}>
              <Col span={12} className="flex items-start flex-col justify-end">
                <EverForm.Item name="sendInvite" valuePropName="checked">
                  <EverCheckbox
                    disabled={savedUserDetails}
                    label="Send activation email"
                  />
                </EverForm.Item>
              </Col>
            </Row>
          )}
        </EverForm>
      </EverModal>
    </Fragment>
  );
});
export default Render;
