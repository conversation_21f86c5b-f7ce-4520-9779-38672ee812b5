import { GenUICanvasWithEditor } from "~/v2/compound-components/GenUICanvas";

import SampleJson from "./sample.json";

// Move arrow functions to outer scope
const handleCanvasState = (state) => {
  // Handle canvas state changes
  console.log("Canvas state:", state);
};

const handleActionClick = (action) => {
  // Handle action click
  console.log("Action clicked:", action);
};

/**
 * Agent Canvas - Main component for the /genui-canvas route
 * Renders the GenUICanvas with JSON editor and sample data
 */
const AgentCanvas = ({ canvasRenderer = false, showActions = true }) => {
  return (
    <GenUICanvasWithEditor
      jsonData={JSON.stringify(SampleJson, null, 2)}
      canvasRenderer={canvasRenderer}
      showActions={showActions}
      handleCanvasState={handleCanvasState}
      handleActionClick={handleActionClick}
    />
  );
};

export default AgentCanvas;

// Re-export other components for backward compatibility
export {
  GenUICanvas,
  GenUIWrapper,
  ShowSpecInCanvas,
  useGenUISpec,
} from "~/v2/compound-components/GenUICanvas";
