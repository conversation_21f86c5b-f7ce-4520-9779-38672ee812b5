# GenUI Canvas Demo - Dynamic UI Generation

A React-based system transforming JSON specifications into dynamic UI components, optimized for AI-driven code generation tools.

## Architecture Overview

**Consumer** of the GenUICanvas component system:

- **Main Components**:

  - Located in `~/v2/compound-components/GenUICanvas/`
  - Core files: `GenUICanvas.js`, `GenUIWrapper.js`
  - Component registry in `genui-components/`
  - Canvas rendering logic in `canvas/`
  - State management hooks in `hooks/`

- **Demo Directory**: `~/v2/features/genui-canvas-demo/`

  - Contains demo-specific files: `prompts.txt`, `sample.json`, and this README

## JSON Specification Structure

Each JSON specification clearly separates component categories, state management, and UI actions:

### General Structure

```json
{
  "elements": [
    {
      "type": "elementType", // form | cards | aggrid | textarea
      "id": "uniqueElementId",
      "title": "Element Title",
      "colSpan": 12,
      "interactive": true,
      "grid": { "columns": 3, "gap": 4 },
      "components": [
        /* component definitions here */
      ],
      "state": {
        /* initial state here */
      },
      "actions": [
        /* action definitions here */
      ],
      "actionsDisplay": "buttons"
    }
  ]
}
```

### Component Categories

#### Form

Contains form inputs (`textInput`, `selectInput`, `checkbox`, `entitySelect`):

- Components update a single aggregated form state object.

Example:

```json
{
  "type": "form",
  "id": "userForm",
  "components": [
    { "type": "textInput", "selectorKey": "name" },
    {
      "type": "entitySelect",
      "selectorKey": "role",
      "entityType": "roles",
      "multiple": false
    }
  ],
  "state": { "name": "", "role": null }
}
```

#### Cards

Multiple selectable cards sharing a single selection state:

```json
{
  "type": "cards",
  "id": "planSelector",
  "components": [
    { "type": "card", "selectorKey": "selectedPlan", "value": "basic" },
    { "type": "card", "selectorKey": "selectedPlan", "value": "pro" }
  ],
  "state": { "selectedPlan": "basic" }
}
```

#### AgGrid

Exactly one interactive data grid per element:

```json
{
  "type": "aggrid",
  "id": "userDataTable",
  "components": [
    {
      "type": "aggrid",
      "selectorKey": "selectedRows",
      "rowData": [{ "id": 1, "name": "John" }],
      "columnDefs": [{ "field": "id" }, { "field": "name" }]
    }
  ],
  "state": { "selectedRows": [] }
}
```

#### Textarea

Single multiline text component with an object-based state:

```json
{
  "type": "textarea",
  "id": "contentEditor",
  "components": [
    {
      "type": "textarea",
      "selectorKey": "content",
      "rows": 6,
      "placeholder": "Enter content here..."
    }
  ],
  "state": { "content": "Initial content" }
}
```

## State Management

- **Forms**: Inputs aggregated into a single object.
- **Cards/Grids**: Shared selection key.
- **Textareas**: Object-based state management.

## Actions

Actions defined per element to operate on entire element state:

```json
"actions": [
  { "id": "submit", "label": "Submit", "prompt": "Submit the form data" }
]
```

## Component Registry

Components are mapped in `genui-components/index.js`:

```javascript
const componentRegistry = {
  textInput: TextInputComponent,
  selectInput: SelectInputComponent,
  checkbox: CheckboxComponent,
  card: CardComponent,
  aggrid: AgGridComponent,
  textarea: TextareaComponent,
  entitySelect: EntitySelectComponent,
};
```

## AI Integration

- Translates natural language into structured JSON using defined prompts (`prompts.txt`).
- Generates UI dynamically based on JSON specifications.

## Mandatory Entity Selection

- Always use `EntitySelect` for entity types: users, dashboards, roles, commission_plans, crystal_views, etc.
- Centralized management ensures consistency and maintainability.

## Implementation Requirements

- **Read-Only Mode**:

  - Set `interactive: false` at the element level for view-only mode.

- **Not Allowed**:

  - Mixing component types within a single element (e.g., form inputs with cards).
  - Multiple aggrid or textarea components within one element.
  - Cards with different `selectorKey` values.

- **Layout System**:

  - Uses a 12-column CSS grid.
  - Element-level `colSpan` defines width.
  - Internal layouts specified by `grid.columns` and `grid.gap`.

## Adding New Element Types

To add new element types:

1. Create the React component in `genui-components/`.
2. Register it in `genui-components/index.js`.
3. Update renderer logic in `canvas/`.
4. Define schema in `prompts.txt`.
5. Document examples in `sample.json`.
