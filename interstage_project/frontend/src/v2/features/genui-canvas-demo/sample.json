{"elements": [{"type": "textarea", "id": "descriptionEditor", "title": "Description Editor", "colSpan": 12, "interactive": true, "grid": {"columns": 1, "gap": 4}, "components": [{"id": "mainDescription", "type": "textarea", "interactive": true, "selectorKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "placeholder": "Enter your description here...", "rows": 6}], "state": {"textareaContent": "This is a sample description with mentions like @usr:<PERSON> and @cp:Q4_Sales_Plan. Also testing @db:Revenue_Dashboard and @cv:Crystal_View_Report."}, "actionsDisplay": "buttons", "actions": [{"id": "saveContent", "label": "Save", "prompt": "Save the content"}, {"id": "clearContent", "label": "Clear", "prompt": "Clear the content"}]}, {"type": "cards", "id": "readOnlyCards", "title": "Read-Only Subscription Plans", "colSpan": 12, "interactive": false, "grid": {"columns": 3, "gap": 4}, "selection": {"mode": "single", "control": "radio"}, "components": [{"id": "basic-plan", "type": "card", "interactive": true, "selectorKey": "<PERSON><PERSON><PERSON>", "header": "Basic Plan", "body": "For individuals and small teams", "footer": "$9.99/month", "value": "basic"}, {"id": "pro-plan", "type": "card", "interactive": true, "selectorKey": "<PERSON><PERSON><PERSON>", "header": "Pro Plan", "body": "For growing businesses", "footer": "$29.99/month", "value": "pro"}, {"id": "enterprise-plan", "type": "card", "interactive": true, "selectorKey": "<PERSON><PERSON><PERSON>", "header": "Enterprise Plan", "body": "For large organizations", "footer": "$99.99/month", "value": "enterprise"}], "state": {"selectedPlan": "pro"}}, {"type": "form", "id": "entitySelectForm", "title": "Entity Selection Form", "colSpan": 12, "interactive": true, "grid": {"columns": 2, "gap": 4}, "components": [{"id": "userSelect", "type": "entitySelect", "interactive": true, "selectorKey": "selected<PERSON>ser", "colSpan": 1, "label": "Select User", "entityType": "users", "multiple": true, "placeholder": "Search and select a user..."}, {"id": "commissionPlanSelect", "type": "entitySelect", "interactive": true, "selectorKey": "selectedCommissionPlan", "colSpan": 1, "label": "Commission Plan", "entityType": "commission_plans", "multiple": false, "placeholder": "Select a commission plan..."}, {"id": "dashboardSelect", "type": "entitySelect", "interactive": true, "selectorKey": "selectedDashboard", "colSpan": 1, "label": "Dashboard", "entityType": "dashboards", "multiple": false, "placeholder": "Pick a dashboard..."}, {"id": "rolesSelect", "type": "entitySelect", "interactive": true, "selectorKey": "selectedRoles", "colSpan": 1, "label": "User Roles", "entityType": "roles", "multiple": false, "placeholder": "Select a role..."}, {"id": "crystalViewsSelect", "type": "entitySelect", "interactive": true, "selectorKey": "selectedCrystalViews", "colSpan": 2, "label": "Crystal Views", "entityType": "crystal_views", "multiple": true, "placeholder": "Select crystal views..."}], "state": {"selectedUser": null, "selectedCommissionPlan": null, "selectedDashboard": null, "selectedRoles": null, "selectedCrystalViews": []}, "actions": [{"id": "assignEntities", "label": "Assign Selected", "prompt": "Assign the selected entities to the user"}, {"id": "saveConfig", "label": "Save Configuration", "prompt": "Save the entity configuration"}]}, {"type": "aggrid", "id": "readOnlyTable", "title": "Read-Only Sales Data", "colSpan": 12, "interactive": false, "selection": {"mode": "multiple", "control": "checkbox"}, "grid": {"columns": 1, "gap": 4}, "components": [{"id": "salesReadOnlyGrid", "type": "aggrid", "interactive": true, "selectorKey": "selectedRows", "rowData": [{"id": 1, "product": "Product A", "revenue": 12500, "quantity": 250}, {"id": 2, "product": "Product B", "revenue": 8750, "quantity": 175}, {"id": 3, "product": "Product C", "revenue": 6300, "quantity": 120}], "columnDefs": [{"field": "id", "headerName": "ID", "width": 70, "checkboxSelection": true}, {"field": "product", "headerName": "Product"}, {"field": "revenue", "headerName": "Revenue ($)"}, {"field": "quantity", "headerName": "Quantity"}], "gridSize": "lg", "pagination": true}], "state": {"selectedRows": [1, 3]}}, {"type": "form", "id": "readOnlyForm", "title": "Read-Only User Profile", "colSpan": 6, "interactive": false, "grid": {"columns": 2, "gap": 12}, "components": [{"id": "nameInput", "type": "textInput", "interactive": true, "selectorKey": "name", "colSpan": 1, "label": "Full Name"}, {"id": "emailInput", "type": "textInput", "interactive": true, "selectorKey": "email", "colSpan": 1, "label": "Email Address"}, {"id": "roleSelect", "type": "selectInput", "interactive": true, "selectorKey": "role", "colSpan": 1, "label": "Role", "options": [{"value": "admin", "label": "Administrator"}, {"value": "user", "label": "Regular User"}, {"value": "guest", "label": "Guest"}]}, {"id": "activeCheckbox", "type": "checkbox", "interactive": true, "selectorKey": "isActive", "colSpan": 1, "label": "Active Account"}], "state": {"name": "<PERSON>", "email": "<EMAIL>", "role": "admin", "isActive": true}}, {"type": "form", "id": "customerOnboardingForm", "title": "Customer Onboarding", "colSpan": 6, "interactive": true, "grid": {"columns": 3, "gap": 12}, "components": [{"id": "firstNameInput", "type": "textInput", "interactive": true, "selectorKey": "firstName", "colSpan": 1, "label": "First Name", "placeholder": "<PERSON>"}, {"id": "countrySelect", "type": "selectInput", "interactive": true, "selectorKey": "country", "colSpan": 2, "label": "Country", "mode": "multiple", "options": [{"value": "in", "label": "India"}, {"value": "us", "label": "United States"}]}, {"id": "termsCheckbox", "type": "checkbox", "interactive": true, "selectorKey": "termsAccepted", "colSpan": 3, "label": "I accept terms & conditions"}], "state": {"firstName": "rag<PERSON>", "country": "in", "termsAccepted": false}, "actionsDisplay": "buttons", "actions": [{"id": "submitForm", "label": "Submit Form", "prompt": "Submit the onboarding form with the provided data"}, {"id": "saveDraft", "label": "Save Draft", "prompt": "Save the form as a draft for later completion"}]}, {"type": "cards", "id": "productCards", "title": "Product Selection", "colSpan": 6, "interactive": true, "grid": {"columns": 2, "gap": 12}, "selection": {"mode": "single", "control": "radio"}, "components": [{"id": "product-1", "type": "card", "interactive": true, "selectorKey": "productId", "header": "Basic Plan", "body": "Our entry-level plan with essential features.", "footer": "$9.99/month", "value": "basic"}, {"id": "product-2", "type": "card", "interactive": true, "selectorKey": "productId", "header": "Pro Plan", "body": "Advanced features for professional users.", "footer": "$19.99/month", "value": "pro"}, {"id": "product-3", "type": "card", "interactive": true, "selectorKey": "productId", "header": "Enterprise Plan", "body": "Full-featured solution for large organizations.", "footer": "$49.99/month", "value": "enterprise"}, {"id": "product-4", "type": "card", "interactive": true, "selectorKey": "productId", "header": "Custom Plan", "body": "Tailored solution for specific needs.", "footer": "Contact sales", "value": "custom"}], "state": {"productId": "pro"}, "actionsDisplay": "buttons", "actions": [{"id": "selectProduct", "label": "Select Plan", "prompt": "Select the chosen product plan"}, {"id": "comparePlans", "label": "Compare Plans", "prompt": "Show a detailed comparison of all plans"}]}, {"type": "aggrid", "id": "salesTable", "title": "Sales Data", "colSpan": 12, "interactive": true, "selection": {"mode": "multiple", "control": "checkbox"}, "grid": {"columns": 1, "gap": 4}, "components": [{"id": "salesGrid", "type": "aggrid", "interactive": true, "selectorKey": "selectedRows", "rowData": [{"id": 1, "product": "Widget A", "revenue": 5000, "quantity": 100}, {"id": 2, "product": "Widget B", "revenue": 3500, "quantity": 75}, {"id": 3, "product": "Widget C", "revenue": 2800, "quantity": 50}], "columnDefs": [{"field": "id", "headerName": "ID", "width": 70, "checkboxSelection": true}, {"field": "product", "headerName": "Product"}, {"field": "revenue", "headerName": "Revenue ($)"}, {"field": "quantity", "headerName": "Quantity"}], "gridSize": "lg", "pagination": true}], "state": {"selectedRows": [1, 2]}, "actionsDisplay": "buttons", "actions": [{"id": "exportData", "label": "Export to CSV", "prompt": "Export the selected sales data to CSV format"}, {"id": "viewDetails", "label": "View Row Details", "prompt": "Show detailed information for the selected row"}]}]}