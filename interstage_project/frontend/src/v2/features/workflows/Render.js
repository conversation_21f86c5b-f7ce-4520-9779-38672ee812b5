import { PlusSquareIcon } from "@everstage/evericons/outlined";
import { observer } from "mobx-react";
import React, { useEffect, useState } from "react";
import { useLocation } from "react-router-dom";
import { useRecoilValue } from "recoil";

import { navPortalAtom } from "~/GlobalStores/atoms";
import { EverButton, EverInput, EverTg, EverTabs } from "~/v2/components";
import EverNavPortal from "~/v2/components/EverNavPortal";

import { Settings } from "./Settings";
import { ApprovalTemplates } from "./templates";

const Render = observer(() => {
  const navPortalLocation = useRecoilValue(navPortalAtom);
  const { state } = useLocation();

  const [searchText, setSearchText] = useState(null);
  const [activeKey, setActiveKey] = useState("1");
  const [showApprovalTemplateBuilder, setShowApprovalTemplateBuilder] =
    useState(false);
  const [showCreateWorkflowBtn, setShowCreateWorkflowBtn] = useState(true);

  useEffect(() => {
    if (state) {
      state === "settings" && setActiveKey("2");
    }
  }, []);

  return (
    <div className="w-full h-full">
      <EverNavPortal target={navPortalLocation}></EverNavPortal>
      <EverTabs
        className="tese"
        activeKey={activeKey}
        onChange={(newActiveKey) => {
          setActiveKey(newActiveKey);
        }}
      >
        <EverTabs.TabPane
          tab={<EverTg.SubHeading4>Templates</EverTg.SubHeading4>}
          key="1"
        >
          <div className="flex gap-4 pt-6 justify-between mx-2">
            <EverInput.Search
              className="w-64"
              allowClear
              placeholder="Search by name"
              onChange={(e) => setSearchText(e.target.value)}
              value={searchText}
              size="small"
            />
            {showCreateWorkflowBtn && (
              <EverButton
                onClick={() => setShowApprovalTemplateBuilder(true)}
                prependIcon={<PlusSquareIcon className="w-4 h-4" />}
                size="small"
              >
                Create Workflow
              </EverButton>
            )}
          </div>

          <div className="pt-6">
            <ApprovalTemplates
              setShowApprovalTemplateBuilder={setShowApprovalTemplateBuilder}
              showApprovalTemplateBuilder={showApprovalTemplateBuilder}
              searchText={searchText}
              setShowCreateWorkflowBtn={setShowCreateWorkflowBtn}
            />
          </div>
        </EverTabs.TabPane>
        <EverTabs.TabPane
          tab={<EverTg.SubHeading4>Settings</EverTg.SubHeading4>}
          key="2"
        >
          <div className="pt-8 h-full">
            <Settings />
          </div>
        </EverTabs.TabPane>
      </EverTabs>
    </div>
  );
});

export default Render;
