import { gql, useQuery } from "@apollo/client";
import { TrashLottie } from "@everstage/evericons/lotties";
import {
  DotsHorizontalIcon,
  EditPencilIcon,
  InfoCircleIcon,
  PlusSquareIcon,
} from "@everstage/evericons/outlined";
import { Dropdown, Empty } from "antd";
import React, { useState, useMemo, useEffect } from "react";
import { useTranslation } from "react-i18next";

import {
  deleteApprovalTemplate,
  cloneApprovalTemplate,
} from "~/Api/ApprovalWorkflowService";
import { useAuthStore } from "~/GlobalStores/AuthStore";
import {
  EverButton,
  EverCard,
  EverListItem,
  EverLoader,
  EverModal,
  EverTg,
  message,
} from "~/v2/components";
import { ApprovalWorkFlowTemplateBuilder } from "~/v2/features/approvals";
import { lostAstronaut } from "~/v2/images";

const GET_EXISTING_TEMPLATES = gql`
  query AllApprovalTemplates($excludeInactive: Boolean!) {
    allApprovalTemplates(excludeInactive: $excludeInactive) {
      templateId
      templateName
      templateDescription
      knowledgeBeginDate
      entityType
    }
  }
`;

export const ApprovalTemplates = (props) => {
  const {
    showApprovalTemplateBuilder,
    setShowApprovalTemplateBuilder,
    searchText,
    setShowCreateWorkflowBtn,
  } = props;

  const { t } = useTranslation();
  const [editTemplateId, setEditTemplateId] = useState(null);
  const {
    loading: fetchTemplatesLoading,
    data,
    refetch: existingTemplatesRefetch,
  } = useQuery(GET_EXISTING_TEMPLATES, {
    variables: { excludeInactive: true },
    fetchPolicy: "no-cache",
  });

  const filteredTemplates = useMemo(() => {
    if (!searchText || searchText.length < 3) return data?.allApprovalTemplates;
    return (
      data?.allApprovalTemplates &&
      data?.allApprovalTemplates.filter((templateMetaData) =>
        templateMetaData?.templateName
          ? templateMetaData?.templateName
              .toLowerCase()
              .includes(searchText.toLowerCase())
          : false
      )
    );
  }, [searchText, data]);

  useEffect(() => {
    // If there are no templates and no search text, hide the create workflow button on the right top of the page
    if (filteredTemplates?.length == 0 && !searchText) {
      setShowCreateWorkflowBtn(false);
    } else {
      setShowCreateWorkflowBtn(true);
    }
  }, [filteredTemplates, searchText]);

  function getEntityTypeText(type) {
    switch (type) {
      case "all": {
        return "All";
      }
      case "payout": {
        return t("PAYOUTS");
      }
      case "commission_adjustment": {
        return `${t("COMMISSION_ADJUSTMENTS")}`;
      }
      case "datasheet_adjustment": {
        return "Datasheet Adjustments";
      }
      case "commission_plan": {
        return `${t("COMMISSION_PLAN")}`;
      }
      default: {
        return t("PAYOUTS");
      }
    }
  }

  return (
    <>
      {!fetchTemplatesLoading ? (
        filteredTemplates?.length > 0 ? (
          filteredTemplates.map((template) => {
            return (
              <div
                key={template.templateId}
                className="flex flex-col gap-3 mx-2 mb-3"
              >
                <ApprovalTemplateCardView
                  id={template.templateId}
                  name={template.templateName}
                  description={template.templateDescription}
                  lastModified={template?.knowledgeBeginDate}
                  existingTemplatesRefetch={existingTemplatesRefetch}
                  setEditTemplateId={setEditTemplateId}
                  setShowApproval={setShowApprovalTemplateBuilder}
                  entityType={getEntityTypeText(template.entityType)}
                />
              </div>
            );
          })
        ) : searchText ? (
          <Empty />
        ) : (
          <EmptyApprovalTemplates
            setShowApproval={setShowApprovalTemplateBuilder}
          />
        )
      ) : (
        <EverLoader indicatorType="spinner" />
      )}
      {showApprovalTemplateBuilder && (
        <ApprovalWorkFlowTemplateBuilder
          showApproval={showApprovalTemplateBuilder}
          setShowApproval={setShowApprovalTemplateBuilder}
          mode={editTemplateId ? "edit" : "create"}
          save="yes"
          title={editTemplateId ? "Edit Workflow" : "New Workflow"}
          type="template"
          existingTemplatesRefetch={existingTemplatesRefetch}
          editTemplateId={editTemplateId}
          setEditTemplateId={(value) => {
            setEditTemplateId(value);
          }}
          onSaveCloseBuilder={true}
          showBackToWorkflows={false}
        />
      )}
    </>
  );
};

const ApprovalTemplateCardView = (props) => {
  const {
    id,
    name,
    description,
    lastModified,
    existingTemplatesRefetch,
    setEditTemplateId,
    setShowApproval,
    entityType,
  } = props;
  const [showDeleteConfirmModal, setShowDeleteConfirmModal] = useState(false);
  const { accessToken } = useAuthStore();

  const onClone = () => {
    return new Promise((settled) => {
      cloneApprovalTemplate({ template_id: id }, accessToken)
        .then((response) => {
          if (response.ok) {
            response.json().then((data) => {
              message.success({ content: data.message });
            });
            existingTemplatesRefetch();
          } else {
            response.json().then((data) => {
              if (data?.message) {
                message.error({ content: data?.message });
              } else {
                message.error({ content: "Workflow deletion failed." });
              }
            });
          }
        })
        .finally(() => {
          settled();
        });
    });
  };

  const getMenu = () => {
    return (
      <EverCard paddingSize="sm">
        <div key="clone" onClick={onClone}>
          <EverListItem selectable={true} title="Clone" />
        </div>
        <div key="delete" onClick={() => setShowDeleteConfirmModal(true)}>
          <EverListItem
            className="hover:bg-ever-error-lite hover:!text-ever-error"
            selectable={true}
            title="Delete"
          />
        </div>
      </EverCard>
    );
  };
  return (
    <>
      <EverCard>
        <div className="w-full flex items-center gap-3">
          <div className="w-1/2 flex flex-col gap-1">
            <div className="flex gap-3">
              <EverTg.Heading3>{name}</EverTg.Heading3>
              <div className="px-2 bg-ever-info-lite rounded-2xl">
                <EverTg.Caption.Medium className="text-ever-info-lite-content">
                  {entityType}
                </EverTg.Caption.Medium>
              </div>
            </div>
            <EverTg.Description>
              {/* {description ? description : "-"} */}
              {description}
            </EverTg.Description>
          </div>
          <div className="ml-auto flex items-center justify-end">
            <EverTg.Caption className="flex flex-nowrap items-center justify-end mr-8 text-ever-base-content-mid">
              <InfoCircleIcon className="w-4 h-4 mr-1" />
              Last modified: {lastModified}
            </EverTg.Caption>
            <EverButton
              onClick={() => {
                setEditTemplateId(id);
                setShowApproval(true);
              }}
              type="ghost"
              color="base"
              size="small"
              prependIcon={<EditPencilIcon className="w-4 h-4" />}
            >
              Edit
            </EverButton>
            <Dropdown
              trigger={["click"]}
              overlay={getMenu()}
              placement="bottomLeft"
            >
              <EverButton.Icon
                icon={<DotsHorizontalIcon />}
                size="small"
                color="base"
                type="text"
                className="hover:bg-ever-base-200 ml-1"
              />
            </Dropdown>
          </div>
        </div>
      </EverCard>
      <DeleteTemplateConfirm
        id={id}
        showModal={showDeleteConfirmModal}
        setShowModal={setShowDeleteConfirmModal}
        existingTemplatesRefetch={existingTemplatesRefetch}
      />
    </>
  );
};

const EmptyApprovalTemplates = (props) => {
  const { setShowApproval } = props;
  return (
    <div className="flex flex-col items-center justify-center text-center gap-6 h-full">
      <img src={lostAstronaut} className="w-72 rounded-lg" />
      <div className="flex flex-col gap-3">
        <EverTg.Heading1>Create your first workflow</EverTg.Heading1>
        <EverTg.Description>
          Build, standardize, and automate your approval process at scale with
          Workflows.
        </EverTg.Description>
      </div>
      <EverButton
        size="small"
        onClick={() => setShowApproval(true)}
        prependIcon={<PlusSquareIcon className="w-4 h-4" />}
      >
        Create Workflow
      </EverButton>
    </div>
  );
};

const DeleteTemplateConfirm = (props) => {
  const { showModal, setShowModal, existingTemplatesRefetch, id } = props;
  const { accessToken } = useAuthStore();

  const onDelete = () => {
    return new Promise((settled) => {
      deleteApprovalTemplate({ template_id: id }, accessToken)
        .then((response) => {
          if (response.ok) {
            response.json().then((data) => {
              message.success({ content: data.message });
            });
            existingTemplatesRefetch();
          } else {
            response.json().then((data) => {
              if (data?.message) {
                message.error({ content: data?.message });
              } else {
                message.error({ content: "Workflow deletion failed." });
              }
            });
          }
        })
        .finally(() => {
          setShowModal(false);
          settled();
        });
    });
  };

  return (
    <EverModal.Confirm
      destroyOnClose={true}
      title="Are you sure you want to delete this workflow?"
      subtitle="You can't undo this action"
      visible={showModal}
      confirmationButtons={[
        <EverButton
          key="cancel"
          color="base"
          onClick={() => setShowModal(false)}
        >
          Cancel
        </EverButton>,
        <EverButton key="submit" onClick={onDelete}>
          Yes, Confirm
        </EverButton>,
      ]}
      icon={
        <TrashLottie
          autoplay
          className="h-10 w-10 text-ever-error-content -translate-y-1"
          loop
        />
      }
      iconContainerClasses="border-ever-error border-solid border bg-transparent"
    />
  );
};
