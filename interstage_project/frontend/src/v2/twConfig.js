const grid24 = {
  "1/24": "4.166666%",
  "2/24": "8.333333%",
  "3/24": "12.5%",
  "4/24": "16.666666%",
  "5/24": "20.833333%",
  "6/24": "25%",
  "7/24": "29.166666%",
  "8/24": "33.333333%",
  "9/24": "37.5%",
  "10/24": "41.666666%",
  "11/24": "45.833333%",
  "12/24": "50%",
  "13/24": "54.166666%",
  "14/24": "58.333333%",
  "15/24": "62.5%",
  "16/24": "66.666666%",
  "17/24": "70.833333%",
  "18/24": "75%",
  "19/24": "79.166666%",
  "20/24": "83.333333%",
  "21/24": "87.5%",
  "22/24": "91.666666%",
  "23/24": "95.833333%",
  "24/24": "100%",
};

export const config = {
  corePlugins: {
    preflight: false,
  },
  content: ["./src/v2/**/*.{js,jsx,ts,tsx}", "./public/index.html"], // scan the content of only the v2 folder
  theme: {
    letterSpacing: {
      tightest: "-.075em",
      tighter: "-.05em",
      tight: "-.025em",
      normal: "-0.01em",
      wide: ".025em",
      wider: ".05em",
      widest: ".1em",
    },
    fontSize: {
      xs: ["12px", "20px"],
      sm: ["14px", "20px"],
      base: ["14px", "20px"],
      lg: ["16px", "24px"],
      xl: ["18px", "26px"],
      "2xl": ["20px", "28px"],
      "3xl": ["24px", "32px"],
      "4xl": ["30px", "36px"],
      "5xl": ["36px", "40px"],
    },
    backdropBlur: {
      none: "none",
      sm: "2px",
      DEFAULT: "4px",
      md: "6px",
      lg: "8px",
      xl: "12px",
    },
    borderRadius: {
      none: "0",
      xs: "2px",
      sm: "4px",
      md: "6px",
      DEFAULT: "6px",
      lg: "8px",
      xl: "12px",
      "2xl": "16px",
      "3xl": "24px",
      full: "9999px",
    },
    extend: {
      width: grid24,
      left: grid24,
      right: grid24,
      margin: grid24,
      padding: grid24,
      flexBasis: grid24,
      order: {
        13: "13",
        14: "14",
        15: "15",
        16: "16",
        17: "17",
        18: "18",
        19: "19",
        20: "20",
        21: "21",
        22: "22",
        23: "23",
        24: "24",
      },
      scale: {
        101: "1.01",
      },
      keyframes: {
        wiggle: {
          "0%, 100%": { transform: "rotate(-3deg)" },
          "50%": { transform: "rotate(3deg)" },
        },
        rotateY: {
          "0%": { transform: "rotateY(360deg)" },
          "100%": { transform: "rotateY(0)" },
        },
        rotateX: {
          "0%": { transform: "rotateX(360deg)" },
          "100%": { transform: "rotateX(0)" },
        },
        jump: {
          "0%,100%": { transform: "scale(1)" },
          "10%": {
            transform: "scale(.8)",
          },
          "50%": {
            transform: "scale(1.2)",
          },
        },
        shake: {
          "0%": {
            transform: "translate(0)",
          },
          "25%": {
            transform: "translate(-1rem)",
          },
          "75%": {
            transform: "translate(1rem)",
          },
          "100%": {
            transform: "translate(0)",
          },
        },
        fadeIn: {
          "0%": { opacity: 0, transform: "translateY(1rem)" },
          "100%": { opacity: 1, transform: "translateY(0)" },
        },
        ringGrow: {
          "0%": { strokeDashoffset: "157" },
          "100%": { strokeDashoffset: "0" },
        },
      },
      animation: {
        wiggle: "wiggle 0.3s ease-in-out infinite",
        rotateY: "rotateY 0.3s ease-in-out infinite both",
        rotateX: "rotateX 0.3s ease-in-out infinite both",
        jump: "jump 0.5s ease-in-out infinite both",
        shake: "shake 0.5s ease-in-out infinite both",
        fadeIn: "fadeIn 0.5s ease-out forwards",
        ringGrow: "ringGrow 0.5s linear forwards",
      },
      screens: {
        xl2: "1440px",
      },
      backgroundImage: {
        publish:
          "linear-gradient(74deg, rgba(216, 250, 255, 0.80) 2.84%, rgba(234, 252, 254, 0.60) 50.28%, rgba(226, 252, 255, 0.80) 97.71%)",
        approvalError: "linear-gradient(90deg, #FFF2F1 15%, #FFF 100%)",
        approvalWarning: "linear-gradient(90deg, #FFF1DB 15%, #FFF 100%)",
        adjustmentDangling:
          "linear-gradient(90deg, #FEF2F2 30.75%, #FFF 162.95%)",
        adjustmentDanglingBorder:
          "linear-gradient(90deg, #FEF2F2 30.75%, #FFF 162.95%)",
      },
    },
  },
  variants: {
    extend: {},
  },
};
