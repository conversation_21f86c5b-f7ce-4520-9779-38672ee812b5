import {
  safeGetLocalStorage,
  safeSetLocalStorage,
  safeRemoveLocalStorage,
  isLocalStorageAvailable,
  LOCAL_STORAGE_KEYS,
} from "./localStorageUtils";

// Mock localStorage
const mockLocalStorage = (() => {
  let store = {};
  return {
    getItem: jest.fn((key) => store[key] || null),
    setItem: jest.fn((key, value) => {
      store[key] = value.toString();
    }),
    removeItem: jest.fn((key) => {
      delete store[key];
    }),
    clear: jest.fn(() => {
      store = {};
    }),
  };
})();

// Mock console.warn to avoid noise in tests
const originalWarn = console.warn;
beforeEach(() => {
  console.warn = jest.fn();
  mockLocalStorage.clear();
  Object.defineProperty(window, "localStorage", {
    value: mockLocalStorage,
    writable: true,
  });
});

afterEach(() => {
  console.warn = originalWarn;
  jest.clearAllMocks();
});

describe("safeGetLocalStorage", () => {
  it("should return parsed value when localStorage works", () => {
    const testData = { test: "value" };
    mockLocalStorage.setItem("testKey", JSON.stringify(testData));

    const result = safeGetLocalStorage("testKey");
    expect(result).toEqual(testData);
  });

  it("should return default value when key doesn't exist", () => {
    const defaultValue = { default: true };
    const result = safeGetLocalStorage("nonExistentKey", defaultValue);
    expect(result).toEqual(defaultValue);
  });

  it("should return default value when JSON parsing fails", () => {
    mockLocalStorage.setItem("invalidJson", "invalid json");
    const defaultValue = [];

    const result = safeGetLocalStorage("invalidJson", defaultValue);
    expect(result).toEqual(defaultValue);
    expect(console.warn).toHaveBeenCalled();
  });

  it("should handle localStorage.getItem throwing error", () => {
    mockLocalStorage.getItem.mockImplementation(() => {
      throw new Error("localStorage not available");
    });

    const defaultValue = "fallback";
    const result = safeGetLocalStorage("testKey", defaultValue);
    expect(result).toBe(defaultValue);
    expect(console.warn).toHaveBeenCalled();
  });
});

describe("safeSetLocalStorage", () => {
  it("should return true when localStorage works", () => {
    const testData = { test: "value" };
    const result = safeSetLocalStorage("testKey", testData);

    expect(result).toBe(true);
    expect(mockLocalStorage.setItem).toHaveBeenCalledWith(
      "testKey",
      JSON.stringify(testData)
    );
  });

  it("should return false when localStorage.setItem throws error", () => {
    mockLocalStorage.setItem.mockImplementation(() => {
      throw new Error("Quota exceeded");
    });

    const result = safeSetLocalStorage("testKey", { test: "value" });
    expect(result).toBe(false);
    expect(console.warn).toHaveBeenCalled();
  });
});

describe("safeRemoveLocalStorage", () => {
  it("should return true when localStorage works", () => {
    const result = safeRemoveLocalStorage("testKey");

    expect(result).toBe(true);
    expect(mockLocalStorage.removeItem).toHaveBeenCalledWith("testKey");
  });

  it("should return false when localStorage.removeItem throws error", () => {
    mockLocalStorage.removeItem.mockImplementation(() => {
      throw new Error("localStorage not available");
    });

    const result = safeRemoveLocalStorage("testKey");
    expect(result).toBe(false);
    expect(console.warn).toHaveBeenCalled();
  });
});

describe("isLocalStorageAvailable", () => {
  it("should return true when localStorage is available", () => {
    const result = isLocalStorageAvailable();
    expect(result).toBe(true);
  });

  it("should return false when localStorage throws error", () => {
    mockLocalStorage.setItem.mockImplementation(() => {
      throw new Error("localStorage not available");
    });

    const result = isLocalStorageAvailable();
    expect(result).toBe(false);
  });
});

describe("LOCAL_STORAGE_KEYS", () => {
  it("should contain expected keys", () => {
    expect(LOCAL_STORAGE_KEYS.RECENT_APPROVAL_WORKFLOW_IDS).toBe(
      "recent_approval_workflow_ids"
    );
  });
});
