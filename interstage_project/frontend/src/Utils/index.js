import { isValidElement } from "react";
import { EVERSTAGE_GENERATED_COLUMNS } from "~/Enums";
import { preprocessTemplateName, removeNonAlphanumeric } from "./stringUtils";

export const safeEquals = (obj, property, value) => {
  if (obj && obj[property] && obj[property] === value) {
    return true;
  }
  return false;
};

export const safeIn = (obj, property, values) => {
  if (obj && values && obj[property] && values.includes(obj[property])) {
    return true;
  }
  return false;
};

export const getRedirectUrl = () => {
  return window.location.origin + "/";
};

export const getLogoutRedirectUrl = () => {
  return window.location.origin + "/login";
};

export const generateTableFilterOptions = (tableData, keys, customOnFilter) => {
  // keys can be String or Array of Strings
  const isArrayKeys = Array.isArray(keys);
  const filteredKeys = [];
  const validateValue = (value) => {
    if (value !== "-" && value) {
      filteredKeys.push(value);
    }
  };
  for (const data of tableData) {
    if (isArrayKeys) {
      for (const keyValue of keys) validateValue(data[keyValue]);
    } else {
      validateValue(data[keys]);
    }
  }
  const uniqueKeys = [...new Set(filteredKeys)];
  const handleOnFilterChange = (value, record) => {
    if (customOnFilter) {
      return customOnFilter(value, record);
    } else {
      if (isArrayKeys) {
        const keyIndex = keys.findIndex(
          (key) => record[key] && record[key].indexOf(value) === 0
        );
        return keyIndex >= 0;
      } else {
        return record[keys] && record[keys].indexOf(value) === 0;
      }
    }
  };
  return {
    filters: uniqueKeys.map((data) => ({ value: data, text: data })),
    onFilter: handleOnFilterChange,
  };
};

export const generateTableSortOptions = (key, isNumeric = false) => {
  const sort = (a, b) => {
    if (typeof a[key] === "string") {
      return a[key].localeCompare(b[key], undefined, { numeric: isNumeric });
    }
    return a[key] - b[key];
  };
  return {
    sorter: sort,
  };
};

export const getEverstageGeneratedKeys = () =>
  Object.values(EVERSTAGE_GENERATED_COLUMNS).map((column) => column.systemName);

export const getNonEverstageGeneratedColumn = (criteriaColumns) =>
  criteriaColumns.filter(
    (criteriaColumn) => !getEverstageGeneratedKeys().includes(criteriaColumn)
  );

export function convertKeysToCamelCase(input) {
  if (Array.isArray(input)) {
    // If it's an array, map through each element and recursively call the function.
    return input.map((item) => convertKeysToCamelCase(item));
  } else if (typeof input === "object" && input !== null) {
    // If it's an object, create a new object with camelCase keys.
    const result = {};
    for (const key in input) {
      if (Object.prototype.hasOwnProperty.call(input, key)) {
        const camelCaseKey = key.replace(/_([a-z])/g, (match, letter) =>
          letter.toUpperCase()
        );
        result[camelCaseKey] = convertKeysToCamelCase(input[key]);
      }
    }
    return result;
  } else {
    // Base case: return the value unchanged for non-object values.
    return input;
  }
}

export const objectToPath = (object) => {
  const findPath = (object, path = "") => {
    if (!object || typeof object !== "object") return path;

    return Object.keys(object).map((key) =>
      findPath(object[key], path ? [path, key].join(".") : key)
    );
  };

  return findPath(object).toString().split(",");
};

//This can be used as compare function in sort for sorting the input based on label
//Sample Format:
// Input:
// {
//   value: '1',
//   label: 'Not Identified',
// },
// {
//   value: '2',
//   label: 'Closed',
// },
// {
//   value: '3',
//   label: 'Communicated',
// },
// ---------
// Output:
// {
//   value: '2',
//   label: 'Closed',
// },
// {
//   value: '3',
//   label: 'Communicated',
// },
// {
//   value: '1',
//   label: 'Not Identified',
// },
export const sortByLabelCmpFun = (optionA, optionB) => {
  return (optionA?.label ?? "")
    .toLowerCase()
    .localeCompare((optionB?.label ?? "").toLowerCase());
};

export const capitalizeFirstLetter = (str) =>
  str?.length > 0 ? `${str.charAt(0).toUpperCase()}${str.slice(1)}` : str;

export const MODULES = {
  ICM: "ICM",
  CPQ: "CPQ",
};

/**
 * Verify if the children is a valid React element.
 * This is written to verify array of children as well as single child.
 * @param {React.ReactNode} children
 * @returns {boolean}
 */
export const verifyValidChildren = (children) => {
  if (Array.isArray(children)) {
    return children.every(isValidElement);
  } else {
    return isValidElement(children);
  }
};
export { preprocessTemplateName, removeNonAlphanumeric };
export {
  safeGetLocalStorage,
  safeSetLocalStorage,
  safeRemoveLocalStorage,
  isLocalStorageAvailable,
  LOCAL_STORAGE_KEYS,
} from "./localStorageUtils";
