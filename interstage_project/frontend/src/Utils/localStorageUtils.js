/**
 * Safe localStorage utility functions that handle errors gracefully
 * Useful for private browsing mode, quota exceeded, and other localStorage failures
 */

/**
 * Safely gets an item from localStorage
 * @param {string} key - The localStorage key
 * @param {*} defaultValue - Default value to return if operation fails
 * @returns {*} The parsed value from localStorage or defaultValue
 */
export const safeGetLocalStorage = (key, defaultValue = null) => {
  try {
    const item = localStorage.getItem(key);
    return item ? JSON.parse(item) : defaultValue;
  } catch (error) {
    console.warn(`Failed to read from localStorage (key: ${key}):`, error);
    return defaultValue;
  }
};

/**
 * Safely sets an item in localStorage
 * @param {string} key - The localStorage key
 * @param {*} value - The value to store (will be JSON.stringified)
 * @returns {boolean} True if successful, false if failed
 */
export const safeSetLocalStorage = (key, value) => {
  try {
    localStorage.setItem(key, JSON.stringify(value));
    return true;
  } catch (error) {
    console.warn(`Failed to write to localStorage (key: ${key}):`, error);
    return false;
  }
};

/**
 * Safely removes an item from localStorage
 * @param {string} key - The localStorage key
 * @returns {boolean} True if successful, false if failed
 */
export const safeRemoveLocalStorage = (key) => {
  try {
    localStorage.removeItem(key);
    return true;
  } catch (error) {
    console.warn(`Failed to remove from localStorage (key: ${key}):`, error);
    return false;
  }
};

/**
 * Checks if localStorage is available and working
 * @returns {boolean} True if localStorage is available
 */
export const isLocalStorageAvailable = () => {
  try {
    const testKey = '__localStorage_test__';
    localStorage.setItem(testKey, 'test');
    localStorage.removeItem(testKey);
    return true;
  } catch (error) {
    return false;
  }
};

/**
 * Constants for localStorage keys used in the application
 */
export const LOCAL_STORAGE_KEYS = {
  RECENT_APPROVAL_WORKFLOW_IDS: 'recent_approval_workflow_ids',
  // Add other localStorage keys here as needed
};
