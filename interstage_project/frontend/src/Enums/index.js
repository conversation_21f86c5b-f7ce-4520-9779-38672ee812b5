import {
  NavigationPointer01Icon,
  ActivityIcon,
  ClockRefreshIcon,
} from "@everstage/evericons/outlined";

export const COMMON_MOMENT_DATE_FORMAT = "MMM DD, YYYY";
export const COMMON_FNS_DATE_FORMAT = "MMM dd, yyyy";

export const DATATYPE = {
  INTEGER: "Integer",
  STRING: "String",
  DATE: "Date",
  BOOLEAN: "Boolean",
  OBJECT: "Object",
  PERCENTAGE: "Percentage",
  DAYDURATION: "DayDuration",
  MINUTEDURATION: "MinuteDuration",
  SECONDDURATION: "SecondDuration",
  ARRAY: "Array",
  INTARRAY: "IntArray",
  STRINGARRAY: "StringArray",
  EMAIL: "Email",
  HIERARCHY: "Hierarchy",
};

export const ADJUSTMENT_TYPE = {
  UPDATE: "UPDATE",
  SPLIT: "SPLIT",
  IGNORE: "IGNORE",
};

export const DANGLING_REASON = {
  ROW_INVALIDATED: "row_invalidated",
};

export const COALESCE_DATATYPES = [
  { label: DATATYPE.STRING, value: DATATYPE.STRING },
  { label: DATATYPE.DATE, value: DATATYPE.DATE },
  { label: DATATYPE.BOOLEAN, value: DATATYPE.BOOLEAN },
  { label: DATATYPE.EMAIL, value: DATATYPE.EMAIL },
];

/**
 * Subset of data types that can be used with the GetLineItemValue function
 */
export const GETLINEITEMVALUE_DATATYPES = [
  { label: DATATYPE.STRING, value: DATATYPE.STRING },
  { label: DATATYPE.DATE, value: DATATYPE.DATE },
  { label: DATATYPE.INTEGER, value: DATATYPE.INTEGER },
];

export const SPLDATATYPE = {
  QUOTA: "Quota",
  ACONFIG: "AConfig",
};

export const TOKENTYPE = {
  VARIABLE: "VARIABLE",
  OPERATOR: "OPERATOR",
  LBRACKET: "LBRACKET",
  RBRACKET: "RBRACKET",
  FUNCTION: "FUNCTION",
};

export const TOKENSUBTYPE = {
  ARITHMETIC: "ARITHMETIC",
  LOGICAL: "LOGICAL",
  LBRACKET: "LBRACKET",
  RBRACKET: "RBRACKET",
  PRIMARY: "PRIMARY",
  SECONDARY: "SECONDARY",
  DERIVED: "DERIVED",
  CONFIG: "CONFIG",
  CALCUALTED: "CALCUALTED",
};

export const TOKENCATEGORY = {
  DYNAMIC: "DYNAMIC",
  STATIC: "STATIC",
};

export const ACTIONS = {
  WRAP: "WRAP",
};

export const EXPR_STATUS = {
  EMPTY: "EMPTY",
  CHECKING: "CHECKING",
  VALID: "VALID",
  INVALID: "INVALID",
};

export const PRIMARYOBJECT = {
  ACCOUNT: "Account",
  OPPORTUNITY: "Opportunity",
  LEAD: "Lead",
  ACTIVITY: "Activity",
};

export const AMONGTYPE = {
  ClosedWonDeals: "Opportunity",
  CreatedDeals: "Opportunity",
  OpenDeals: "Opportunity",
  ClosedLostDeals: "Opportunity",
  QualifiedLeads: "Lead",
  CreatedLeads: "Lead",
  Accounts: "Account",
  CreatedActivities: "Activity",
};

export const FUNCTIONTYPES = {
  TOKEN: "TOKEN",
  SIMPLE: "SIMPLE",
  CONDITIONAL: "CONDITIONAL",
  COUNT: "COUNT",
  DATEDIFF: "DATEDIFF",
};

export const MODELMAP = {
  extendedDeal: PRIMARYOBJECT.OPPORTUNITY,
  extendedLead: PRIMARYOBJECT.LEAD,
  extendedAccount: PRIMARYOBJECT.ACCOUNT,
};

export const PLANTYPE = { SPIFF: "SPIFF", MAIN: "MAIN" };

export const DRS_STATUS = {
  ASSIGNED: "Assigned",
  OPEN: "Open",
  CLOSED: "Closed",
};

export const PAYOUT_FREQUENCY = {
  MONTHLY: "Monthly",
  QUARTERLY: "Quarterly",
  HALFYEARLY: "Halfyearly",
  ANNUAL: "Annual",
};

export const LINE_ITEM_TYPE = {
  Deal: "Opportunity",
  Lead: "Lead",
  Accout: "Account",
  Activity: "Activity",
};

export const DRAW_TYPE = {
  NonRecoverableGuarantee: "NRG",
  RecoverableGuarantee: "RG",
  NonRecoverable: "NR",
  Recoverable: "R",
};

export const DRAW_TYPE_NAMES = {
  [DRAW_TYPE.NonRecoverableGuarantee]: "Non-recoverable Guarantee",
  [DRAW_TYPE.RecoverableGuarantee]: "Recoverable Guarantee",
  [DRAW_TYPE.NonRecoverable]: "Non-Recoverable",
  [DRAW_TYPE.Recoverable]: "Recoverable",
};

export const HOT_TABLE_DATATYPES = {
  [DATATYPE.INTEGER]: "numeric",
  [DATATYPE.STRING]: "text",
  [DATATYPE.DATE]: "date",
  [DATATYPE.PERCENTAGE]: "numeric",
  [DATATYPE.BOOLEAN]: "dropdown",
};

export const QUOTA_CATEGORIES = {
  PRIMARY_QUOTA: "Primary",
};

export const QUOTA_CATEGORY_NAME = {
  [QUOTA_CATEGORIES.PRIMARY_QUOTA]: "PRIMARY_QUOTA",
};

export const NEW_DATATYPES = {
  INTEGER: "Integer",
  STRING: "String",
  DATE: "Date",
  BOOLEAN: "Boolean",
  PERCENTAGE: "Percentage",
  EMAIL: "Email",
  HIERARCHY: "Hierarchy",
};

export const CHECKBOX_STATUS = {
  ALL: "all",
  NONE: "none",
  INDETERMINATE: "indeterminate",
};

export const APPLICABLE_PRIMARY_OBJECTS = {
  [PRIMARYOBJECT.OPPORTUNITY]: [
    PRIMARYOBJECT.OPPORTUNITY,
    PRIMARYOBJECT.LEAD,
    PRIMARYOBJECT.ACTIVITY,
  ],
  [PRIMARYOBJECT.LEAD]: [
    PRIMARYOBJECT.OPPORTUNITY,
    PRIMARYOBJECT.LEAD,
    PRIMARYOBJECT.ACTIVITY,
  ],
  [PRIMARYOBJECT.ACCOUNT]: [
    PRIMARYOBJECT.OPPORTUNITY,
    PRIMARYOBJECT.LEAD,
    PRIMARYOBJECT.ACCOUNT,
    PRIMARYOBJECT.ACTIVITY,
  ],
  Contract: [
    PRIMARYOBJECT.OPPORTUNITY,
    PRIMARYOBJECT.LEAD,
    PRIMARYOBJECT.ACTIVITY,
  ],
  [PRIMARYOBJECT.ACTIVITY]: [PRIMARYOBJECT.ACTIVITY],
};

export const MODELS = {
  OPPORTUNITY: PRIMARYOBJECT.OPPORTUNITY,
  LEAD: PRIMARYOBJECT.LEAD,
  ACCOUNT: PRIMARYOBJECT.ACCOUNT,
  CONTRACT: "Contract",
  ACTIVITY: PRIMARYOBJECT.ACTIVITY,
  INVOICE: "Invoice",
};

export const PAYOUT_FREQ_MONTHS = {
  Monthly: 1,
  Quarterly: 3,
  Halfyearly: 6,
  Annual: 12,
};

export const UI_TEXT = {
  NA: "NA",
  NOT_AVAILABLE: "Not Available",
};

export const PAYOUT_PERIOD = {
  DAY: "Day",
  MONTH: "Month",
  QUARTER: "Quarter",
  HALFYEAR: "Halfyear",
  YEAR: "Year",
};

export const DATASHEET_SOURCE_TYPE_CODES = {
  OBJECT: {
    label: "Object",
    value: "custom_object",
  },
  REPORT_OBJECT: {
    label: "Report Object",
    value: "report_object",
  },
  DATASHEET: {
    label: "Datasheet",
    value: "datasheet",
  },
};

export const SOURCE_TYPE_CODES = {
  OBJECT: "object",
  REPORT_OBJECT: "report",
  DATASHEET: "datasheet",
};

export const SOURCE_TYPE_NAMES = {
  [SOURCE_TYPE_CODES.OBJECT]: "Object",
  [SOURCE_TYPE_CODES.DATASHEET]: "Datasheet",
  [SOURCE_TYPE_CODES.REPORT_OBJECT]: "Report Object",
};

export const DATA_ORIGIN = {
  COMMISSION_OBJECT: "commission_object",
  SYSTEM_OBJECT: "system_object",
  CUSTOM_OBJECT: "custom_object",
  INTER_OBJECT: "inter_object",
  FORECAST_OBJECT: "forecast_object",
  INTER_FORECAST_OBJECT: "inter_forecast_object",
};

export const T_CODES = {
  FILTER: "FILTER",
  SORT: "SORT",
  UNION: "UNION",
  JOIN: "JOIN",
  GROUP_BY: "GROUP_BY",
  ADVANCED_FILTER: "ADVANCED_FILTER",
  ADVANCED_FILTER_V2: "ADVANCED_FILTER_V2",
  TEMPORAL_SPLICE: "TEMPORAL_SPLICE",
  FLATTEN: "FLATTEN",
  GET_USER_PROPERTIES: "GET_USER_PROPERTIES",
};

export const T_NAMES = {
  [T_CODES.FILTER]: "Basic Filter",
  [T_CODES.SORT]: "Sort",
  [T_CODES.UNION]: "Union",
  [T_CODES.JOIN]: "Join",
  [T_CODES.GROUP_BY]: "Group by",
  [T_CODES.ADVANCED_FILTER]: "Filter",
  [T_CODES.ADVANCED_FILTER_V2]: "Filter",
  [T_CODES.TEMPORAL_SPLICE]: "Temporal Splice",
  [T_CODES.FLATTEN]: "Flatten Hierarchy",
  [T_CODES.GET_USER_PROPERTIES]: "Get User Properties",
};

export const AGG = {
  AVG: {
    code: "AVG",
    name: "AVG",
    dataType: [DATATYPE.INTEGER],
    prefix: "avg_",
    dPrefix: "AVG::",
    outputType: DATATYPE.INTEGER,
  },
  COUNT: {
    code: "COUNT",
    name: "COUNT",
    dataType: null,
    prefix: "count_",
    dPrefix: "COUNT::",
    outputType: DATATYPE.INTEGER,
  },
  COUNT_DISTINCT: {
    code: "COUNT_DISTINCT",
    name: "COUNTDISTINCT",
    dataType: null,
    prefix: "count_distinct_",
    dPrefix: "COUNT DISTINCT::",
    outputType: DATATYPE.INTEGER,
  },
  MIN: {
    code: "MIN",
    name: "MIN",
    dataType: [DATATYPE.INTEGER, DATATYPE.DATE],
    prefix: "min_",
    dPrefix: "MIN::",
    outputType: null,
  },
  MAX: {
    code: "MAX",
    name: "MAX",
    dataType: [DATATYPE.INTEGER, DATATYPE.DATE],
    prefix: "max_",
    dPrefix: "MAX::",
    outputType: null,
  },
  SUM: {
    code: "SUM",
    name: "SUM",
    dataType: [DATATYPE.INTEGER],
    prefix: "sum_",
    dPrefix: "SUM::",
    outputType: DATATYPE.INTEGER,
  },
};

export const EVERSTAGE_GENERATED_COLUMNS = {
  TIERNAME: {
    name: "Tier / Overridden Tier",
    shortName: "Tier",
    systemName: "tierName",
  },
  ORIGINALTIERNAME: {
    name: "Actual Tier",
    systemName: "originalTierName",
  },
  QUOTAEROSION: {
    name: "Quota Retirement",
    systemName: "quotaErosion",
  },
  COMMISSION: {
    name: "Commissions",
    systemName: "commission",
  },
};

export const FIXED_COLUMNS = {
  COMMISSION: EVERSTAGE_GENERATED_COLUMNS.COMMISSION.systemName,
};

export const PAYMENT_STATUS = {
  PAID: "Paid",
  UNPAID: "Unpaid",
  // TODO Commission
  PARTIALLY_PAID: "Partially Paid",
  ZERO_PAYOUT: "Zero Payout",
  OVER_PAID: "Over Paid",
};

export const APPROVAL_STATUS = {
  APPROVED: "approved",
  REJECTED: "rejected",
  WITHDRAWN: "withdrawn",
  REQUESTED: "requested",
  ABORTED: "aborted",
  NOT_REQUESTED: "not_requested",
  PENDING: "pending",
  NEEDS_ATTENTION: "needs_attention",
  REVOKED: "revoked",
};

export const APPROVAL_STATUS_STATEMENTS = {
  approved: "APPROVAL_STATUS.APPROVED",
  rejected: "APPROVAL_STATUS.REJECTED",
  withdrawn: "APPROVAL_STATUS.WITHDRAWN",
  requested: "APPROVAL_STATUS.REQUESTED",
  aborted: "APPROVAL_STATUS.ABORTED",
  not_requested: "APPROVAL_STATUS.NOT_REQUESTED",
  pending: "APPROVAL_STATUS.PENDING",
  revoked: "APPROVAL_STATUS.REVOKED",
  needs_attention: "APPROVAL_STATUS.NEEDS_ATTENTION",
};

export const LINE_ITEM_APPROVAL_STATUS = {
  APPROVED: "accepted",
  REJECTED: "declined",
  WITHDRAWN: "withdrawn",
  PENDING: "requested",
  CANCELLED: "aborted",
  REVOKED: "revoked",
};
export const CALCULATION_STATUS = {
  FROZEN: "Frozen",
  NOTFROZEN: "Not Frozen",
};

export const MONTHS = [
  "January",
  "February",
  "March",
  "April",
  "May",
  "June",
  "July",
  "August",
  "September",
  "October",
  "November",
  "December",
];

export const COMMISSION_ACTION_STATUS = {
  FREEZE: "freeze",
  UNFREEZE: "unfreeze",
  MAKE_PAYMENT: "makePayment",
  COMMISSION: "commissions",
  SETTLEMENT: "settlements",
  REQUEST_APPROVAL: "requestApproval",
  REVOKE_APPROVAL: "revokeApproval",
  EMAIL_STATEMENTS: "emailStatements",
  DOWNLOAD_STATEMENTS: "downloadStatements",
};

export const COMMISSION_VIEW = {
  PAYOUTS: "payouts",
  ARREARS: "arrears",
};

export const FILTER_CONFIG_TYPE = {
  FILTER: "FILTER",
  PIVOT: "PIVOT",
};

export const PIVOT_AGG = {
  Boolean: [{ label: "Count", value: "count" }],
  Integer: [
    { label: "Sum", value: "sum" },
    { label: "Count", value: "count" },
    { label: "Distinct Count", value: "nunique" },
    { label: "Max", value: "max" },
    { label: "Min", value: "min" },
    { label: "Average", value: "avg" },
  ],
  Percentage: [
    { label: "Sum", value: "sum" },
    { label: "Count", value: "count" },
    { label: "Distinct Count", value: "nunique" },
    { label: "Max", value: "max" },
    { label: "Min", value: "min" },
    { label: "Average", value: "avg" },
  ],
  String: [
    { label: "Count", value: "count" },
    { label: "Distinct Count", value: "nunique" },
  ],
  Date: [
    { label: "Count", value: "count" },
    { label: "Distinct Count", value: "nunique" },
    { label: "Max", value: "max" },
    { label: "Min", value: "min" },
  ],
  Email: [
    { label: "Count", value: "count" },
    { label: "Distinct Count", value: "nunique" },
  ],
};

export const PIVOT_AGG_MAP = {
  sum: "Sum",
  count: "Count",
  nunique: "Distinct Count",
  max: "Max",
  min: "Min",
  avg: "Average",
};

export const USER_STATUS_MAP = {
  ADDED: "Added",
  INVITED: "Invited",
  ACTIVE: "Active",
  INACTIVE: "Inactive",
  MARKED_FOR_EXIT: "Marked for exit",
  PENDING_EXIT: "Pending exit",
  MARKED_FOR_DEACTIVATION: "Marked for deactivation",
};

export const PAYEE_ROLE_FILTER = {
  ALL: "all",
  SELF: "self",
  SELF_AND_REPORTEES: "self_and_reportees",
};

export const SEARCH_BOX = {
  DEBOUNCE_TIME: 500,
  MINIMUM_CHARS: 3,
};

export const CONNECTION_STATUS = {
  CONNECTED: "CONNECTED",
  ERROR: "ERROR",
};

export const INTEGRATION = {
  ADP_WORKFORCE: "adp_workforce",
  AIRTABLE: "airtable",
  AWS_S3: "aws_s3",
  BAMBOO_HR: "bamboo_hr",
  BIGQUERY: "bigquery",
  BULLHORN: "bullhorn",
  CERIDIAN_DAYFORCE: "ceridian_dayforce",
  CHARGEBEE: "chargebee",
  CLOSE: "close",
  CSV: "csv",
  EMPLOYMENT_HERO: "employment_hero",
  FRESHSALES: "freshworks",
  GOOGLE_SHEETS: "google_sheets",
  GUSTO: "gusto",
  HUBSPOT: "hubspot",
  MANUAL_UPLOAD: "manual upload",
  MAXIO: "maxio",
  MERGE: "merge",
  MICROSOFT_DYNAMICS_365: "microsoft_dynamics_365",
  MSSQL: "mssql",
  MYSQL: "mysql",
  PERSONIO: "personio",
  PIPEDRIVE: "pipedrive",
  POSTGRESQL: "postgres",
  QUICKBOOKS: "quickbooks",
  REDSHIFT: "redshift",
  S3: "s3",
  SAASOPTICS: "saasoptics",
  GONG: "gong",
  SAGE_INTACCT: "sage_intact",
  SALESFORCE: "salesforce",
  SERVICETITAN: "servicetitan",
  SFTP: "sftp",
  SHAREPOINT: "sharepoint",
  SHOPIFY: "shopify",
  SNOWFLAKE: "snowflake",
  SQL: "sql",
  STRIPE: "stripe",
  SUITEANALYTICS: "suiteanalytics",
  WORKDAY: "workday",
  XERO: "xero",
  ZOHO: "zoho",
};

export const ACTIVITY_LOGS_STATUS = {
  PENDING: "PENDING",
  PROCESSING: "PROCESSING",
  DONE: "DONE",
  FAILED: "FAILED",
  PARTIALLY_FAILED: "PARTIALLYFAILED",
};

export const ACTIVITY_LOGS_JOBS = {
  DATAIMPORT: "DATAIMPORT",
  QUOTAIMPORT: "QUOTAIMPORT",
  USERIMPORT: "USERIMPORT",
  COMMISSIONSYNC: "COMMISSIONSYNC",
  CONTRACTS_STATUS_EXPORT: "CONTRACTS_STATUS_EXPORT",
  BULK_DOWNLOAD_STATEMENTS: "BULK_DOWNLOAD_STATEMENTS",
  BULK_DELETE_USERS: "BULK_DELETE_USERS",
  DATASHEET_EXPORT: "DATASHEET_EXPORT",
};

export const CONNECTORS = {
  OBJECTS: "objects",
  CONNECTIONS: "connections",
  INTEGRATION: "integration",
  OBJECT: "object",
  CONNECTION: "connection",
  OBJECT_BACK: "object-back",
  CREATE_CONNECTION: "create-connection",
  CREATE_CONNECTION_TO_LINK: "create-connection-to-link",
  CREATE_OBJECT_CONNECTION: "create-object-connection",
  CREATE_OBJECT_MANUALLY: "create-object-manually",
  CREATE_CONNECTION_SUCCESS: "create-connection-success",
};

export const CSV_EXPORT_TYPE = {
  FILTERED: "FILTERED",
  ALL: "ALL",
};

export const REQUEST_STATUS = {
  REQUESTED: "requested",
  REJECTED: "rejected",
  APPROVED: "approved",
  WITHDRAWN: "withdrawn",
};

export const APPROVAL_ENTITY_TYPES = {
  PAYOUT: "payout",
  QUOTE: "quote",
  PAYOUT_LINE_ITEM: "payout_line_item",
};

export const HEADER_STATE = {
  EVERYTHING: "EVERYTHING",
  ALL: "ALL",
  NONE: "NONE",
  PARTIAL: "PARTIAL",
};

export const ENTITY_KEY_DELIMITER = "##::##";

export const APPROVAL_STAGE_STATUS = {
  NOTSTARTED: "not_started",
  STARTED: "started",
  REQUESTED: "requested",
  COMPLETED: "completed",
  WITHDRAWN: "withdrawn",
  REJECTED: "rejected",
  ABORTED: "aborted",
  REVOKED: "revoked",
  REVOKED_TITLE: "revoked_title",
};

export const LIST_TYPE = {
  HIERARCHY_TREE: "HierarchyTree",
};

export const QUOTA_SCHEDULE = {
  MONTHLY: "Monthly",
  QUARTERLY: "Quarterly",
  HALF_YEARLY: "Halfyearly",
  ANNUAL: "Annual",
};

export const REQUEST_APPROVAL_SINGLE_WARNING =
  "Cannot send an approval request when payout is unlocked, or when approval status is already Requested or Approved.";

export const REQUEST_APPROVAL_BULK_WARNING =
  'Payouts that have approval status as "Requested", "Approved" and Payouts that are not locked will be skipped while sending this approval request.';

export const APPROVAL_STATUS_OPTIONS = [
  { label: "Approved", value: "approved" },
  { label: "Rejected", value: "rejected" },
  { label: "Requested", value: "requested" },
  { label: "Not Requested", value: "not_requested" },
  { label: "Auto cancelled", value: "aborted" },
  { label: "Withdrawn", value: "withdrawn" },
  { label: "Revoked", value: "revoked" },
  { label: "Needs Attention", value: "needs_attention" },
];

export const STAGE_STATUS_OPTIONS = [
  { label: "On track", value: "on_track" },
  { label: "Overdue", value: "overdue" },
];

export const DATASHEET_VIEW_ID = {
  ALL_DATA: "all_data",
  ALL_ADJUSTMENTS: "all_adjustments",
};

export const RBAC_ROLES = {
  VIEW_DASHBOARD: "view:dashboard",
  MANAGE_DASHBOARD: "manage:dashboard",
  DELETE_DASHBOARD: "delete:dashboard",
  VIEW_ADMINDASHBOARD: "view:admindashboard",
  VIEW_PAYEEDASHBOARD: "view:payeedashboard",
  VIEW_DATABOOK: "view:databook",
  MANAGE_DATABOOK: "manage:databook",
  MANAGE_DATASHEETPERMISSIONS: "manage:datasheetpermissions",
  MANAGE_DATASHEETADJUSTMENTS: "manage:datasheetadjustments",
  EXPORT_DATASHEET: "export:datasheet",
  DELETE_DATASHEET: "delete:datasheet",
  VIEW_TERRITORY_PLANS: "view:territoryplans",
  EXPLORE_TERRITORY_PLANS: "explore:territoryplans",
  VIEW_COMMISSIONPLAN: "view:commissionplan",
  EDIT_COMMISSIONPLAN: "edit:commissionplan",
  CREATE_COMMISSIONPLAN: "create:commissionplan",
  DELETE_COMMISSIONPLAN: "delete:commissionplan",
  VIEW_COMMISSIONFEED: "view:commissionfeed",
  MANAGE_COMMISSIONFEED: "manage:commissionfeed",
  VIEW_PAYOUTS: "view:payouts",
  MANAGE_PAYOUTS: "manage:payouts",
  REGISTER_PAYOUTS: "register:payouts",
  INVALIDATE_PAYOUTS: "invalidate:payouts",
  VIEW_REQUESTAPPROVALS: "view:requestapprovals",
  EXPORT_PAYOUTS: "export:payouts",
  VIEW_STATEMENTS: "view:statements",
  EXPORT_STATEMENT: "export:statement",
  VIEW_HIDDENCRITERIA: "view:hiddencriteria",
  MANAGE_COMMISSIONADJUSTMENT: "manage:commissionadjustment",
  VIEW_PAYOUTVALUEOTHERS: "view:payoutvalueothers",
  MANAGE_CRYSTAL: "manage:crystal",
  VIEW_QUOTAS: "view:quotas",
  MANAGE_QUOTAS: "manage:quotas",
  MANAGE_QUOTA_SETTINGS: "manage:quotasettings",
  VIEW_HIDDENQUOTAS: "view:hiddenquotas",
  VIEW_DRAWS: "view:draws",
  MANAGE_DRAWS: "manage:draws",
  MANAGE_USERS: "manage:users",
  VIEW_USERS: "view:users",
  DELETE_USERS: "delete:users",
  VIEW_PAYROLL: "view:payroll",
  EDIT_PAYROLL: "edit:payroll",
  EXPORT_USERS: "export:users",
  ALLOW_IMPERSONATION: "allow:impersonation",
  VIEW_TEAMS: "view:teams",
  MANAGE_USERGROUPS: "manage:usergroups",
  MANAGE_USERCUSTOMFIELD: "manage:usercustomfield",
  VIEW_QUERIES: "view:queries",
  CREATE_QUERIES: "create:queries",
  EDIT_QUERIES: "edit:queries",
  DELETE_QUERIES: "delete:queries",
  MANAGE_CONFIG: "manage:config",
  MANAGE_CONTRACTS: "manage:contracts",
  MANAGE_DATASETTINGS: "manage:datasettings",
  MANAGE_ROLES: "manage:roles",
  MANAGE_OWNDATA: "manage:owndata",
  MANAGE_REPORTENRICH: "manage:reportenrich",
  MANAGE_ALL_ADMINS: "manage:alladmins",
  MANAGE_ANALYTICS: "manage:analytics",
  MANAGE_ACCOUNTNOTIFICATIONS: "manage:accountnotifications",
  VIEW_EVERSTAGE: "view:everstage",
  VIEW_APPROVALS: "view:approvals",
  MANAGE_AGENT_STUDIO: "manage:agentstudio",
  MANAGE_AGENT_WORKBENCH: "manage:agentworkbench",
  MANAGE_AUTOGEN_DESCRIPTION: "manage:autogendescription",
  MANAGE_COMMISSION_FORMULA_GENERATION: "manage:commissionformulageneration",
  MANAGE_DATASHEET_AI_GENERATION: "manage:datasheetaigeneration",
  MANAGE_APPROVAL_WORKFLOWS: "manage:approvalworkflows",
  PUBLISH_COMMISSION_PLAN: "publish:commissionplan",
  ACCESS_HELP_CENTRE: "access:helpcenter",
  MANAGE_MULTI_PERIOD_SYNC: "manage:multiperiodsync",
  VIEW_GLOBAL_SEARCH: "view:globalsearch",
  VIEW_ADMIN_CONTENT: "view:admincontent",
  VIEW_PAYEE_CONTENT: "view:payeecontent",
};

export const DASHBOARD_URLS = {
  DASHBOARDS: "/dashboards",
  DASHBOARD: "/dashboard",
};

export const DASHBOARD_TYPES = {
  ADMIN_DASHBOARD: "admin_dashboard",
  PAYEE_DASHBOARD: "payee_dashboard",
  SUPERSET_DASHBOARD: "superset_dashboard",
};

export const DASHBOARD_PAGE_NAMES = {
  DEFAULT_DASHBOARD: "Default Dashboard",
  SUPERSET_DASHBOARD: "Superset Dashboard",
};

export const DEFAULT_DASHBOARD_ID = {
  ADMIN_DASHBOARD: "1",
  PAYEE_DASHBOARD: "2",
};

export const DATABOOK_URLS = {
  DATABOOKS: "/databook",
};

export const CPQ_URLS = {
  QUOTES: "/cpq/quotes",
};

export const CPQ_PAGE_NAMES = {
  DATABOOK: "Databook",
  DATASHEET: "Datasheet",
  DATASHEETS: "Datasheets",
  QUOTES: "Quotes",
  CATALOG: "Catalog",
  PRODUCT: "Product",
  PRICE_BOOK: "PriceBook",
  USERS: "Users",
  USER_GROUPS: "Groups",
  SETTINGS: "Settings",
  EVERAI: "EverAI",
};

export const EVERSIDER_PAGE_NAMES = {
  DASHBOARD: "Dashboards",
  DATABOOK: "Databook",
  DATASHEET: "Datasheet",
  DATASHEETS: "Datasheets",
  OBJECTS: "Objects",
  TERRITORY_PLANS: "Territory Plans",
  STATEMENTS: "Statements",
  COMMISSION_PLAN: "Plans",
  FORECAST: "Forecasts",
  COMMISSION_FEED: "Feed",
  COMMISSION_PAYOUTS: "Payouts",
  APPROVALS: "Approvals",
  PAYOUT_APPROVALS: "Payout",
  COMMISSION_ADJUSTMENT_APPROVALS: "Commission",
  CRYSTAL: "Crystal",
  USERS: "Users",
  USER_GROUPS: "Groups",
  TEAMS: "Teams",
  QUOTAS: "Quotas",
  DRAWS: "Draws",
  QUERIES: "Queries",
  CONTRACTS: "Contracts",
  SETTINGS: "Settings",
  METRICS: "Metrics",
  EXPLORER: "Explorer",
  SEARCH: "Search",
  TRANSFORMATION: "Transformation",
  KPI: "KPI",
  EVERAI: "EverAI",
  ...CPQ_PAGE_NAMES,
};

export const EVERSIDER_PAGE_NAME_LOCALIZED = {
  DASHBOARDS: "EVERSIDER_PAGE_NAMES.DASHBOARDS",
  STATEMENTS: "EVERSIDER_PAGE_NAMES.STATEMENTS",
  QUERIES: "EVERSIDER_PAGE_NAMES.QUERIES",
};

export const INVALID_DATE = "Invalid date";

export const SUPABASE_CONSTANTS = {
  BULK_CREATE_APPROVAL_INSTANCES: {
    TASK_NAME: "BULK_CREATE_APPROVAL_INSTANCES",
    CHANNEL_NAME: "create-bulk-approval-instance",
    EVENT_TYPE: ["INSERT", "UPDATE"],
    PENDING: "PENDING",
    PROCESSING: "PROCESSING",
    DONE: "DONE",
    FAILED: "FAILED",
  },
  BULK_REVOKE_APPROVAL_INSTANCES: {
    TASK_NAME: "BULK_REVOKE_APPROVAL_INSTANCES",
    CHANNEL_NAME: "bulk-revoke-approval-instances",
    EVENT_TYPE: ["INSERT", "UPDATE"],
    PENDING: "PENDING",
    PROCESSING: "PROCESSING",
    DONE: "DONE",
    FAILED: "FAILED",
  },
  EMAIL_STATEMENTS: {
    TASK_NAME: "BULK_EMAIL_STATEMENTS",
    CHANNEL_NAME: "email-statements",
    EVENT_TYPE: { UPDATE: "UPDATE", INSERT: "INSERT" },
    PENDING: "PENDING",
    PROCESSING: "PROCESSING",
    DONE: "DONE",
    FAILED: "FAILED",
  },
  BULK_IMPORT_DATA: {
    TASK_NAME: "BULK_IMPORT_DATA",
    CHANNEL_NAME: "bulk-import-data",
    EVENT_TYPE: ["INSERT", "UPDATE"],
  },
  HRIS_SYNC: {
    TASK_NAME: "UPSTREAM_SYNC",
    CHANNEL_NAME: "hris-sync",
    EVENT_TYPE: ["INSERT", "UPDATE"],
    STARTED: "STARTED",
    FAILED: "FAILED",
    COMPLETED: "COMPLETE",
  },
  UPSTREAM_SYNC: {
    TASK_NAME: "UPSTREAM_SYNC",
    CHANNEL_NAME: "upstream-sync",
    EVENT_TYPE: ["INSERT", "UPDATE"],
  },
  HRIS_IMPORT_RECORDS: {
    TASK_NAME: "IMPORT_HRIS_DATA",
    CHANNEL_NAME: "import-hris-data",
    EVENT_TYPE: ["INSERT", "UPDATE"],
    STARTED: "started",
    FAILED: "FAILED",
    COMPLETED: "DONE",
  },
  BULK_DOWNLOAD_STATEMENTS: {
    TASK_NAME: "BULK_DOWNLOAD_STATEMENTS",
    CHANNEL_NAME: "bulk-download-statements",
    EVENT_TYPE: ["INSERT", "UPDATE"],
    PROCESSING: "PROCESSING",
    PENDING: "PENDING",
    DONE: "DONE",
    FAILED: "FAILED",
  },
  EVERAI_MESSAGE: {
    TASK_NAME: "EVERAI_MESSAGE",
    CHANNEL_NAME: "everai-message",
    EVENT_TYPE: ["INSERT", "UPDATE"],
  },
  DATASHEET_EXPORT_V2: {
    TASK_NAME: "DATASHEET_EXPORT_V2",
    CHANNEL_NAME: "datasheet-export-v2",
    EVENT_TYPE: ["INSERT", "UPDATE"],
    PENDING: "PENDING",
    PROCESSING: "PROCESSING",
    DONE: "DONE",
    FAILED: "FAILED",
  },
};
export const ALL_ROLES_KEY = "ALL";

export const MAPPING_STATUS = {
  MAPPED: "Mapped",
  UNMAPPED: "Unmapped",
};

export const COMPONENTS = {
  DASHBOARD: "dashboard",
  DATABOOKS: "databooks",
  COMMISSION_PLANS: "commission_plans",
  COMMISSION_FEED: "commission_feed",
  PAYOUTS_STATEMENTS: "payouts_statements",
  CRYSTAL: "crystal",
  TERRITORY_PLANS: "territory_plans",
  LEARNING: "learning",
  QUOTAS_DRAWS: "quotas_draws",
  MANAGE_USERS: "manage_users",
  QUERIES: "queries",
  SETTINGS: "settings",
  ADVANCED_PERMISSION: "advanced_permissions",
  ALL_DATA: "all_data",
};

export const DATA_PERMISSION_TYPES = {
  INDIVIDUAL_DATA: "INDIVIDUAL_DATA",
  INDIVIDUAL_AND_TEAM_DATA: "INDIVIDUAL_AND_TEAM_DATA",
  ALL_DATA: "ALL_DATA",
};

export const ANALYTICS_EVENTS = Object.freeze({
  VISIT_HELP_CENTER: "VISIT_HELP_CENTER",
  VISIT_RAISE_QUERY: "VISIT_RAISE_QUERY",
  VISIT_CONTACT_EVERSTAGE: "VISIT_CONTACT_EVERSTAGE",
  VISIT_SOLUTIONS_CORNER: "VISIT_SOLUTIONS_CORNER",
  SWITCH_PERIOD: "SWITCH_PERIOD",
  SWITCH_DISPLAY_CURRENCY: "SWITCH_DISPLAY_CURRENCY",
  SWITCH_PAYEES: "SWITCH_PAYEES",
  RAISE_QUERY_FROM_STATEMENTS: "RAISE_QUERY_FROM_STATEMENTS",
  VISIT_PAGE: "VISIT_PAGE",
  VIEW_PROFILE_CARD: "VIEW_PROFILE_CARD",
  VIEW_PLAN_DETAILS: "VIEW_PLAN_DETAILS",
  VIEW_CRITERIA_TABLE_VIEW: "VIEW_CRITERIA_TABLE_VIEW",
  SWITCH_QUOTA_CATEGORIES: "SWITCH_QUOTA_CATEGORIES",
  SWITCH_FISCAL_YEAR: "SWITCH_FISCAL_YEAR",
  VIEW_QUOTA: "VIEW_QUOTA",
  VIEW_LEADERBOARD: "VIEW_LEADERBOARD",
  QUICK_FILTER: "QUICK_FILTER",
  USER_EXPORT: "USER_EXPORT",
  IMPORT_NEW_USERS: "IMPORT_NEW_USERS",
  EDIT_EXISTING_USERS: "EDIT_EXISTING_USERS",
  FILTER_PARAM: "FILTER_PARAM",
  LOGIN_AS_USER: "LOGIN_AS_USER",
  SEARCH_USAGE: "SEARCH_USAGE",
  DOWNLOAD_TEMPLATE: "DOWNLOAD_TEMPLATE",
  EXPORTS_CSV: "EXPORTS_CSV",
  ARREARS_VIEW: "ARREARS_VIEW",
  MANAGE_PAYEE: "MANAGE_PAYEE",
  REMOVE_PAYEE: "REMOVE_PAYEE",
  PREVIEW_AS_A_PAYEE: "PREVIEW_AS_A_PAYEE",
  SHOWING_FOR: "SHOWING_FOR",
  VIEW_FILTER: "VIEW_FILTER",
  PAYEE_STATEMENT: "PAYEE_STATEMENT",
  CONNECT_MICROSOFT_TEAMS: "CONNECT_MICROSOFT_TEAMS",
  DISCONNECT_MICROSOFT_TEAMS: "DISCONNECT_MICROSOFT_TEAMS",
  REPORT_ENRICHMENT: "REPORT_ENRICHMENT",
  DELETE_REPORT_ENRICHMENT: "DELETE_REPORT_ENRICHMENT",
  BULK_DELETE_REPORT_ENRICHMENT: "BULK_DELETE_REPORT_ENRICHMENT",
  QUERY_ASSIGNMENT: "QUERY_ASSIGNMENT",
  GET_DATA_FROM: "GET_DATA_FROM",
  ADD_TRANSFORMATION: "ADD_TRANSFORMATION",
  ADD_FORMULA_FIELD: "ADD_FORMULA_FIELD",
  EDIT_VARIABLE_FIELD: "EDIT_VARIABLE_FIELD",
  EXPORT_CSV: "EXPORT_CSV",
  CUSTOMIZE_COLUMNS: "CUSTOMIZE_COLUMNS",
  PIVOT_MODE: "PIVOT_MODE",
  ADJUSTMENT_TYPE: "ADJUSTMENT_TYPE",
  COUNT_OF_SAVED_VIEWS_PER_SHEET: "COUNT_OF_SAVED_VIEWS_PER_SHEET",
  GET_JOIN_TYPE: "GET_JOIN_TYPE",
  GET_AGGREGATION_TYPE: "GET_AGGREGATION_TYPE",
  COUNT_OF_TRANSFORMATIONS: "COUNT_OF_TRANSFORMATIONS",
  COUNT_OF_FIELDS: "COUNT_OF_FIELDS",
  COUNT_OF_FORMULA_FIELDS: "COUNT_OF_FORMULA_FIELDS",
  APPLY_FILTER: "APPLY_FILTER",
  SEARCH_PAYOUTS: "SEARCH_PAYOUTS",
  PAGINATION: "PAGINATION",
  CREATE_VIEW: "CREATE_VIEW",
  RENAME_DATASHEET_COLUMN_HYPERLINK_CLICKED:
    "RENAME_DATASHEET_COLUMN_HYPERLINK_CLICKED",
});

export const ANALYTICS_PROPERTIES = Object.freeze({
  ENTRY_POINT: "entry_point",
  PAYEE_NAME: "payee_name",
  CURRENCY_TYPE: "currency_type",
  CURRENCY_NAME: "currency_name",
  PERIOD: "period",
  PAGE_NAME: "page_name",
  CRITERIA_NAME: "criteria_name",
  QUOTA_CATEGORY: "quota_category",
  FISAL_YEAR: "fiscal_year",
  WIDGET_TYPE_NAME: "widget_type_name",
  DASHBOARD_NAME: "dashboard_name",
  FILTER_COLUMNS: "filter_columns",
  QUICK_FILTER_TYPE: "quick_filter_type",
  EXPORT_OPTION: "export_option",
  COUNT_OF_USERS_IMPORTED: "count_of_users_imported",
  LIST_OF_FILTERS: "list_of_filters",
  USER_NAME: "user_name",
  USER_ROLE: "user_role",
  TEXT_SEARCHED: "text_searched",
  TEMPLATE_DOWNLOAD: "template_download",
  TIME_TO_EXPORT: "time_to_export",
  IS_ARREARS_VIEW: "is_arrears_view",
  CRYSTAL_SIMULATOR_NAME: "crystal_simulator_name",
  ADDL_PAYEES_TO_CRYSTAL_VIEW: "addl_payees_to_crystal_view",
  REMOVE_PAYEE: "remove_payee",
  CRYSTAL_VIEW_NAME: "crystal_view_name",
  OVERWRITE_VALUES: "overwrite_values",
  TIME_IN_FILTER_PAGE: "time_in_filter_page",
  VIEW_PAYOUTS_FILTER: "view_payouts_filter",
  ADMIN_EMAIL: "admin_email",
  CONNECT_MS_TEAMS_DATE: "connect_ms_teams_date",
  DISCONNECT_MS_TEAMS: "disconnect_ms_teams",
  DISCONNECT_MS_TEAMS_DATE: "disconnect_ms_teams_date",
  COMMISSION_PLAN_NAME: "commission_plan_name",
  CRITERIA: "criteria",
  DATABOOK: "databook",
  DATASHEET: "datasheet",
  REPORT_TYPE: "report_type",
  COUNT_OF_VARIABLES_ADDED: "count_of_variables_added",
  INCLUDE_ALL_PLANS_USING_THIS_DS: "include_all_plans_using_this_ds",
  QUERY_ASSIGNMENT_OPTION: "query_assignment_option",
  ALLOW_CC: "allow_cc",
  DATASHEET_NAME: "datasheet_name",
  DATA_OPTION: "data_option",
  TRANSFORMATION_OPTION: "transformation_option",
  DATA_TYPE: "data_type",
  FORMULA_FIELD_NAME: "formula_field_name",
  EXPORT_TYPE: "export_type",
  COUNT_OF_ROW_GROUPS: "count_of_row_groups",
  COUNT_OF_COLUMN_LABELS: "count_of_column_labels",
  COUNT_OF_VALUES: "count_of_values",
  ADJUSTMENT_TYPE: "adjustment_type",
  IS_GLOBAL_ADJUSTMENT: "is_global_adjustment",
  COUNT_OF_SAVED_VIEWS: "count_of_saved_views",
  JOIN_TYPE: "join_type",
  AGGREGATION_TYPE: "aggregation_type",
  COUNT_OF_TRANSFORMATIONS: "count_of_transformations",
  COUNT_OF_FIELDS: "count_of_fields",
  APPLY_FILTER: "apply_filter",
  SEARCH_TEXT: "search_text",
  QUICK_FILTER: "quick_filter",
  PAGINATED_VALUE: "paginated_value",
  PLATFORM_USED: "platform_used",
  DISPLAY_CONDITION_FIELD: "display_condition_field",
  COUNT_OF_DISPLAY_CONDITIONS: "count_of_display_conditions",
  SUCCESS_ACTION_FIELD: "success_action_field",
  COUNT_OF_SUCCESS_ACTION: "count_of_success_action",
  DATE_FIELD_NAME: "date_field_name",
  ROW_NAME_FIELD: "row_name_field",
  COUNT_OF_COLUMNS: "count_of_columns",
  COUNT_OF_EDITABLE_COLUMN_NAMES: "count_of_editable_column_names",
  DATE_OF_CREATION: "date_of_creation",
  VIEW_NAME: "view_name",
  COUNT_OF_USERS_UPDATED: "count_of_users_updated",
  ENRICHMENT_RECORDS: "enrichment_records",
  LOCATION: "location",
});

export const PLATFORM = {
  USER: "user",
  PAYOUTS: "payouts",
  DATABOOK: "databook",
};

export const ANALYTICS_PAGE_PATHS = {
  "/statements": "Statements",
  "/commissions": "Payouts",
  "/crystal": "Crystal",
  "/quotas": "Quotas",
  "/draws": "Draws",
  "/queries": "Queries",
  "/settings/notifications": "Notification settings",
  "/profile-settings": "Profile Settings",
  "/settings/workflows": "Approval Workflows",
  "/approvals/payouts": "Payout Approvals",
  "/approvals/commission-adjustments": "Commission Adjustment Approvals",
};

export const DATE_FORMATS = {
  DDMMMYYYY: "DD MMM YYYY",
  DDMMMYYYY_datefns: "dd MMM yyyy",
  MMMYYYY: "MMM YYYY",
};

export const PLAN_STATUS = {
  ALL: "All",
  PUBLISHED: "Published",
  DRAFT: "Draft",
};

export const SORT_BY = [
  { label: "Ascending (A-Z)", value: "asc" },
  { label: "Descending (Z-A)", value: "desc" },
  { label: "Last modified", value: "sortByModifiedDate" },
  { label: "Created at", value: "sortByCreateDate" },
  { label: "Plan Start Date", value: "sortByPlanStartDate" },
];

export const FUNCTION_NAMES = {
  SUM: "SUM",
  MIN: "MIN",
  MAX: "MAX",
  AVG: "AVG",
  SUM_IF: "SUMIF",
  COUNT_IF: "COUNTIF",
  COUNT_NOT_NULL: "CountNotNull",
  DISTINCT_COUNT: "DistinctCount",
  IS_EMPTY: "IsEmpty",
  IS_NOT_EMPTY: "IsNotEmpty",
  LOWER: "Lower",
  CONCAT: "Concat",
  COALESCE: "Coalesce",
  FIND: "Find",
  CURRENT_PAYOUT_PERIOD: "CurrentPayoutPeriod",
  CONTAINS: "Contains",
  NOT_CONTAINS: "NotContains",
  DATE_DIFF: "DATEDIFF",
  GETDATE: "GetDate",
  CONFIG: "Config",
  QUOTA_ATTAINMENT: "QuotaAttainment",
  QUOTA_EROSION: "QuotaErosion",
  QUOTA: "Quota",
  TIERED_VALUE: "TieredValue",
  TIERED_PERCENTAGE: "TieredPercentage",
  TEAM_SUM: "TEAM-SUM",
  TEAM_COUNT_NOT_NULL: "TEAM-COUNT-NOT-NULL",
  TEAM_DISTINCT_COUNT: "TEAM-DISTINCT-COUNT",
  TEAM_MIN: "TEAM-MIN",
  TEAM_MAX: "TEAM-MAX",
  TEAM_AVG: "TEAM-AVG",
  TEAM_SUM_IF: "TEAM-SUMIF",
  TEAM_COUNT_IF: "TEAM-COUNTIF",
  TEAM_QUOTA_ATTAINMENT: "TEAM-QuotaAttainment",
  TEAM_QUOTA_EROSION: "TEAM-QuotaErosion",
  TEAM_QUOTA: "TEAM-Quota",
  START_DATE: "StartDate",
  LAST_DATE: "LastDate",
  DATE_ADD: "DateAdd",
  TIMEZONE: "Timezone",
  ROUND: "Round",
  ROUND_UP: "RoundUp",
  ROUND_DOWN: "RoundDown",
  RANK: "Rank",
  TRIM: "Trim",
  LEN: "Len",
  LEFT: "Left",
  RIGHT: "Right",
  MID: "Mid",
  ROLLING_SUM: "RollingSum",
  GET_USER_PROPERTY: "GetUserProperty",
};

export const PROFILE_MODALS = {
  UPLOAD: "upload",
  GENERATE: "generate",
};

export const DOCUMENT_VIEW_SOURCE = "profileDrawer";

export const WINDOW_CALCULATED_FIELDS = ["rank", "rolling", "hierarchy"];

export const DATA_TYPES_TO_BE_IGNORED_IN_MODULE = {
  DATASHEET_ADJUSTMENT: [NEW_DATATYPES.HIERARCHY],
  REPORT_ENRICHMENT: [NEW_DATATYPES.HIERARCHY],
  CRYSTAL: [NEW_DATATYPES.HIERARCHY],
  CRITERIA_DESIGNER: [NEW_DATATYPES.HIERARCHY],
};

export const CUSTOM_TERMINOLOGY_DEFAULT_TERMS = {
  en: {
    QUOTA: "Quota",
    QUOTAS: "Quotas",
    QUOTA_EROSION: "Quota Erosion",
    COMMISSION: "Commission",
    COMMISSIONS: "Commissions",
    PAYOUT: "Payout",
    PAYOUTS: "Payouts",
    ADJUSTMENT: "Adjustment",
    ADJUSTMENTS: "Adjustments",
    ARREARS: "Arrears",
    ARREAR: "Arrear",
    DEFERRED: "Deferred",
    EARNED: "Earned",
    ON_TARGET_VARIABLE_PAY: "On-Target Variable Pay",
  },
  es: {
    QUOTA: "Cuota",
    QUOTAS: "Cuotas",
    QUOTA_EROSION: "Erosión de Cuota",
    COMMISSION: "Comisión",
    COMMISSIONS: "Comisiones",
    PAYOUT: "Pago",
    PAYOUTS: "Pagos",
    ADJUSTMENT: "Ajuste",
    ADJUSTMENTS: "Ajustes",
    ARREARS: "Atrasos",
    ARREAR: "Atraso",
    DEFERRED: "Diferido",
    EARNED: "Ganado",
    ON_TARGET_VARIABLE_PAY: "Pago Variable Objetivo",
  },
  pt: {
    QUOTA: "Meta",
    QUOTAS: "Metas",
    QUOTA_EROSION: "Erosão de Meta",
    COMMISSION: "Comissão",
    COMMISSIONS: "Comissões",
    PAYOUT: "Pagamento",
    PAYOUTS: "Pagamentos",
    ADJUSTMENT: "Ajuste",
    ADJUSTMENTS: "Ajustes",
    ARREARS: "Atrasos",
    ARREAR: "Atraso",
    DEFERRED: "Diferido",
    EARNED: "Ganho",
    ON_TARGET_VARIABLE_PAY: "Remuneração Variável-Alvo",
  },
};

export const HIERARCHY_CURRENT_SHEET_KEY = "CURRENT_SHEET";
export const CURRENT_DATE_KEY = "CURRENT_DATE";
export const FOR_WHOM_THE_COMMISSION_IS_CALCULATED_KEY =
  "FOR_WHOM_THE_COMMISSION_IS_CALCULATED";

// Values that should be displayed in the UI to denote FOR_WHOM_THE_COMMISSION_IS_CALCULATED and CURRENT_DATE
// When used inside the GetUserProperty commission plan function
export const FOR_WHOM_THE_COMMISSION_IS_CALCULATED_DISPLAY =
  "For whom the commission is calculated";
export const CURRENT_DATE_DISPLAY = "Current date";
export const DATE_PLACEHOLDER = "MMM DD, YYYY";
export const PAYOUT_APPROVAL_TYPES = {
  STATEMENT_LEVEL: "statementLevel",
  LINE_ITEM_LEVEL: "lineItemLevel",
};

export const LINE_ITEM_TYPE_ENTITY_TYPE = "payout_line_item";
export const DATASHEET_VIEW_BY = {
  DATABOOKS: "databooks",
  COMMISSION_PLAN: "commission_plans",
  ARCHIVED_DATABOOKS: "archived_databooks",
  SEPERATOR: "seprator",
};
export const EXPRESSION_TOKEN_TYPES = {
  DATASHEET_VARIABLES: "DATASHEET_VARIABLES",
  DATASHEET_INTER_VARIABLE: "DATASHEET_INTER_VARIABLE",
  CONSTANTS: "CONSTANT_VARIABLES",
  ARRAY: "ARRAY",
  FUNCTIONS: "FUNCTIONS",
  OPERATORS: "OPERATORS",
  BRACKETS: "GROUPING_OPERATORS",
  CLIENT_VARIABLES: "CLIENT_VARIABLES",
  CONFIG_VARIABLES: "CONFIG_VARIABLES",
  QUOTE_VARIABLES: "FORM_VARIABLES",
  LEFT_BRACKET: "(",
  RIGHT_BRACKET: ")",
  USER_DEFINED_FUNCTIONS: "USER_DEFINED_FUNCTIONS",
};

export const EXPRESSION_FUNCTION_TYPES = {
  TieredValue: "TieredValue",
  TieredPercentage: "TieredPercentage",
  SUM: "SUM",
  MIN: "MIN",
  MAX: "MAX",
  AVG: "AVG",
  DISTINCT_COUNT: "DistinctCount",
  Config: "Config",
  QuotaAttainment: "QuotaAttainment",
  QuotaErosion: "QuotaErosion",
  Quota: "Quota",
  DATEDIFF: "DATEDIFF",
  SUMIF: "SUMIF",
  COUNTIF: "COUNTIF",
  IsEmpty: "IsEmpty",
  IsNotEmpty: "IsNotEmpty",
  Contains: "Contains",
  NotContains: "NotContains",
  Lower: "Lower",
  Find: "Find",
  Round: "Round",
  RoundUp: "RoundUp",
  RoundDown: "RoundDown",
  GET_DATE: "GetDate",
  CurrentPayoutPeriod: "CurrentPayoutPeriod",
  CountNotNull: "CountNotNull",
  Concat: "Concat",
  Coalesce: "Coalesce",
  Rank: "Rank",
  Rolling: "Rolling",
  StartDate: "StartDate",
  LastDate: "LastDate",
  DateAdd: "DateAdd",
  Trim: "Trim",
  Len: "Len",
  Left: "Left",
  Right: "Right",
  Mid: "Mid",
  Timezone: "Timezone",
  GetValueFromHierarchy: "GetNthLevelNode",
  Hierarchy: "Hierarchy",
  GetUserProperty: "GetUserProperty",
  StartsWith: "StartsWith",
  EndsWith: "EndsWith",
  DateIsIn: "DateIsIn",
  GetLineItemValue: "GetLineItemValue",

  ["TEAM-SUM"]: "TEAM-SUM",
  ["TEAM-MIN"]: "TEAM-MIN",
  ["TEAM-MAX"]: "TEAM-MAX",
  ["TEAM-AVG"]: "TEAM-AVG",
  ["TEAM-DISTINCT-COUNT"]: "TEAM-DISTINCT-COUNT",
  ["TEAM-COUNT-NOT-NULL"]: "TEAM-COUNT-NOT-NULL",
  ["TEAM-QuotaAttainment"]: "TEAM-QuotaAttainment",
  ["TEAM-QuotaErosion"]: "TEAM-QuotaErosion",
  ["TEAM-Quota"]: "TEAM-Quota",
  ["TEAM-SUMIF"]: "TEAM-SUMIF",
  ["TEAM-COUNTIF"]: "TEAM-COUNTIF",
  ["TEAM-COUNT"]: "TEAM-COUNT",
};

export const EXPRESSION_BOX_STATUS = {
  LOADING: "LOADING",
  WARNING: "WARNING",
  INVALID: "INVALID",
  INITIAL: "INITIAL",
  VALID: "VALID",
  ABORTED: "ABORTED",
};

export const LEARN_MORE_FOR_EXPRESSION_BOX_FUNCTIONS = "LEARN_MORE";

export const COMMISSION_TYPE = {
  COMMISSION_PLAN: "commissionplan",
  FORECAST_PLAN: "forecastplan",
};

export const LOGIN_METHOD = {
  GOOGLE: "GOOGLE",
  SALESFORCE: "SALESFORCE",
};

export const LOGIN_METHOD_TO_AUTH0_CONNECTION = {
  GOOGLE: "google-oauth2",
  SALESFORCE: "salesforce",
};

export const TRIGGER_TYPES = {
  DATASHEET: "datasheet",
  DATASHEET_DATA: "datasheet_data",
  COMMISSION: "commission",
};

export const DATABOOK_TRIGGERS = {
  NEW: "new",
  UPDATED: "updated",
  DELETED: "deleted",
};

export const TRIGGER_CATEGORIES = {
  event_based: {
    label: "Event Based Trigger",
    categories: ["trigger"],
    icon: <NavigationPointer01Icon className="w-4 h-4" />,
  },
  scheduled: {
    label: "Scheduled Trigger",
    categories: ["frequency"],
    icon: <ClockRefreshIcon className="w-4 h-4" />,
  },
  on_demand: {
    label: "On demand Trigger",
    categories: [],
    icon: <ActivityIcon className="w-4 h-4" />,
  },
};

export const STATEMENT_EXPORT_TYPES = {
  PDF: "pdf",
  XLSX: "xlsx",
};

export const COMMISSION_PLAN_WORKFLOW_STATUS = {
  UNDER_REVIEW: "Under review",
  PUBLISHED: "published",
  REJECTED: "rejected",
  REVOKED: "revoked",
  APPROVED: "approved",
  REQUESTED: "requested",
  NOT_REQUESTED: "not_requested",
};
