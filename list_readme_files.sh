#!/bin/bash

# Script to list README.md files (case insensitive) with exclusions
# Usage: ./list_readme_files.sh [--delete]

DELETE_MODE=false

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --delete|-d)
            DELETE_MODE=true
            shift
            ;;
        --help|-h)
            echo "Usage: $0 [--delete|-d]"
            echo "Lists all README.md files (case insensitive) excluding:"
            echo "  - node_modules directories"
            echo "  - mnemosyne folder"
            echo "  - top-level README.md file"
            echo ""
            echo "Options:"
            echo "  --delete, -d    Delete the found files after listing them"
            echo "  --help, -h      Show this help message"
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

# Get the absolute path of the current directory
CURRENT_DIR=$(pwd)
TOP_LEVEL_README="$CURRENT_DIR/README.md"

echo "Searching for README.md files (case insensitive)..."
echo "Excluding: node_modules, mnemosyne folder, and top-level README.md"
echo "Current directory: $CURRENT_DIR"
echo ""

# Find README files (case insensitive) with exclusions
# -iname for case insensitive matching
# -path exclusions for node_modules and mnemosyne
README_FILES=$(find "$CURRENT_DIR" \
    -type f \
    -iname "readme.md" \
    ! -path "*/node_modules/*" \
    ! -path "*/mnemosyne/*" \
    ! -path "$TOP_LEVEL_README")

# Convert to array for easier processing (compatible with older bash)
FILTERED_FILES=()
while IFS= read -r -d '' file; do
    if [[ -n "$file" && -f "$file" ]]; then
        FILTERED_FILES+=("$file")
    fi
done < <(find "$CURRENT_DIR" \
    -type f \
    -iname "readme.md" \
    ! -path "*/node_modules/*" \
    ! -path "*/mnemosyne/*" \
    ! -path "$TOP_LEVEL_README" \
    -print0)

# Display results
if [[ ${#FILTERED_FILES[@]} -eq 0 ]]; then
    echo "No README.md files found matching the criteria."
    exit 0
fi

echo "Found ${#FILTERED_FILES[@]} README.md file(s):"
echo ""

# List all found files with relative paths
for file in "${FILTERED_FILES[@]}"; do
    # Convert to relative path for cleaner display
    REL_PATH=$(realpath --relative-to="$CURRENT_DIR" "$file" 2>/dev/null || echo "$file")
    echo "  $REL_PATH"
done

echo ""

# Handle deletion if requested
if [[ "$DELETE_MODE" == true ]]; then
    echo "⚠️  WARNING: You are about to delete ${#FILTERED_FILES[@]} README.md file(s)!"
    echo ""
    read -p "Are you sure you want to proceed? (y/N): " -n 1 -r
    echo ""
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo ""
        echo "Deleting files..."
        
        DELETED_COUNT=0
        FAILED_COUNT=0
        
        for file in "${FILTERED_FILES[@]}"; do
            REL_PATH=$(realpath --relative-to="$CURRENT_DIR" "$file" 2>/dev/null || echo "$file")
            if rm "$file" 2>/dev/null; then
                echo "  ✓ Deleted: $REL_PATH"
                ((DELETED_COUNT++))
            else
                echo "  ✗ Failed to delete: $REL_PATH"
                ((FAILED_COUNT++))
            fi
        done
        
        echo ""
        echo "Summary:"
        echo "  Deleted: $DELETED_COUNT file(s)"
        if [[ $FAILED_COUNT -gt 0 ]]; then
            echo "  Failed: $FAILED_COUNT file(s)"
        fi
    else
        echo "Operation cancelled."
    fi
else
    echo "To delete these files, run: $0 --delete"
fi 