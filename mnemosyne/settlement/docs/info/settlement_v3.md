# Settlement v3

## Introduction

Settlement v3 is a significant enhancement to the settlement synchronization process in the Everstage platform. This feature replaces the previous two-step settlement sync process (commission-changes + datasheet-changes) with period level sync similar to commission sync.
Settlement v3 is also referred to as the Settlement Revamp


Key improvements include:

- More precise synchronization at the criteria level instead of payee level
- Decoupling from commission sync for better performance and reliability
- Running settlement sync only for intended periods
- Enhanced batching strategies for improved memory management
- Configurable execution modes for safe rollout

## Execution Modes

Settlement v3 supports three execution modes controlled by the `settlement_v3_execution_mode` feature flag:

1. `DISABLED` - Settlement v3 sync is completely disabled; the system will use the legacy sync process
2. `DRY_RUN` - Settlement v3 sync runs in parallel with the legacy process but writes to a separate dry-run table (`settlement_{client_id}_dry_run`)
3. `ACTUAL_RUN` - Settlement v3 sync is fully enabled and writes to the production settlement table (`settlement_{client_id}`)

The default mode is `DISABLED` to ensure safe rollout.

## Sync Granularity

### Per-Criteria vs. Previous Per-Payee

Settlement Sync v3 processes data at the criteria level rather than the payee level. This provides several advantages:

- Improved performance through more efficient batching

### Decoupling from Commission Sync

Unlike the previous implementation, Settlement Sync v3 is completely decoupled from the commission sync process. This decoupling allows:

- Independent scheduling and execution
- Reduced interdependencies between commission and settlement sync

## Input Data Sources & Join Logic

Settlement v3 uses two primary input tables:

1. `payout_snapshot_data` - Contains commission data snapshots
2. `datasheet_data` - Contains datasheet information

The sync process joins these data sources based on the criteria configuration and applies settlement rules to determine the final settlement amounts.

When calculating settlement on collection, we also filter out the records which have settlement date (date field in datasheet) less than commission date (commission_date in payout_snapshot_data). This only compares date not time. This is done only in settlement v3. In older settlement modes records with settlement date less than commission date are  included in settlement calculation.

## Flow of the Settlement Sync

The Settlement Sync v3 process follows these key steps:

1. **Initialization**:
   - Entry point through either `run_settlement_v3_task` (main entry) or `dry_run_settlement_v3_sync` (dry run entry)
   - Code path: `settlement_v3/task_engine/tasks/settlement_tasks.py`
   - Check execution mode and prepare appropriate stage name and queue
   - Initialize monitoring and tracking via `mark_stage_as_started`

2. **Plan Building**:
   - `SettlementSyncPlanBuilder` constructs the execution plan
   - Code path: `settlement_v3/task_engine/settlement_sync_plan_builder.py`
   - Identifies valid payees and their payout frequencies via `get_all_valid_frequency_to_payee_map` or `get_valid_frequency_to_payee_map_for_given_payees`
   - Determines periods to process
   - Creates task groups for settlement processing via `get_settlement_task_group`

3. **Task Preparation**:
   - Setup tasks prepare the environment and temporary tables via `setup_settlement_sync`
   - Tasks are organized by plan, criteria, and period in `_prepare_settlement_tasks`
   - Metadata is persisted to Snowflake tables for tracking in `_persist_payee_plan_metadata` and `_persist_non_settlement_criterias_meta_data`

4. **Execution**:
   - Tasks are executed in parallel based on batching strategy
   - Each criteria is processed independently via `run_settlement_task`
   - For criteria without settlement rules, commission data is mirrored directly via `run_no_rule_settlement_task`
   - Result is written as parquet file to s3
   - Code path: `settlement_v3/task_engine/settlement_task_executor.py` and `settlement_v3/task_engine/no_rule_settlement_task_executor.py`

5. **Data Loading**:
   - Processed settlement data is loaded into Snowflake via `load_settlement_v3_data_to_snowflake`
   - Written to either production (`settlement_{client_id}`) or dry-run tables (`settlement_{client_id}_dry_run`) based on execution mode from parquet files.

6. **Cleanup**:
   - Temporary tables are dropped via `drop_settlement_sync_helper_tables`
   - Process status is updated via `mark_stage_as_completed`
   - Resources are released via `end_settlement_wrapper_process`

The flow is orchestrated through Celery tasks, with appropriate locking to prevent concurrent modifications to the same data. The main orchestration happens in `run_settlement_v3_sync` function in `commission_engine/services/maestro.py`.

## Batching Strategies

Settlement Sync v3 implements sophisticated batching strategies to efficiently process large datasets while managing memory usage.

### In-Memory Batching

Two approaches are available for in-memory batching:

1. **Fixed Batch Size**: Controlled by the `settlement_in_memory_batch_size` feature flag
   - Uses a constant batch size for all processing
   - Default value is 30,000 records if not specified
   - Implementation in `InMemorySettlementStrategy._get_batch_size`

```python
def _get_batch_size(self, query):
    if self.is_mem_regulated_batching_enabled:
        return get_memory_regulated_batch_size_sf(query)
    return self.fixed_batch_size
```

2. **Adaptive Memory-Regulated Batching**: Controlled by the `mem_regulated_batching` feature flag
   - Dynamically adjusts batch size based on query complexity and available memory
   - Optimizes performance while preventing out-of-memory errors
   - Uses `get_memory_regulated_batch_size_sf` to determine optimal batch size

### Lambda Batching

For larger datasets or complex calculations, Settlement Sync v3 can leverage AWS Lambda:

- Enabled via the `lambda_for_settlement` feature flag
- Batch size controlled by `settlement_lambda_batch_size` (default: 100,000 records)
- Distributes processing across multiple Lambda functions for parallel execution
- Automatically handles result aggregation

The `LambdaSettlementStrategy` class manages this process, breaking down large datasets into manageable chunks and distributing them across Lambda functions.

## Locking Behavior

Settlement Sync v3 does not run sync for locked payees.

## Summary of Feature Flags

| Feature Flag | Description | Default Value |
|--------------|-------------|---------------|
| `settlement_v3_execution_mode` | Controls execution mode (DISABLED, DRY_RUN, ACTUAL_RUN) | DISABLED |
| `settlement_in_memory_batch_size` | Fixed batch size for in-memory processing | 30,000 |
| `mem_regulated_batching` | Enables adaptive memory-regulated batching | false |
| `lambda_for_settlement` | Enables Lambda-based distributed processing | false |
| `settlement_lambda_batch_size` | Batch size for Lambda processing | 100,000 |

These feature flags provide fine-grained control over the Settlement Sync v3 behavior, allowing for customization based on client needs and system capabilities.

## Feature Flag Combinations

The feature flags work together in specific combinations to control the settlement sync behavior:

1. **Lambda Processing**:
   - When `lambda_for_settlement` is enabled:
     - `settlement_lambda_batch_size` controls the batch size
     - In-memory batching (`settlement_in_memory_batch_size`) and memory-regulated batching (`mem_regulated_batching`) are ignored
   - When `lambda_for_settlement` is disabled:
     - `settlement_lambda_batch_size` is not used
     - Processing falls back to in-memory batching

2. **In-Memory Processing**:
   - When `lambda_for_settlement` is disabled:
     - If `mem_regulated_batching` is enabled:
       - `settlement_in_memory_batch_size` is ignored
       - Batch size is dynamically calculated based on available memory
     - If `mem_regulated_batching` is disabled:
       - `settlement_in_memory_batch_size` controls the fixed batch size
       - Defaults to 30,000 if not specified

3. **Execution Mode**:
   - `settlement_v3_execution_mode` is independent of batching strategies
   - Controls whether the sync runs in DISABLED, DRY_RUN, or ACTUAL_RUN mode
   - Affects the target table for data loading but not the processing method

These feature flag combinations ensure optimal resource utilization while maintaining flexibility in deployment configurations.

## Testing

For testing the Settlement Sync v3 functionality, use client_id 6901 with name 'Settlement 3.0' in the unit test database. This client has been configured with the necessary data and settings to test all aspects of the settlement sync process. 