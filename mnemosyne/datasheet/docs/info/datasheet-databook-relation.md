# Datasheet

Datasheets are the primary datasource for any information in our product.  The datasheet derives its information from a custom object, report object or
another datasheet.

# Databook

* A databook is a way of grouping datasheets in the product.  Each datasheet can belong to exactly one databook.
* A databook has the following properties:
    * Can be `active` or `archived` - only datasheets in an `active` databook will participate in the daily ETL run
    * Can be cloned - if a databook is cloned, all the datasheets in it will be cloned as well
    * Can be deleted - if a databook is deleted, all the datasheets in it will be deleted as well

# Linking datasheets across databooks

* (TODO: Draw a diagram of how crosslinking works)

* A datasheet can derive its information from another datasheet in the same databook or in another databook (added this feature in October 2023)
* The transformations that allow for a datasheet in another databook to be used are - 
  * Source of datasheet
  * Join
  * Union
  * GetHierarchy calculated field
* A datasheet used in this manner is called a `crosslinked-datasheet`
* A databook where one or more constituent datasheets are crosslinked-datasheets will be called a `crosslinked-databook`
* A `crosslinked-databook` and the databooks of its cross-linked datasheets are called a `crosslinked-databook-set`
* A `crosslink-reasons` field on a datasheet tells us why a datasheet is crosslinked.  For each `crosslink`,
there can be one or more reasons
