# Hierarchy - an Everstage custom datatype

**Work In Progress - Information here is incomplete**

## Design of `generateHierarchy()` Calc Field Function

### Args:

(\*) -> mandatory arguments

#### Argument Description

- `REF_SHEET*`: Specifies the reference sheet name (e.g., user_report).
- `CHILD_COL*`: Specifies the column name for the child element (e.g., payee_email).
- `PARENT_COL*`: Specifies the column name for the parent element (e.g., manager_email).
- `ESD_COL`: Specifies the column name for the effective start date (optional).
- `EED_COL`: Specifies the column name for the effective end date (optional).
- `HIERARCHY_FOR*`: Specifies the column name for which the hierarchy is to be generated (e.g., deal_owner_email).
- `AS_OF_DATE*`: Specifies the column name for the reference date.

**NOTE:** This can be selected as `CURRENT_DATE`.

The `ESD_COL` and `EED_COL` are kept as optional to support custom hierarchy, as custom hierarchy will not have ESD and EED (e.g., Region, etc.). When skipped, the `ESD_COL` will be `datetime.datetime.min`, and `EED_COL` will be `datetime.datetime.max`. In situations where it is skipped and there are multiple hierarchies present, the result might be inconsistent.

The `REF_SHEET` can either be the same sheet or a different datasheet.

## Technical Solution

1. **Generate the hierarchy graph:**

   - Utilize the `networkx` package to generate the graph.
   - Construct the graph by iterating through the rows of the reference sheet and adding edges to represent parent-child relationships.
   - Store the effective start and end dates as edge information.

2. **Upload the graph to the Snowflake stage:**

   - Serialize the graph object and upload it to the Snowflake stage as a JSON file.
   - Utilize the `networkx` built-in function to serialize the graph object.

3. **Snowflake UDF:**
   - Create a Snowflake UDF to read the JSON file from the stage and deserialize it to a graph object.
   - Traverse the graph to generate the hierarchy for the given user id and `as_of_date`.

See code snippet below -

https://github.com/Everstage/everstage-spm/blob/4ca239a1982a3d3d2ea02af77b443298e4060661/interstage_project/everstage_etl/databook_etl/snowpark_calc_fields.py#L175

**Note:** Upload to the stage is necessary as inside UDF, databases cannot be accessed.

## Flow

- The 'Edit datasheet' UI is used to add a new calculated field as a datasheet variable called 'Hierarchy'
- Once defined, the `reference datasheet` will be made a dependency (cannot be deleted, will run before the source datasheet in ETL etc)
- The new calculated field will produce an output as a json string that looks like -

```json
{
  "level": {
    "1": {
      "node": "<EMAIL>"
    },
    "2": {
      "node": "<EMAIL>"
    }
  },
  "max_level": 3,
  "node": "<EMAIL>",
  "repr": "<EMAIL>--><EMAIL>--><EMAIL>"
}
```

- Stormbreaker has been configured to read only the `repr` field in case of a hierarchy data field

- Refer to the video attached -

<a href="https://www.loom.com/share/8b93ccd5568a4c76bd9193ab023665e5">
    <p>New Hierarchy Data Type Walkthrough - Watch Video</p>
    <img style="max-width:300px;" src="https://cdn.loom.com/sessions/thumbnails/8b93ccd5568a4c76bd9193ab023665e5-with-play.gif">
</a>

## Flattening the Hierarchy Column

We're introduced a new datasheet transformation called FLATTEN HIERARCHY...

The query looks something like below:

```sql
SELECT *
FROM "my_table" AS p
LATERAL FLATTEN(input => p.hierarchy::object, recursive => true)
WHERE key = 'node';
```

| USER | MANAGER | USER_HIERARCHY | VALUE |
| ---- | ------- | -------------- | ----- |
| D    | NULL    | D              | D     |
| G    | NULL    | G              | G     |
| C    | D       | C>D            | C     |
| C    | D       | C>D            | D     |
| F    | G       | F>G            | F     |
| F    | G       | F>G            | G     |
| B    | C       | B>C>D          | B     |
| B    | C       | B>C>D          | C     |
| E    | F       | E>F>G          | E     |
| E    | F       | E>F>G          | F     |
| A    | B       | A>B>C>D        | A     |
| A    | B       | A>B>C>D        | B     |

**Note:** The PATH, and VALUE will be generated by the FLATTEN Snowflake function. We can utilize them to construct a row_key to prevent duplicate row_keys. However, it's important to note that PATH will not be stored in the database.

<a href="https://www.loom.com/share/8d54990de5734ae9b6fa92750e08a2db">
    <p>FLATTEN transformation Walkthrough - Watch Video</p>
</a>
<a href="https://www.loom.com/share/8d54990de5734ae9b6fa92750e08a2db">
    <img style="max-width:300px;" src="https://cdn.loom.com/sessions/thumbnails/8d54990de5734ae9b6fa92750e08a2db-with-play.gif">
</a>

## GetValueFromHierarchy Function

This function can be used to retrieve either the top-level node in the hierarchy or a node at a specific level from the hierarchy variable.

### Argument Description

- `HIERARCHY_FIELD*`: Specifies the hierarchy variable column name.
- `LEVEL*`: Specifies the hierarchy level to retrieve.

### Output

The output of this function may be either a STRING or an EMAIL field...

### Example

```python
h1 = Rohit → Virat → Dhoni → Sachin
h2 = <EMAIL> → <EMAIL> → <EMAIL> → <EMAIL>

### RESULT ###
GetValueFromHierarchy(h1, 2) ---- Virat (STRING)
GetValueFromHierarchy(h2, 2) ---- <EMAIL> (EMAIL)
```

## Artifacts

- [Original Product Specification](https://interstage.atlassian.net/wiki/spaces/PRODUCT/pages/548045257/Data+aggregation+in+dashboards+including+multi-level+hierarchy)
- [Technical Design Doc](https://interstage.atlassian.net/wiki/spaces/TECH/pages/619774014/Multi-level+Hierarchy+Inside+Datasheets)
- [Implementation tickets](https://interstage.atlassian.net/browse/INTER-3353)
