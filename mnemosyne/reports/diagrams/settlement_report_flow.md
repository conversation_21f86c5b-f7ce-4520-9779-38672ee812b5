# Settlement Report Flow

This diagram illustrates the workflow of settlement report synchronization. It includes steps for populating payee period data, invalidating records, and inserting records into Snowflake.

```mermaid
flowchart TD
    Start(["Start"])
    SR1{mode}
    SR2["Populate payee period data to temp table in Snowflake<br/>(For changes mode, get from 'settlement_changes' table after last successful report sync.<br/>For custom mode, we pass period and payees as params.)"]
    SR3["For ALL mode invalidate all Settlement type records.<br/>For other modes, invalidate report records by joining with temp table created."]
    SR4["Insert records from settlement snapshot.<br/>ALL – insert all records.<br/>CHANGES/CUSTOM – insert records for periods and payees.<br/>Filter out records with (settlement period) period_start_date < (earned period) comm_period_start_date."]
    SR5["Invalidate Commission Adjustments records from settlement reports<br/>(based on record_type).<br/>ALL/CHANGES – invalidate all Commission adjustments.<br/>CUSTOM – invalidate only for given periods and payees."]
    SR6["Insert Commission Adjustment records from 'adjustment_changes_{client_id}_{e2e_sync_run_id}' created during Report PRE work."]
    SR7["Invalidate Draw Adjustments records from settlement reports<br/>(based on record_type).<br/>ALL/CHANGES – invalidate all Draw adjustments.<br/>CUSTOM – invalidate only for given periods and payees."]
    SR8["Insert Draw Adjustment records from 'adjustment_changes_draws_{client_id}_{e2e_sync_run_id}' created during Report PRE work."]
    End(["End"])

    Start --> SR1
    SR1 -- "CUSTOM/CHANGES" --> SR2
    SR1 -- "ALL"           --> SR3
    SR2 --> SR3
    SR3 --> SR4
    SR4 --> SR5
    SR5 --> SR6
    SR6 --> SR7
    SR7 --> SR8
    SR8 --> End
```